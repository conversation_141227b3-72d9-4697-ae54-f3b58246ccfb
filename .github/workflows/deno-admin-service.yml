name: Deno Admin Service CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'services/admin-deno/**'
      - '.github/workflows/deno-admin-service.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'services/admin-deno/**'
      - '.github/workflows/deno-admin-service.yml'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/admin-service-deno

jobs:
  test:
    name: Test Deno Admin Service
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Deno
      uses: denoland/setup-deno@v1
      with:
        deno-version: v2.4.0
        
    - name: Cache Deno dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/deno
        key: ${{ runner.os }}-deno-${{ hashFiles('services/admin-deno/deno.json') }}
        restore-keys: |
          ${{ runner.os }}-deno-
          
    - name: Check TypeScript compilation
      working-directory: services/admin-deno
      run: deno check src/main.ts
      
    - name: Run linting
      working-directory: services/admin-deno
      run: deno lint src/ tests/
      
    - name: Run formatting check
      working-directory: services/admin-deno
      run: deno fmt --check src/ tests/
      
    - name: Run tests
      working-directory: services/admin-deno
      run: deno test --allow-all --coverage=coverage tests/
      
    - name: Generate coverage report
      working-directory: services/admin-deno
      run: deno coverage coverage --lcov --output=coverage.lcov
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: services/admin-deno/coverage.lcov
        flags: deno-admin-service
        name: deno-admin-service-coverage

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Deno
      uses: denoland/setup-deno@v1
      with:
        deno-version: v2.4.0
        
    - name: Run security audit
      working-directory: services/admin-deno
      run: |
        # Check for known vulnerabilities in dependencies
        deno info --json src/main.ts | jq '.modules[].specifier' | grep -E '^https?://' | sort -u > deps.txt
        echo "External dependencies:"
        cat deps.txt
        
    - name: Scan for secrets
      uses: trufflesecurity/trufflehog@main
      with:
        path: services/admin-deno/
        base: main
        head: HEAD

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [test, security]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: services/admin-deno
        file: services/admin-deno/Dockerfile.deno
        target: production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  performance:
    name: Performance Benchmarks
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Deno
      uses: denoland/setup-deno@v1
      with:
        deno-version: v2.4.0
        
    - name: Run performance benchmarks
      working-directory: services/admin-deno
      run: |
        echo "## Performance Benchmark Results" >> $GITHUB_STEP_SUMMARY
        echo "| Metric | Value | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|--------|-------|--------|" >> $GITHUB_STEP_SUMMARY
        
        # Run performance tests and capture output
        deno test --allow-all tests/performance_test.ts > perf_output.txt 2>&1
        deno test --allow-all tests/nodejs_comparison_test.ts >> perf_output.txt 2>&1
        
        # Extract key metrics (this would need to be customized based on actual output)
        echo "| Startup Time | $(grep -o 'Startup time: [0-9.]*ms' perf_output.txt | head -1) | ✅ |" >> $GITHUB_STEP_SUMMARY
        echo "| Memory Usage | $(grep -o 'Memory usage: [0-9.]* MB' perf_output.txt | head -1) | ✅ |" >> $GITHUB_STEP_SUMMARY
        echo "| Module Load | $(grep -o 'Module load time: [0-9.]*ms' perf_output.txt | head -1) | ✅ |" >> $GITHUB_STEP_SUMMARY
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "Full performance test output:" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        cat perf_output.txt >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to staging
      run: |
        echo "Deploying Deno admin service to staging environment"
        # This would contain actual deployment commands
        # For example, updating Kubernetes manifests, Helm charts, etc.
        
    - name: Run health check
      run: |
        echo "Running post-deployment health checks"
        # Health check commands would go here

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, deploy-staging]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to production
      run: |
        echo "Deploying Deno admin service to production environment"
        # Production deployment commands
        
    - name: Run health check
      run: |
        echo "Running post-deployment health checks"
        # Production health check commands
        
    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        text: 'Deno Admin Service deployed to production'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      if: always()
