name: Production Deployment Pipeline

on:
  push:
    branches: [main]
    paths:
      - 'services/**'
      - 'frontend/**'
      - 'infrastructure/**'
      - '.github/workflows/**'
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  CLUSTER_NAME: ecommerce-analytics-prod
  CLUSTER_REGION: us-east-1

jobs:
  # Security and Quality Gates
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript, typescript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Comprehensive Testing Suite
  test-suite:
    name: Comprehensive Testing
    runs-on: ubuntu-latest
    services:
      postgres:
        image: timescale/timescaledb:latest-pg15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: ecommerce_analytics_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    strategy:
      matrix:
        test-suite:
          - analytics
          - performance
          - security
          - frontend
          - integration

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          cd services/analytics && npm ci
          cd ../integration && npm ci
          cd ../../frontend && npm ci

      - name: Setup test environment
        run: |
          cp .env.test.example .env.test
          npm run test:setup

      - name: Run test suite - ${{ matrix.test-suite }}
        run: npm run test:${{ matrix.test-suite }}
        env:
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: ecommerce_analytics_test
          DB_USER: postgres
          DB_PASSWORD: test_password
          REDIS_URL: redis://localhost:6379
          NODE_ENV: test

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.test-suite }}
          path: |
            testing/reports/
            coverage/
            junit.xml

  # Build and Push Container Images
  build-images:
    name: Build Container Images
    runs-on: ubuntu-latest
    needs: [security-scan, test-suite]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        service:
          - analytics
          - integration
          - dashboard
    outputs:
      analytics-image: ${{ steps.meta-analytics.outputs.tags }}
      integration-image: ${{ steps.meta-integration.outputs.tags }}
      dashboard-image: ${{ steps.meta-dashboard.outputs.tags }}
      analytics-digest: ${{ steps.build-analytics.outputs.digest }}
      integration-digest: ${{ steps.build-integration.outputs.digest }}
      dashboard-digest: ${{ steps.build-dashboard.outputs.digest }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta-${{ matrix.service }}
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push image
        id: build-${{ matrix.service }}
        uses: docker/build-push-action@v5
        with:
          context: .
          file: services/${{ matrix.service }}/Dockerfile
          push: true
          tags: ${{ steps.meta-${{ matrix.service }}.outputs.tags }}
          labels: ${{ steps.meta-${{ matrix.service }}.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Generate SBOM
        uses: anchore/sbom-action@v0
        with:
          image: ${{ steps.meta-${{ matrix.service }}.outputs.tags }}
          format: spdx-json
          output-file: sbom-${{ matrix.service }}.spdx.json

      - name: Upload SBOM
        uses: actions/upload-artifact@v4
        with:
          name: sbom-${{ matrix.service }}
          path: sbom-${{ matrix.service }}.spdx.json

  # Infrastructure Validation
  validate-infrastructure:
    name: Validate Infrastructure
    runs-on: ubuntu-latest
    needs: [security-scan]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.6.0

      - name: Terraform Format Check
        run: terraform fmt -check -recursive infrastructure/terraform/

      - name: Terraform Validate
        run: |
          cd infrastructure/terraform/
          terraform init -backend=false
          terraform validate

      - name: Validate Kubernetes manifests
        run: |
          # Install kubeval
          wget https://github.com/instrumenta/kubeval/releases/latest/download/kubeval-linux-amd64.tar.gz
          tar xf kubeval-linux-amd64.tar.gz
          sudo mv kubeval /usr/local/bin
          
          # Validate all Kubernetes YAML files
          find infrastructure/k8s -name "*.yaml" -exec kubeval {} \;

      - name: Helm Lint
        run: |
          # Install Helm
          curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
          
          # Lint Helm charts
          find infrastructure/helm -name "Chart.yaml" -execdir helm lint . \;

  # Staging Deployment
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-images, validate-infrastructure]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: https://staging.ecommerce-analytics.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.CLUSTER_REGION }}

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region ${{ env.CLUSTER_REGION }} --name ${{ env.CLUSTER_NAME }}-staging

      - name: Deploy to staging
        run: |
          # Update image tags in Helm values
          sed -i "s|image:.*|image: ${{ needs.build-images.outputs.analytics-image }}|g" infrastructure/helm/analytics/values-staging.yaml
          sed -i "s|image:.*|image: ${{ needs.build-images.outputs.integration-image }}|g" infrastructure/helm/integration/values-staging.yaml
          sed -i "s|image:.*|image: ${{ needs.build-images.outputs.dashboard-image }}|g" infrastructure/helm/dashboard/values-staging.yaml
          
          # Deploy with Helm
          helm upgrade --install analytics infrastructure/helm/analytics/ \
            --namespace staging \
            --values infrastructure/helm/analytics/values-staging.yaml \
            --wait --timeout=10m
          
          helm upgrade --install integration infrastructure/helm/integration/ \
            --namespace staging \
            --values infrastructure/helm/integration/values-staging.yaml \
            --wait --timeout=10m
          
          helm upgrade --install dashboard infrastructure/helm/dashboard/ \
            --namespace staging \
            --values infrastructure/helm/dashboard/values-staging.yaml \
            --wait --timeout=10m

      - name: Run smoke tests
        run: |
          # Wait for deployment to be ready
          kubectl wait --for=condition=available --timeout=300s deployment/analytics-service -n staging
          kubectl wait --for=condition=available --timeout=300s deployment/integration-service -n staging
          kubectl wait --for=condition=available --timeout=300s deployment/dashboard-service -n staging
          
          # Run smoke tests
          npm run test:smoke -- --environment=staging

  # Production Deployment
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://app.ecommerce-analytics.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.CLUSTER_REGION }}

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region ${{ env.CLUSTER_REGION }} --name ${{ env.CLUSTER_NAME }}

      - name: Blue-Green Deployment
        run: |
          # Create blue-green deployment script
          cat > deploy-blue-green.sh << 'EOF'
          #!/bin/bash
          set -euo pipefail
          
          SERVICE=$1
          IMAGE=$2
          NAMESPACE=production
          
          # Get current deployment
          CURRENT_COLOR=$(kubectl get deployment ${SERVICE}-service -n $NAMESPACE -o jsonpath='{.metadata.labels.color}' || echo "blue")
          NEW_COLOR=$([ "$CURRENT_COLOR" = "blue" ] && echo "green" || echo "blue")
          
          echo "Current color: $CURRENT_COLOR, New color: $NEW_COLOR"
          
          # Deploy new version
          helm upgrade --install ${SERVICE}-${NEW_COLOR} infrastructure/helm/${SERVICE}/ \
            --namespace $NAMESPACE \
            --values infrastructure/helm/${SERVICE}/values-production.yaml \
            --set image.tag=${IMAGE##*:} \
            --set color=$NEW_COLOR \
            --wait --timeout=10m
          
          # Health check
          kubectl wait --for=condition=available --timeout=300s deployment/${SERVICE}-service-${NEW_COLOR} -n $NAMESPACE
          
          # Run health checks
          HEALTH_URL="http://${SERVICE}-service-${NEW_COLOR}.${NAMESPACE}.svc.cluster.local:8080/health"
          for i in {1..30}; do
            if kubectl run health-check-${NEW_COLOR} --rm -i --restart=Never --image=curlimages/curl -- curl -f $HEALTH_URL; then
              echo "Health check passed"
              break
            fi
            echo "Health check failed, retrying in 10s..."
            sleep 10
          done
          
          # Switch traffic
          kubectl patch service ${SERVICE}-service -n $NAMESPACE -p '{"spec":{"selector":{"color":"'$NEW_COLOR'"}}}'
          
          # Wait and verify
          sleep 30
          
          # Cleanup old deployment
          helm uninstall ${SERVICE}-${CURRENT_COLOR} -n $NAMESPACE || true
          EOF
          
          chmod +x deploy-blue-green.sh
          
          # Deploy each service
          ./deploy-blue-green.sh analytics ${{ needs.build-images.outputs.analytics-image }}
          ./deploy-blue-green.sh integration ${{ needs.build-images.outputs.integration-image }}
          ./deploy-blue-green.sh dashboard ${{ needs.build-images.outputs.dashboard-image }}

      - name: Run production health checks
        run: |
          # Comprehensive health checks
          npm run test:health -- --environment=production
          
          # Performance baseline tests
          npm run test:performance -- --environment=production --baseline

      - name: Update deployment status
        run: |
          # Create deployment record
          kubectl create configmap deployment-$(date +%Y%m%d-%H%M%S) \
            --from-literal=analytics-image=${{ needs.build-images.outputs.analytics-image }} \
            --from-literal=integration-image=${{ needs.build-images.outputs.integration-image }} \
            --from-literal=dashboard-image=${{ needs.build-images.outputs.dashboard-image }} \
            --from-literal=commit-sha=${{ github.sha }} \
            --from-literal=deployed-by=${{ github.actor }} \
            --from-literal=deployed-at=$(date -u +%Y-%m-%dT%H:%M:%SZ) \
            -n production

  # Post-deployment monitoring
  post-deployment-monitoring:
    name: Post-deployment Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always() && needs.deploy-production.result == 'success'
    steps:
      - name: Monitor deployment metrics
        run: |
          # Monitor key metrics for 10 minutes post-deployment
          echo "Monitoring deployment metrics..."
          
          # This would typically integrate with your monitoring system
          # to check error rates, latency, and other SLIs
          
          sleep 600  # 10 minutes
          
          echo "Post-deployment monitoring completed"

      - name: Notify deployment status
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
