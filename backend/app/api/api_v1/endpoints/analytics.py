from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, List
from datetime import datetime, timedelta
import random

from app.core.sentry import capture_message, add_breadcrumb, set_tag

router = APIRouter()


@router.get("/metrics")
async def get_analytics_metrics(
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    metric_type: Optional[str] = Query(None, description="Type of metric (revenue, clicks, conversions)")
):
    """Get analytics metrics with optional filtering."""
    
    # Add Sentry breadcrumb
    add_breadcrumb(
        message="Analytics metrics requested",
        category="analytics",
        data={
            "start_date": start_date,
            "end_date": end_date,
            "metric_type": metric_type
        }
    )
    
    # Set Sentry tag
    set_tag("endpoint", "analytics_metrics")
    
    try:
        # Mock analytics data
        metrics = {
            "revenue": {
                "current": random.randint(10000, 50000),
                "previous": random.randint(8000, 45000),
                "growth": random.uniform(-10, 25)
            },
            "clicks": {
                "current": random.randint(1000, 10000),
                "previous": random.randint(800, 9000),
                "growth": random.uniform(-5, 30)
            },
            "conversions": {
                "current": random.randint(100, 1000),
                "previous": random.randint(80, 900),
                "growth": random.uniform(-15, 35)
            },
            "period": {
                "start": start_date or (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"),
                "end": end_date or datetime.now().strftime("%Y-%m-%d")
            }
        }
        
        # Filter by metric type if specified
        if metric_type and metric_type in metrics:
            filtered_metrics = {
                metric_type: metrics[metric_type],
                "period": metrics["period"]
            }
            return filtered_metrics
        
        capture_message(f"Analytics metrics retrieved successfully", "info")
        return metrics
        
    except Exception as e:
        capture_message(f"Error retrieving analytics metrics: {str(e)}", "error")
        raise HTTPException(status_code=500, detail="Failed to retrieve analytics metrics")


@router.get("/trends")
async def get_analytics_trends(
    period: str = Query("7d", description="Time period (7d, 30d, 90d)")
):
    """Get analytics trends over time."""
    
    add_breadcrumb(
        message="Analytics trends requested",
        category="analytics",
        data={"period": period}
    )
    
    set_tag("endpoint", "analytics_trends")
    
    try:
        # Generate mock trend data
        days = {"7d": 7, "30d": 30, "90d": 90}.get(period, 7)
        
        trends = []
        base_date = datetime.now() - timedelta(days=days)
        
        for i in range(days):
            date = base_date + timedelta(days=i)
            trends.append({
                "date": date.strftime("%Y-%m-%d"),
                "revenue": random.randint(500, 2000),
                "clicks": random.randint(50, 500),
                "conversions": random.randint(5, 50),
                "conversion_rate": random.uniform(2, 15)
            })
        
        capture_message(f"Analytics trends retrieved for period: {period}", "info")
        return {
            "period": period,
            "data": trends
        }
        
    except Exception as e:
        capture_message(f"Error retrieving analytics trends: {str(e)}", "error")
        raise HTTPException(status_code=500, detail="Failed to retrieve analytics trends")


@router.get("/cohorts")
async def get_cohort_analysis():
    """Get cohort analysis data."""
    
    add_breadcrumb(
        message="Cohort analysis requested",
        category="analytics"
    )
    
    set_tag("endpoint", "cohort_analysis")
    
    try:
        # Generate mock cohort data
        cohorts = []
        for i in range(12):  # 12 months of cohorts
            cohort_date = datetime.now() - timedelta(days=30 * i)
            
            # Generate retention rates
            retention_rates = []
            for week in range(12):
                rate = max(0, random.uniform(0.1, 0.8) * (0.9 ** week))
                retention_rates.append(round(rate, 3))
            
            cohorts.append({
                "cohort_date": cohort_date.strftime("%Y-%m"),
                "initial_users": random.randint(100, 1000),
                "retention_rates": retention_rates
            })
        
        capture_message("Cohort analysis data retrieved successfully", "info")
        return {
            "cohorts": cohorts
        }
        
    except Exception as e:
        capture_message(f"Error retrieving cohort analysis: {str(e)}", "error")
        raise HTTPException(status_code=500, detail="Failed to retrieve cohort analysis")


@router.post("/events")
async def track_event(
    event_name: str,
    user_id: str,
    properties: dict = None
):
    """Track a custom analytics event."""
    
    add_breadcrumb(
        message=f"Event tracked: {event_name}",
        category="event_tracking",
        data={
            "event_name": event_name,
            "user_id": user_id,
            "properties": properties
        }
    )
    
    set_tag("endpoint", "track_event")
    set_tag("event_name", event_name)
    
    try:
        # Here you would typically save the event to a database
        # For now, we'll just log it
        
        event_data = {
            "event_name": event_name,
            "user_id": user_id,
            "properties": properties or {},
            "timestamp": datetime.now().isoformat(),
            "event_id": f"evt_{random.randint(10000, 99999)}"
        }
        
        capture_message(f"Event tracked: {event_name} for user: {user_id}", "info")
        
        return {
            "success": True,
            "event_id": event_data["event_id"],
            "message": "Event tracked successfully"
        }
        
    except Exception as e:
        capture_message(f"Error tracking event: {str(e)}", "error")
        raise HTTPException(status_code=500, detail="Failed to track event")