from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Optional

from app.core.sentry import capture_message, set_user_context, add_breadcrumb

router = APIRouter()
security = HTTPBearer()


@router.post("/login")
async def login(email: str, password: str):
    """User login endpoint."""
    
    add_breadcrumb(
        message=f"Login attempt for email: {email}",
        category="auth",
        data={"email": email}
    )
    
    try:
        # Mock authentication - replace with real auth logic
        if email == "<EMAIL>" and password == "demo":
            user_data = {
                "user_id": "user_123",
                "email": email,
                "name": "Demo User",
                "token": "mock_jwt_token_here"
            }
            
            # Set user context in Sentry
            set_user_context(
                user_id=user_data["user_id"],
                email=user_data["email"],
                username=user_data["name"]
            )
            
            capture_message(f"User logged in successfully: {email}", "info")
            
            return {
                "access_token": user_data["token"],
                "token_type": "bearer",
                "user": {
                    "id": user_data["user_id"],
                    "email": user_data["email"],
                    "name": user_data["name"]
                }
            }
        else:
            capture_message(f"Failed login attempt for email: {email}", "warning")
            raise HTTPException(status_code=401, detail="Invalid credentials")
            
    except HTTPException:
        raise
    except Exception as e:
        capture_message(f"Error during login: {str(e)}", "error")
        raise HTTPException(status_code=500, detail="Login failed")


@router.post("/logout")
async def logout():
    """User logout endpoint."""
    
    add_breadcrumb(
        message="User logout",
        category="auth"
    )
    
    try:
        # Here you would typically invalidate the token
        # For now, we'll just return success
        
        capture_message("User logged out successfully", "info")
        
        return {"message": "Logged out successfully"}
        
    except Exception as e:
        capture_message(f"Error during logout: {str(e)}", "error")
        raise HTTPException(status_code=500, detail="Logout failed")


@router.get("/me")
async def get_current_user():
    """Get current user information."""
    
    add_breadcrumb(
        message="Get current user info",
        category="auth"
    )
    
    try:
        # Mock user data - replace with real user lookup
        user_data = {
            "id": "user_123",
            "email": "<EMAIL>",
            "name": "Demo User",
            "created_at": "2024-01-01T00:00:00Z",
            "last_login": "2024-01-15T10:30:00Z"
        }
        
        capture_message("User info retrieved successfully", "info")
        
        return user_data
        
    except Exception as e:
        capture_message(f"Error retrieving user info: {str(e)}", "error")
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")