from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
import random
from datetime import datetime, timedelta

from app.core.sentry import capture_message, add_breadcrumb, set_tag

router = APIRouter()


@router.get("/overview")
async def get_dashboard_overview():
    """Get dashboard overview data."""
    
    add_breadcrumb(
        message="Dashboard overview requested",
        category="dashboard"
    )
    
    set_tag("endpoint", "dashboard_overview")
    
    try:
        # Generate mock dashboard data
        overview = {
            "summary": {
                "total_revenue": random.randint(50000, 200000),
                "total_orders": random.randint(1000, 5000),
                "conversion_rate": round(random.uniform(2.0, 8.0), 2),
                "avg_order_value": round(random.uniform(50, 200), 2)
            },
            "recent_activity": [
                {
                    "id": f"activity_{i}",
                    "type": random.choice(["order", "signup", "click", "conversion"]),
                    "description": f"Sample activity {i}",
                    "timestamp": (datetime.now() - timedelta(minutes=random.randint(1, 1440))).isoformat(),
                    "value": random.randint(10, 500)
                }
                for i in range(10)
            ],
            "top_products": [
                {
                    "id": f"product_{i}",
                    "name": f"Product {i}",
                    "revenue": random.randint(1000, 10000),
                    "orders": random.randint(50, 500),
                    "growth": round(random.uniform(-20, 50), 1)
                }
                for i in range(5)
            ],
            "performance_metrics": {
                "page_views": random.randint(10000, 100000),
                "unique_visitors": random.randint(5000, 50000),
                "bounce_rate": round(random.uniform(30, 70), 1),
                "avg_session_duration": round(random.uniform(120, 600), 0)
            }
        }
        
        capture_message("Dashboard overview data retrieved successfully", "info")
        return overview
        
    except Exception as e:
        capture_message(f"Error retrieving dashboard overview: {str(e)}", "error")
        raise HTTPException(status_code=500, detail="Failed to retrieve dashboard overview")


@router.get("/widgets")
async def get_dashboard_widgets():
    """Get available dashboard widgets."""
    
    add_breadcrumb(
        message="Dashboard widgets requested",
        category="dashboard"
    )
    
    set_tag("endpoint", "dashboard_widgets")
    
    try:
        widgets = [
            {
                "id": "revenue_chart",
                "name": "Revenue Chart",
                "type": "line_chart",
                "description": "Revenue trends over time",
                "config": {
                    "timeframe": "30d",
                    "currency": "USD"
                }
            },
            {
                "id": "conversion_funnel",
                "name": "Conversion Funnel",
                "type": "funnel_chart",
                "description": "Conversion rates by stage",
                "config": {
                    "stages": ["visit", "add_to_cart", "checkout", "purchase"]
                }
            },
            {
                "id": "top_products",
                "name": "Top Products",
                "type": "table",
                "description": "Best performing products",
                "config": {
                    "limit": 10,
                    "sort_by": "revenue"
                }
            },
            {
                "id": "real_time_visitors",
                "name": "Real-time Visitors",
                "type": "counter",
                "description": "Current active visitors",
                "config": {
                    "refresh_interval": 30
                }
            }
        ]
        
        capture_message("Dashboard widgets retrieved successfully", "info")
        return {"widgets": widgets}
        
    except Exception as e:
        capture_message(f"Error retrieving dashboard widgets: {str(e)}", "error")
        raise HTTPException(status_code=500, detail="Failed to retrieve dashboard widgets")


@router.get("/widget/{widget_id}/data")
async def get_widget_data(widget_id: str):
    """Get data for a specific widget."""
    
    add_breadcrumb(
        message=f"Widget data requested: {widget_id}",
        category="dashboard",
        data={"widget_id": widget_id}
    )
    
    set_tag("endpoint", "widget_data")
    set_tag("widget_id", widget_id)
    
    try:
        # Generate mock data based on widget type
        if widget_id == "revenue_chart":
            data = {
                "data": [
                    {
                        "date": (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d"),
                        "revenue": random.randint(1000, 5000)
                    }
                    for i in range(30, 0, -1)
                ]
            }
        elif widget_id == "conversion_funnel":
            data = {
                "data": [
                    {"stage": "visit", "count": 10000, "rate": 100},
                    {"stage": "add_to_cart", "count": 2500, "rate": 25},
                    {"stage": "checkout", "count": 1000, "rate": 10},
                    {"stage": "purchase", "count": 300, "rate": 3}
                ]
            }
        elif widget_id == "real_time_visitors":
            data = {
                "data": {
                    "current_visitors": random.randint(50, 500),
                    "active_sessions": random.randint(30, 300),
                    "page_views_per_minute": random.randint(10, 100)
                }
            }
        else:
            data = {
                "data": {
                    "message": f"Sample data for widget: {widget_id}",
                    "value": random.randint(1, 1000)
                }
            }
        
        capture_message(f"Widget data retrieved for: {widget_id}", "info")
        return data
        
    except Exception as e:
        capture_message(f"Error retrieving widget data for {widget_id}: {str(e)}", "error")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve data for widget: {widget_id}")