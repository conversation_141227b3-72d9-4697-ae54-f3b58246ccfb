import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import sentry_sdk

from app.core.sentry import add_breadcrumb, set_tag


class SentryMiddleware(BaseHTTPMiddleware):
    """Middleware to enhance Sentry context for each request."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Set Sentry context
        with sentry_sdk.push_scope() as scope:
            scope.set_tag("request_id", request_id)
            scope.set_tag("method", request.method)
            scope.set_tag("endpoint", str(request.url.path))
            
            # Add request breadcrumb
            add_breadcrumb(
                message=f"{request.method} {request.url.path}",
                category="request",
                level="info",
                data={
                    "request_id": request_id,
                    "method": request.method,
                    "url": str(request.url),
                    "user_agent": request.headers.get("user-agent"),
                }
            )
            
            # Add request ID to request state
            request.state.request_id = request_id
            
            start_time = time.time()
            
            try:
                response = await call_next(request)
                
                # Add response breadcrumb
                process_time = time.time() - start_time
                add_breadcrumb(
                    message=f"Response {response.status_code}",
                    category="response",
                    level="info",
                    data={
                        "status_code": response.status_code,
                        "process_time": process_time,
                        "request_id": request_id,
                    }
                )
                
                # Add response headers
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Process-Time"] = str(process_time)
                
                return response
                
            except Exception as e:
                # Add error breadcrumb
                add_breadcrumb(
                    message=f"Request failed: {str(e)}",
                    category="error",
                    level="error",
                    data={
                        "request_id": request_id,
                        "error": str(e),
                        "error_type": type(e).__name__,
                    }
                )
                
                # Set error context
                scope.set_extra("request_id", request_id)
                scope.set_extra("request_url", str(request.url))
                scope.set_extra("request_method", request.method)
                
                # Re-raise the exception to be handled by FastAPI
                raise


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Log request
        print(f"🚀 {request.method} {request.url.path}")
        
        try:
            response = await call_next(request)
            process_time = time.time() - start_time
            
            # Log response
            print(f"✅ {response.status_code} - {process_time:.4f}s")
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            print(f"❌ Error - {process_time:.4f}s - {str(e)}")
            raise