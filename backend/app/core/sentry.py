import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
import logging

from app.core.config import settings


def init_sentry():
    """Initialize Sentry for error tracking and performance monitoring."""
    
    if not settings.SENTRY_DSN:
        print("Sentry DSN not provided, skipping Sentry initialization")
        return
    
    # Configure logging integration
    logging_integration = LoggingIntegration(
        level=logging.INFO,        # Capture info and above as breadcrumbs
        event_level=logging.ERROR  # Send errors as events
    )
    
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        environment=settings.ENVIRONMENT,
        integrations=[
            FastApiIntegration(auto_enabling_integrations=False),
            SqlalchemyIntegration(),
            RedisIntegration(),
            logging_integration,
        ],
        # Performance Monitoring
        traces_sample_rate=1.0 if settings.ENVIRONMENT == "development" else 0.1,
        
        # Release tracking
        release=f"{settings.PROJECT_NAME}@1.0.0",
        
        # Additional options
        attach_stacktrace=True,
        send_default_pii=False,  # Don't send personally identifiable information
        
        # Error filtering
        before_send=filter_sensitive_data,
    )
    
    print(f"Sentry initialized for environment: {settings.ENVIRONMENT}")


def filter_sensitive_data(event, hint):
    """Filter out sensitive data before sending to Sentry."""
    
    # Remove sensitive headers
    if 'request' in event:
        headers = event['request'].get('headers', {})
        sensitive_headers = ['authorization', 'cookie', 'x-api-key']
        for header in sensitive_headers:
            if header in headers:
                headers[header] = '[Filtered]'
    
    # Remove sensitive query parameters
    if 'request' in event and 'query_string' in event['request']:
        # Add logic to filter sensitive query parameters if needed
        pass
    
    # Remove sensitive form data
    if 'request' in event and 'data' in event['request']:
        data = event['request']['data']
        if isinstance(data, dict):
            sensitive_fields = ['password', 'token', 'secret', 'api_key']
            for field in sensitive_fields:
                if field in data:
                    data[field] = '[Filtered]'
    
    return event


def capture_message(message: str, level: str = "info"):
    """Capture a message to Sentry."""
    sentry_sdk.capture_message(message, level)


def capture_exception(exception: Exception, extra_data: dict = None):
    """Capture an exception to Sentry with optional extra data."""
    with sentry_sdk.push_scope() as scope:
        if extra_data:
            for key, value in extra_data.items():
                scope.set_extra(key, value)
        sentry_sdk.capture_exception(exception)


def set_user_context(user_id: str, email: str = None, username: str = None):
    """Set user context for Sentry."""
    sentry_sdk.set_user({
        "id": user_id,
        "email": email,
        "username": username
    })


def set_tag(key: str, value: str):
    """Set a tag for Sentry."""
    sentry_sdk.set_tag(key, value)


def add_breadcrumb(message: str, category: str = "custom", level: str = "info", data: dict = None):
    """Add a breadcrumb for Sentry."""
    sentry_sdk.add_breadcrumb(
        message=message,
        category=category,
        level=level,
        data=data or {}
    )