-- Enhanced Analytics Schema Migration
-- Phase 1: Foundation & Core Analytics Implementation
-- Migration 020: Create comprehensive data models with TimescaleDB optimization

BEGIN;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- =====================================================
-- CORE BUSINESS ENTITIES
-- =====================================================

-- Tenants (Multi-tenant architecture) - Enhanced from existing
CREATE TABLE IF NOT EXISTS tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'starter',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- E-commerce Platforms Integration
CREATE TABLE IF NOT EXISTS ecommerce_platforms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    platform_type VARCHAR(50) NOT NULL, -- 'shopify', 'woocommerce', 'amazon', 'ebay'
    platform_name VARCHAR(255) NOT NULL,
    api_credentials JSONB NOT NULL, -- Encrypted credentials
    webhook_endpoints JSONB DEFAULT '{}',
    sync_settings JSONB DEFAULT '{}',
    last_sync_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, platform_type, platform_name)
);

-- Products (Unified across platforms)
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    platform_id UUID NOT NULL REFERENCES ecommerce_platforms(id) ON DELETE CASCADE,
    external_id VARCHAR(255) NOT NULL, -- Platform-specific product ID
    sku VARCHAR(255),
    name VARCHAR(500) NOT NULL,
    description TEXT,
    category VARCHAR(255),
    brand VARCHAR(255),
    price DECIMAL(12,2),
    currency VARCHAR(3) DEFAULT 'USD',
    images JSONB DEFAULT '[]',
    attributes JSONB DEFAULT '{}',
    inventory_quantity INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, platform_id, external_id)
);

-- Customers (Unified across platforms)
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255),
    phone VARCHAR(50),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    date_of_birth DATE,
    gender VARCHAR(20),
    location JSONB, -- {country, state, city, postal_code, coordinates}
    acquisition_channel VARCHAR(100),
    acquisition_date TIMESTAMPTZ,
    customer_segments JSONB DEFAULT '[]',
    lifetime_value DECIMAL(12,2) DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(12,2) DEFAULT 0,
    avg_order_value DECIMAL(12,2) DEFAULT 0,
    last_order_date TIMESTAMPTZ,
    churn_probability DECIMAL(5,4) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, email)
);

-- =====================================================
-- TIME-SERIES ANALYTICS TABLES (TimescaleDB Hypertables)
-- =====================================================

-- Customer Events (Time-series for all customer interactions)
CREATE TABLE IF NOT EXISTS customer_events (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    event_type VARCHAR(100) NOT NULL, -- 'page_view', 'product_view', 'add_to_cart', 'purchase', etc.
    event_source VARCHAR(50) NOT NULL, -- 'website', 'mobile_app', 'social_media', 'email'
    platform_id UUID REFERENCES ecommerce_platforms(id) ON DELETE SET NULL,
    product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    event_data JSONB DEFAULT '{}',
    revenue DECIMAL(12,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    user_agent TEXT,
    ip_address INET,
    referrer TEXT,
    utm_source VARCHAR(255),
    utm_medium VARCHAR(255),
    utm_campaign VARCHAR(255),
    utm_content VARCHAR(255),
    utm_term VARCHAR(255),
    device_type VARCHAR(50),
    browser VARCHAR(100),
    os VARCHAR(100),
    country VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, timestamp)
);

-- Orders (Time-series for order tracking)
CREATE TABLE IF NOT EXISTS orders (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    platform_id UUID NOT NULL REFERENCES ecommerce_platforms(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    external_order_id VARCHAR(255) NOT NULL,
    order_number VARCHAR(255),
    status VARCHAR(50) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    shipping_amount DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(100),
    shipping_address JSONB,
    billing_address JSONB,
    line_items JSONB NOT NULL, -- Array of order items
    attribution_data JSONB DEFAULT '{}', -- Attribution touchpoints
    fulfillment_status VARCHAR(50),
    financial_status VARCHAR(50),
    notes TEXT,
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (id, created_at)
);

-- =====================================================
-- BRANDED LINK TRACKING
-- =====================================================

-- Branded Links
CREATE TABLE IF NOT EXISTS branded_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    campaign_id UUID,
    short_code VARCHAR(50) NOT NULL,
    original_url TEXT NOT NULL,
    branded_url VARCHAR(500) NOT NULL,
    title VARCHAR(255),
    description TEXT,
    tags JSONB DEFAULT '[]',
    utm_parameters JSONB DEFAULT '{}',
    expiry_date TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    click_count INTEGER DEFAULT 0,
    unique_click_count INTEGER DEFAULT 0,
    conversion_count INTEGER DEFAULT 0,
    revenue_generated DECIMAL(12,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, short_code)
);

-- Link Clicks (Time-series)
CREATE TABLE IF NOT EXISTS link_clicks (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    link_id UUID NOT NULL REFERENCES branded_links(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    country VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    device_type VARCHAR(50),
    browser VARCHAR(100),
    os VARCHAR(100),
    is_unique_click BOOLEAN DEFAULT false,
    click_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, click_timestamp)
);

-- =====================================================
-- SOCIAL MEDIA INTEGRATION
-- =====================================================

-- Social Media Accounts
CREATE TABLE IF NOT EXISTS social_media_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL, -- 'youtube', 'facebook', 'instagram', 'tiktok'
    account_id VARCHAR(255) NOT NULL,
    account_name VARCHAR(255),
    access_token TEXT, -- Encrypted
    refresh_token TEXT, -- Encrypted
    token_expires_at TIMESTAMPTZ,
    account_metadata JSONB DEFAULT '{}',
    sync_settings JSONB DEFAULT '{}',
    last_sync_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, platform, account_id)
);

-- Social Media Content
CREATE TABLE IF NOT EXISTS social_media_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES social_media_accounts(id) ON DELETE CASCADE,
    external_post_id VARCHAR(255) NOT NULL,
    content_type VARCHAR(50), -- 'post', 'story', 'video', 'reel'
    title VARCHAR(500),
    description TEXT,
    media_urls JSONB DEFAULT '[]',
    hashtags JSONB DEFAULT '[]',
    mentions JSONB DEFAULT '[]',
    branded_links JSONB DEFAULT '[]', -- Links to our branded_links table
    published_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, account_id, external_post_id)
);

-- Social Media Metrics (Time-series)
CREATE TABLE IF NOT EXISTS social_media_metrics (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES social_media_accounts(id) ON DELETE CASCADE,
    content_id UUID REFERENCES social_media_content(id) ON DELETE SET NULL,
    metric_type VARCHAR(100) NOT NULL, -- 'impressions', 'reach', 'engagement', 'clicks', 'shares'
    metric_value BIGINT NOT NULL,
    metric_metadata JSONB DEFAULT '{}',
    recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, recorded_at)
);

-- =====================================================
-- ATTRIBUTION & CUSTOMER JOURNEY
-- =====================================================

-- Attribution Models Configuration
CREATE TABLE IF NOT EXISTS attribution_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- 'first_touch', 'last_touch', 'linear', 'time_decay', 'position_based', 'data_driven'
    configuration JSONB NOT NULL,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Customer Journey Touchpoints (Time-series)
CREATE TABLE IF NOT EXISTS customer_touchpoints (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    touchpoint_type VARCHAR(100) NOT NULL, -- 'social_media', 'email', 'paid_search', 'organic_search', 'direct'
    channel VARCHAR(100) NOT NULL,
    campaign VARCHAR(255),
    content VARCHAR(255),
    medium VARCHAR(100),
    source VARCHAR(255),
    link_id UUID REFERENCES branded_links(id) ON DELETE SET NULL,
    content_id UUID REFERENCES social_media_content(id) ON DELETE SET NULL,
    touchpoint_value DECIMAL(12,2) DEFAULT 0, -- Revenue attributed to this touchpoint
    position_in_journey INTEGER,
    time_to_conversion INTERVAL,
    touchpoint_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, touchpoint_timestamp)
);

-- =====================================================
-- AI/ML MODELS & PREDICTIONS
-- =====================================================

-- ML Models Registry
CREATE TABLE IF NOT EXISTS ml_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    model_name VARCHAR(255) NOT NULL,
    model_type VARCHAR(100) NOT NULL, -- 'churn_prediction', 'clv_prediction', 'demand_forecasting', 'recommendation'
    model_version VARCHAR(50) NOT NULL,
    model_metadata JSONB NOT NULL,
    training_data_period JSONB, -- {start_date, end_date}
    performance_metrics JSONB, -- {accuracy, precision, recall, f1_score, etc.}
    model_artifacts_path TEXT, -- Path to stored model files
    is_active BOOLEAN DEFAULT false,
    deployed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ML Predictions (Time-series)
CREATE TABLE IF NOT EXISTS ml_predictions (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES ml_models(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL, -- 'customer', 'product', 'campaign'
    entity_id UUID NOT NULL,
    prediction_type VARCHAR(100) NOT NULL,
    prediction_value DECIMAL(12,4) NOT NULL,
    confidence_score DECIMAL(5,4),
    prediction_metadata JSONB DEFAULT '{}',
    predicted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, predicted_at)
);

COMMIT;
