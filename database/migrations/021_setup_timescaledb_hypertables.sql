-- TimescaleDB Hypertables and Optimization Setup
-- Phase 1: Foundation & Core Analytics Implementation
-- Migration 021: Configure TimescaleDB hypertables with proper partitioning

BEGIN;

-- =====================================================
-- CREATE HYPERTABLES FOR TIME-SERIES DATA
-- =====================================================

-- Convert customer_events to hypertable (1 day chunks for high-frequency data)
SELECT create_hypertable(
    'customer_events', 
    'timestamp',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Convert orders to hypertable (1 day chunks)
SELECT create_hypertable(
    'orders', 
    'created_at',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Convert link_clicks to hypertable (1 hour chunks for high-frequency tracking)
SELECT create_hypertable(
    'link_clicks', 
    'click_timestamp',
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Convert social_media_metrics to hypertable (1 hour chunks)
SELECT create_hypertable(
    'social_media_metrics', 
    'recorded_at',
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Convert customer_touchpoints to hypertable (1 hour chunks)
SELECT create_hypertable(
    'customer_touchpoints', 
    'touchpoint_timestamp',
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Convert ml_predictions to hypertable (1 day chunks)
SELECT create_hypertable(
    'ml_predictions', 
    'predicted_at',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- =====================================================
-- RETENTION POLICIES FOR DATA LIFECYCLE MANAGEMENT
-- =====================================================

-- Customer events: Keep 2 years of detailed data
SELECT add_retention_policy(
    'customer_events', 
    INTERVAL '2 years',
    if_not_exists => TRUE
);

-- Orders: Keep 5 years (important for business records)
SELECT add_retention_policy(
    'orders', 
    INTERVAL '5 years',
    if_not_exists => TRUE
);

-- Link clicks: Keep 1 year of detailed click data
SELECT add_retention_policy(
    'link_clicks', 
    INTERVAL '1 year',
    if_not_exists => TRUE
);

-- Social media metrics: Keep 1 year
SELECT add_retention_policy(
    'social_media_metrics', 
    INTERVAL '1 year',
    if_not_exists => TRUE
);

-- Customer touchpoints: Keep 2 years for attribution analysis
SELECT add_retention_policy(
    'customer_touchpoints', 
    INTERVAL '2 years',
    if_not_exists => TRUE
);

-- ML predictions: Keep 1 year
SELECT add_retention_policy(
    'ml_predictions', 
    INTERVAL '1 year',
    if_not_exists => TRUE
);

-- =====================================================
-- CONTINUOUS AGGREGATES FOR PERFORMANCE
-- =====================================================

-- Daily customer metrics aggregate
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_customer_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', timestamp) AS day,
    tenant_id,
    COUNT(*) as total_events,
    COUNT(DISTINCT customer_id) as unique_customers,
    COUNT(*) FILTER (WHERE event_type = 'purchase') as purchases,
    COUNT(*) FILTER (WHERE event_type = 'product_view') as product_views,
    COUNT(*) FILTER (WHERE event_type = 'add_to_cart') as add_to_carts,
    SUM(revenue) as total_revenue,
    AVG(revenue) FILTER (WHERE revenue > 0) as avg_revenue_per_event,
    COUNT(DISTINCT session_id) as unique_sessions
FROM customer_events
GROUP BY day, tenant_id;

-- Hourly customer metrics for real-time dashboards
CREATE MATERIALIZED VIEW IF NOT EXISTS hourly_customer_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', timestamp) AS hour,
    tenant_id,
    COUNT(*) as total_events,
    COUNT(DISTINCT customer_id) as unique_customers,
    COUNT(*) FILTER (WHERE event_type = 'purchase') as purchases,
    SUM(revenue) as total_revenue,
    COUNT(DISTINCT session_id) as unique_sessions
FROM customer_events
GROUP BY hour, tenant_id;

-- Daily order metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_order_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', created_at) AS day,
    tenant_id,
    platform_id,
    COUNT(*) as total_orders,
    COUNT(DISTINCT customer_id) as unique_customers,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_order_value,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_orders,
    COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_orders
FROM orders
GROUP BY day, tenant_id, platform_id;

-- Daily link performance metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_link_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', click_timestamp) AS day,
    tenant_id,
    link_id,
    COUNT(*) as total_clicks,
    COUNT(DISTINCT ip_address) as unique_clicks,
    COUNT(DISTINCT customer_id) as identified_customers,
    COUNT(DISTINCT country) as countries_reached,
    COUNT(*) FILTER (WHERE device_type = 'mobile') as mobile_clicks,
    COUNT(*) FILTER (WHERE device_type = 'desktop') as desktop_clicks
FROM link_clicks
GROUP BY day, tenant_id, link_id;

-- Daily social media metrics aggregate
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_social_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', recorded_at) AS day,
    tenant_id,
    account_id,
    metric_type,
    SUM(metric_value) as total_value,
    AVG(metric_value) as avg_value,
    MAX(metric_value) as max_value,
    COUNT(*) as metric_count
FROM social_media_metrics
GROUP BY day, tenant_id, account_id, metric_type;

-- Daily attribution metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_attribution_metrics
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', touchpoint_timestamp) AS day,
    tenant_id,
    channel,
    touchpoint_type,
    COUNT(*) as total_touchpoints,
    COUNT(DISTINCT customer_id) as unique_customers,
    SUM(touchpoint_value) as total_attributed_value,
    AVG(touchpoint_value) FILTER (WHERE touchpoint_value > 0) as avg_attribution_value,
    COUNT(DISTINCT session_id) as unique_sessions
FROM customer_touchpoints
GROUP BY day, tenant_id, channel, touchpoint_type;

-- =====================================================
-- REFRESH POLICIES FOR CONTINUOUS AGGREGATES
-- =====================================================

-- Refresh policies for real-time data (every 5 minutes)
SELECT add_continuous_aggregate_policy(
    'hourly_customer_metrics',
    start_offset => INTERVAL '2 hours',
    end_offset => INTERVAL '5 minutes',
    schedule_interval => INTERVAL '5 minutes',
    if_not_exists => TRUE
);

-- Refresh policies for daily aggregates (every hour)
SELECT add_continuous_aggregate_policy(
    'daily_customer_metrics',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
    'daily_order_metrics',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
    'daily_link_metrics',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
    'daily_social_metrics',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

SELECT add_continuous_aggregate_policy(
    'daily_attribution_metrics',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- =====================================================
-- COMPRESSION POLICIES FOR STORAGE OPTIMIZATION
-- =====================================================

-- Enable compression on hypertables (target 70% compression ratio)
ALTER TABLE customer_events SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'tenant_id, event_type',
    timescaledb.compress_orderby = 'timestamp DESC'
);

ALTER TABLE orders SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'tenant_id, platform_id',
    timescaledb.compress_orderby = 'created_at DESC'
);

ALTER TABLE link_clicks SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'tenant_id, link_id',
    timescaledb.compress_orderby = 'click_timestamp DESC'
);

ALTER TABLE social_media_metrics SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'tenant_id, account_id, metric_type',
    timescaledb.compress_orderby = 'recorded_at DESC'
);

ALTER TABLE customer_touchpoints SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'tenant_id, channel, touchpoint_type',
    timescaledb.compress_orderby = 'touchpoint_timestamp DESC'
);

ALTER TABLE ml_predictions SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'tenant_id, model_id, prediction_type',
    timescaledb.compress_orderby = 'predicted_at DESC'
);

-- Add compression policies (compress data older than 7 days)
SELECT add_compression_policy(
    'customer_events', 
    INTERVAL '7 days',
    if_not_exists => TRUE
);

SELECT add_compression_policy(
    'orders', 
    INTERVAL '7 days',
    if_not_exists => TRUE
);

SELECT add_compression_policy(
    'link_clicks', 
    INTERVAL '7 days',
    if_not_exists => TRUE
);

SELECT add_compression_policy(
    'social_media_metrics', 
    INTERVAL '7 days',
    if_not_exists => TRUE
);

SELECT add_compression_policy(
    'customer_touchpoints', 
    INTERVAL '7 days',
    if_not_exists => TRUE
);

SELECT add_compression_policy(
    'ml_predictions', 
    INTERVAL '7 days',
    if_not_exists => TRUE
);

COMMIT;
