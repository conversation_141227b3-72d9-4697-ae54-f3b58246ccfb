-- Performance Indexes for Enhanced Analytics Schema
-- Phase 1: Foundation & Core Analytics Implementation
-- Migration 022: Create comprehensive indexing strategy for multi-tenant performance

BEGIN;

-- =====================================================
-- TENANT-BASED INDEXES FOR MULTI-TENANCY
-- =====================================================

-- Core business entities indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tenants_domain ON tenants (domain);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tenants_subscription_tier ON tenants (subscription_tier);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ecommerce_platforms_tenant_active 
ON ecommerce_platforms (tenant_id, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ecommerce_platforms_type 
ON ecommerce_platforms (tenant_id, platform_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_tenant_platform 
ON products (tenant_id, platform_id, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_sku 
ON products (tenant_id, sku) WHERE sku IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_category 
ON products (tenant_id, category) WHERE category IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_tenant_email 
ON customers (tenant_id, email) WHERE email IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_acquisition 
ON customers (tenant_id, acquisition_channel, acquisition_date);

-- =====================================================
-- TIME-SERIES HYPERTABLE INDEXES
-- =====================================================

-- Customer Events Indexes (optimized for dashboard queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_tenant_timestamp 
ON customer_events (tenant_id, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_customer_timestamp 
ON customer_events (customer_id, timestamp DESC) WHERE customer_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_type_timestamp 
ON customer_events (tenant_id, event_type, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_session 
ON customer_events (tenant_id, session_id, timestamp DESC) WHERE session_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_product 
ON customer_events (product_id, timestamp DESC) WHERE product_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_revenue 
ON customer_events (tenant_id, timestamp DESC) WHERE revenue > 0;

-- UTM and attribution indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_utm_campaign 
ON customer_events (tenant_id, utm_campaign, timestamp DESC) WHERE utm_campaign IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_utm_source 
ON customer_events (tenant_id, utm_source, timestamp DESC) WHERE utm_source IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_utm_medium 
ON customer_events (tenant_id, utm_medium, timestamp DESC) WHERE utm_medium IS NOT NULL;

-- Geographic and device indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_country 
ON customer_events (tenant_id, country, timestamp DESC) WHERE country IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_device 
ON customer_events (tenant_id, device_type, timestamp DESC) WHERE device_type IS NOT NULL;

-- Orders Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_tenant_created 
ON orders (tenant_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_customer_created 
ON orders (customer_id, created_at DESC) WHERE customer_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_platform_created 
ON orders (platform_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status 
ON orders (tenant_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_external_id 
ON orders (tenant_id, platform_id, external_order_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_amount 
ON orders (tenant_id, total_amount, created_at DESC);

-- =====================================================
-- BRANDED LINK TRACKING INDEXES
-- =====================================================

-- Branded Links Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_branded_links_tenant_active 
ON branded_links (tenant_id, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_branded_links_short_code 
ON branded_links (short_code) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_branded_links_campaign 
ON branded_links (tenant_id, campaign_id) WHERE campaign_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_branded_links_performance 
ON branded_links (tenant_id, click_count DESC, conversion_count DESC);

-- Link Clicks Indexes (high-performance for tracking)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_tenant_timestamp 
ON link_clicks (tenant_id, click_timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_link_timestamp 
ON link_clicks (link_id, click_timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_customer 
ON link_clicks (customer_id, click_timestamp DESC) WHERE customer_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_session 
ON link_clicks (tenant_id, session_id, click_timestamp DESC) WHERE session_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_unique 
ON link_clicks (tenant_id, link_id, click_timestamp DESC) WHERE is_unique_click = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_clicks_country 
ON link_clicks (tenant_id, country, click_timestamp DESC) WHERE country IS NOT NULL;

-- =====================================================
-- SOCIAL MEDIA INDEXES
-- =====================================================

-- Social Media Accounts Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_accounts_tenant_platform 
ON social_media_accounts (tenant_id, platform, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_accounts_platform_account 
ON social_media_accounts (platform, account_id);

-- Social Media Content Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_content_tenant_account 
ON social_media_content (tenant_id, account_id, published_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_content_type 
ON social_media_content (tenant_id, content_type, published_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_content_external 
ON social_media_content (account_id, external_post_id);

-- Social Media Metrics Indexes (high-frequency data)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_metrics_tenant_recorded 
ON social_media_metrics (tenant_id, recorded_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_metrics_account_recorded 
ON social_media_metrics (account_id, recorded_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_metrics_content_recorded 
ON social_media_metrics (content_id, recorded_at DESC) WHERE content_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_metrics_type 
ON social_media_metrics (tenant_id, metric_type, recorded_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_metrics_value 
ON social_media_metrics (tenant_id, metric_type, metric_value DESC, recorded_at DESC);

-- =====================================================
-- ATTRIBUTION & CUSTOMER JOURNEY INDEXES
-- =====================================================

-- Attribution Models Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attribution_models_tenant_active 
ON attribution_models (tenant_id, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attribution_models_default 
ON attribution_models (tenant_id, is_default) WHERE is_default = true;

-- Customer Touchpoints Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_touchpoints_tenant_timestamp 
ON customer_touchpoints (tenant_id, touchpoint_timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_touchpoints_customer_timestamp 
ON customer_touchpoints (customer_id, touchpoint_timestamp DESC) WHERE customer_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_touchpoints_channel 
ON customer_touchpoints (tenant_id, channel, touchpoint_timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_touchpoints_type 
ON customer_touchpoints (tenant_id, touchpoint_type, touchpoint_timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_touchpoints_campaign 
ON customer_touchpoints (tenant_id, campaign, touchpoint_timestamp DESC) WHERE campaign IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_touchpoints_session 
ON customer_touchpoints (tenant_id, session_id, touchpoint_timestamp DESC) WHERE session_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_touchpoints_link 
ON customer_touchpoints (link_id, touchpoint_timestamp DESC) WHERE link_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_touchpoints_value 
ON customer_touchpoints (tenant_id, touchpoint_value DESC, touchpoint_timestamp DESC) WHERE touchpoint_value > 0;

-- =====================================================
-- ML MODELS & PREDICTIONS INDEXES
-- =====================================================

-- ML Models Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ml_models_tenant_type 
ON ml_models (tenant_id, model_type, is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ml_models_active 
ON ml_models (tenant_id, model_name, is_active) WHERE is_active = true;

-- ML Predictions Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ml_predictions_tenant_predicted 
ON ml_predictions (tenant_id, predicted_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ml_predictions_model_predicted 
ON ml_predictions (model_id, predicted_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ml_predictions_entity 
ON ml_predictions (entity_type, entity_id, predicted_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ml_predictions_type 
ON ml_predictions (tenant_id, prediction_type, predicted_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ml_predictions_value 
ON ml_predictions (tenant_id, prediction_type, prediction_value DESC, predicted_at DESC);

-- =====================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =====================================================

-- Customer analytics composite indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_analytics 
ON customer_events (tenant_id, event_type, customer_id, timestamp DESC) 
WHERE customer_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_analytics 
ON orders (tenant_id, customer_id, status, created_at DESC) 
WHERE customer_id IS NOT NULL;

-- Attribution analysis composite indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_touchpoints_attribution 
ON customer_touchpoints (tenant_id, customer_id, channel, touchpoint_timestamp DESC) 
WHERE customer_id IS NOT NULL;

-- Social media performance composite indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_social_performance 
ON social_media_metrics (tenant_id, account_id, metric_type, recorded_at DESC);

-- Link performance composite indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_link_performance 
ON link_clicks (tenant_id, link_id, is_unique_click, click_timestamp DESC);

-- =====================================================
-- PARTIAL INDEXES FOR SPECIFIC USE CASES
-- =====================================================

-- Active customers only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_active 
ON customers (tenant_id, last_order_date DESC) 
WHERE last_order_date >= NOW() - INTERVAL '1 year';

-- High-value customers
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_high_value 
ON customers (tenant_id, lifetime_value DESC) 
WHERE lifetime_value > 100;

-- Recent events for real-time dashboards
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_events_recent 
ON customer_events (tenant_id, event_type, timestamp DESC) 
WHERE timestamp >= NOW() - INTERVAL '24 hours';

-- Successful orders only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_successful 
ON orders (tenant_id, created_at DESC, total_amount DESC) 
WHERE status IN ('completed', 'fulfilled', 'paid');

-- Active branded links
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_branded_links_active_performance 
ON branded_links (tenant_id, click_count DESC, revenue_generated DESC) 
WHERE is_active = true AND (expiry_date IS NULL OR expiry_date > NOW());

COMMIT;
