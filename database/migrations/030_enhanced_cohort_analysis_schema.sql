-- Enhanced Cohort Analysis Schema - Phase 2 Implementation
-- Advanced cohort segmentation and retention modeling with TimescaleDB optimization

BEGIN;

-- =====================================================
-- ENHANCED COHORT ANALYSIS TABLES
-- =====================================================

-- Cohort segments with advanced analytics
CREATE TABLE IF NOT EXISTS cohort_segments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    cohort_month DATE NOT NULL,
    segment_name VARCHAR(100) NOT NULL,
    cohort_type VARCHAR(50) NOT NULL, -- 'acquisition', 'behavioral', 'value'
    granularity VARCHAR(20) NOT NULL DEFAULT 'monthly', -- 'daily', 'weekly', 'monthly'
    customer_count INTEGER NOT NULL DEFAULT 0,
    total_revenue DECIMAL(12,2) NOT NULL DEFAULT 0,
    avg_order_value DECIMAL(10,2) NOT NULL DEFAULT 0,
    avg_churn_probability DECIMAL(5,4) NOT NULL DEFAULT 0,
    retention_rates JSONB NOT NULL DEFAULT '{}', -- {month_1: 85.5, month_3: 72.3, month_6: 65.1}
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT cohort_segments_unique_key UNIQUE (tenant_id, cohort_month, segment_name, cohort_type, granularity),
    CONSTRAINT cohort_segments_customer_count_positive CHECK (customer_count >= 0),
    CONSTRAINT cohort_segments_revenue_positive CHECK (total_revenue >= 0),
    CONSTRAINT cohort_segments_churn_valid CHECK (avg_churn_probability >= 0 AND avg_churn_probability <= 1)
);

-- Retention curves for detailed visualization
CREATE TABLE IF NOT EXISTS retention_curves (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    cohort_segment_id UUID NOT NULL REFERENCES cohort_segments(id) ON DELETE CASCADE,
    period_number INTEGER NOT NULL, -- 0, 1, 2, 3... (months from cohort start)
    retention_rate DECIMAL(5,2) NOT NULL, -- Percentage (0-100)
    customer_count INTEGER NOT NULL DEFAULT 0,
    revenue DECIMAL(12,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT retention_curves_unique_key UNIQUE (cohort_segment_id, period_number),
    CONSTRAINT retention_curves_rate_valid CHECK (retention_rate >= 0 AND retention_rate <= 100),
    CONSTRAINT retention_curves_period_valid CHECK (period_number >= 0)
);

-- Cohort analysis results cache for performance
CREATE TABLE IF NOT EXISTS cohort_analysis_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    cache_key VARCHAR(255) NOT NULL, -- Hash of analysis parameters
    cohort_type VARCHAR(50) NOT NULL,
    granularity VARCHAR(20) NOT NULL,
    date_from TIMESTAMPTZ NOT NULL,
    date_to TIMESTAMPTZ NOT NULL,
    analysis_result JSONB NOT NULL, -- Cached analysis results
    execution_time_ms INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    
    -- Constraints
    CONSTRAINT cohort_cache_unique_key UNIQUE (tenant_id, cache_key),
    CONSTRAINT cohort_cache_execution_time_positive CHECK (execution_time_ms > 0)
);

-- Predictive cohort insights
CREATE TABLE IF NOT EXISTS cohort_predictions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    cohort_segment_id UUID NOT NULL REFERENCES cohort_segments(id) ON DELETE CASCADE,
    prediction_type VARCHAR(50) NOT NULL, -- 'churn', 'revenue', 'retention'
    prediction_period INTEGER NOT NULL, -- Months into the future
    predicted_value DECIMAL(12,4) NOT NULL,
    confidence_score DECIMAL(5,4) NOT NULL, -- 0-1 confidence level
    model_version VARCHAR(50) NOT NULL DEFAULT 'v1.0',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT cohort_predictions_confidence_valid CHECK (confidence_score >= 0 AND confidence_score <= 1),
    CONSTRAINT cohort_predictions_period_positive CHECK (prediction_period > 0)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Primary lookup indexes
CREATE INDEX IF NOT EXISTS idx_cohort_segments_tenant_month 
    ON cohort_segments(tenant_id, cohort_month DESC);

CREATE INDEX IF NOT EXISTS idx_cohort_segments_type_granularity 
    ON cohort_segments(tenant_id, cohort_type, granularity);

CREATE INDEX IF NOT EXISTS idx_retention_curves_segment 
    ON retention_curves(cohort_segment_id, period_number);

CREATE INDEX IF NOT EXISTS idx_cohort_cache_tenant_key 
    ON cohort_analysis_cache(tenant_id, cache_key);

CREATE INDEX IF NOT EXISTS idx_cohort_cache_expires
    ON cohort_analysis_cache(expires_at);

CREATE INDEX IF NOT EXISTS idx_cohort_predictions_segment_type 
    ON cohort_predictions(cohort_segment_id, prediction_type);

-- Performance indexes for analytics queries
CREATE INDEX IF NOT EXISTS idx_cohort_segments_revenue 
    ON cohort_segments(tenant_id, total_revenue DESC) 
    WHERE total_revenue > 0;

CREATE INDEX IF NOT EXISTS idx_cohort_segments_customer_count 
    ON cohort_segments(tenant_id, customer_count DESC) 
    WHERE customer_count > 0;

CREATE INDEX IF NOT EXISTS idx_cohort_segments_churn 
    ON cohort_segments(tenant_id, avg_churn_probability DESC);

-- =====================================================
-- TIMESCALEDB HYPERTABLES (if applicable)
-- =====================================================

-- Convert retention_curves to hypertable for time-series optimization
-- Note: Only if we add a time dimension to retention_curves
-- SELECT create_hypertable('retention_curves', 'created_at', if_not_exists => TRUE);

-- =====================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- =====================================================

-- Cohort performance summary view
CREATE MATERIALIZED VIEW IF NOT EXISTS cohort_performance_summary AS
SELECT 
    cs.tenant_id,
    cs.cohort_type,
    cs.granularity,
    DATE_TRUNC('month', cs.cohort_month) as month,
    COUNT(*) as segment_count,
    SUM(cs.customer_count) as total_customers,
    SUM(cs.total_revenue) as total_revenue,
    AVG(cs.avg_order_value) as avg_order_value,
    AVG(cs.avg_churn_probability) as avg_churn_probability,
    AVG((cs.retention_rates->>'month_1')::decimal) as avg_retention_month_1,
    AVG((cs.retention_rates->>'month_3')::decimal) as avg_retention_month_3,
    AVG((cs.retention_rates->>'month_6')::decimal) as avg_retention_month_6
FROM cohort_segments cs
GROUP BY cs.tenant_id, cs.cohort_type, cs.granularity, DATE_TRUNC('month', cs.cohort_month);

-- Create index on materialized view
CREATE INDEX IF NOT EXISTS idx_cohort_performance_summary_tenant_month 
    ON cohort_performance_summary(tenant_id, month DESC);

-- =====================================================
-- FUNCTIONS FOR COHORT ANALYSIS
-- =====================================================

-- Function to calculate retention rate
CREATE OR REPLACE FUNCTION calculate_retention_rate(
    p_tenant_id UUID,
    p_cohort_month DATE,
    p_period_months INTEGER
) RETURNS DECIMAL(5,2) AS $$
DECLARE
    cohort_size INTEGER;
    active_customers INTEGER;
    retention_rate DECIMAL(5,2);
BEGIN
    -- Get cohort size (customers acquired in the cohort month)
    SELECT COUNT(DISTINCT id) INTO cohort_size
    FROM customers 
    WHERE tenant_id = p_tenant_id 
      AND DATE_TRUNC('month', acquisition_date) = p_cohort_month;
    
    -- Return 0 if no cohort
    IF cohort_size = 0 THEN
        RETURN 0;
    END IF;
    
    -- Get active customers in the target period
    SELECT COUNT(DISTINCT c.id) INTO active_customers
    FROM customers c
    JOIN orders o ON c.id = o.customer_id
    WHERE c.tenant_id = p_tenant_id
      AND DATE_TRUNC('month', c.acquisition_date) = p_cohort_month
      AND o.created_at >= p_cohort_month + (p_period_months || ' months')::interval
      AND o.created_at < p_cohort_month + ((p_period_months + 1) || ' months')::interval;
    
    -- Calculate retention rate
    retention_rate := (active_customers::DECIMAL / cohort_size) * 100;
    
    RETURN ROUND(retention_rate, 2);
END;
$$ LANGUAGE plpgsql;

-- Function to refresh cohort analysis cache
CREATE OR REPLACE FUNCTION refresh_cohort_analysis_cache(
    p_tenant_id UUID,
    p_max_age_hours INTEGER DEFAULT 24
) RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete expired cache entries
    DELETE FROM cohort_analysis_cache 
    WHERE tenant_id = p_tenant_id 
      AND (expires_at < NOW() OR created_at < NOW() - (p_max_age_hours || ' hours')::interval);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update cohort segments timestamp
CREATE OR REPLACE FUNCTION update_cohort_segments_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for cohort_segments
CREATE TRIGGER trigger_update_cohort_segments_timestamp
    BEFORE UPDATE ON cohort_segments
    FOR EACH ROW
    EXECUTE FUNCTION update_cohort_segments_timestamp();

-- =====================================================
-- INITIAL DATA AND CONFIGURATION
-- =====================================================

-- Note: Default configuration will be inserted when tenants are created
-- This ensures proper foreign key relationships

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- View for monitoring cohort analysis performance
CREATE OR REPLACE VIEW cohort_analysis_performance AS
SELECT 
    tenant_id,
    cohort_type,
    granularity,
    COUNT(*) as cache_entries,
    AVG(execution_time_ms) as avg_execution_time_ms,
    MIN(execution_time_ms) as min_execution_time_ms,
    MAX(execution_time_ms) as max_execution_time_ms,
    COUNT(*) FILTER (WHERE expires_at > NOW()) as active_cache_entries,
    COUNT(*) FILTER (WHERE expires_at <= NOW()) as expired_cache_entries
FROM cohort_analysis_cache
GROUP BY tenant_id, cohort_type, granularity;

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify table creation
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename IN ('cohort_segments', 'retention_curves', 'cohort_analysis_cache', 'cohort_predictions')
ORDER BY tablename;

-- Verify indexes
SELECT 
    indexname,
    tablename,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('cohort_segments', 'retention_curves', 'cohort_analysis_cache', 'cohort_predictions')
ORDER BY tablename, indexname;
