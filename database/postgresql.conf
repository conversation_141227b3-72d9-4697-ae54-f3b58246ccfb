# PostgreSQL Performance Monitoring Configuration with TimescaleDB
# This configuration enables pg_stat_statements for query performance tracking
# and optimizes for TimescaleDB time-series workloads

# Extensions for TimescaleDB and monitoring
shared_preload_libraries = 'timescaledb,pg_stat_statements'

# pg_stat_statements settings
pg_stat_statements.max = 10000
pg_stat_statements.track = all
pg_stat_statements.track_utility = on
pg_stat_statements.save = on

# Performance monitoring settings
log_statement = 'all'
log_duration = on
log_min_duration_statement = 100  # Log queries taking longer than 100ms
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on

# Memory settings optimized for TimescaleDB and analytics workloads
shared_buffers = 512MB                    # Increased for time-series data
work_mem = 32MB                          # Increased for complex analytics queries
maintenance_work_mem = 128MB             # Increased for compression and maintenance
effective_cache_size = 2GB               # Increased for better query planning
temp_buffers = 32MB                      # For temporary tables in analytics

# TimescaleDB specific memory settings
timescaledb.max_background_workers = 8   # For parallel chunk processing

# Checkpoint settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB

# Connection settings
max_connections = 200

# Query planner settings
random_page_cost = 1.1
effective_io_concurrency = 200

# Autovacuum settings
autovacuum = on
autovacuum_analyze_scale_factor = 0.1
autovacuum_vacuum_scale_factor = 0.2

# Statistics settings
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all

# TimescaleDB compression settings
timescaledb.enable_transparent_decompression = on
timescaledb.enable_chunk_skipping = on
timescaledb.enable_constraint_aware_append = on
timescaledb.enable_ordered_append = on
timescaledb.enable_chunk_append = on

# Parallel processing for analytics
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4

# JIT compilation for complex queries
jit = on
jit_above_cost = 100000
jit_inline_above_cost = 500000
jit_optimize_above_cost = 500000