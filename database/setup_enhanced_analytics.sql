-- Enhanced Analytics Database Setup Script
-- Phase 1: Foundation & Core Analytics Implementation
-- Execute this script to set up the complete enhanced analytics schema

-- Check if TimescaleDB extension is available
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_available_extensions WHERE name = 'timescaledb') THEN
        RAISE EXCEPTION 'TimescaleDB extension is not available. Please install TimescaleDB first.';
    END IF;
END $$;

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Display TimescaleDB version
SELECT extversion FROM pg_extension WHERE extname = 'timescaledb';

-- Execute the enhanced analytics schema
\echo 'Creating enhanced analytics schema...'
\i 020_create_enhanced_analytics_schema.sql

-- Setup TimescaleDB hypertables and optimization
\echo 'Setting up TimescaleDB hypertables and optimization...'
\i 021_setup_timescaledb_hypertables.sql

-- Create performance indexes
\echo 'Creating performance indexes...'
\i 022_create_performance_indexes.sql

-- Verify hypertables were created successfully
\echo 'Verifying hypertable setup...'
SELECT 
    hypertable_name,
    num_dimensions,
    num_chunks,
    compression_enabled,
    compressed_chunks,
    uncompressed_chunks
FROM timescaledb_information.hypertables 
WHERE hypertable_schema = 'public'
ORDER BY hypertable_name;

-- Display continuous aggregates
\echo 'Continuous aggregates created:'
SELECT 
    view_name,
    materialized_only,
    compression_enabled
FROM timescaledb_information.continuous_aggregates
ORDER BY view_name;

-- Display retention policies
\echo 'Retention policies configured:'
SELECT 
    hypertable_name,
    older_than,
    cascade_to_materializations
FROM timescaledb_information.drop_chunks_policies
ORDER BY hypertable_name;

-- Display compression policies
\echo 'Compression policies configured:'
SELECT 
    hypertable_name,
    older_than
FROM timescaledb_information.compression_settings
ORDER BY hypertable_name;

-- Create sample tenant for testing
\echo 'Creating sample tenant for testing...'
INSERT INTO tenants (id, name, domain, subscription_tier, settings)
VALUES (
    '00000000-0000-0000-0000-000000000001',
    'Demo Tenant',
    'demo.example.com',
    'enterprise',
    '{"features": ["analytics", "social_media", "ml_predictions"], "limits": {"events_per_month": 1000000}}'
) ON CONFLICT (domain) DO NOTHING;

-- Create sample e-commerce platform
INSERT INTO ecommerce_platforms (id, tenant_id, platform_type, platform_name, api_credentials, is_active)
VALUES (
    '00000000-0000-0000-0000-000000000002',
    '00000000-0000-0000-0000-000000000001',
    'shopify',
    'Demo Store',
    '{"shop_domain": "demo-store.myshopify.com", "access_token": "encrypted_token"}',
    true
) ON CONFLICT (tenant_id, platform_type, platform_name) DO NOTHING;

-- Create sample attribution model
INSERT INTO attribution_models (tenant_id, name, model_type, configuration, is_default, is_active)
VALUES (
    '00000000-0000-0000-0000-000000000001',
    'Linear Attribution',
    'linear',
    '{"window_days": 30, "weight_distribution": "equal"}',
    true,
    true
) ON CONFLICT DO NOTHING;

-- Insert sample data for testing (small dataset)
\echo 'Inserting sample data for testing...'

-- Sample customers
INSERT INTO customers (id, tenant_id, email, first_name, last_name, acquisition_channel, acquisition_date)
VALUES 
    ('10000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '<EMAIL>', 'John', 'Doe', 'social_media', NOW() - INTERVAL '30 days'),
    ('10000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001', '<EMAIL>', 'Jane', 'Smith', 'organic_search', NOW() - INTERVAL '25 days'),
    ('10000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000001', '<EMAIL>', 'Bob', 'Johnson', 'paid_search', NOW() - INTERVAL '20 days')
ON CONFLICT (tenant_id, email) DO NOTHING;

-- Sample products
INSERT INTO products (id, tenant_id, platform_id, external_id, sku, name, category, price, is_active)
VALUES 
    ('20000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002', 'prod_001', 'SKU001', 'Premium Widget', 'Electronics', 99.99, true),
    ('20000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002', 'prod_002', 'SKU002', 'Standard Widget', 'Electronics', 49.99, true),
    ('20000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000002', 'prod_003', 'SKU003', 'Basic Widget', 'Electronics', 29.99, true)
ON CONFLICT (tenant_id, platform_id, external_id) DO NOTHING;

-- Sample customer events (last 7 days)
INSERT INTO customer_events (tenant_id, customer_id, event_type, event_source, product_id, revenue, utm_source, utm_medium, utm_campaign, timestamp)
SELECT 
    '00000000-0000-0000-0000-000000000001',
    (ARRAY['10000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000003'])[floor(random() * 3 + 1)],
    (ARRAY['page_view', 'product_view', 'add_to_cart', 'purchase'])[floor(random() * 4 + 1)],
    'website',
    (ARRAY['20000000-0000-0000-0000-000000000001', '20000000-0000-0000-0000-000000000002', '20000000-0000-0000-0000-000000000003'])[floor(random() * 3 + 1)],
    CASE WHEN random() < 0.1 THEN random() * 100 ELSE 0 END,
    (ARRAY['google', 'facebook', 'instagram', 'youtube'])[floor(random() * 4 + 1)],
    (ARRAY['organic', 'cpc', 'social', 'video'])[floor(random() * 4 + 1)],
    'demo_campaign',
    NOW() - (random() * INTERVAL '7 days')
FROM generate_series(1, 1000);

-- Sample orders
INSERT INTO orders (tenant_id, platform_id, customer_id, external_order_id, order_number, status, total_amount, line_items, created_at)
SELECT 
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000002',
    (ARRAY['10000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000003'])[floor(random() * 3 + 1)],
    'order_' || generate_random_uuid(),
    'ORD-' || LPAD((row_number() OVER())::text, 6, '0'),
    (ARRAY['pending', 'completed', 'shipped', 'delivered'])[floor(random() * 4 + 1)],
    (random() * 200 + 20)::decimal(10,2),
    '[{"product_id": "prod_001", "quantity": 1, "price": 99.99}]',
    NOW() - (random() * INTERVAL '30 days')
FROM generate_series(1, 100);

-- Sample branded links
INSERT INTO branded_links (tenant_id, short_code, original_url, branded_url, title, utm_parameters, is_active)
VALUES 
    ('00000000-0000-0000-0000-000000000001', 'demo1', 'https://demo-store.com/products/widget', 'https://track.demo.com/demo1', 'Premium Widget Link', '{"utm_source": "social", "utm_medium": "post", "utm_campaign": "demo"}', true),
    ('00000000-0000-0000-0000-000000000001', 'demo2', 'https://demo-store.com/sale', 'https://track.demo.com/demo2', 'Sale Page Link', '{"utm_source": "email", "utm_medium": "newsletter", "utm_campaign": "sale"}', true)
ON CONFLICT (tenant_id, short_code) DO NOTHING;

-- Sample link clicks
INSERT INTO link_clicks (tenant_id, link_id, ip_address, country, device_type, click_timestamp)
SELECT 
    '00000000-0000-0000-0000-000000000001',
    bl.id,
    ('192.168.1.' || floor(random() * 255))::inet,
    (ARRAY['US', 'CA', 'UK', 'DE', 'FR'])[floor(random() * 5 + 1)],
    (ARRAY['desktop', 'mobile', 'tablet'])[floor(random() * 3 + 1)],
    NOW() - (random() * INTERVAL '7 days')
FROM branded_links bl, generate_series(1, 50)
WHERE bl.tenant_id = '00000000-0000-0000-0000-000000000001';

\echo 'Enhanced analytics database setup completed successfully!'
\echo 'Sample data inserted for testing.'
\echo ''
\echo 'Next steps:'
\echo '1. Update your Analytics Service to use the new schema'
\echo '2. Implement the business metrics queries'
\echo '3. Update the Fresh frontend with new dashboard components'
\echo ''
\echo 'Performance targets:'
\echo '- Query response time: <100ms for dashboard queries'
\echo '- Data ingestion: 10,000+ events/second'
\echo '- Storage compression: 70% ratio'
