#!/bin/bash

# Deployment script for Deno Admin Service
# Supports both Docker Compose and Kubernetes deployments

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
SERVICE_NAME="admin-service-deno"
NAMESPACE="ecommerce-analytics"
DEPLOYMENT_TYPE="${1:-docker-compose}"
ENVIRONMENT="${2:-development}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    case $DEPLOYMENT_TYPE in
        "docker-compose")
            if ! command -v docker &> /dev/null; then
                log_error "Docker is not installed"
                exit 1
            fi
            
            if ! command -v docker-compose &> /dev/null; then
                log_error "Docker Compose is not installed"
                exit 1
            fi
            ;;
            
        "kubernetes")
            if ! command -v kubectl &> /dev/null; then
                log_error "kubectl is not installed"
                exit 1
            fi
            
            if ! kubectl cluster-info &> /dev/null; then
                log_error "Cannot connect to Kubernetes cluster"
                exit 1
            fi
            ;;
            
        *)
            log_error "Invalid deployment type: $DEPLOYMENT_TYPE"
            echo "Usage: $0 [docker-compose|kubernetes] [development|staging|production]"
            exit 1
            ;;
    esac
    
    log_success "Prerequisites check passed"
}

# Build Docker image
build_image() {
    log_info "Building Deno admin service Docker image..."
    
    cd "${PROJECT_ROOT}/services/admin-deno"
    
    local target="production"
    if [[ "$ENVIRONMENT" == "development" ]]; then
        target="development"
    fi
    
    docker build \
        -f Dockerfile.deno \
        --target "$target" \
        -t "${SERVICE_NAME}:${ENVIRONMENT}" \
        -t "${SERVICE_NAME}:latest" \
        .
    
    log_success "Docker image built successfully"
}

# Deploy with Docker Compose
deploy_docker_compose() {
    log_info "Deploying with Docker Compose..."
    
    cd "$PROJECT_ROOT"
    
    # Set environment variables
    export BUILD_TARGET="production"
    export NODE_ENV="$ENVIRONMENT"
    
    # Start the admin service
    docker-compose up -d admin-service
    
    # Wait for service to be healthy
    log_info "Waiting for service to be healthy..."
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T admin-service deno run --allow-net --allow-env healthcheck.ts &> /dev/null; then
            log_success "Service is healthy"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            log_error "Service failed to become healthy after $max_attempts attempts"
            docker-compose logs admin-service
            exit 1
        fi
        
        log_info "Attempt $attempt/$max_attempts - waiting for service..."
        sleep 10
        ((attempt++))
    done
    
    log_success "Docker Compose deployment completed"
}

# Deploy to Kubernetes
deploy_kubernetes() {
    log_info "Deploying to Kubernetes..."
    
    # Create namespace if it doesn't exist
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Apply Kubernetes manifests
    local k8s_dir="${SCRIPT_DIR}/k8s/${SERVICE_NAME}"
    
    log_info "Applying RBAC configuration..."
    kubectl apply -f "${k8s_dir}/rbac.yaml"
    
    log_info "Applying ConfigMap..."
    kubectl apply -f "${k8s_dir}/configmap.yaml"
    
    log_info "Applying Service..."
    kubectl apply -f "${k8s_dir}/service.yaml"
    
    log_info "Applying Deployment..."
    kubectl apply -f "${k8s_dir}/deployment.yaml"
    
    log_info "Applying HPA and PDB..."
    kubectl apply -f "${k8s_dir}/hpa.yaml"
    
    # Wait for deployment to be ready
    log_info "Waiting for deployment to be ready..."
    kubectl rollout status deployment/"$SERVICE_NAME" -n "$NAMESPACE" --timeout=300s
    
    # Verify pods are running
    log_info "Verifying pods are running..."
    kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME"
    
    # Test service health
    log_info "Testing service health..."
    local pod_name=$(kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME" -o jsonpath='{.items[0].metadata.name}')
    kubectl exec -n "$NAMESPACE" "$pod_name" -- deno run --allow-net --allow-env healthcheck.ts
    
    log_success "Kubernetes deployment completed"
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    cd "${PROJECT_ROOT}/services/admin-deno"
    
    # Check if Deno is available
    if ! command -v deno &> /dev/null; then
        log_warning "Deno not found, skipping tests"
        return
    fi
    
    # Run tests
    deno test --allow-all tests/
    
    log_success "Tests completed successfully"
}

# Performance benchmark
run_performance_benchmark() {
    log_info "Running performance benchmark..."
    
    cd "${PROJECT_ROOT}/services/admin-deno"
    
    if ! command -v deno &> /dev/null; then
        log_warning "Deno not found, skipping performance benchmark"
        return
    fi
    
    # Run performance tests
    deno test --allow-all tests/performance_test.ts
    deno test --allow-all tests/nodejs_comparison_test.ts
    
    log_success "Performance benchmark completed"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    
    case $DEPLOYMENT_TYPE in
        "docker-compose")
            cd "$PROJECT_ROOT"
            docker-compose down admin-service
            ;;
            
        "kubernetes")
            kubectl delete -f "${SCRIPT_DIR}/k8s/${SERVICE_NAME}/" --ignore-not-found=true
            ;;
    esac
    
    log_success "Cleanup completed"
}

# Main deployment function
main() {
    log_info "Starting Deno Admin Service deployment"
    log_info "Deployment type: $DEPLOYMENT_TYPE"
    log_info "Environment: $ENVIRONMENT"
    
    # Check prerequisites
    check_prerequisites
    
    # Run tests if in development
    if [[ "$ENVIRONMENT" == "development" ]]; then
        run_tests
        run_performance_benchmark
    fi
    
    # Build image
    build_image
    
    # Deploy based on type
    case $DEPLOYMENT_TYPE in
        "docker-compose")
            deploy_docker_compose
            ;;
            
        "kubernetes")
            deploy_kubernetes
            ;;
    esac
    
    log_success "Deployment completed successfully!"
    
    # Show service information
    case $DEPLOYMENT_TYPE in
        "docker-compose")
            log_info "Service URL: http://localhost:3004"
            log_info "Health check: http://localhost:3004/api/health"
            ;;
            
        "kubernetes")
            log_info "Service deployed to namespace: $NAMESPACE"
            log_info "To access the service:"
            log_info "  kubectl port-forward -n $NAMESPACE svc/$SERVICE_NAME 3004:3004"
            log_info "  Then visit: http://localhost:3004"
            ;;
    esac
}

# Handle script arguments
case "${1:-help}" in
    "docker-compose"|"kubernetes")
        main
        ;;
    "cleanup")
        cleanup
        ;;
    "test")
        run_tests
        ;;
    "benchmark")
        run_performance_benchmark
        ;;
    "help"|*)
        echo "Usage: $0 [command] [environment]"
        echo ""
        echo "Commands:"
        echo "  docker-compose [dev|staging|prod]  Deploy using Docker Compose"
        echo "  kubernetes [dev|staging|prod]      Deploy to Kubernetes"
        echo "  cleanup                             Clean up deployment"
        echo "  test                                Run tests only"
        echo "  benchmark                           Run performance benchmark"
        echo "  help                                Show this help"
        echo ""
        echo "Environments:"
        echo "  development (default)"
        echo "  staging"
        echo "  production"
        ;;
esac
