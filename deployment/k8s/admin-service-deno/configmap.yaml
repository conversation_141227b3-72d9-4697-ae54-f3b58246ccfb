apiVersion: v1
kind: ConfigMap
metadata:
  name: admin-service-config
  namespace: ecommerce-analytics
  labels:
    app: admin-service-deno
    component: admin
    runtime: deno
data:
  cors-origins: "https://admin.ecommerce-analytics.com,https://dashboard.ecommerce-analytics.com"
  log-level: "info"
  jwt-issuer: "ecommerce-analytics-admin"
  jwt-audience: "admin-dashboard"
  analytics-service-url: "http://analytics-service:3002"
  dashboard-service-url: "http://dashboard-service:3001"
  integration-service-url: "http://integration-service:3003"
  
  # Feature flags
  feature-system-metrics: "true"
  feature-user-management: "true"
  feature-log-viewing: "true"
  feature-backup-management: "true"
  feature-analytics-overview: "true"
  feature-real-time-monitoring: "true"
  feature-alert-management: "true"
  feature-report-generation: "true"
  
  # Rate limiting configuration
  rate-limit-window: "900000"  # 15 minutes
  rate-limit-max: "100"        # requests per window
  
  # Security configuration
  bcrypt-rounds: "12"
  session-timeout: "1800000"   # 30 minutes
  max-login-attempts: "5"
  lockout-duration: "900000"   # 15 minutes
  password-min-length: "8"
  
  # Upload configuration
  upload-max-size: "52428800"  # 50MB
  upload-allowed-types: "image/jpeg,image/png,text/csv,application/json"
  
  # Monitoring configuration
  monitoring-interval: "60000"     # 1 minute
  monitoring-retention-days: "30"
  alert-cpu-threshold: "80"
  alert-memory-threshold: "85"
  alert-disk-threshold: "90"
  alert-response-time-threshold: "5000"
  alert-error-rate-threshold: "5"
