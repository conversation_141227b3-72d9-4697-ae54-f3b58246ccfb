apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-service-deno
  namespace: ecommerce-analytics
  labels:
    app: admin-service-deno
    version: v1
    component: admin
    runtime: deno
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: admin-service-deno
  template:
    metadata:
      labels:
        app: admin-service-deno
        version: v1
        component: admin
        runtime: deno
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3004"
        prometheus.io/path: "/api/metrics"
    spec:
      serviceAccountName: admin-service-deno
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: admin-service-deno
        image: ghcr.io/cavelltopdev/realclickninja/admin-service-deno:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 3004
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: ADMIN_PORT
          value: "3004"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: host
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: database
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: password
        - name: DB_SSL
          value: "true"
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: host
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: admin-service-secrets
              key: jwt-secret
        - name: JWT_ISSUER
          value: "ecommerce-analytics-admin"
        - name: JWT_AUDIENCE
          value: "admin-dashboard"
        - name: CORS_ORIGIN
          valueFrom:
            configMapKeyRef:
              name: admin-service-config
              key: cors-origins
        - name: LOG_LEVEL
          value: "info"
        - name: ANALYTICS_SERVICE_URL
          value: "http://analytics-service:3002"
        - name: DASHBOARD_SERVICE_URL
          value: "http://dashboard-service:3001"
        - name: INTEGRATION_SERVICE_URL
          value: "http://integration-service:3003"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health/live
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health/ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/health/live
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 6
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: logs
        emptyDir: {}
      - name: uploads
        persistentVolumeClaim:
          claimName: admin-service-uploads
      - name: tmp
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - admin-service-deno
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
