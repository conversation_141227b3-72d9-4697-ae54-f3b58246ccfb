apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: admin-service-deno-hpa
  namespace: ecommerce-analytics
  labels:
    app: admin-service-deno
    component: admin
    runtime: deno
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: admin-service-deno
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: admin-service-deno-pdb
  namespace: ecommerce-analytics
  labels:
    app: admin-service-deno
    component: admin
    runtime: deno
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: admin-service-deno
