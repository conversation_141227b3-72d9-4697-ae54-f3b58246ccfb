apiVersion: v1
kind: ServiceAccount
metadata:
  name: admin-service-deno
  namespace: ecommerce-analytics
  labels:
    app: admin-service-deno
    component: admin
    runtime: deno
automountServiceAccountToken: false

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: admin-service-deno
  namespace: ecommerce-analytics
  labels:
    app: admin-service-deno
    component: admin
    runtime: deno
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: admin-service-deno
  namespace: ecommerce-analytics
  labels:
    app: admin-service-deno
    component: admin
    runtime: deno
subjects:
- kind: ServiceAccount
  name: admin-service-deno
  namespace: ecommerce-analytics
roleRef:
  kind: Role
  name: admin-service-deno
  apiGroup: rbac.authorization.k8s.io
