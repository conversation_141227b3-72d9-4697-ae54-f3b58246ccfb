apiVersion: v1
kind: Service
metadata:
  name: admin-service-deno
  namespace: ecommerce-analytics
  labels:
    app: admin-service-deno
    component: admin
    runtime: deno
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 3004
    targetPort: http
    protocol: TCP
  selector:
    app: admin-service-deno

---
apiVersion: v1
kind: Service
metadata:
  name: admin-service-deno-headless
  namespace: ecommerce-analytics
  labels:
    app: admin-service-deno
    component: admin
    runtime: deno
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 3004
    targetPort: http
    protocol: TCP
  selector:
    app: admin-service-deno
