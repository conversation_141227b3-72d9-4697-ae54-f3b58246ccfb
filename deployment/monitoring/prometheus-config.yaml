apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-deno-admin-config
  namespace: monitoring
  labels:
    app: prometheus
    component: config
data:
  deno-admin-service.yml: |
    # Prometheus configuration for Deno Admin Service
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "deno-admin-alerts.yml"
    
    scrape_configs:
      # Deno Admin Service metrics
      - job_name: 'deno-admin-service'
        static_configs:
          - targets: ['admin-service-deno:3004']
        metrics_path: '/api/metrics'
        scrape_interval: 10s
        scrape_timeout: 5s
        honor_labels: true
        params:
          format: ['prometheus']
        relabel_configs:
          - source_labels: [__address__]
            target_label: __param_target
          - source_labels: [__param_target]
            target_label: instance
          - target_label: __address__
            replacement: admin-service-deno:3004
        metric_relabel_configs:
          - source_labels: [__name__]
            regex: 'deno_.*'
            target_label: service
            replacement: 'admin-service-deno'
      
      # Health check monitoring
      - job_name: 'deno-admin-health'
        static_configs:
          - targets: ['admin-service-deno:3004']
        metrics_path: '/api/health'
        scrape_interval: 30s
        scrape_timeout: 10s
        honor_labels: true
        
      # System metrics from Deno runtime
      - job_name: 'deno-admin-system'
        static_configs:
          - targets: ['admin-service-deno:3004']
        metrics_path: '/api/system/stats'
        scrape_interval: 60s
        scrape_timeout: 15s
        honor_labels: true

  deno-admin-alerts.yml: |
    # Alert rules for Deno Admin Service
    groups:
      - name: deno-admin-service
        rules:
          # Service availability
          - alert: DenoAdminServiceDown
            expr: up{job="deno-admin-service"} == 0
            for: 1m
            labels:
              severity: critical
              service: admin-service-deno
            annotations:
              summary: "Deno Admin Service is down"
              description: "Deno Admin Service has been down for more than 1 minute"
          
          # High response time
          - alert: DenoAdminHighResponseTime
            expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="deno-admin-service"}[5m])) > 1
            for: 5m
            labels:
              severity: warning
              service: admin-service-deno
            annotations:
              summary: "High response time for Deno Admin Service"
              description: "95th percentile response time is {{ $value }}s"
          
          # High error rate
          - alert: DenoAdminHighErrorRate
            expr: rate(http_requests_total{job="deno-admin-service",status=~"5.."}[5m]) / rate(http_requests_total{job="deno-admin-service"}[5m]) > 0.05
            for: 5m
            labels:
              severity: warning
              service: admin-service-deno
            annotations:
              summary: "High error rate for Deno Admin Service"
              description: "Error rate is {{ $value | humanizePercentage }}"
          
          # High memory usage
          - alert: DenoAdminHighMemoryUsage
            expr: (deno_memory_usage_bytes{job="deno-admin-service"} / deno_memory_limit_bytes{job="deno-admin-service"}) > 0.85
            for: 10m
            labels:
              severity: warning
              service: admin-service-deno
            annotations:
              summary: "High memory usage for Deno Admin Service"
              description: "Memory usage is {{ $value | humanizePercentage }}"
          
          # High CPU usage
          - alert: DenoAdminHighCPUUsage
            expr: rate(deno_cpu_usage_seconds_total{job="deno-admin-service"}[5m]) > 0.8
            for: 10m
            labels:
              severity: warning
              service: admin-service-deno
            annotations:
              summary: "High CPU usage for Deno Admin Service"
              description: "CPU usage is {{ $value | humanizePercentage }}"
          
          # Database connection issues
          - alert: DenoAdminDatabaseConnectionFailed
            expr: deno_database_connections_failed_total{job="deno-admin-service"} > 0
            for: 1m
            labels:
              severity: critical
              service: admin-service-deno
            annotations:
              summary: "Database connection failures for Deno Admin Service"
              description: "{{ $value }} database connection failures detected"
          
          # Redis connection issues
          - alert: DenoAdminRedisConnectionFailed
            expr: deno_redis_connections_failed_total{job="deno-admin-service"} > 0
            for: 1m
            labels:
              severity: warning
              service: admin-service-deno
            annotations:
              summary: "Redis connection failures for Deno Admin Service"
              description: "{{ $value }} Redis connection failures detected"
          
          # Authentication failures
          - alert: DenoAdminHighAuthFailures
            expr: rate(deno_auth_failures_total{job="deno-admin-service"}[5m]) > 10
            for: 5m
            labels:
              severity: warning
              service: admin-service-deno
            annotations:
              summary: "High authentication failure rate for Deno Admin Service"
              description: "{{ $value }} authentication failures per second"
          
          # Rate limiting triggered
          - alert: DenoAdminRateLimitTriggered
            expr: rate(deno_rate_limit_exceeded_total{job="deno-admin-service"}[5m]) > 1
            for: 5m
            labels:
              severity: info
              service: admin-service-deno
            annotations:
              summary: "Rate limiting frequently triggered for Deno Admin Service"
              description: "{{ $value }} rate limit violations per second"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-deno-admin-dashboard
  namespace: monitoring
  labels:
    app: grafana
    component: dashboard
data:
  deno-admin-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Deno Admin Service Dashboard",
        "tags": ["deno", "admin", "ecommerce"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Service Overview",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=\"deno-admin-service\"}",
                "legendFormat": "Service Status"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "green", "value": 1}
                  ]
                }
              }
            }
          },
          {
            "id": 2,
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(http_requests_total{job=\"deno-admin-service\"}[5m])",
                "legendFormat": "Requests/sec"
              }
            ]
          },
          {
            "id": 3,
            "title": "Response Time",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"deno-admin-service\"}[5m]))",
                "legendFormat": "50th percentile"
              },
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"deno-admin-service\"}[5m]))",
                "legendFormat": "95th percentile"
              }
            ]
          },
          {
            "id": 4,
            "title": "Memory Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "deno_memory_usage_bytes{job=\"deno-admin-service\"}",
                "legendFormat": "Memory Usage"
              }
            ]
          },
          {
            "id": 5,
            "title": "Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(http_requests_total{job=\"deno-admin-service\",status=~\"5..\"}[5m])",
                "legendFormat": "5xx Errors/sec"
              }
            ]
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }
