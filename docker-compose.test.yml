version: '3.8'

services:
  # PostgreSQL for testing
  postgres:
    image: postgres:14-alpine
    container_name: test-postgres
    environment:
      POSTGRES_DB: ecommerce_analytics_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: test_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./services/analytics/migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ecommerce_analytics_test"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - test-network

  # Redis for testing
  redis:
    image: redis:7-alpine
    container_name: test-redis
    command: redis-server --requirepass test_password --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    networks:
      - test-network

  # TimescaleDB for advanced testing (optional)
  timescaledb:
    image: timescale/timescaledb:latest-pg14
    container_name: test-timescaledb
    environment:
      POSTGRES_DB: ecommerce_analytics_timeseries_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    volumes:
      - timescaledb_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ecommerce_analytics_timeseries_test"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - test-network

  # Elasticsearch for search testing (optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: test-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_test_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - test-network

  # MailCatcher for email testing
  mailcatcher:
    image: schickling/mailcatcher
    container_name: test-mailcatcher
    ports:
      - "1080:1080"  # Web interface
      - "1025:1025"  # SMTP
    networks:
      - test-network

  # MinIO for S3-compatible storage testing
  minio:
    image: minio/minio:latest
    container_name: test-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: testuser
      MINIO_ROOT_PASSWORD: testpassword
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_test_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s
    networks:
      - test-network

  # Test data seeder
  test-seeder:
    build:
      context: .
      dockerfile: testing/Dockerfile.seeder
    container_name: test-seeder
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ecommerce_analytics_test
      DB_USER: postgres
      DB_PASSWORD: test_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: test_password
    volumes:
      - ./testing/fixtures:/app/fixtures
    networks:
      - test-network
    profiles:
      - seeding

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local
  timescaledb_test_data:
    driver: local
  elasticsearch_test_data:
    driver: local
  minio_test_data:
    driver: local

networks:
  test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
