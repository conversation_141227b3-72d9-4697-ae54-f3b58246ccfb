# System Architecture Guide
## E-commerce Analytics SaaS Platform - Complete Deno 2 Implementation

### 🏗️ Architecture Overview

The E-commerce Analytics SaaS platform is built on a modern microservices architecture with all backend services migrated to Deno 2 and a Fresh frontend providing server-side rendering with Islands architecture. The system processes customer journey data from multiple e-commerce platforms and provides real-time analytics through an optimized dashboard interface.

## 🔧 Core System Components

### Service Architecture
```
┌─────────────────────────────────────────────────────────────────┐
│                    Fresh Frontend (Port 8000)                   │
│                Server-Side Rendering + Islands                  │
└─────────────────────┬───────────────────────────────────────────┘
                      │ HTTP/API Calls
┌─────────────────────▼───────────────────────────────────────────┐
│                Dashboard Backend (Port 3000)                    │
│                   API Gateway + Aggregation                     │
└─────┬─────────┬─────────────┬─────────────┬───────────────────────┘
      │         │             │             │
      ▼         ▼             ▼             ▼
┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────────┐
│Analytics │ │ Billing  │ │Integration│ │Link Tracking │
│Service   │ │ Service  │ │ Service   │ │   Service    │
│(Port     │ │(Port     │ │(Port      │ │(Port 8080)   │
│ 3002)    │ │ 3003)    │ │ 3001)     │ │    (Go)      │
└─────┬────┘ └─────┬────┘ └─────┬─────┘ └──────┬───────┘
      │            │            │              │
      └────────────┼────────────┼──────────────┘
                   │            │
                   ▼            ▼
┌─────────────────────────────────────────────────────────────────┐
│              Shared Data Layer                                  │
│  PostgreSQL + TimescaleDB + Redis                              │
└─────────────────────────────────────────────────────────────────┘
```

### Technology Stack
- **Runtime**: Deno 2.0+ for all backend services
- **Framework**: Oak (Express.js equivalent for Deno)
- **Frontend**: Fresh with Islands architecture
- **Database**: PostgreSQL 15+ with TimescaleDB extension
- **Cache**: Redis 7+ for session management and caching
- **Link Tracking**: Go service for high-performance tracking

## 📊 Service Responsibilities

### Analytics Service (Port 3002)
**Primary Function**: Customer journey tracking and advanced analytics

**Key Responsibilities**:
- Time-series data processing with TimescaleDB
- Customer cohort analysis and retention tracking
- Multi-touch attribution modeling
- Conversion funnel analysis
- Customer lifetime value calculations
- Real-time analytics processing

**API Endpoints**:
- `GET /api/analytics/summary` - Analytics overview with date filtering
- `GET /api/analytics/cohorts` - Cohort analysis data
- `GET /api/analytics/attribution` - Attribution model results
- `GET /api/reports/performance` - Performance reports
- `GET /api/reports/conversion-funnel` - Funnel analysis

### Dashboard Backend (Port 3000)
**Primary Function**: API gateway and data aggregation

**Key Responsibilities**:
- Authentication and authorization management
- Inter-service communication coordination
- Data aggregation from multiple services
- Real-time metrics collection and caching
- Multi-tenant user management
- API proxy layer for frontend

**API Endpoints**:
- `POST /api/auth/login` - User authentication
- `GET /api/dashboard/overview` - Dashboard overview data
- `GET /api/dashboard/metrics` - Real-time metrics
- `GET /api/dashboard/activity` - Recent activity feed
- `GET /api/users/profile` - User profile management

### Fresh Frontend (Port 8000)
**Primary Function**: Server-side rendered UI with interactive islands

**Key Features**:
- Server-side rendering for SEO optimization
- Islands architecture with selective hydration
- D3.js visualizations in client-side islands
- Real-time updates via Server-Sent Events
- Multi-tenant UI with server-rendered content
- Responsive design with Tailwind CSS

**Route Structure**:
- `/` - Dashboard homepage (server-rendered)
- `/analytics` - Analytics dashboard with D3.js charts
- `/integrations` - Platform integration management
- `/billing` - Subscription and billing interface
- `/api/*` - API proxy routes to backend services

### Billing Service (Port 3003)
**Primary Function**: Subscription management and payment processing

**Key Responsibilities**:
- Stripe integration for payment processing
- Subscription lifecycle management
- Invoice generation and management
- Webhook processing for payment events
- Multi-tenant billing isolation
- Background job processing for recurring tasks

**API Endpoints**:
- `GET /api/subscriptions` - User subscription management
- `POST /api/payments/process` - Payment processing
- `GET /api/invoices` - Invoice management
- `POST /webhooks/stripe` - Stripe webhook handling

### Integration Service (Port 3001)
**Primary Function**: E-commerce platform API integrations

**Key Responsibilities**:
- Shopify GraphQL and REST API integration
- WooCommerce REST API with OAuth authentication
- eBay Trading API integration
- Real-time webhook processing from platforms
- Rate limiting and retry mechanisms
- Data normalization across platforms

**API Endpoints**:
- `GET /api/integrations` - Platform integration management
- `POST /api/shopify/sync` - Shopify data synchronization
- `POST /api/woocommerce/sync` - WooCommerce data sync
- `POST /webhooks/shopify` - Shopify webhook processing
- `POST /webhooks/woocommerce` - WooCommerce webhook handling

### Link Tracking Service (Port 8080)
**Primary Function**: High-performance branded link tracking

**Key Responsibilities**:
- Branded link creation and management
- Real-time click tracking and analytics
- Geographic and device analytics
- Campaign attribution tracking
- High-throughput click processing

**API Endpoints**:
- `POST /api/links` - Create branded links
- `GET /api/links/:id/analytics` - Link performance data
- `GET /r/:shortCode` - Link redirection with tracking

## 🔄 Data Flow Architecture

### Request Processing Flow
1. **User Request** → Fresh Frontend receives HTTP request
2. **Server-Side Rendering** → Fresh processes route and renders initial HTML
3. **API Calls** → Frontend makes API calls to Dashboard Backend
4. **Service Coordination** → Dashboard Backend coordinates with target services
5. **Database Queries** → Services execute multi-tenant database queries
6. **Response Aggregation** → Dashboard Backend aggregates responses
7. **Client Hydration** → Fresh islands hydrate for interactivity
8. **Real-time Updates** → Server-Sent Events provide live updates

### Multi-Tenant Data Isolation
```sql
-- Example multi-tenant query pattern
SELECT * FROM analytics_events 
WHERE tenant_id = $1 
  AND created_at >= $2 
  AND created_at <= $3
ORDER BY created_at DESC;
```

### Inter-Service Communication
- **Authentication**: JWT tokens validated at each service boundary
- **Tenant Context**: Tenant ID propagated through request headers
- **Error Handling**: Centralized error handling with correlation IDs
- **Rate Limiting**: Per-tenant rate limiting across all services
- **Circuit Breaker**: Fault tolerance for service dependencies

## 🔐 Security Architecture

### Authentication Flow
1. User submits credentials to Dashboard Backend
2. Backend validates against PostgreSQL user store
3. JWT token generated with user and tenant information
4. Token included in all subsequent requests
5. Each service validates JWT independently
6. Tenant context extracted for data isolation

### Multi-Tenant Security
- **Data Isolation**: All database queries include tenant_id filtering
- **API Security**: Tenant validation at service boundaries
- **Resource Isolation**: Per-tenant rate limiting and quotas
- **Audit Logging**: Comprehensive security event logging

### Deno Security Model
- **Secure by Default**: No file, network, or environment access without explicit permission
- **Permission Flags**: `--allow-net`, `--allow-env`, `--allow-read` for controlled access
- **Sandboxed Execution**: Isolated runtime environment
- **No npm Vulnerabilities**: Direct URL imports eliminate package security issues

## 📈 Performance Optimizations

### Frontend Performance
- **Server-Side Rendering**: 83% faster initial load times
- **Islands Architecture**: Selective hydration reduces JavaScript bundle size
- **Code Splitting**: Automatic code splitting by Fresh framework
- **Caching Strategy**: Redis caching for frequently accessed data

### Backend Performance
- **Connection Pooling**: Optimized PostgreSQL connection pools
- **Query Optimization**: TimescaleDB for time-series data
- **Service Mesh**: Efficient inter-service communication
- **Monitoring**: Prometheus metrics for performance tracking

### Database Performance
- **TimescaleDB**: Optimized for time-series analytics data
- **Indexing Strategy**: Composite indexes on tenant_id and timestamps
- **Query Optimization**: Parameterized queries with connection pooling
- **Caching Layer**: Redis for frequently accessed data

## 🔍 Monitoring & Observability

### Health Monitoring
- **Health Checks**: `/health`, `/ready`, `/live` endpoints for each service
- **Service Discovery**: Kubernetes service discovery and load balancing
- **Circuit Breakers**: Fault tolerance for service dependencies
- **Graceful Degradation**: Fallback mechanisms for service failures

### Metrics Collection
- **Prometheus**: System and application metrics collection
- **Custom Metrics**: Business logic performance tracking
- **Real-time Dashboards**: Grafana visualization
- **Alerting**: Automated alerting for critical issues

### Logging Strategy
- **Structured Logging**: JSON-based logging across all services
- **Correlation IDs**: Request tracing across service boundaries
- **Log Aggregation**: Centralized logging with ELK stack
- **Security Logging**: Comprehensive audit trail

This architecture provides a scalable, secure, and high-performance foundation for the e-commerce analytics platform, leveraging modern technologies and best practices for enterprise-grade applications.
