# Troubleshooting Guide
## E-commerce Analytics SaaS Platform - Common Issues & Solutions

This guide covers common issues, debugging procedures, and troubleshooting steps for the complete Deno 2 + Fresh e-commerce analytics platform.

## 🚨 Quick Diagnostics

### System Health Check
```bash
# Check all service health endpoints
./scripts/health-check.sh

# Or manually check each service
curl http://localhost:3002/health  # Analytics Service
curl http://localhost:3000/health  # Dashboard Backend
curl http://localhost:8000/api/health  # Fresh Frontend
curl http://localhost:3003/health  # Billing Service
curl http://localhost:3001/health  # Integration Service
curl http://localhost:8080/health  # Link Tracking Service
```

### Service Status Check
```bash
# Docker Compose services
docker-compose ps

# Individual service logs
docker-compose logs analytics-service
docker-compose logs dashboard-service
docker-compose logs dashboard-fresh
docker-compose logs billing-service
docker-compose logs integration-service
```

## 🔧 Common Issues & Solutions

### 1. Service Startup Issues

#### Problem: Deno Service Won't Start
**Symptoms:**
- Service exits immediately
- Permission denied errors
- Module not found errors

**Solutions:**
```bash
# Check Deno permissions
deno run --allow-net --allow-env --allow-read --allow-write src/main.ts

# Clear Deno cache
deno cache --reload src/main.ts

# Check environment variables
printenv | grep -E "(DB_|REDIS_|JWT_)"

# Verify file permissions
ls -la services/analytics-deno/src/main.ts
```

#### Problem: Fresh Frontend Build Failures
**Symptoms:**
- Build process fails
- TypeScript compilation errors
- Import resolution issues

**Solutions:**
```bash
# Clear Fresh cache
cd services/dashboard-fresh
rm -rf .fresh/
deno cache --reload main.ts

# Check Fresh configuration
deno task check

# Verify import paths
deno info main.ts
```

### 2. Database Connection Issues

#### Problem: PostgreSQL Connection Refused
**Symptoms:**
- "Connection refused" errors
- Services can't connect to database
- Health checks failing

**Solutions:**
```bash
# Check PostgreSQL status
docker-compose ps postgres

# View PostgreSQL logs
docker-compose logs postgres

# Test database connection
docker exec -it ecommerce-postgres psql -U postgres -d ecommerce_analytics

# Check network connectivity
docker exec ecommerce-analytics-deno ping postgres

# Verify environment variables
echo $DB_HOST $DB_PORT $DB_NAME $DB_USER
```

#### Problem: TimescaleDB Extension Issues
**Symptoms:**
- TimescaleDB functions not available
- Time-series queries failing
- Extension not loaded

**Solutions:**
```sql
-- Connect to database and check extensions
\dx

-- Enable TimescaleDB if not enabled
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Check hypertables
SELECT * FROM timescaledb_information.hypertables;

-- Recreate hypertable if needed
SELECT create_hypertable('analytics_events', 'created_at');
```

### 3. Redis Connection Issues

#### Problem: Redis Connection Failures
**Symptoms:**
- Cache operations failing
- Session management issues
- Rate limiting not working

**Solutions:**
```bash
# Check Redis status
docker-compose ps redis

# Test Redis connection
docker exec -it ecommerce-redis redis-cli ping

# Check Redis logs
docker-compose logs redis

# Test Redis from service container
docker exec ecommerce-analytics-deno deno eval "
const redis = new Redis('redis://redis:6379');
console.log(await redis.ping());
"
```

### 4. Authentication & Authorization Issues

#### Problem: JWT Token Validation Failures
**Symptoms:**
- 401 Unauthorized errors
- Token expired messages
- Authentication loops

**Solutions:**
```bash
# Check JWT secret consistency across services
grep JWT_SECRET .env

# Verify token format
echo "your-jwt-token" | base64 -d

# Test authentication endpoint
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Check token expiration
deno eval "
const token = 'your-jwt-token';
const payload = JSON.parse(atob(token.split('.')[1]));
console.log('Expires:', new Date(payload.exp * 1000));
"
```

### 5. Inter-Service Communication Issues

#### Problem: Service-to-Service API Calls Failing
**Symptoms:**
- 500 Internal Server Error
- Service unavailable errors
- Timeout errors

**Solutions:**
```bash
# Check service discovery
docker exec ecommerce-dashboard-deno nslookup analytics-service

# Test inter-service connectivity
docker exec ecommerce-dashboard-deno curl http://analytics-service:3002/health

# Check service URLs in environment
docker exec ecommerce-dashboard-deno printenv | grep SERVICE_URL

# Verify network configuration
docker network ls
docker network inspect ecommerce-analytics-saas_ecommerce-network
```

### 6. Fresh Frontend Issues

#### Problem: Islands Not Hydrating
**Symptoms:**
- Interactive components not working
- JavaScript errors in browser
- Islands appearing as static content

**Solutions:**
```bash
# Check browser console for errors
# Open Developer Tools → Console

# Verify island registration
cd services/dashboard-fresh
grep -r "export default function" islands/

# Check Fresh dev server
deno task dev --verbose

# Clear browser cache and reload
```

#### Problem: Server-Side Rendering Errors
**Symptoms:**
- 500 errors on page load
- Blank pages
- Template rendering failures

**Solutions:**
```bash
# Check Fresh logs
docker-compose logs dashboard-fresh

# Verify route configuration
ls -la services/dashboard-fresh/routes/

# Test route handlers
curl -v http://localhost:8000/

# Check middleware chain
grep -r "_middleware" services/dashboard-fresh/routes/
```

### 7. Performance Issues

#### Problem: Slow API Response Times
**Symptoms:**
- High response times
- Timeout errors
- Poor user experience

**Solutions:**
```bash
# Check service metrics
curl http://localhost:3002/metrics

# Monitor database queries
docker exec ecommerce-postgres psql -U postgres -d ecommerce_analytics -c "
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;
"

# Check Redis performance
docker exec ecommerce-redis redis-cli --latency-history

# Monitor resource usage
docker stats
```

#### Problem: High Memory Usage
**Symptoms:**
- Services consuming excessive memory
- Out of memory errors
- System slowdown

**Solutions:**
```bash
# Check memory usage per service
docker stats --format "table {{.Container}}\t{{.MemUsage}}\t{{.MemPerc}}"

# Analyze Deno memory usage
deno eval --v8-flags=--expose-gc "
console.log('Memory usage:', Deno.memoryUsage());
"

# Check for memory leaks
# Monitor memory usage over time
watch -n 5 'docker stats --no-stream'
```

### 8. E-commerce Integration Issues

#### Problem: Shopify API Integration Failures
**Symptoms:**
- Webhook processing failures
- API rate limit errors
- Authentication failures

**Solutions:**
```bash
# Check Shopify credentials
echo $SHOPIFY_API_KEY $SHOPIFY_SECRET

# Test Shopify API connection
curl -H "X-Shopify-Access-Token: $SHOPIFY_ACCESS_TOKEN" \
  "https://$SHOP_DOMAIN.myshopify.com/admin/api/2023-10/shop.json"

# Verify webhook endpoints
curl -X POST http://localhost:3001/webhooks/shopify \
  -H "Content-Type: application/json" \
  -H "X-Shopify-Hmac-Sha256: test" \
  -d '{"test": true}'

# Check integration service logs
docker-compose logs integration-service | grep -i shopify
```

### 9. Payment Processing Issues

#### Problem: Stripe Integration Failures
**Symptoms:**
- Payment processing errors
- Webhook validation failures
- Subscription creation issues

**Solutions:**
```bash
# Check Stripe credentials
echo $STRIPE_SECRET_KEY | head -c 20

# Test Stripe API connection
curl https://api.stripe.com/v1/customers \
  -u $STRIPE_SECRET_KEY:

# Verify webhook endpoint
curl -X POST http://localhost:3003/webhooks/stripe \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: test" \
  -d '{"type": "invoice.payment_succeeded"}'

# Check billing service logs
docker-compose logs billing-service | grep -i stripe
```

### 10. Development Environment Issues

#### Problem: Port Conflicts
**Symptoms:**
- "Port already in use" errors
- Services failing to start
- Connection refused errors

**Solutions:**
```bash
# Check what's using ports
lsof -i :3000
lsof -i :3001
lsof -i :3002
lsof -i :3003
lsof -i :8000
lsof -i :8080

# Kill processes using ports
kill -9 $(lsof -t -i:3000)

# Use different ports
export ANALYTICS_PORT=3012
export DASHBOARD_PORT=3010
export FRESH_PORT=8010
```

## 🔍 Debugging Tools & Commands

### Log Analysis
```bash
# Follow all service logs
docker-compose logs -f

# Filter logs by service
docker-compose logs -f analytics-service | grep ERROR

# Search logs for specific patterns
docker-compose logs | grep -i "database\|redis\|error"

# Export logs for analysis
docker-compose logs > debug-logs-$(date +%Y%m%d).txt
```

### Database Debugging
```bash
# Connect to PostgreSQL
docker exec -it ecommerce-postgres psql -U postgres -d ecommerce_analytics

# Check active connections
SELECT * FROM pg_stat_activity WHERE datname = 'ecommerce_analytics';

# Check slow queries
SELECT query, mean_exec_time, calls FROM pg_stat_statements 
ORDER BY mean_exec_time DESC LIMIT 10;

# Check table sizes
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) 
FROM pg_tables WHERE schemaname = 'public';
```

### Network Debugging
```bash
# Check container networking
docker network inspect ecommerce-analytics-saas_ecommerce-network

# Test connectivity between containers
docker exec ecommerce-dashboard-deno ping analytics-service
docker exec ecommerce-fresh-frontend curl http://dashboard-service:3000/health

# Check DNS resolution
docker exec ecommerce-analytics-deno nslookup postgres
```

## 🆘 Emergency Procedures

### Service Recovery
```bash
# Restart specific service
docker-compose restart analytics-service

# Rebuild and restart service
docker-compose up -d --build analytics-service

# Full system restart
docker-compose down && docker-compose up -d
```

### Database Recovery
```bash
# Restore from backup
./scripts/restore-database.sh backup-file.sql

# Reset database (CAUTION: Data loss)
docker-compose down
docker volume rm ecommerce-analytics-saas_postgres_data
docker-compose up -d postgres
./scripts/migrate.sh
```

### Rollback Procedures
```bash
# Rollback to previous Docker images
docker-compose down
docker-compose up -d --scale analytics-service=0
# Deploy previous version
docker-compose up -d
```

## 📞 Getting Help

### Information to Collect
When reporting issues, include:
1. Service logs: `docker-compose logs [service-name]`
2. Environment variables (sanitized)
3. Docker Compose configuration
4. Error messages and stack traces
5. Steps to reproduce the issue

### Log Collection Script
```bash
#!/bin/bash
# collect-debug-info.sh
mkdir -p debug-info
docker-compose ps > debug-info/services-status.txt
docker-compose logs > debug-info/all-logs.txt
docker stats --no-stream > debug-info/resource-usage.txt
printenv | grep -E "(DB_|REDIS_|JWT_|STRIPE_|SHOPIFY_)" > debug-info/env-vars.txt
tar -czf debug-info-$(date +%Y%m%d-%H%M%S).tar.gz debug-info/
```

### Support Channels
1. **GitHub Issues**: Create detailed issue reports
2. **Documentation**: Check service-specific READMEs
3. **Health Checks**: Use built-in monitoring endpoints
4. **Community**: Deno and Fresh community resources

This troubleshooting guide should help resolve most common issues with the e-commerce analytics platform. For persistent issues, collect debug information and create a detailed issue report.
