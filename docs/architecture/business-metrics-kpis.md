# Business Metrics & KPIs for AI-Powered E-Commerce Analytics

## Core Business Intelligence Framework

### 1. Customer Acquisition Metrics

#### Customer Acquisition Cost (CAC)
```sql
-- CAC by Channel
SELECT 
    utm_source,
    utm_medium,
    COUNT(DISTINCT customer_id) as new_customers,
    SUM(marketing_spend) / COUNT(DISTINCT customer_id) as cac
FROM customer_events ce
JOIN marketing_campaigns mc ON ce.utm_campaign = mc.campaign_id
WHERE event_type = 'first_purchase'
    AND timestamp >= NOW() - INTERVAL '30 days'
GROUP BY utm_source, utm_medium;
```

#### Customer Lifetime Value (CLV)
```sql
-- Predictive CLV using ML models
SELECT 
    c.id,
    c.email,
    c.total_spent as historical_clv,
    mp.prediction_value as predicted_clv,
    mp.confidence_score
FROM customers c
JOIN ml_predictions mp ON c.id = mp.entity_id
WHERE mp.prediction_type = 'customer_lifetime_value'
    AND mp.predicted_at >= NOW() - INTERVAL '7 days';
```

#### Acquisition Channel Performance
```sql
-- Channel attribution with conversion rates
SELECT 
    ct.channel,
    COUNT(DISTINCT ct.customer_id) as touchpoints,
    COUNT(DISTINCT o.customer_id) as conversions,
    (COUNT(DISTINCT o.customer_id)::float / COUNT(DISTINCT ct.customer_id)) * 100 as conversion_rate,
    SUM(o.total_amount) as revenue,
    AVG(o.total_amount) as avg_order_value
FROM customer_touchpoints ct
LEFT JOIN orders o ON ct.customer_id = o.customer_id 
    AND o.created_at BETWEEN ct.touchpoint_timestamp AND ct.touchpoint_timestamp + INTERVAL '30 days'
WHERE ct.touchpoint_timestamp >= NOW() - INTERVAL '90 days'
GROUP BY ct.channel
ORDER BY revenue DESC;
```

### 2. Customer Engagement & Retention Metrics

#### Cohort Retention Analysis
```sql
-- Monthly cohort retention rates
WITH cohorts AS (
    SELECT 
        customer_id,
        DATE_TRUNC('month', MIN(created_at)) as cohort_month
    FROM orders
    GROUP BY customer_id
),
customer_activities AS (
    SELECT 
        c.customer_id,
        c.cohort_month,
        DATE_TRUNC('month', o.created_at) as activity_month,
        EXTRACT(MONTH FROM AGE(o.created_at, c.cohort_month)) as period_number
    FROM cohorts c
    JOIN orders o ON c.customer_id = o.customer_id
)
SELECT 
    cohort_month,
    period_number,
    COUNT(DISTINCT customer_id) as customers,
    ROUND(
        COUNT(DISTINCT customer_id) * 100.0 / 
        FIRST_VALUE(COUNT(DISTINCT customer_id)) OVER (
            PARTITION BY cohort_month ORDER BY period_number
        ), 2
    ) as retention_rate
FROM customer_activities
GROUP BY cohort_month, period_number
ORDER BY cohort_month, period_number;
```

#### Customer Churn Prediction
```sql
-- Churn risk segmentation
SELECT 
    CASE 
        WHEN mp.prediction_value >= 0.8 THEN 'High Risk'
        WHEN mp.prediction_value >= 0.5 THEN 'Medium Risk'
        WHEN mp.prediction_value >= 0.2 THEN 'Low Risk'
        ELSE 'Safe'
    END as churn_risk_segment,
    COUNT(*) as customer_count,
    AVG(c.lifetime_value) as avg_clv,
    SUM(c.lifetime_value) as total_at_risk_value
FROM customers c
JOIN ml_predictions mp ON c.id = mp.entity_id
WHERE mp.prediction_type = 'churn_probability'
    AND mp.predicted_at >= NOW() - INTERVAL '7 days'
GROUP BY churn_risk_segment
ORDER BY avg_clv DESC;
```

### 3. Social Media & Content Performance

#### Social Media ROI
```sql
-- Social media content performance with attribution
SELECT 
    sma.platform,
    smc.content_type,
    COUNT(DISTINCT smc.id) as content_pieces,
    SUM(smm.metric_value) FILTER (WHERE smm.metric_type = 'impressions') as total_impressions,
    SUM(smm.metric_value) FILTER (WHERE smm.metric_type = 'engagement') as total_engagement,
    COUNT(DISTINCT lc.id) as link_clicks,
    COUNT(DISTINCT o.id) as attributed_orders,
    SUM(o.total_amount) as attributed_revenue,
    ROUND(
        SUM(o.total_amount) / NULLIF(COUNT(DISTINCT smc.id), 0), 2
    ) as revenue_per_content
FROM social_media_accounts sma
JOIN social_media_content smc ON sma.id = smc.account_id
LEFT JOIN social_media_metrics smm ON smc.id = smm.content_id
LEFT JOIN branded_links bl ON smc.branded_links @> CONCAT('[{"link_id":"', bl.id, '"}]')::jsonb
LEFT JOIN link_clicks lc ON bl.id = lc.link_id
LEFT JOIN customer_touchpoints ct ON lc.customer_id = ct.customer_id
LEFT JOIN orders o ON ct.customer_id = o.customer_id 
    AND o.created_at BETWEEN ct.touchpoint_timestamp AND ct.touchpoint_timestamp + INTERVAL '7 days'
WHERE smc.published_at >= NOW() - INTERVAL '30 days'
GROUP BY sma.platform, smc.content_type
ORDER BY attributed_revenue DESC;
```

#### Content Engagement Rates
```sql
-- Engagement rate by content type and platform
SELECT 
    sma.platform,
    smc.content_type,
    AVG(
        CASE 
            WHEN smm_impressions.metric_value > 0 
            THEN (smm_engagement.metric_value::float / smm_impressions.metric_value) * 100
            ELSE 0 
        END
    ) as avg_engagement_rate,
    COUNT(DISTINCT smc.id) as content_count
FROM social_media_accounts sma
JOIN social_media_content smc ON sma.id = smc.account_id
LEFT JOIN social_media_metrics smm_impressions ON smc.id = smm_impressions.content_id 
    AND smm_impressions.metric_type = 'impressions'
LEFT JOIN social_media_metrics smm_engagement ON smc.id = smm_engagement.content_id 
    AND smm_engagement.metric_type = 'engagement'
WHERE smc.published_at >= NOW() - INTERVAL '30 days'
GROUP BY sma.platform, smc.content_type
HAVING COUNT(DISTINCT smc.id) >= 5
ORDER BY avg_engagement_rate DESC;
```

### 4. E-Commerce Platform Performance

#### Platform Revenue Comparison
```sql
-- Revenue and performance by e-commerce platform
SELECT 
    ep.platform_type,
    ep.platform_name,
    COUNT(DISTINCT o.id) as total_orders,
    SUM(o.total_amount) as total_revenue,
    AVG(o.total_amount) as avg_order_value,
    COUNT(DISTINCT o.customer_id) as unique_customers,
    SUM(o.total_amount) / COUNT(DISTINCT o.customer_id) as revenue_per_customer,
    COUNT(DISTINCT p.id) as active_products,
    SUM(o.total_amount) / COUNT(DISTINCT p.id) as revenue_per_product
FROM ecommerce_platforms ep
LEFT JOIN orders o ON ep.id = o.platform_id 
    AND o.created_at >= NOW() - INTERVAL '30 days'
LEFT JOIN products p ON ep.id = p.platform_id AND p.is_active = true
WHERE ep.is_active = true
GROUP BY ep.platform_type, ep.platform_name
ORDER BY total_revenue DESC;
```

#### Product Performance Analysis
```sql
-- Top performing products across platforms
SELECT 
    p.name,
    p.category,
    p.brand,
    ep.platform_type,
    COUNT(DISTINCT ce.id) FILTER (WHERE ce.event_type = 'product_view') as product_views,
    COUNT(DISTINCT ce.id) FILTER (WHERE ce.event_type = 'add_to_cart') as add_to_carts,
    COUNT(DISTINCT o.id) as orders,
    SUM(oli.quantity * oli.price) as revenue,
    ROUND(
        (COUNT(DISTINCT o.id)::float / 
         NULLIF(COUNT(DISTINCT ce.id) FILTER (WHERE ce.event_type = 'product_view'), 0)) * 100, 2
    ) as conversion_rate
FROM products p
JOIN ecommerce_platforms ep ON p.platform_id = ep.id
LEFT JOIN customer_events ce ON p.id = ce.product_id 
    AND ce.timestamp >= NOW() - INTERVAL '30 days'
LEFT JOIN orders o ON p.platform_id = o.platform_id 
    AND o.created_at >= NOW() - INTERVAL '30 days'
    AND o.line_items @> CONCAT('[{"product_id":"', p.external_id, '"}]')::jsonb
LEFT JOIN LATERAL jsonb_array_elements(o.line_items) AS oli(item) ON true
WHERE p.is_active = true
GROUP BY p.id, p.name, p.category, p.brand, ep.platform_type
HAVING COUNT(DISTINCT ce.id) FILTER (WHERE ce.event_type = 'product_view') >= 10
ORDER BY revenue DESC
LIMIT 50;
```

### 5. Marketing Attribution & Campaign Performance

#### Multi-Touch Attribution Analysis
```sql
-- Attribution model comparison
WITH attribution_weights AS (
    SELECT 
        customer_id,
        touchpoint_timestamp,
        channel,
        campaign,
        ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY touchpoint_timestamp) as touch_position,
        COUNT(*) OVER (PARTITION BY customer_id) as total_touches,
        CASE 
            WHEN ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY touchpoint_timestamp) = 1 
            THEN 1.0 -- First touch
            ELSE 0.0 
        END as first_touch_weight,
        CASE 
            WHEN ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY touchpoint_timestamp DESC) = 1 
            THEN 1.0 -- Last touch
            ELSE 0.0 
        END as last_touch_weight,
        1.0 / COUNT(*) OVER (PARTITION BY customer_id) as linear_weight
    FROM customer_touchpoints
    WHERE touchpoint_timestamp >= NOW() - INTERVAL '90 days'
),
conversions AS (
    SELECT 
        customer_id,
        SUM(total_amount) as conversion_value
    FROM orders
    WHERE created_at >= NOW() - INTERVAL '90 days'
    GROUP BY customer_id
)
SELECT 
    aw.channel,
    SUM(c.conversion_value * aw.first_touch_weight) as first_touch_attribution,
    SUM(c.conversion_value * aw.last_touch_weight) as last_touch_attribution,
    SUM(c.conversion_value * aw.linear_weight) as linear_attribution,
    COUNT(DISTINCT aw.customer_id) as customers_influenced
FROM attribution_weights aw
JOIN conversions c ON aw.customer_id = c.customer_id
GROUP BY aw.channel
ORDER BY linear_attribution DESC;
```

### 6. Real-Time Performance Dashboards

#### Real-Time Revenue Tracking
```sql
-- Real-time revenue metrics (last 24 hours)
SELECT 
    DATE_TRUNC('hour', created_at) as hour,
    COUNT(*) as orders,
    SUM(total_amount) as revenue,
    AVG(total_amount) as avg_order_value,
    COUNT(DISTINCT customer_id) as unique_customers
FROM orders
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', created_at)
ORDER BY hour DESC;
```

#### Live Social Media Performance
```sql
-- Real-time social media metrics (last 4 hours)
SELECT 
    sma.platform,
    smm.metric_type,
    SUM(smm.metric_value) as total_value,
    COUNT(DISTINCT smm.content_id) as content_pieces
FROM social_media_metrics smm
JOIN social_media_accounts sma ON smm.account_id = sma.id
WHERE smm.recorded_at >= NOW() - INTERVAL '4 hours'
GROUP BY sma.platform, smm.metric_type
ORDER BY sma.platform, smm.metric_type;
```

### 7. AI-Driven Insights & Predictions

#### Demand Forecasting
```sql
-- Product demand predictions for next 30 days
SELECT 
    p.name,
    p.category,
    ep.platform_type,
    mp.prediction_value as predicted_demand,
    mp.confidence_score,
    p.inventory_quantity as current_inventory,
    CASE 
        WHEN p.inventory_quantity < mp.prediction_value * 0.5 
        THEN 'Restock Needed'
        WHEN p.inventory_quantity < mp.prediction_value 
        THEN 'Monitor Closely'
        ELSE 'Sufficient Stock'
    END as inventory_status
FROM products p
JOIN ecommerce_platforms ep ON p.platform_id = ep.id
JOIN ml_predictions mp ON p.id = mp.entity_id
WHERE mp.prediction_type = 'demand_forecast_30d'
    AND mp.predicted_at >= NOW() - INTERVAL '1 day'
    AND p.is_active = true
ORDER BY mp.prediction_value DESC;
```

#### Customer Segment Performance
```sql
-- AI-driven customer segmentation performance
SELECT 
    segment_name,
    COUNT(*) as customer_count,
    AVG(lifetime_value) as avg_clv,
    AVG(total_orders) as avg_orders,
    AVG(churn_probability) as avg_churn_risk,
    SUM(lifetime_value) as total_segment_value
FROM (
    SELECT 
        c.*,
        CASE 
            WHEN mp_clv.prediction_value >= 1000 AND mp_churn.prediction_value <= 0.2 
            THEN 'VIP Champions'
            WHEN mp_clv.prediction_value >= 500 AND mp_churn.prediction_value <= 0.4 
            THEN 'Loyal Customers'
            WHEN mp_clv.prediction_value >= 200 AND mp_churn.prediction_value <= 0.6 
            THEN 'Potential Loyalists'
            WHEN mp_churn.prediction_value >= 0.7 
            THEN 'At Risk'
            ELSE 'New Customers'
        END as segment_name
    FROM customers c
    LEFT JOIN ml_predictions mp_clv ON c.id = mp_clv.entity_id 
        AND mp_clv.prediction_type = 'customer_lifetime_value'
        AND mp_clv.predicted_at >= NOW() - INTERVAL '7 days'
    LEFT JOIN ml_predictions mp_churn ON c.id = mp_churn.entity_id 
        AND mp_churn.prediction_type = 'churn_probability'
        AND mp_churn.predicted_at >= NOW() - INTERVAL '7 days'
) segmented_customers
GROUP BY segment_name
ORDER BY total_segment_value DESC;
```

## Key Performance Indicators (KPIs) Dashboard

### Executive Summary Metrics
1. **Total Revenue** (MTD, QTD, YTD)
2. **Customer Acquisition Cost (CAC)**
3. **Customer Lifetime Value (CLV)**
4. **Monthly Recurring Revenue (MRR)**
5. **Churn Rate**
6. **Net Promoter Score (NPS)**

### Marketing Performance
1. **Return on Ad Spend (ROAS)**
2. **Cost Per Acquisition (CPA)**
3. **Attribution Revenue by Channel**
4. **Social Media Engagement Rate**
5. **Email Open/Click Rates**
6. **Organic vs Paid Traffic Conversion**

### E-Commerce Operations
1. **Conversion Rate by Platform**
2. **Average Order Value (AOV)**
3. **Cart Abandonment Rate**
4. **Product Performance Index**
5. **Inventory Turnover Rate**
6. **Cross-sell/Upsell Revenue**

### Customer Experience
1. **Customer Satisfaction Score (CSAT)**
2. **First Response Time**
3. **Resolution Rate**
4. **Repeat Purchase Rate**
5. **Customer Support Ticket Volume**
6. **Product Return Rate**
