-- AI-Powered E-Commerce Analytics Platform: Core Data Models
-- Comprehensive schema for multi-platform analytics with TimescaleDB optimization

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "timescaledb";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- =====================================================
-- CORE BUSINESS ENTITIES
-- =====================================================

-- Tenants (Multi-tenant architecture)
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'starter',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- E-commerce Platforms Integration
CREATE TABLE ecommerce_platforms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    platform_type VARCHAR(50) NOT NULL, -- 'shopify', 'woocommerce', 'amazon', 'ebay'
    platform_name VARCHAR(255) NOT NULL,
    api_credentials JSONB NOT NULL, -- Encrypted credentials
    webhook_endpoints JSONB DEFAULT '{}',
    sync_settings JSONB DEFAULT '{}',
    last_sync_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, platform_type, platform_name)
);

-- Products (Unified across platforms)
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    platform_id UUID NOT NULL REFERENCES ecommerce_platforms(id) ON DELETE CASCADE,
    external_id VARCHAR(255) NOT NULL, -- Platform-specific product ID
    sku VARCHAR(255),
    name VARCHAR(500) NOT NULL,
    description TEXT,
    category VARCHAR(255),
    brand VARCHAR(255),
    price DECIMAL(12,2),
    currency VARCHAR(3) DEFAULT 'USD',
    images JSONB DEFAULT '[]',
    attributes JSONB DEFAULT '{}',
    inventory_quantity INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, platform_id, external_id)
);

-- Customers (Unified across platforms)
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255),
    phone VARCHAR(50),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    date_of_birth DATE,
    gender VARCHAR(20),
    location JSONB, -- {country, state, city, postal_code, coordinates}
    acquisition_channel VARCHAR(100),
    acquisition_date TIMESTAMPTZ,
    customer_segments JSONB DEFAULT '[]',
    lifetime_value DECIMAL(12,2) DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(12,2) DEFAULT 0,
    avg_order_value DECIMAL(12,2) DEFAULT 0,
    last_order_date TIMESTAMPTZ,
    churn_probability DECIMAL(5,4) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, email)
);

-- =====================================================
-- TIME-SERIES ANALYTICS TABLES (TimescaleDB Hypertables)
-- =====================================================

-- Customer Events (Time-series for all customer interactions)
CREATE TABLE customer_events (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    event_type VARCHAR(100) NOT NULL, -- 'page_view', 'product_view', 'add_to_cart', 'purchase', etc.
    event_source VARCHAR(50) NOT NULL, -- 'website', 'mobile_app', 'social_media', 'email'
    platform_id UUID REFERENCES ecommerce_platforms(id) ON DELETE SET NULL,
    product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    event_data JSONB DEFAULT '{}',
    revenue DECIMAL(12,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    user_agent TEXT,
    ip_address INET,
    referrer TEXT,
    utm_source VARCHAR(255),
    utm_medium VARCHAR(255),
    utm_campaign VARCHAR(255),
    utm_content VARCHAR(255),
    utm_term VARCHAR(255),
    device_type VARCHAR(50),
    browser VARCHAR(100),
    os VARCHAR(100),
    country VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, timestamp)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('customer_events', 'timestamp', chunk_time_interval => INTERVAL '1 day');

-- Orders (Time-series for order tracking)
CREATE TABLE orders (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    platform_id UUID NOT NULL REFERENCES ecommerce_platforms(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    external_order_id VARCHAR(255) NOT NULL,
    order_number VARCHAR(255),
    status VARCHAR(50) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    shipping_amount DECIMAL(12,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(100),
    shipping_address JSONB,
    billing_address JSONB,
    line_items JSONB NOT NULL, -- Array of order items
    attribution_data JSONB DEFAULT '{}', -- Attribution touchpoints
    fulfillment_status VARCHAR(50),
    financial_status VARCHAR(50),
    notes TEXT,
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (id, created_at)
);

SELECT create_hypertable('orders', 'created_at', chunk_time_interval => INTERVAL '1 day');

-- =====================================================
-- BRANDED LINK TRACKING
-- =====================================================

-- Branded Links
CREATE TABLE branded_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    campaign_id UUID,
    short_code VARCHAR(50) NOT NULL,
    original_url TEXT NOT NULL,
    branded_url VARCHAR(500) NOT NULL,
    title VARCHAR(255),
    description TEXT,
    tags JSONB DEFAULT '[]',
    utm_parameters JSONB DEFAULT '{}',
    expiry_date TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    click_count INTEGER DEFAULT 0,
    unique_click_count INTEGER DEFAULT 0,
    conversion_count INTEGER DEFAULT 0,
    revenue_generated DECIMAL(12,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, short_code)
);

-- Link Clicks (Time-series)
CREATE TABLE link_clicks (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    link_id UUID NOT NULL REFERENCES branded_links(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    country VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    device_type VARCHAR(50),
    browser VARCHAR(100),
    os VARCHAR(100),
    is_unique_click BOOLEAN DEFAULT false,
    click_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, click_timestamp)
);

SELECT create_hypertable('link_clicks', 'click_timestamp', chunk_time_interval => INTERVAL '1 hour');

-- =====================================================
-- SOCIAL MEDIA INTEGRATION
-- =====================================================

-- Social Media Accounts
CREATE TABLE social_media_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL, -- 'youtube', 'facebook', 'instagram', 'tiktok'
    account_id VARCHAR(255) NOT NULL,
    account_name VARCHAR(255),
    access_token TEXT, -- Encrypted
    refresh_token TEXT, -- Encrypted
    token_expires_at TIMESTAMPTZ,
    account_metadata JSONB DEFAULT '{}',
    sync_settings JSONB DEFAULT '{}',
    last_sync_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, platform, account_id)
);

-- Social Media Posts/Content
CREATE TABLE social_media_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES social_media_accounts(id) ON DELETE CASCADE,
    external_post_id VARCHAR(255) NOT NULL,
    content_type VARCHAR(50), -- 'post', 'story', 'video', 'reel'
    title VARCHAR(500),
    description TEXT,
    media_urls JSONB DEFAULT '[]',
    hashtags JSONB DEFAULT '[]',
    mentions JSONB DEFAULT '[]',
    branded_links JSONB DEFAULT '[]', -- Links to our branded_links table
    published_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, account_id, external_post_id)
);

-- Social Media Metrics (Time-series)
CREATE TABLE social_media_metrics (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES social_media_accounts(id) ON DELETE CASCADE,
    content_id UUID REFERENCES social_media_content(id) ON DELETE SET NULL,
    metric_type VARCHAR(100) NOT NULL, -- 'impressions', 'reach', 'engagement', 'clicks', 'shares'
    metric_value BIGINT NOT NULL,
    metric_metadata JSONB DEFAULT '{}',
    recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, recorded_at)
);

SELECT create_hypertable('social_media_metrics', 'recorded_at', chunk_time_interval => INTERVAL '1 hour');

-- =====================================================
-- ATTRIBUTION & CUSTOMER JOURNEY
-- =====================================================

-- Attribution Models Configuration
CREATE TABLE attribution_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- 'first_touch', 'last_touch', 'linear', 'time_decay', 'position_based', 'data_driven'
    configuration JSONB NOT NULL,
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Customer Journey Touchpoints (Time-series)
CREATE TABLE customer_touchpoints (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    touchpoint_type VARCHAR(100) NOT NULL, -- 'social_media', 'email', 'paid_search', 'organic_search', 'direct'
    channel VARCHAR(100) NOT NULL,
    campaign VARCHAR(255),
    content VARCHAR(255),
    medium VARCHAR(100),
    source VARCHAR(255),
    link_id UUID REFERENCES branded_links(id) ON DELETE SET NULL,
    content_id UUID REFERENCES social_media_content(id) ON DELETE SET NULL,
    touchpoint_value DECIMAL(12,2) DEFAULT 0, -- Revenue attributed to this touchpoint
    position_in_journey INTEGER,
    time_to_conversion INTERVAL,
    touchpoint_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, touchpoint_timestamp)
);

SELECT create_hypertable('customer_touchpoints', 'touchpoint_timestamp', chunk_time_interval => INTERVAL '1 hour');

-- =====================================================
-- AI/ML MODELS & PREDICTIONS
-- =====================================================

-- ML Models Registry
CREATE TABLE ml_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    model_name VARCHAR(255) NOT NULL,
    model_type VARCHAR(100) NOT NULL, -- 'churn_prediction', 'clv_prediction', 'demand_forecasting', 'recommendation'
    model_version VARCHAR(50) NOT NULL,
    model_metadata JSONB NOT NULL,
    training_data_period JSONB, -- {start_date, end_date}
    performance_metrics JSONB, -- {accuracy, precision, recall, f1_score, etc.}
    model_artifacts_path TEXT, -- Path to stored model files
    is_active BOOLEAN DEFAULT false,
    deployed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ML Predictions (Time-series)
CREATE TABLE ml_predictions (
    id UUID DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    model_id UUID NOT NULL REFERENCES ml_models(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL, -- 'customer', 'product', 'campaign'
    entity_id UUID NOT NULL,
    prediction_type VARCHAR(100) NOT NULL,
    prediction_value DECIMAL(12,4) NOT NULL,
    confidence_score DECIMAL(5,4),
    prediction_metadata JSONB DEFAULT '{}',
    predicted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (id, predicted_at)
);

SELECT create_hypertable('ml_predictions', 'predicted_at', chunk_time_interval => INTERVAL '1 day');

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Tenant-based indexes for multi-tenancy
CREATE INDEX idx_customer_events_tenant_timestamp ON customer_events (tenant_id, timestamp DESC);
CREATE INDEX idx_orders_tenant_created ON orders (tenant_id, created_at DESC);
CREATE INDEX idx_link_clicks_tenant_timestamp ON link_clicks (tenant_id, click_timestamp DESC);
CREATE INDEX idx_social_media_metrics_tenant_recorded ON social_media_metrics (tenant_id, recorded_at DESC);
CREATE INDEX idx_customer_touchpoints_tenant_timestamp ON customer_touchpoints (tenant_id, touchpoint_timestamp DESC);
CREATE INDEX idx_ml_predictions_tenant_predicted ON ml_predictions (tenant_id, predicted_at DESC);

-- Customer-based indexes
CREATE INDEX idx_customer_events_customer_timestamp ON customer_events (customer_id, timestamp DESC);
CREATE INDEX idx_orders_customer_created ON orders (customer_id, created_at DESC);
CREATE INDEX idx_customer_touchpoints_customer_timestamp ON customer_touchpoints (customer_id, touchpoint_timestamp DESC);

-- Campaign and attribution indexes
CREATE INDEX idx_customer_events_utm_campaign ON customer_events (tenant_id, utm_campaign, timestamp DESC);
CREATE INDEX idx_link_clicks_link_timestamp ON link_clicks (link_id, click_timestamp DESC);
CREATE INDEX idx_customer_touchpoints_campaign ON customer_touchpoints (tenant_id, campaign, touchpoint_timestamp DESC);

-- Product and platform indexes
CREATE INDEX idx_customer_events_product_timestamp ON customer_events (product_id, timestamp DESC);
CREATE INDEX idx_orders_platform_created ON orders (platform_id, created_at DESC);
CREATE INDEX idx_products_platform_active ON products (platform_id, is_active);

-- Social media indexes
CREATE INDEX idx_social_media_metrics_account_recorded ON social_media_metrics (account_id, recorded_at DESC);
CREATE INDEX idx_social_media_content_account_published ON social_media_content (account_id, published_at DESC);

-- ML model indexes
CREATE INDEX idx_ml_predictions_model_predicted ON ml_predictions (model_id, predicted_at DESC);
CREATE INDEX idx_ml_predictions_entity ON ml_predictions (entity_type, entity_id, predicted_at DESC);
