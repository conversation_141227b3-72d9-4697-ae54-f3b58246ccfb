# E-Commerce Platform Integration Architecture

## Overview

Comprehensive integration framework for multi-platform e-commerce analytics supporting Shopify, WooCommerce, Amazon, and eBay with real-time data synchronization and branded link tracking.

## 1. Shopify Integration Architecture

### API Integration Pattern
```typescript
// Shopify Client Configuration
interface ShopifyConfig {
  shopDomain: string;
  accessToken: string;
  apiVersion: string;
  webhookSecret: string;
  scopes: string[];
}

class ShopifyIntegration {
  private client: ShopifyAPIClient;
  private webhookHandler: WebhookHandler;
  private rateLimiter: RateLimiter;

  constructor(config: ShopifyConfig) {
    this.client = new ShopifyAPIClient(config);
    this.webhookHandler = new WebhookHandler(config.webhookSecret);
    this.rateLimiter = new RateLimiter({
      requestsPerSecond: 2, // Shopify REST API limit
      burstLimit: 40
    });
  }

  // Real-time webhook handlers
  async handleOrderCreated(order: ShopifyOrder): Promise<void> {
    const normalizedOrder = await this.normalizeOrder(order);
    await this.saveOrder(normalizedOrder);
    await this.trackCustomerJourney(order);
    await this.updateInventory(order.line_items);
  }

  async handleOrderUpdated(order: ShopifyOrder): Promise<void> {
    await this.updateOrderStatus(order);
    await this.recalculateMetrics(order.customer.id);
  }

  // Bulk data synchronization
  async syncProducts(sinceId?: number): Promise<SyncResult> {
    const products = await this.client.getProducts({ since_id: sinceId });
    const syncResults = await Promise.all(
      products.map(product => this.normalizeAndSaveProduct(product))
    );
    return this.aggregateSyncResults(syncResults);
  }

  async syncCustomers(sinceId?: number): Promise<SyncResult> {
    const customers = await this.client.getCustomers({ since_id: sinceId });
    return await this.bulkUpsertCustomers(customers);
  }

  // GraphQL for complex queries
  async getCustomerJourney(customerId: string): Promise<CustomerJourney> {
    const query = `
      query getCustomerJourney($customerId: ID!) {
        customer(id: $customerId) {
          orders(first: 100) {
            edges {
              node {
                id
                createdAt
                totalPrice
                lineItems(first: 50) {
                  edges {
                    node {
                      product {
                        id
                        title
                        vendor
                      }
                      quantity
                      originalUnitPrice
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;
    return await this.client.graphql(query, { customerId });
  }
}
```

### Webhook Configuration
```typescript
// Required Shopify webhooks for real-time analytics
const REQUIRED_WEBHOOKS = [
  'orders/create',
  'orders/updated',
  'orders/paid',
  'orders/cancelled',
  'orders/fulfilled',
  'customers/create',
  'customers/update',
  'app/uninstalled',
  'products/create',
  'products/update',
  'inventory_levels/update'
];

// Webhook endpoint handlers
app.post('/webhooks/shopify/:event', async (req, res) => {
  const event = req.params.event;
  const hmac = req.get('X-Shopify-Hmac-Sha256');
  const body = req.body;
  
  if (!verifyWebhook(body, hmac)) {
    return res.status(401).send('Unauthorized');
  }

  await processWebhookEvent(event, body);
  res.status(200).send('OK');
});
```

## 2. WooCommerce Integration Architecture

### REST API Integration
```typescript
interface WooCommerceConfig {
  storeUrl: string;
  consumerKey: string;
  consumerSecret: string;
  version: string;
  timeout: number;
}

class WooCommerceIntegration {
  private client: WooCommerceAPIClient;
  private webhookProcessor: WebhookProcessor;

  constructor(config: WooCommerceConfig) {
    this.client = new WooCommerceAPIClient(config);
    this.webhookProcessor = new WebhookProcessor();
  }

  // OAuth 1.0a authentication
  private generateAuthHeader(method: string, url: string): string {
    const oauth = {
      consumer_key: this.config.consumerKey,
      consumer_secret: this.config.consumerSecret,
      signature_method: 'HMAC-SHA256',
      timestamp: Math.floor(Date.now() / 1000),
      nonce: this.generateNonce(),
      version: '1.0'
    };
    
    return this.createOAuthSignature(method, url, oauth);
  }

  // Batch synchronization with pagination
  async syncOrders(params: SyncParams = {}): Promise<SyncResult> {
    let page = 1;
    let hasMore = true;
    const allOrders: WooCommerceOrder[] = [];

    while (hasMore) {
      const response = await this.client.get('/orders', {
        page,
        per_page: 100,
        after: params.after,
        status: 'any',
        orderby: 'date',
        order: 'desc'
      });

      allOrders.push(...response.data);
      
      // Check if there are more pages
      hasMore = response.headers['x-wp-totalpages'] > page;
      page++;
      
      // Rate limiting
      await this.delay(100);
    }

    return await this.processOrdersBatch(allOrders);
  }

  // Product catalog synchronization
  async syncProducts(): Promise<SyncResult> {
    const products = await this.getAllProducts();
    const variations = await this.getAllProductVariations();
    
    return await this.processProductCatalog(products, variations);
  }

  // Customer data synchronization
  async syncCustomers(): Promise<SyncResult> {
    const customers = await this.getAllCustomers();
    return await this.processCustomerData(customers);
  }

  // Webhook handling for real-time updates
  async handleWebhook(event: string, data: any): Promise<void> {
    switch (event) {
      case 'order.created':
        await this.processNewOrder(data);
        break;
      case 'order.updated':
        await this.updateOrder(data);
        break;
      case 'customer.created':
        await this.processNewCustomer(data);
        break;
      case 'product.updated':
        await this.updateProduct(data);
        break;
    }
  }
}
```

## 3. Amazon SP-API Integration

### Authentication & API Client
```typescript
interface AmazonConfig {
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  sellerId: string;
  marketplace: string;
  region: string;
}

class AmazonSPAPIIntegration {
  private authClient: LWAAuthClient;
  private apiClient: SPAPIClient;
  private rateLimiter: AmazonRateLimiter;

  constructor(config: AmazonConfig) {
    this.authClient = new LWAAuthClient(config);
    this.apiClient = new SPAPIClient(config);
    this.rateLimiter = new AmazonRateLimiter();
  }

  // LWA (Login with Amazon) token management
  async getAccessToken(): Promise<string> {
    const tokenResponse = await this.authClient.getAccessToken({
      grant_type: 'refresh_token',
      refresh_token: this.config.refreshToken,
      client_id: this.config.clientId,
      client_secret: this.config.clientSecret
    });
    
    return tokenResponse.access_token;
  }

  // Orders API integration
  async syncOrders(createdAfter: Date): Promise<SyncResult> {
    await this.rateLimiter.waitForCapacity('orders');
    
    const orders = await this.apiClient.orders.getOrders({
      MarketplaceIds: [this.config.marketplace],
      CreatedAfter: createdAfter.toISOString(),
      OrderStatuses: ['Unshipped', 'PartiallyShipped', 'Shipped', 'Canceled']
    });

    const orderDetails = await Promise.all(
      orders.Orders.map(order => this.getOrderDetails(order.AmazonOrderId))
    );

    return await this.processAmazonOrders(orderDetails);
  }

  // Product catalog via Catalog Items API
  async syncProducts(): Promise<SyncResult> {
    const products = await this.apiClient.catalogItems.searchCatalogItems({
      marketplaceIds: [this.config.marketplace],
      keywords: '*', // Get all products
      includedData: ['attributes', 'images', 'productTypes', 'salesRanks']
    });

    return await this.processAmazonProducts(products.items);
  }

  // Inventory management
  async syncInventory(): Promise<SyncResult> {
    const inventory = await this.apiClient.fbaInventory.getInventorySummaries({
      granularityType: 'Marketplace',
      granularityId: this.config.marketplace,
      marketplaceIds: [this.config.marketplace]
    });

    return await this.processInventoryData(inventory.inventorySummaries);
  }

  // Reports API for analytics data
  async getBusinessReport(reportType: string, dataStartTime: Date): Promise<Report> {
    const reportRequest = await this.apiClient.reports.createReport({
      reportType,
      dataStartTime: dataStartTime.toISOString(),
      marketplaceIds: [this.config.marketplace]
    });

    // Poll for report completion
    let report = await this.waitForReportCompletion(reportRequest.reportId);
    
    if (report.reportDocumentId) {
      const reportData = await this.downloadReport(report.reportDocumentId);
      return this.parseReportData(reportData, reportType);
    }
  }
}
```

## 4. eBay API Integration

### Trading API Integration
```typescript
interface eBayConfig {
  appId: string;
  devId: string;
  certId: string;
  token: string;
  siteId: number;
  environment: 'sandbox' | 'production';
}

class eBayIntegration {
  private apiClient: eBayAPIClient;
  private xmlParser: XMLParser;

  constructor(config: eBayConfig) {
    this.apiClient = new eBayAPIClient(config);
    this.xmlParser = new XMLParser();
  }

  // Get active listings
  async syncListings(): Promise<SyncResult> {
    const request = {
      RequesterCredentials: { eBayAuthToken: this.config.token },
      Pagination: { EntriesPerPage: 200, PageNumber: 1 },
      GranularityLevel: 'Fine',
      IncludeWatchCount: true
    };

    const listings = await this.apiClient.call('GetMyeBaySelling', request);
    return await this.processeBayListings(listings.ActiveList.ItemArray.Item);
  }

  // Get completed orders
  async syncOrders(daysBack: number = 30): Promise<SyncResult> {
    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - (daysBack * 24 * 60 * 60 * 1000));

    const request = {
      RequesterCredentials: { eBayAuthToken: this.config.token },
      CreateTimeFrom: startTime.toISOString(),
      CreateTimeTo: endTime.toISOString(),
      OrderRole: 'Seller',
      OrderStatus: 'Completed',
      Pagination: { EntriesPerPage: 100, PageNumber: 1 }
    };

    const orders = await this.apiClient.call('GetOrders', request);
    return await this.processeBayOrders(orders.OrderArray.Order);
  }

  // Item specifics and analytics
  async getItemAnalytics(itemId: string): Promise<ItemAnalytics> {
    const request = {
      RequesterCredentials: { eBayAuthToken: this.config.token },
      ItemID: itemId,
      IncludeItemSpecifics: true,
      IncludeWatchCount: true
    };

    const item = await this.apiClient.call('GetItem', request);
    return this.extractItemAnalytics(item.Item);
  }
}
```

## 5. Data Synchronization Strategy

### Unified Data Pipeline
```typescript
class DataSynchronizationEngine {
  private platforms: Map<string, PlatformIntegration>;
  private eventBus: EventBus;
  private dataProcessor: DataProcessor;

  constructor() {
    this.platforms = new Map();
    this.eventBus = new EventBus();
    this.dataProcessor = new DataProcessor();
  }

  // Register platform integrations
  registerPlatform(type: string, integration: PlatformIntegration): void {
    this.platforms.set(type, integration);
  }

  // Orchestrated synchronization
  async syncAllPlatforms(tenantId: string): Promise<SyncReport> {
    const syncTasks = Array.from(this.platforms.entries()).map(
      ([type, integration]) => this.syncPlatform(tenantId, type, integration)
    );

    const results = await Promise.allSettled(syncTasks);
    return this.generateSyncReport(results);
  }

  // Real-time event processing
  async processRealTimeEvent(event: PlatformEvent): Promise<void> {
    // Normalize event data
    const normalizedEvent = await this.dataProcessor.normalize(event);
    
    // Update customer journey
    await this.updateCustomerJourney(normalizedEvent);
    
    // Trigger ML predictions
    await this.triggerMLPredictions(normalizedEvent);
    
    // Update real-time dashboards
    await this.updateDashboards(normalizedEvent);
    
    // Send notifications if needed
    await this.checkAlerts(normalizedEvent);
  }

  // Incremental synchronization
  async incrementalSync(tenantId: string, platform: string): Promise<SyncResult> {
    const lastSync = await this.getLastSyncTimestamp(tenantId, platform);
    const integration = this.platforms.get(platform);
    
    if (!integration) {
      throw new Error(`Platform ${platform} not registered`);
    }

    return await integration.syncSince(lastSync);
  }
}
```

## 6. Branded Link Tracking System

### Link Generation & Tracking
```typescript
interface BrandedLinkConfig {
  domain: string;
  customDomains: string[];
  trackingParameters: string[];
  redirectDelay: number;
}

class BrandedLinkTracker {
  private linkGenerator: LinkGenerator;
  private clickTracker: ClickTracker;
  private analyticsEngine: AnalyticsEngine;

  constructor(config: BrandedLinkConfig) {
    this.linkGenerator = new LinkGenerator(config);
    this.clickTracker = new ClickTracker();
    this.analyticsEngine = new AnalyticsEngine();
  }

  // Generate branded links with UTM parameters
  async createBrandedLink(request: LinkCreationRequest): Promise<BrandedLink> {
    const shortCode = await this.linkGenerator.generateShortCode();
    const utmParameters = this.buildUTMParameters(request);
    
    const brandedLink: BrandedLink = {
      id: generateUUID(),
      tenantId: request.tenantId,
      shortCode,
      originalUrl: this.appendUTMParameters(request.originalUrl, utmParameters),
      brandedUrl: `https://${request.domain}/${shortCode}`,
      title: request.title,
      campaign: request.campaign,
      utmParameters,
      createdAt: new Date()
    };

    await this.saveBrandedLink(brandedLink);
    return brandedLink;
  }

  // High-performance click tracking
  async trackClick(shortCode: string, request: ClickRequest): Promise<ClickResponse> {
    const startTime = performance.now();
    
    // Get link details (cached)
    const link = await this.getLinkByShortCode(shortCode);
    if (!link || !link.isActive) {
      throw new Error('Link not found or inactive');
    }

    // Extract visitor information
    const visitorInfo = await this.extractVisitorInfo(request);
    
    // Record click asynchronously
    this.recordClickAsync(link, visitorInfo, request);
    
    // Return redirect response quickly
    const processingTime = performance.now() - startTime;
    
    return {
      redirectUrl: link.originalUrl,
      processingTime,
      clickId: generateUUID()
    };
  }

  // Analytics and attribution
  async getClickAnalytics(linkId: string, timeRange: TimeRange): Promise<ClickAnalytics> {
    const clicks = await this.getClicksForLink(linkId, timeRange);
    
    return {
      totalClicks: clicks.length,
      uniqueClicks: this.countUniqueClicks(clicks),
      clicksByCountry: this.groupClicksByCountry(clicks),
      clicksByDevice: this.groupClicksByDevice(clicks),
      clicksByHour: this.groupClicksByHour(clicks),
      conversionRate: await this.calculateConversionRate(linkId, timeRange),
      revenueAttribution: await this.calculateRevenueAttribution(linkId, timeRange)
    };
  }

  // Social media integration
  async createSocialMediaLinks(campaign: SocialMediaCampaign): Promise<BrandedLink[]> {
    const links = await Promise.all(
      campaign.platforms.map(platform => 
        this.createBrandedLink({
          tenantId: campaign.tenantId,
          originalUrl: campaign.landingUrl,
          domain: campaign.brandedDomain,
          title: `${campaign.title} - ${platform}`,
          campaign: campaign.name,
          source: platform,
          medium: 'social',
          content: campaign.content
        })
      )
    );

    // Auto-schedule social media posts
    await this.scheduleSocialMediaPosts(campaign, links);
    
    return links;
  }
}
```

## 7. Error Handling & Resilience

### Retry Logic & Circuit Breaker
```typescript
class IntegrationResilience {
  private circuitBreakers: Map<string, CircuitBreaker>;
  private retryPolicies: Map<string, RetryPolicy>;

  constructor() {
    this.circuitBreakers = new Map();
    this.retryPolicies = new Map();
  }

  // Platform-specific retry policies
  setupRetryPolicies(): void {
    this.retryPolicies.set('shopify', {
      maxRetries: 3,
      backoffStrategy: 'exponential',
      baseDelay: 1000,
      maxDelay: 30000,
      retryableErrors: ['RATE_LIMITED', 'TIMEOUT', 'SERVER_ERROR']
    });

    this.retryPolicies.set('amazon', {
      maxRetries: 5,
      backoffStrategy: 'linear',
      baseDelay: 2000,
      maxDelay: 60000,
      retryableErrors: ['THROTTLED', 'QUOTA_EXCEEDED', 'TIMEOUT']
    });
  }

  // Circuit breaker for platform health
  async executeWithCircuitBreaker<T>(
    platform: string, 
    operation: () => Promise<T>
  ): Promise<T> {
    const circuitBreaker = this.getCircuitBreaker(platform);
    
    if (circuitBreaker.isOpen()) {
      throw new Error(`Circuit breaker open for ${platform}`);
    }

    try {
      const result = await operation();
      circuitBreaker.recordSuccess();
      return result;
    } catch (error) {
      circuitBreaker.recordFailure();
      throw error;
    }
  }
}
```

This integration architecture provides:

1. **Unified API patterns** across all e-commerce platforms
2. **Real-time webhook processing** for immediate data updates
3. **Efficient bulk synchronization** with rate limiting and pagination
4. **Branded link tracking** with sub-millisecond redirect performance
5. **Resilient error handling** with circuit breakers and retry logic
6. **Scalable data pipeline** supporting multi-tenant architecture

The next section will cover social media and marketing data integration.
