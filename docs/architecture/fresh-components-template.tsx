// Fresh Frontend Components Template for E-Commerce Analytics Platform
// Complete implementation examples for Islands architecture

import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { JSX } from "preact";

// =====================================================
// CORE DASHBOARD COMPONENTS
// =====================================================

// Main Analytics Dashboard Island
export default function AnalyticsDashboard() {
  const timeRange = useSignal("30d");
  const dashboardData = useSignal<DashboardData | null>(null);
  const loading = useSignal(false);
  const error = useSignal<string | null>(null);

  const fetchDashboardData = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch(`/api/analytics/dashboard?range=${timeRange.value}`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Tenant-ID': getTenantId(),
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      dashboardData.value = await response.json();
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An error occurred';
    } finally {
      loading.value = false;
    }
  };

  useEffect(() => {
    fetchDashboardData();
    
    // Set up real-time updates via SSE
    const eventSource = new EventSource(`/api/analytics/stream?range=${timeRange.value}`);
    
    eventSource.onmessage = (event) => {
      const update = JSON.parse(event.data);
      dashboardData.value = { ...dashboardData.value, ...update };
    };

    eventSource.onerror = () => {
      console.error('SSE connection error');
    };

    return () => {
      eventSource.close();
    };
  }, [timeRange.value]);

  if (loading.value) {
    return <DashboardSkeleton />;
  }

  if (error.value) {
    return <ErrorDisplay error={error.value} onRetry={fetchDashboardData} />;
  }

  return (
    <div class="analytics-dashboard min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardHeader 
        timeRange={timeRange.value}
        onTimeRangeChange={(range) => timeRange.value = range}
      />
      
      <div class="container mx-auto px-4 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="Total Revenue"
            value={formatCurrency(dashboardData.value?.totalRevenue || 0)}
            change={dashboardData.value?.revenueChange || 0}
            icon="💰"
          />
          <MetricCard
            title="Customers"
            value={formatNumber(dashboardData.value?.totalCustomers || 0)}
            change={dashboardData.value?.customerChange || 0}
            icon="👥"
          />
          <MetricCard
            title="Conversion Rate"
            value={`${(dashboardData.value?.conversionRate || 0).toFixed(2)}%`}
            change={dashboardData.value?.conversionChange || 0}
            icon="📈"
          />
          <MetricCard
            title="AOV"
            value={formatCurrency(dashboardData.value?.averageOrderValue || 0)}
            change={dashboardData.value?.aovChange || 0}
            icon="🛒"
          />
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
          <RevenueChart data={dashboardData.value?.revenueChart} />
          <CustomerAcquisitionChart data={dashboardData.value?.acquisitionChart} />
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
          <TopProducts data={dashboardData.value?.topProducts} />
          <CustomerSegments data={dashboardData.value?.customerSegments} />
          <RecentActivity data={dashboardData.value?.recentActivity} />
        </div>
      </div>
    </div>
  );
}

// =====================================================
// SOCIAL MEDIA ANALYTICS COMPONENTS
// =====================================================

export function SocialMediaDashboard() {
  const selectedPlatform = useSignal<string>("all");
  const socialData = useSignal<SocialMediaData | null>(null);
  const contentPerformance = useSignal<ContentPerformance[]>([]);

  const platforms = useComputed(() => [
    { id: "all", name: "All Platforms", icon: "🌐" },
    { id: "youtube", name: "YouTube", icon: "📺" },
    { id: "facebook", name: "Facebook", icon: "📘" },
    { id: "instagram", name: "Instagram", icon: "📷" },
    { id: "tiktok", name: "TikTok", icon: "🎵" },
  ]);

  useEffect(() => {
    const fetchSocialData = async () => {
      const response = await fetch(`/api/social-media/analytics?platform=${selectedPlatform.value}`);
      socialData.value = await response.json();
    };

    const fetchContentPerformance = async () => {
      const response = await fetch(`/api/social-media/content?platform=${selectedPlatform.value}`);
      contentPerformance.value = await response.json();
    };

    Promise.all([fetchSocialData(), fetchContentPerformance()]);
  }, [selectedPlatform.value]);

  return (
    <div class="social-media-dashboard">
      <div class="flex flex-wrap gap-2 mb-6">
        {platforms.value.map((platform) => (
          <button
            key={platform.id}
            class={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedPlatform.value === platform.id
                ? "bg-blue-600 text-white"
                : "bg-white text-gray-700 hover:bg-gray-50 border"
            }`}
            onClick={() => selectedPlatform.value = platform.id}
          >
            <span class="mr-2">{platform.icon}</span>
            {platform.name}
          </button>
        ))}
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <SocialMetricCard
          title="Total Reach"
          value={formatNumber(socialData.value?.totalReach || 0)}
          platform={selectedPlatform.value}
        />
        <SocialMetricCard
          title="Engagement Rate"
          value={`${(socialData.value?.engagementRate || 0).toFixed(2)}%`}
          platform={selectedPlatform.value}
        />
        <SocialMetricCard
          title="Attributed Revenue"
          value={formatCurrency(socialData.value?.attributedRevenue || 0)}
          platform={selectedPlatform.value}
        />
        <SocialMetricCard
          title="ROAS"
          value={`${(socialData.value?.roas || 0).toFixed(2)}x`}
          platform={selectedPlatform.value}
        />
      </div>

      <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
        <EngagementTrendsChart data={socialData.value?.engagementTrends} />
        <AttributionBreakdown data={socialData.value?.attributionData} />
      </div>

      <ContentPerformanceTable data={contentPerformance.value} />
    </div>
  );
}

// =====================================================
// ML INSIGHTS COMPONENTS
// =====================================================

export function MLInsightsDashboard() {
  const selectedModel = useSignal<string>("clv");
  const insights = useSignal<MLInsights | null>(null);
  const predictions = useSignal<Prediction[]>([]);

  const models = useComputed(() => [
    { id: "clv", name: "Customer Lifetime Value", icon: "💎" },
    { id: "churn", name: "Churn Prediction", icon: "⚠️" },
    { id: "demand", name: "Demand Forecasting", icon: "📊" },
    { id: "recommendation", name: "Product Recommendations", icon: "🎯" },
  ]);

  useEffect(() => {
    const fetchMLInsights = async () => {
      const response = await fetch(`/api/ml/insights?model=${selectedModel.value}`);
      insights.value = await response.json();
    };

    const fetchPredictions = async () => {
      const response = await fetch(`/api/ml/predictions?model=${selectedModel.value}&limit=100`);
      predictions.value = await response.json();
    };

    Promise.all([fetchMLInsights(), fetchPredictions()]);
  }, [selectedModel.value]);

  return (
    <div class="ml-insights-dashboard">
      <div class="flex flex-wrap gap-2 mb-6">
        {models.value.map((model) => (
          <button
            key={model.id}
            class={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedModel.value === model.id
                ? "bg-purple-600 text-white"
                : "bg-white text-gray-700 hover:bg-gray-50 border"
            }`}
            onClick={() => selectedModel.value = model.id}
          >
            <span class="mr-2">{model.icon}</span>
            {model.name}
          </button>
        ))}
      </div>

      {selectedModel.value === "clv" && (
        <CLVInsights data={insights.value?.clv} predictions={predictions.value} />
      )}

      {selectedModel.value === "churn" && (
        <ChurnInsights data={insights.value?.churn} predictions={predictions.value} />
      )}

      {selectedModel.value === "demand" && (
        <DemandForecastInsights data={insights.value?.demand} predictions={predictions.value} />
      )}

      {selectedModel.value === "recommendation" && (
        <RecommendationInsights data={insights.value?.recommendations} />
      )}

      <ModelPerformanceMetrics model={selectedModel.value} />
    </div>
  );
}

// =====================================================
// REUSABLE UI COMPONENTS
// =====================================================

interface MetricCardProps {
  title: string;
  value: string;
  change?: number;
  icon?: string;
}

function MetricCard({ title, value, change, icon }: MetricCardProps) {
  const changeColor = change && change > 0 ? "text-green-600" : "text-red-600";
  const changeIcon = change && change > 0 ? "↗️" : "↘️";

  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">{value}</p>
          {change !== undefined && (
            <p class={`text-sm mt-1 ${changeColor}`}>
              <span class="mr-1">{changeIcon}</span>
              {Math.abs(change).toFixed(1)}%
            </p>
          )}
        </div>
        {icon && (
          <div class="text-3xl opacity-80">{icon}</div>
        )}
      </div>
    </div>
  );
}

function DashboardSkeleton() {
  return (
    <div class="animate-pulse">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} class="bg-gray-200 dark:bg-gray-700 rounded-lg h-24"></div>
        ))}
      </div>
      <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
        <div class="bg-gray-200 dark:bg-gray-700 rounded-lg h-64"></div>
        <div class="bg-gray-200 dark:bg-gray-700 rounded-lg h-64"></div>
      </div>
    </div>
  );
}

function ErrorDisplay({ error, onRetry }: { error: string; onRetry: () => void }) {
  return (
    <div class="flex flex-col items-center justify-center min-h-64 text-center">
      <div class="text-red-500 text-6xl mb-4">⚠️</div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        Something went wrong
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
      <button
        onClick={onRetry}
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Try Again
      </button>
    </div>
  );
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num);
}

function getTenantId(): string {
  // Extract tenant ID from context or localStorage
  return localStorage.getItem('tenantId') || '';
}

// =====================================================
// TYPE DEFINITIONS
// =====================================================

interface DashboardData {
  totalRevenue: number;
  revenueChange: number;
  totalCustomers: number;
  customerChange: number;
  conversionRate: number;
  conversionChange: number;
  averageOrderValue: number;
  aovChange: number;
  revenueChart: ChartData[];
  acquisitionChart: ChartData[];
  topProducts: Product[];
  customerSegments: CustomerSegment[];
  recentActivity: Activity[];
}

interface SocialMediaData {
  totalReach: number;
  engagementRate: number;
  attributedRevenue: number;
  roas: number;
  engagementTrends: ChartData[];
  attributionData: AttributionData[];
}

interface MLInsights {
  clv: CLVData;
  churn: ChurnData;
  demand: DemandData;
  recommendations: RecommendationData;
}

interface ChartData {
  date: string;
  value: number;
  label?: string;
}

interface Product {
  id: string;
  name: string;
  revenue: number;
  orders: number;
  conversionRate: number;
}

interface CustomerSegment {
  name: string;
  count: number;
  revenue: number;
  percentage: number;
}

interface Activity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
  user?: string;
}
