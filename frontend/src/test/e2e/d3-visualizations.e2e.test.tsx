import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import * as d3 from 'd3';
import D3AnalyticsDashboard from '../../components/visualization/D3/D3AnalyticsDashboard';
import D3RealTimeMetrics from '../../components/visualization/D3/D3RealTimeMetrics';
import D3CustomerJourney from '../../components/visualization/D3/D3CustomerJourney';
import D3CohortAnalysis from '../../components/visualization/D3/D3CohortAnalysis';

// Mock D3 to avoid complex SVG rendering in tests
vi.mock('d3', async () => {
  const actual = await vi.importActual('d3');
  return {
    ...actual,
    select: vi.fn().mockReturnValue({
      selectAll: vi.fn().mockReturnValue({
        remove: vi.fn(),
        data: vi.fn().mockReturnThis(),
        enter: vi.fn().mockReturnThis(),
        append: vi.fn().mockReturnThis(),
        attr: vi.fn().mockReturnThis(),
        style: vi.fn().mockReturnThis(),
        text: vi.fn().mockReturnThis(),
        on: vi.fn().mockReturnThis(),
        transition: vi.fn().mockReturnThis(),
        duration: vi.fn().mockReturnThis(),
        ease: vi.fn().mockReturnThis(),
      }),
      append: vi.fn().mockReturnThis(),
      attr: vi.fn().mockReturnThis(),
      style: vi.fn().mockReturnThis(),
      text: vi.fn().mockReturnThis(),
      on: vi.fn().mockReturnThis(),
      datum: vi.fn().mockReturnThis(),
      node: vi.fn().mockReturnValue({ getBBox: () => ({ width: 100, height: 50 }) }),
    }),
  };
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

describe('D3 Visualizations E2E Tests', () => {
  const mockChartData = [
    {
      type: 'line' as const,
      title: 'Click Trends',
      data: [
        { date: '2024-01-01', value: 100, label: 'Day 1' },
        { date: '2024-01-02', value: 150, label: 'Day 2' },
        { date: '2024-01-03', value: 120, label: 'Day 3' },
        { date: '2024-01-04', value: 180, label: 'Day 4' },
        { date: '2024-01-05', value: 200, label: 'Day 5' }
      ],
      width: 400,
      height: 300
    },
    {
      type: 'bar' as const,
      title: 'Revenue by Channel',
      data: [
        { channel: 'Email', revenue: 5000, percentage: 35 },
        { channel: 'Social', revenue: 3000, percentage: 21 },
        { channel: 'Direct', revenue: 7000, percentage: 44 }
      ],
      width: 400,
      height: 300
    },
    {
      type: 'pie' as const,
      title: 'Traffic Sources',
      data: [
        { source: 'Organic', value: 45, color: '#059669' },
        { source: 'Paid', value: 30, color: '#2563eb' },
        { source: 'Social', value: 15, color: '#dc2626' },
        { source: 'Direct', value: 10, color: '#7c3aed' }
      ],
      width: 300,
      height: 300
    }
  ];

  const mockRealTimeStreams = [
    {
      id: 'active-users',
      name: 'Active Users',
      data: Array.from({ length: 20 }, (_, i) => ({
        timestamp: new Date(Date.now() - (19 - i) * 60000),
        value: Math.floor(Math.random() * 100) + 50,
        metric: 'users',
        change: Math.random() * 20 - 10
      })),
      color: '#059669',
      unit: 'users',
      format: 'number' as const
    },
    {
      id: 'revenue',
      name: 'Revenue',
      data: Array.from({ length: 20 }, (_, i) => ({
        timestamp: new Date(Date.now() - (19 - i) * 60000),
        value: Math.floor(Math.random() * 1000) + 500,
        metric: 'revenue',
        change: Math.random() * 100 - 50
      })),
      color: '#2563eb',
      unit: 'USD',
      format: 'currency' as const
    }
  ];

  const mockJourneyData = {
    nodes: [
      { id: 'landing', name: 'Landing Page', type: 'page', users: 1000, x: 100, y: 100 },
      { id: 'product', name: 'Product Page', type: 'page', users: 800, x: 300, y: 100 },
      { id: 'cart', name: 'Shopping Cart', type: 'action', users: 300, x: 500, y: 100 },
      { id: 'checkout', name: 'Checkout', type: 'action', users: 150, x: 700, y: 100 },
      { id: 'purchase', name: 'Purchase', type: 'conversion', users: 100, x: 900, y: 100 }
    ],
    links: [
      { source: 'landing', target: 'product', value: 800 },
      { source: 'product', target: 'cart', value: 300 },
      { source: 'cart', target: 'checkout', value: 150 },
      { source: 'checkout', target: 'purchase', value: 100 }
    ]
  };

  const mockCohortData = [
    { cohort: '2024-01', period: 0, users: 100, retention: 100 },
    { cohort: '2024-01', period: 1, users: 85, retention: 85 },
    { cohort: '2024-01', period: 2, users: 70, retention: 70 },
    { cohort: '2024-02', period: 0, users: 120, retention: 100 },
    { cohort: '2024-02', period: 1, users: 95, retention: 79 },
    { cohort: '2024-03', period: 0, users: 150, retention: 100 }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('D3 Analytics Dashboard', () => {
    it('should render multiple chart types correctly', async () => {
      const onDataInteraction = vi.fn();
      
      render(
        <D3AnalyticsDashboard
          charts={mockChartData}
          realTimeData={true}
          interactive={true}
          onDataInteraction={onDataInteraction}
        />
      );

      // Check if dashboard renders
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
      
      // Check if all chart types are rendered
      expect(screen.getByText('Click Trends')).toBeInTheDocument();
      expect(screen.getByText('Revenue by Channel')).toBeInTheDocument();
      expect(screen.getByText('Traffic Sources')).toBeInTheDocument();
      
      // Check for real-time and interactive badges
      expect(screen.getByText('Real-time')).toBeInTheDocument();
      expect(screen.getByText('Interactive')).toBeInTheDocument();
    });

    it('should handle chart interactions', async () => {
      const user = userEvent.setup();
      const onDataInteraction = vi.fn();
      
      render(
        <D3AnalyticsDashboard
          charts={mockChartData}
          realTimeData={true}
          interactive={true}
          onDataInteraction={onDataInteraction}
        />
      );

      // Find and click on a chart element
      const chartTitle = screen.getByText('Click Trends');
      await user.click(chartTitle);

      // Interaction should be handled
      expect(chartTitle).toBeInTheDocument();
    });

    it('should handle empty data gracefully', async () => {
      render(
        <D3AnalyticsDashboard
          charts={[]}
          realTimeData={false}
          interactive={false}
          onDataInteraction={vi.fn()}
        />
      );

      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
      expect(screen.queryByText('Real-time')).not.toBeInTheDocument();
      expect(screen.queryByText('Interactive')).not.toBeInTheDocument();
    });

    it('should handle chart data updates', async () => {
      const onDataInteraction = vi.fn();
      const { rerender } = render(
        <D3AnalyticsDashboard
          charts={mockChartData}
          realTimeData={true}
          interactive={true}
          onDataInteraction={onDataInteraction}
        />
      );

      // Initial render
      expect(screen.getByText('Click Trends')).toBeInTheDocument();

      // Update with new data
      const updatedChartData = [
        {
          ...mockChartData[0],
          title: 'Updated Click Trends',
          data: [...mockChartData[0].data, { date: '2024-01-06', value: 250, label: 'Day 6' }]
        }
      ];

      rerender(
        <D3AnalyticsDashboard
          charts={updatedChartData}
          realTimeData={true}
          interactive={true}
          onDataInteraction={onDataInteraction}
        />
      );

      expect(screen.getByText('Updated Click Trends')).toBeInTheDocument();
    });
  });

  describe('D3 Real-time Metrics', () => {
    it('should render real-time metrics with live updates', async () => {
      const onMetricClick = vi.fn();
      
      render(
        <D3RealTimeMetrics
          streams={mockRealTimeStreams}
          updateInterval={1000}
          maxDataPoints={50}
          onMetricClick={onMetricClick}
        />
      );

      // Check if metrics are rendered
      expect(screen.getByText('Real-time Analytics')).toBeInTheDocument();
      expect(screen.getByText('Active Users')).toBeInTheDocument();
      expect(screen.getByText('Revenue')).toBeInTheDocument();
    });

    it('should handle metric selection and detailed view', async () => {
      const user = userEvent.setup();
      const onMetricClick = vi.fn();
      
      render(
        <D3RealTimeMetrics
          streams={mockRealTimeStreams}
          updateInterval={1000}
          maxDataPoints={50}
          onMetricClick={onMetricClick}
        />
      );

      // Click on a metric to view details
      const activeUsersMetric = screen.getByText('Active Users');
      await user.click(activeUsersMetric);

      // Should handle metric selection
      expect(activeUsersMetric).toBeInTheDocument();
    });

    it('should toggle live updates', async () => {
      const user = userEvent.setup();
      
      render(
        <D3RealTimeMetrics
          streams={mockRealTimeStreams}
          updateInterval={1000}
          maxDataPoints={50}
          onMetricClick={vi.fn()}
        />
      );

      // Find and click the live toggle button
      const liveButton = screen.getByText('Live');
      await user.click(liveButton);

      // Button should still be present (may change state)
      expect(liveButton).toBeInTheDocument();
    });

    it('should handle connection status changes', async () => {
      const { rerender } = render(
        <D3RealTimeMetrics
          streams={mockRealTimeStreams}
          updateInterval={1000}
          maxDataPoints={50}
          onMetricClick={vi.fn()}
        />
      );

      // Initial render with data
      expect(screen.getByText('Active Users')).toBeInTheDocument();

      // Simulate connection loss (empty streams)
      rerender(
        <D3RealTimeMetrics
          streams={[]}
          updateInterval={1000}
          maxDataPoints={50}
          onMetricClick={vi.fn()}
        />
      );

      // Should handle empty state
      expect(screen.getByText('Real-time Analytics')).toBeInTheDocument();
    });
  });

  describe('D3 Customer Journey', () => {
    it('should render customer journey visualization', async () => {
      const onNodeClick = vi.fn();
      
      render(
        <D3CustomerJourney
          data={mockJourneyData}
          width={800}
          height={400}
          onNodeClick={onNodeClick}
        />
      );

      // Check if journey nodes are rendered
      expect(screen.getByText('Landing Page')).toBeInTheDocument();
      expect(screen.getByText('Product Page')).toBeInTheDocument();
      expect(screen.getByText('Purchase')).toBeInTheDocument();
    });

    it('should handle node interactions', async () => {
      const user = userEvent.setup();
      const onNodeClick = vi.fn();
      
      render(
        <D3CustomerJourney
          data={mockJourneyData}
          width={800}
          height={400}
          onNodeClick={onNodeClick}
        />
      );

      // Click on a journey node
      const landingNode = screen.getByText('Landing Page');
      await user.click(landingNode);

      // Should handle node click
      expect(onNodeClick).toHaveBeenCalled();
    });

    it('should display conversion rates correctly', async () => {
      render(
        <D3CustomerJourney
          data={mockJourneyData}
          width={800}
          height={400}
          onNodeClick={vi.fn()}
        />
      );

      // Check if user counts are displayed
      expect(screen.getByText('1,000')).toBeInTheDocument(); // Landing page users
      expect(screen.getByText('100')).toBeInTheDocument(); // Purchase users
    });

    it('should handle responsive resizing', async () => {
      const { rerender } = render(
        <D3CustomerJourney
          data={mockJourneyData}
          width={800}
          height={400}
          onNodeClick={vi.fn()}
        />
      );

      // Resize to smaller dimensions
      rerender(
        <D3CustomerJourney
          data={mockJourneyData}
          width={400}
          height={200}
          onNodeClick={vi.fn()}
        />
      );

      // Should still render correctly
      expect(screen.getByText('Landing Page')).toBeInTheDocument();
    });
  });

  describe('D3 Cohort Analysis', () => {
    it('should render cohort heatmap', async () => {
      const onCellClick = vi.fn();
      
      render(
        <D3CohortAnalysis
          data={mockCohortData}
          metric="retention"
          onCellClick={onCellClick}
        />
      );

      // Check if cohort analysis is rendered
      expect(screen.getByText('Cohort Analysis')).toBeInTheDocument();
    });

    it('should handle cell interactions in heatmap', async () => {
      const user = userEvent.setup();
      const onCellClick = vi.fn();
      
      render(
        <D3CohortAnalysis
          data={mockCohortData}
          metric="retention"
          onCellClick={onCellClick}
        />
      );

      // The actual cell interaction would be tested with the real D3 implementation
      // For now, we verify the component renders
      expect(screen.getByText('Cohort Analysis')).toBeInTheDocument();
    });

    it('should display retention percentages', async () => {
      render(
        <D3CohortAnalysis
          data={mockCohortData}
          metric="retention"
          onCellClick={vi.fn()}
        />
      );

      // Check if retention data is processed
      expect(screen.getByText('Cohort Analysis')).toBeInTheDocument();
    });

    it('should handle different metrics', async () => {
      const { rerender } = render(
        <D3CohortAnalysis
          data={mockCohortData}
          metric="retention"
          onCellClick={vi.fn()}
        />
      );

      // Switch to different metric
      rerender(
        <D3CohortAnalysis
          data={mockCohortData}
          metric="revenue"
          onCellClick={vi.fn()}
        />
      );

      // Should handle metric change
      expect(screen.getByText('Cohort Analysis')).toBeInTheDocument();
    });
  });

  describe('Performance and Optimization', () => {
    it('should handle large datasets efficiently', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        type: 'line' as const,
        title: `Chart ${i}`,
        data: Array.from({ length: 100 }, (_, j) => ({
          date: `2024-01-${j + 1}`,
          value: Math.random() * 1000,
          label: `Point ${j}`
        })),
        width: 400,
        height: 300
      }));

      const startTime = performance.now();
      
      render(
        <D3AnalyticsDashboard
          charts={largeDataset.slice(0, 10)} // Limit for test performance
          realTimeData={true}
          interactive={true}
          onDataInteraction={vi.fn()}
        />
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time
      expect(renderTime).toBeLessThan(1000);
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
    });

    it('should cleanup D3 elements on unmount', async () => {
      const { unmount } = render(
        <D3AnalyticsDashboard
          charts={mockChartData}
          realTimeData={true}
          interactive={true}
          onDataInteraction={vi.fn()}
        />
      );

      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();

      // Unmount component
      unmount();

      // Should cleanup without errors
      expect(screen.queryByText('Analytics Dashboard')).not.toBeInTheDocument();
    });

    it('should handle rapid data updates without memory leaks', async () => {
      const { rerender } = render(
        <D3RealTimeMetrics
          streams={mockRealTimeStreams}
          updateInterval={100}
          maxDataPoints={50}
          onMetricClick={vi.fn()}
        />
      );

      // Simulate rapid updates
      for (let i = 0; i < 10; i++) {
        const updatedStreams = mockRealTimeStreams.map(stream => ({
          ...stream,
          data: [
            ...stream.data.slice(1),
            {
              timestamp: new Date(),
              value: Math.random() * 1000,
              metric: stream.id,
              change: Math.random() * 20 - 10
            }
          ]
        }));

        rerender(
          <D3RealTimeMetrics
            streams={updatedStreams}
            updateInterval={100}
            maxDataPoints={50}
            onMetricClick={vi.fn()}
          />
        );
      }

      // Should handle rapid updates without crashing
      expect(screen.getByText('Real-time Analytics')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should provide proper ARIA labels', async () => {
      render(
        <D3AnalyticsDashboard
          charts={mockChartData}
          realTimeData={true}
          interactive={true}
          onDataInteraction={vi.fn()}
        />
      );

      // Check for accessible elements
      const dashboard = screen.getByText('Analytics Dashboard');
      expect(dashboard).toBeInTheDocument();
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <D3CustomerJourney
          data={mockJourneyData}
          width={800}
          height={400}
          onNodeClick={vi.fn()}
        />
      );

      // Test keyboard navigation
      await user.tab();
      
      // Should be able to navigate with keyboard
      const activeElement = document.activeElement;
      expect(activeElement).toBeTruthy();
    });

    it('should provide screen reader friendly content', async () => {
      render(
        <D3CohortAnalysis
          data={mockCohortData}
          metric="retention"
          onCellClick={vi.fn()}
        />
      );

      // Check for screen reader content
      expect(screen.getByText('Cohort Analysis')).toBeInTheDocument();
    });
  });
});
