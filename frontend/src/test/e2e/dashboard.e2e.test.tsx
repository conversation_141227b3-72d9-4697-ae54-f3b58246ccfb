import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import DashboardPage from '../../pages/DashboardPage';
import AnalyticsDashboardPage from '../../pages/AnalyticsDashboardPage';
import { useRealTimeAnalytics } from '../../hooks/useRealTimeAnalytics';

// Mock the real-time analytics hook
vi.mock('../../hooks/useRealTimeAnalytics');

// Mock D3 components to avoid complex SVG rendering in tests
vi.mock('../../components/visualization/D3/D3AnalyticsDashboard', () => ({
  default: ({ charts, onDataInteraction }: any) => (
    <div data-testid="d3-analytics-dashboard">
      <div data-testid="chart-count">{charts.length}</div>
      {charts.map((chart: any, index: number) => (
        <div 
          key={index} 
          data-testid={`chart-${chart.type}-${index}`}
          onClick={() => onDataInteraction?.(chart, { value: 100 })}
        >
          {chart.title}
        </div>
      ))}
    </div>
  )
}));

vi.mock('../../components/visualization/D3/D3CustomerJourney', () => ({
  default: ({ data, onNodeClick }: any) => (
    <div data-testid="d3-customer-journey">
      <div data-testid="journey-nodes">{data.nodes?.length || 0}</div>
      {data.nodes?.map((node: any, index: number) => (
        <div 
          key={index}
          data-testid={`journey-node-${index}`}
          onClick={() => onNodeClick?.(node)}
        >
          {node.name}
        </div>
      ))}
    </div>
  )
}));

vi.mock('../../components/visualization/D3/D3RealTimeMetrics', () => ({
  default: ({ streams, onMetricClick }: any) => (
    <div data-testid="d3-realtime-metrics">
      <div data-testid="metrics-count">{streams.length}</div>
      {streams.map((stream: any, index: number) => (
        <div 
          key={index}
          data-testid={`metric-${stream.id}`}
          onClick={() => onMetricClick?.(stream)}
        >
          <span data-testid={`metric-name-${index}`}>{stream.name}</span>
          <span data-testid={`metric-value-${index}`}>{stream.data[stream.data.length - 1]?.value}</span>
        </div>
      ))}
    </div>
  )
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock WebSocket
global.WebSocket = vi.fn().mockImplementation(() => ({
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: WebSocket.OPEN,
}));

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Dashboard E2E Tests', () => {
  const mockRealTimeData = {
    liveMetrics: {
      activeUsers: 1250,
      clicksPerMinute: 45,
      conversionsPerHour: 12,
      revenueToday: 15420.50
    },
    recentClicks: [
      { id: '1', timestamp: new Date(), linkId: 'link1', country: 'US' },
      { id: '2', timestamp: new Date(), linkId: 'link2', country: 'CA' }
    ]
  };

  beforeEach(() => {
    vi.mocked(useRealTimeAnalytics).mockReturnValue({
      data: mockRealTimeData,
      isConnected: true,
      isConnecting: false,
      error: null,
      connect: vi.fn(),
      disconnect: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      connectionCount: 1
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Main Dashboard Page', () => {
    it('should render dashboard components without errors', async () => {
      renderWithRouter(<DashboardPage />);
      
      // Check for main dashboard elements
      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
      
      // Wait for components to load
      await waitFor(() => {
        expect(screen.getByTestId('kpi-scorecard')).toBeInTheDocument();
      });
    });

    it('should handle component errors gracefully with error boundaries', async () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      renderWithRouter(<DashboardPage />);
      
      // Error boundaries should catch any component failures
      await waitFor(() => {
        const errorBoundaries = screen.queryAllByText(/failed to load/i);
        // Should not have any error messages visible initially
        expect(errorBoundaries).toHaveLength(0);
      });
      
      consoleSpy.mockRestore();
    });

    it('should display real-time data correctly', async () => {
      renderWithRouter(<DashboardPage />);
      
      await waitFor(() => {
        // Check if real-time data is displayed
        expect(screen.getByText('1,250')).toBeInTheDocument(); // Active users
        expect(screen.getByText('$15,420.50')).toBeInTheDocument(); // Revenue
      });
    });
  });

  describe('Analytics Dashboard Page', () => {
    const mockChartData = [
      {
        type: 'line',
        title: 'Click Trends',
        data: [
          { date: '2024-01-01', value: 100 },
          { date: '2024-01-02', value: 150 },
          { date: '2024-01-03', value: 120 }
        ],
        width: 400,
        height: 300
      },
      {
        type: 'bar',
        title: 'Revenue by Channel',
        data: [
          { channel: 'Email', revenue: 5000 },
          { channel: 'Social', revenue: 3000 },
          { channel: 'Direct', revenue: 7000 }
        ],
        width: 400,
        height: 300
      }
    ];

    it('should render analytics dashboard with all tabs', async () => {
      renderWithRouter(<AnalyticsDashboardPage />);
      
      // Check for tab navigation
      expect(screen.getByRole('tab', { name: 'Overview' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Customer Journey' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Attribution' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Cohorts' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Real-time' })).toBeInTheDocument();
    });

    it('should switch between tabs correctly', async () => {
      const user = userEvent.setup();
      renderWithRouter(<AnalyticsDashboardPage />);
      
      // Initially on Overview tab
      expect(screen.getByRole('tab', { name: 'Overview' })).toHaveAttribute('data-state', 'active');
      
      // Click on Customer Journey tab
      await user.click(screen.getByRole('tab', { name: 'Customer Journey' }));
      
      await waitFor(() => {
        expect(screen.getByRole('tab', { name: 'Customer Journey' })).toHaveAttribute('data-state', 'active');
        expect(screen.getByTestId('d3-customer-journey')).toBeInTheDocument();
      });
      
      // Click on Real-time tab
      await user.click(screen.getByRole('tab', { name: 'Real-time' }));
      
      await waitFor(() => {
        expect(screen.getByRole('tab', { name: 'Real-time' })).toHaveAttribute('data-state', 'active');
        expect(screen.getByTestId('d3-realtime-metrics')).toBeInTheDocument();
      });
    });

    it('should handle chart interactions', async () => {
      const user = userEvent.setup();
      renderWithRouter(<AnalyticsDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByTestId('d3-analytics-dashboard')).toBeInTheDocument();
      });
      
      // Simulate clicking on a chart
      const chartElement = screen.getByTestId('chart-line-0');
      await user.click(chartElement);
      
      // Chart interaction should trigger without errors
      expect(chartElement).toBeInTheDocument();
    });
  });

  describe('D3 Visualizations', () => {
    it('should render D3 analytics dashboard with correct chart count', async () => {
      renderWithRouter(<AnalyticsDashboardPage />);
      
      await waitFor(() => {
        const dashboard = screen.getByTestId('d3-analytics-dashboard');
        expect(dashboard).toBeInTheDocument();
        
        // Should have multiple charts
        const chartCount = screen.getByTestId('chart-count');
        expect(parseInt(chartCount.textContent || '0')).toBeGreaterThan(0);
      });
    });

    it('should handle customer journey node interactions', async () => {
      const user = userEvent.setup();
      renderWithRouter(<AnalyticsDashboardPage />);
      
      // Switch to Customer Journey tab
      await user.click(screen.getByRole('tab', { name: 'Customer Journey' }));
      
      await waitFor(() => {
        const journeyComponent = screen.getByTestId('d3-customer-journey');
        expect(journeyComponent).toBeInTheDocument();
        
        // Check if journey nodes are rendered
        const nodesCount = screen.getByTestId('journey-nodes');
        expect(parseInt(nodesCount.textContent || '0')).toBeGreaterThanOrEqual(0);
      });
    });

    it('should display real-time metrics with live data', async () => {
      const user = userEvent.setup();
      renderWithRouter(<AnalyticsDashboardPage />);
      
      // Switch to Real-time tab
      await user.click(screen.getByRole('tab', { name: 'Real-time' }));
      
      await waitFor(() => {
        const metricsComponent = screen.getByTestId('d3-realtime-metrics');
        expect(metricsComponent).toBeInTheDocument();
        
        // Check if metrics are displayed
        const metricsCount = screen.getByTestId('metrics-count');
        expect(parseInt(metricsCount.textContent || '0')).toBeGreaterThan(0);
      });
    });
  });

  describe('Responsive Design', () => {
    it('should adapt to mobile viewport', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      });
      
      renderWithRouter(<AnalyticsDashboardPage />);
      
      // Trigger resize event
      fireEvent(window, new Event('resize'));
      
      await waitFor(() => {
        // Dashboard should still be functional on mobile
        expect(screen.getByRole('tab', { name: 'Overview' })).toBeInTheDocument();
      });
    });

    it('should adapt to tablet viewport', async () => {
      // Mock tablet viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 1024,
      });
      
      renderWithRouter(<AnalyticsDashboardPage />);
      
      fireEvent(window, new Event('resize'));
      
      await waitFor(() => {
        expect(screen.getByTestId('d3-analytics-dashboard')).toBeInTheDocument();
      });
    });

    it('should handle window resize events', async () => {
      renderWithRouter(<AnalyticsDashboardPage />);
      
      // Simulate multiple resize events
      for (let i = 0; i < 3; i++) {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          configurable: true,
          value: 1200 + i * 100,
        });
        fireEvent(window, new Event('resize'));
      }
      
      await waitFor(() => {
        // Dashboard should remain stable after multiple resizes
        expect(screen.getByTestId('d3-analytics-dashboard')).toBeInTheDocument();
      });
    });
  });

  describe('Real-time Data Updates', () => {
    it('should handle real-time data connection', async () => {
      renderWithRouter(<AnalyticsDashboardPage />);
      
      // Check if real-time connection is established
      expect(useRealTimeAnalytics).toHaveBeenCalled();
      
      const mockHook = vi.mocked(useRealTimeAnalytics);
      expect(mockHook.mock.results[0].value.isConnected).toBe(true);
    });

    it('should handle connection errors gracefully', async () => {
      // Mock connection error
      vi.mocked(useRealTimeAnalytics).mockReturnValue({
        data: null,
        isConnected: false,
        isConnecting: false,
        error: 'Connection failed',
        connect: vi.fn(),
        disconnect: vi.fn(),
        subscribe: vi.fn(),
        unsubscribe: vi.fn(),
        connectionCount: 0
      });
      
      renderWithRouter(<AnalyticsDashboardPage />);
      
      await waitFor(() => {
        // Dashboard should still render even with connection errors
        expect(screen.getByRole('tab', { name: 'Overview' })).toBeInTheDocument();
      });
    });

    it('should update metrics when new data arrives', async () => {
      const { rerender } = renderWithRouter(<AnalyticsDashboardPage />);
      
      // Initial render with first data set
      await waitFor(() => {
        expect(screen.getByTestId('d3-analytics-dashboard')).toBeInTheDocument();
      });
      
      // Update with new data
      const updatedData = {
        ...mockRealTimeData,
        liveMetrics: {
          ...mockRealTimeData.liveMetrics,
          activeUsers: 1500, // Updated value
        }
      };
      
      vi.mocked(useRealTimeAnalytics).mockReturnValue({
        data: updatedData,
        isConnected: true,
        isConnecting: false,
        error: null,
        connect: vi.fn(),
        disconnect: vi.fn(),
        subscribe: vi.fn(),
        unsubscribe: vi.fn(),
        connectionCount: 1
      });
      
      rerender(
        <BrowserRouter>
          <AnalyticsDashboardPage />
        </BrowserRouter>
      );
      
      // Component should handle data updates
      await waitFor(() => {
        expect(screen.getByTestId('d3-analytics-dashboard')).toBeInTheDocument();
      });
    });
  });

  describe('User Interactions', () => {
    it('should handle keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithRouter(<AnalyticsDashboardPage />);
      
      // Tab navigation should work
      await user.tab();
      await user.tab();
      
      // Should be able to navigate with keyboard
      const activeElement = document.activeElement;
      expect(activeElement).toBeTruthy();
    });

    it('should handle focus management', async () => {
      const user = userEvent.setup();
      renderWithRouter(<AnalyticsDashboardPage />);
      
      // Click on a tab
      const journeyTab = screen.getByRole('tab', { name: 'Customer Journey' });
      await user.click(journeyTab);
      
      await waitFor(() => {
        expect(journeyTab).toHaveAttribute('data-state', 'active');
      });
    });

    it('should handle touch interactions on mobile', async () => {
      // Mock touch events
      const touchStartEvent = new TouchEvent('touchstart', {
        touches: [{ clientX: 100, clientY: 100 } as Touch]
      });
      
      renderWithRouter(<AnalyticsDashboardPage />);
      
      await waitFor(() => {
        const dashboard = screen.getByTestId('d3-analytics-dashboard');
        fireEvent(dashboard, touchStartEvent);
        
        // Should handle touch events without errors
        expect(dashboard).toBeInTheDocument();
      });
    });
  });

  describe('Performance and Loading', () => {
    it('should render within acceptable time limits', async () => {
      const startTime = performance.now();
      
      renderWithRouter(<AnalyticsDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByTestId('d3-analytics-dashboard')).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render within 2 seconds
      expect(renderTime).toBeLessThan(2000);
    });

    it('should handle large datasets efficiently', async () => {
      // Mock large dataset
      const largeChartData = Array.from({ length: 1000 }, (_, i) => ({
        type: 'line',
        title: `Chart ${i}`,
        data: Array.from({ length: 100 }, (_, j) => ({
          date: `2024-01-${j + 1}`,
          value: Math.random() * 1000
        }))
      }));
      
      renderWithRouter(<AnalyticsDashboardPage />);
      
      await waitFor(() => {
        // Should handle large datasets without crashing
        expect(screen.getByTestId('d3-analytics-dashboard')).toBeInTheDocument();
      });
    });

    it('should cleanup resources on unmount', async () => {
      const { unmount } = renderWithRouter(<AnalyticsDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByTestId('d3-analytics-dashboard')).toBeInTheDocument();
      });
      
      // Unmount component
      unmount();
      
      // Should cleanup without errors
      expect(screen.queryByTestId('d3-analytics-dashboard')).not.toBeInTheDocument();
    });
  });
});
