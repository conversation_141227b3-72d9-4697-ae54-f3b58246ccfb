# GDPR Compliance Automation System
# Implements automated data protection, privacy controls, and compliance monitoring

apiVersion: v1
kind: Namespace
metadata:
  name: gdpr-compliance
  labels:
    app.kubernetes.io/name: gdpr-compliance
    compliance.ecommerce.io/framework: gdpr

---
# Data Processing Registry
apiVersion: v1
kind: ConfigMap
metadata:
  name: data-processing-registry
  namespace: gdpr-compliance
data:
  processing-activities.yaml: |
    processing_activities:
      - id: "PA001"
        name: "Customer Analytics Processing"
        controller: "E-commerce Analytics SaaS"
        purpose: "Provide analytics insights to customers"
        legal_basis: "legitimate_interest"
        categories_of_data:
          - "customer_identifiers"
          - "transaction_data"
          - "behavioral_data"
        categories_of_recipients:
          - "internal_analytics_team"
          - "customer_organizations"
        retention_period: "3_years"
        security_measures:
          - "encryption_at_rest"
          - "encryption_in_transit"
          - "access_controls"
          - "audit_logging"
      
      - id: "PA002"
        name: "User Account Management"
        controller: "E-commerce Analytics SaaS"
        purpose: "Manage user accounts and authentication"
        legal_basis: "contract"
        categories_of_data:
          - "contact_details"
          - "authentication_data"
          - "account_preferences"
        retention_period: "account_lifetime_plus_7_years"
        security_measures:
          - "password_hashing"
          - "mfa_protection"
          - "session_management"

---
# Data Subject Rights Automation
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gdpr-rights-processor
  namespace: gdpr-compliance
  labels:
    app: gdpr-rights-processor
spec:
  replicas: 2
  selector:
    matchLabels:
      app: gdpr-rights-processor
  template:
    metadata:
      labels:
        app: gdpr-rights-processor
    spec:
      serviceAccountName: gdpr-processor
      containers:
      - name: rights-processor
        image: ecommerce-analytics/gdpr-processor:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: gdpr-encryption-keys
              key: data-encryption-key
        - name: AUDIT_WEBHOOK_URL
          valueFrom:
            configMapKeyRef:
              name: gdpr-config
              key: audit-webhook-url
        volumeMounts:
        - name: gdpr-config
          mountPath: /etc/gdpr
        - name: temp-storage
          mountPath: /tmp/gdpr-exports
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: gdpr-config
        configMap:
          name: gdpr-config
      - name: temp-storage
        emptyDir:
          sizeLimit: 10Gi

---
# GDPR Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: gdpr-config
  namespace: gdpr-compliance
data:
  gdpr-settings.yaml: |
    # Data Subject Rights Configuration
    rights_processing:
      access_request_timeout: "30_days"
      rectification_timeout: "30_days"
      erasure_timeout: "30_days"
      portability_timeout: "30_days"
      objection_timeout: "30_days"
      
      # Automated processing thresholds
      auto_approve_access: true
      auto_approve_portability: true
      manual_review_erasure: true
      manual_review_rectification: true
    
    # Data Retention Policies
    retention_policies:
      customer_data:
        default_retention: "3_years"
        legal_hold_override: true
        deletion_grace_period: "30_days"
      
      analytics_data:
        aggregated_data: "7_years"
        individual_data: "3_years"
        anonymization_threshold: "1_year"
      
      audit_logs:
        security_events: "7_years"
        access_logs: "2_years"
        compliance_logs: "10_years"
    
    # Consent Management
    consent_management:
      require_explicit_consent: true
      consent_withdrawal_immediate: true
      consent_granularity: "purpose_specific"
      consent_proof_retention: "7_years"
    
    # Data Minimization
    data_minimization:
      enable_automatic_anonymization: true
      anonymization_delay: "1_year"
      pseudonymization_required: true
      data_classification_required: true

  audit-webhook-url: "https://compliance-audit.ecommerce-analytics.com/webhook"

---
# Data Anonymization CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: data-anonymization
  namespace: gdpr-compliance
spec:
  schedule: "0 3 * * 0"  # Weekly on Sunday at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: gdpr-processor
          containers:
          - name: anonymizer
            image: ecommerce-analytics/data-anonymizer:latest
            command:
            - /bin/sh
            - -c
            - |
              echo "Starting data anonymization process..."
              
              # Anonymize customer data older than retention period
              python3 /app/anonymize_customer_data.py \
                --retention-period=3years \
                --batch-size=1000 \
                --dry-run=false
              
              # Anonymize analytics data
              python3 /app/anonymize_analytics_data.py \
                --anonymization-threshold=1year \
                --preserve-aggregates=true
              
              # Generate anonymization report
              python3 /app/generate_anonymization_report.py \
                --output=/reports/anonymization-$(date +%Y%m%d).json
              
              echo "Data anonymization completed"
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: database-credentials
                  key: url
            - name: ENCRYPTION_KEY
              valueFrom:
                secretKeyRef:
                  name: gdpr-encryption-keys
                  key: anonymization-key
            volumeMounts:
            - name: reports
              mountPath: /reports
            resources:
              requests:
                memory: "1Gi"
                cpu: "500m"
              limits:
                memory: "2Gi"
                cpu: "1"
          volumes:
          - name: reports
            persistentVolumeClaim:
              claimName: gdpr-reports-pvc
          restartPolicy: OnFailure

---
# Data Breach Detection and Response
apiVersion: apps/v1
kind: Deployment
metadata:
  name: breach-detector
  namespace: gdpr-compliance
  labels:
    app: breach-detector
spec:
  replicas: 1
  selector:
    matchLabels:
      app: breach-detector
  template:
    metadata:
      labels:
        app: breach-detector
    spec:
      serviceAccountName: gdpr-processor
      containers:
      - name: breach-detector
        image: ecommerce-analytics/breach-detector:latest
        ports:
        - containerPort: 8080
        env:
        - name: ALERT_WEBHOOK_URL
          value: "https://alerts.ecommerce-analytics.com/gdpr-breach"
        - name: DPA_NOTIFICATION_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: gdpr-secrets
              key: dpa-notification-endpoint
        - name: BREACH_NOTIFICATION_EMAIL
          value: "<EMAIL>"
        volumeMounts:
        - name: breach-config
          mountPath: /etc/breach-detection
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
      volumes:
      - name: breach-config
        configMap:
          name: breach-detection-config

---
# Breach Detection Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: breach-detection-config
  namespace: gdpr-compliance
data:
  breach-rules.yaml: |
    breach_detection_rules:
      - name: "Unauthorized Data Access"
        pattern: "unauthorized_access"
        severity: "high"
        auto_notify: true
        notification_delay: "0h"
        criteria:
          - "failed_authentication_attempts > 100"
          - "privilege_escalation_detected = true"
          - "data_access_outside_hours = true"
      
      - name: "Data Exfiltration"
        pattern: "data_exfiltration"
        severity: "critical"
        auto_notify: true
        notification_delay: "0h"
        criteria:
          - "large_data_transfer > 1GB"
          - "unusual_export_activity = true"
          - "external_data_transfer = true"
      
      - name: "System Compromise"
        pattern: "system_compromise"
        severity: "critical"
        auto_notify: true
        notification_delay: "0h"
        criteria:
          - "malware_detected = true"
          - "unauthorized_system_changes = true"
          - "suspicious_admin_activity = true"
    
    notification_templates:
      dpa_notification: |
        Subject: Data Breach Notification - {{ .BreachId }}
        
        Dear Data Protection Authority,
        
        We are writing to notify you of a personal data breach that occurred on {{ .BreachDate }}.
        
        Breach Details:
        - Breach ID: {{ .BreachId }}
        - Date/Time: {{ .BreachDate }}
        - Type: {{ .BreachType }}
        - Severity: {{ .Severity }}
        - Affected Records: {{ .AffectedRecords }}
        - Data Categories: {{ .DataCategories }}
        
        Immediate Actions Taken:
        {{ range .ImmediateActions }}
        - {{ . }}
        {{ end }}
        
        Risk Assessment:
        {{ .RiskAssessment }}
        
        Remediation Plan:
        {{ .RemediationPlan }}
        
        Contact Information:
        Data Protection Officer: {{ .DPOContact }}
        
        Sincerely,
        E-commerce Analytics SaaS Platform

---
# Consent Management Service
apiVersion: v1
kind: Service
metadata:
  name: consent-management
  namespace: gdpr-compliance
spec:
  selector:
    app: consent-management
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: consent-management
  namespace: gdpr-compliance
  labels:
    app: consent-management
spec:
  replicas: 2
  selector:
    matchLabels:
      app: consent-management
  template:
    metadata:
      labels:
        app: consent-management
    spec:
      serviceAccountName: gdpr-processor
      containers:
      - name: consent-manager
        image: ecommerce-analytics/consent-manager:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: CONSENT_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: gdpr-encryption-keys
              key: consent-encryption-key
        volumeMounts:
        - name: consent-config
          mountPath: /etc/consent
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: consent-config
        configMap:
          name: consent-config

---
# Consent Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: consent-config
  namespace: gdpr-compliance
data:
  consent-purposes.yaml: |
    consent_purposes:
      - id: "analytics_processing"
        name: "Analytics Data Processing"
        description: "Process your data to provide analytics insights"
        legal_basis: "consent"
        required: false
        granular_controls:
          - "behavioral_analytics"
          - "performance_analytics"
          - "conversion_tracking"
      
      - id: "marketing_communications"
        name: "Marketing Communications"
        description: "Send you marketing emails and notifications"
        legal_basis: "consent"
        required: false
        granular_controls:
          - "product_updates"
          - "promotional_offers"
          - "newsletter"
      
      - id: "service_provision"
        name: "Service Provision"
        description: "Provide the core analytics service"
        legal_basis: "contract"
        required: true
        granular_controls: []
    
    consent_ui_config:
      banner_text: "We use cookies and process personal data to provide our analytics service and improve your experience."
      accept_all_text: "Accept All"
      reject_all_text: "Reject All"
      customize_text: "Customize Settings"
      privacy_policy_url: "https://ecommerce-analytics.com/privacy"
      cookie_policy_url: "https://ecommerce-analytics.com/cookies"

---
# GDPR Compliance Monitoring
apiVersion: v1
kind: ConfigMap
metadata:
  name: gdpr-monitoring-rules
  namespace: gdpr-compliance
data:
  compliance-alerts.yaml: |
    groups:
    - name: gdpr.compliance
      rules:
      # Data retention violations
      - alert: DataRetentionViolation
        expr: data_retention_days > retention_policy_days
        for: 0m
        labels:
          severity: critical
          compliance: gdpr
        annotations:
          summary: "Data retention policy violation"
          description: "Data older than retention policy detected for {{ $labels.data_type }}"
      
      # Consent violations
      - alert: ProcessingWithoutConsent
        expr: processing_without_consent_total > 0
        for: 0m
        labels:
          severity: critical
          compliance: gdpr
        annotations:
          summary: "Processing without valid consent"
          description: "Data processing detected without valid consent for purpose {{ $labels.purpose }}"
      
      # Data subject rights violations
      - alert: RightsRequestOverdue
        expr: rights_request_days_pending > 30
        for: 0m
        labels:
          severity: warning
          compliance: gdpr
        annotations:
          summary: "Data subject rights request overdue"
          description: "Rights request {{ $labels.request_id }} is overdue by {{ $value }} days"
      
      # Breach notification delays
      - alert: BreachNotificationDelayed
        expr: breach_notification_hours_delayed > 72
        for: 0m
        labels:
          severity: critical
          compliance: gdpr
        annotations:
          summary: "Breach notification deadline missed"
          description: "Breach {{ $labels.breach_id }} notification is {{ $value }} hours overdue"

---
# GDPR Service Account and RBAC
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gdpr-processor
  namespace: gdpr-compliance

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: gdpr-processor-role
  namespace: gdpr-compliance
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["create", "get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: gdpr-processor-binding
  namespace: gdpr-compliance
subjects:
- kind: ServiceAccount
  name: gdpr-processor
  namespace: gdpr-compliance
roleRef:
  kind: Role
  name: gdpr-processor-role
  apiGroup: rbac.authorization.k8s.io

---
# GDPR Reports Storage
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: gdpr-reports-pvc
  namespace: gdpr-compliance
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard
  resources:
    requests:
      storage: 100Gi
