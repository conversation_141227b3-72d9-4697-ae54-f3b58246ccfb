-- Advanced TimescaleDB Configuration for Production Scale
-- Optimized for multi-tenant e-commerce analytics with high-volume time-series data

-- ============================================================================
-- ADVANCED HYPERTABLE CONFIGURATION
-- ============================================================================

-- Configure advanced partitioning for clicks table with space partitioning
SELECT create_hypertable(
  'clicks', 
  'clicked_at',
  partitioning_column => 'tenant_id',
  number_partitions => 16,
  chunk_time_interval => INTERVAL '6 hours',
  create_default_indexes => FALSE,
  if_not_exists => TRUE
);

-- Configure orders table with adaptive chunk sizing
SELECT create_hypertable(
  'orders', 
  'created_at',
  partitioning_column => 'tenant_id',
  number_partitions => 8,
  chunk_time_interval => INTERVAL '1 day',
  create_default_indexes => FALSE,
  if_not_exists => TRUE
);

-- Configure attributions with smaller chunks for better compression
SELECT create_hypertable(
  'attributions', 
  'created_at',
  partitioning_column => 'tenant_id',
  number_partitions => 4,
  chunk_time_interval => INTERVAL '12 hours',
  create_default_indexes => FALSE,
  if_not_exists => TRUE
);

-- ============================================================================
-- ADVANCED INDEXING STRATEGY
-- ============================================================================

-- Composite indexes for multi-tenant queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_tenant_time_device
ON clicks (tenant_id, clicked_at DESC, device_type) 
WHERE clicked_at >= NOW() - INTERVAL '90 days';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_tenant_link_time
ON clicks (tenant_id, link_id, clicked_at DESC)
WHERE clicked_at >= NOW() - INTERVAL '30 days';

-- Partial indexes for hot data
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_recent_conversions
ON clicks (tenant_id, clicked_at DESC, ip_address)
WHERE clicked_at >= NOW() - INTERVAL '7 days' 
  AND EXISTS (
    SELECT 1 FROM attributions a 
    WHERE a.click_id = clicks.id
  );

-- BRIN indexes for time-series columns (space efficient)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_time_brin
ON clicks USING BRIN (clicked_at) WITH (pages_per_range = 128);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_time_brin
ON orders USING BRIN (created_at) WITH (pages_per_range = 128);

-- Hash indexes for exact tenant lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_tenant_hash
ON clicks USING HASH (tenant_id);

-- GIN indexes for JSON columns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_metadata_gin
ON orders USING GIN (metadata) 
WHERE metadata IS NOT NULL;

-- ============================================================================
-- CONTINUOUS AGGREGATES FOR REAL-TIME ANALYTICS
-- ============================================================================

-- Real-time hourly metrics with 1-minute refresh
CREATE MATERIALIZED VIEW IF NOT EXISTS realtime_hourly_metrics
WITH (timescaledb.continuous) AS
SELECT 
  tenant_id,
  time_bucket('1 hour', clicked_at) AS hour_bucket,
  COUNT(*) AS total_clicks,
  COUNT(DISTINCT ip_address) AS unique_visitors,
  COUNT(DISTINCT link_id) AS unique_links,
  COUNT(DISTINCT device_type) AS device_types,
  COUNT(DISTINCT country) AS countries,
  AVG(CASE WHEN device_type = 'mobile' THEN 1.0 ELSE 0.0 END) * 100 AS mobile_percentage,
  COUNT(CASE WHEN referrer IS NOT NULL THEN 1 END) AS referred_clicks
FROM clicks
GROUP BY tenant_id, hour_bucket;

-- Daily conversion funnel metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_conversion_funnel
WITH (timescaledb.continuous) AS
SELECT 
  tenant_id,
  time_bucket('1 day', c.clicked_at) AS day_bucket,
  COUNT(DISTINCT c.id) AS total_clicks,
  COUNT(DISTINCT o.id) AS total_orders,
  COALESCE(SUM(o.total_amount), 0) AS total_revenue,
  COUNT(DISTINCT c.ip_address) AS unique_visitors,
  COUNT(DISTINCT CASE WHEN o.id IS NOT NULL THEN c.ip_address END) AS converting_visitors,
  CASE 
    WHEN COUNT(DISTINCT c.ip_address) > 0 
    THEN (COUNT(DISTINCT CASE WHEN o.id IS NOT NULL THEN c.ip_address END)::float / COUNT(DISTINCT c.ip_address)::float) * 100
    ELSE 0 
  END AS conversion_rate
FROM clicks c
LEFT JOIN attributions a ON c.id = a.click_id
LEFT JOIN orders o ON a.order_id = o.id
GROUP BY tenant_id, day_bucket;

-- Customer lifetime value aggregates
CREATE MATERIALIZED VIEW IF NOT EXISTS customer_clv_summary
WITH (timescaledb.continuous) AS
SELECT 
  tenant_id,
  time_bucket('1 week', created_at) AS week_bucket,
  customer_email,
  COUNT(*) AS order_count,
  SUM(total_amount) AS total_spent,
  AVG(total_amount) AS avg_order_value,
  MIN(created_at) AS first_order_date,
  MAX(created_at) AS last_order_date,
  EXTRACT(DAYS FROM MAX(created_at) - MIN(created_at)) AS customer_lifespan_days
FROM orders
WHERE status IN ('completed', 'fulfilled')
GROUP BY tenant_id, week_bucket, customer_email;

-- ============================================================================
-- COMPRESSION POLICIES
-- ============================================================================

-- Aggressive compression for clicks (high volume, low update frequency)
ALTER TABLE clicks SET (
  timescaledb.compress,
  timescaledb.compress_segmentby = 'tenant_id, device_type',
  timescaledb.compress_orderby = 'clicked_at DESC, link_id'
);

-- Moderate compression for orders (lower volume, occasional updates)
ALTER TABLE orders SET (
  timescaledb.compress,
  timescaledb.compress_segmentby = 'tenant_id, status',
  timescaledb.compress_orderby = 'created_at DESC, customer_email'
);

-- Light compression for attributions (complex relationships)
ALTER TABLE attributions SET (
  timescaledb.compress,
  timescaledb.compress_segmentby = 'tenant_id, attribution_model',
  timescaledb.compress_orderby = 'created_at DESC'
);

-- Compression policies with different intervals based on data patterns
SELECT add_compression_policy('clicks', INTERVAL '3 days', if_not_exists => TRUE);
SELECT add_compression_policy('orders', INTERVAL '7 days', if_not_exists => TRUE);
SELECT add_compression_policy('attributions', INTERVAL '14 days', if_not_exists => TRUE);

-- ============================================================================
-- RETENTION POLICIES
-- ============================================================================

-- Tiered retention based on tenant plans
-- Enterprise: 5 years, Professional: 3 years, Basic: 1 year
CREATE OR REPLACE FUNCTION apply_tenant_retention_policy()
RETURNS void AS $$
DECLARE
  tenant_record RECORD;
BEGIN
  FOR tenant_record IN 
    SELECT id, plan FROM tenants 
  LOOP
    CASE tenant_record.plan
      WHEN 'enterprise' THEN
        -- 5 years retention for enterprise
        PERFORM add_retention_policy(
          format('clicks_tenant_%s', tenant_record.id),
          INTERVAL '5 years',
          if_not_exists => TRUE
        );
      WHEN 'professional' THEN
        -- 3 years retention for professional
        PERFORM add_retention_policy(
          format('clicks_tenant_%s', tenant_record.id),
          INTERVAL '3 years',
          if_not_exists => TRUE
        );
      ELSE
        -- 1 year retention for basic
        PERFORM add_retention_policy(
          format('clicks_tenant_%s', tenant_record.id),
          INTERVAL '1 year',
          if_not_exists => TRUE
        );
    END CASE;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- CONTINUOUS AGGREGATE REFRESH POLICIES
-- ============================================================================

-- Real-time metrics refresh every minute
SELECT add_continuous_aggregate_policy(
  'realtime_hourly_metrics',
  start_offset => INTERVAL '2 hours',
  end_offset => INTERVAL '1 minute',
  schedule_interval => INTERVAL '1 minute',
  if_not_exists => TRUE
);

-- Daily funnel refresh every 15 minutes
SELECT add_continuous_aggregate_policy(
  'daily_conversion_funnel',
  start_offset => INTERVAL '3 days',
  end_offset => INTERVAL '15 minutes',
  schedule_interval => INTERVAL '15 minutes',
  if_not_exists => TRUE
);

-- CLV summary refresh every hour
SELECT add_continuous_aggregate_policy(
  'customer_clv_summary',
  start_offset => INTERVAL '1 week',
  end_offset => INTERVAL '1 hour',
  schedule_interval => INTERVAL '1 hour',
  if_not_exists => TRUE
);

-- ============================================================================
-- ADVANCED QUERY OPTIMIZATION
-- ============================================================================

-- Custom statistics for better query planning
CREATE STATISTICS IF NOT EXISTS clicks_tenant_time_stats 
ON tenant_id, clicked_at FROM clicks;

CREATE STATISTICS IF NOT EXISTS orders_tenant_customer_stats 
ON tenant_id, customer_email, created_at FROM orders;

-- Analyze tables to update statistics
ANALYZE clicks;
ANALYZE orders;
ANALYZE attributions;

-- ============================================================================
-- PERFORMANCE MONITORING VIEWS
-- ============================================================================

-- Chunk information view for monitoring
CREATE OR REPLACE VIEW chunk_performance_stats AS
SELECT 
  hypertable_name,
  chunk_name,
  range_start,
  range_end,
  is_compressed,
  compressed_heap_size,
  uncompressed_heap_size,
  CASE 
    WHEN uncompressed_heap_size > 0 
    THEN round((1 - compressed_heap_size::float / uncompressed_heap_size::float) * 100, 2)
    ELSE 0 
  END as compression_ratio
FROM timescaledb_information.chunks c
JOIN timescaledb_information.chunk_compression_stats ccs 
  ON c.chunk_name = ccs.chunk_name
ORDER BY range_start DESC;

-- Query performance monitoring view
CREATE OR REPLACE VIEW slow_queries_analysis AS
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  stddev_time,
  rows,
  100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 100  -- Queries taking more than 100ms on average
ORDER BY mean_time DESC;

-- ============================================================================
-- AUTOMATED MAINTENANCE PROCEDURES
-- ============================================================================

-- Automated chunk management
CREATE OR REPLACE FUNCTION optimize_chunks()
RETURNS void AS $$
DECLARE
  chunk_record RECORD;
BEGIN
  -- Reorder chunks for better compression
  FOR chunk_record IN 
    SELECT chunk_name, hypertable_name 
    FROM timescaledb_information.chunks 
    WHERE NOT is_compressed 
      AND range_end < NOW() - INTERVAL '1 day'
  LOOP
    EXECUTE format('REINDEX TABLE %I', chunk_record.chunk_name);
  END LOOP;
  
  -- Update statistics on recently modified chunks
  FOR chunk_record IN 
    SELECT chunk_name 
    FROM timescaledb_information.chunks 
    WHERE range_end > NOW() - INTERVAL '1 hour'
  LOOP
    EXECUTE format('ANALYZE %I', chunk_record.chunk_name);
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Schedule automated maintenance
SELECT cron.schedule('optimize-chunks', '0 2 * * *', 'SELECT optimize_chunks();');
SELECT cron.schedule('update-statistics', '0 */6 * * *', 'ANALYZE;');

-- ============================================================================
-- CONNECTION POOLING OPTIMIZATION
-- ============================================================================

-- Connection pooling settings for high concurrency
ALTER SYSTEM SET max_connections = 500;
ALTER SYSTEM SET shared_buffers = '2GB';
ALTER SYSTEM SET effective_cache_size = '6GB';
ALTER SYSTEM SET work_mem = '32MB';
ALTER SYSTEM SET maintenance_work_mem = '512MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '64MB';
ALTER SYSTEM SET default_statistics_target = 500;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Reload configuration
SELECT pg_reload_conf();
