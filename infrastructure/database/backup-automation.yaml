# Automated Database Backup and Disaster Recovery System
# Implements continuous WAL archiving, scheduled backups, and point-in-time recovery

apiVersion: v1
kind: Namespace
metadata:
  name: backup-system
  labels:
    app.kubernetes.io/name: backup-system

---
# Backup Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-config
  namespace: backup-system
data:
  backup.conf: |
    # Backup Configuration
    BACKUP_RETENTION_DAYS=30
    BACKUP_RETENTION_WEEKS=12
    BACKUP_RETENTION_MONTHS=12
    BACKUP_RETENTION_YEARS=7
    
    # Compression settings
    COMPRESSION_LEVEL=6
    PARALLEL_JOBS=4
    
    # S3 Configuration
    S3_BUCKET=ecommerce-analytics-backups
    S3_REGION=us-east-1
    S3_STORAGE_CLASS=STANDARD_IA
    
    # Database settings
    DB_HOST=timescaledb-primary.database.svc.cluster.local
    DB_PORT=5432
    DB_NAME=ecommerce_analytics
    
    # WAL-E Configuration
    WALE_S3_PREFIX=s3://ecommerce-analytics-wal/wal-e
    WALE_BACKUP_THRESHOLD_PERCENTAGE=30
    WALE_BACKUP_THRESHOLD_MEGABYTES=10240

  wal-e.conf: |
    # WAL-E Configuration for continuous archiving
    export AWS_REGION=us-east-1
    export WALE_S3_PREFIX=s3://ecommerce-analytics-wal/wal-e
    export WALE_BACKUP_THRESHOLD_PERCENTAGE=30
    export WALE_BACKUP_THRESHOLD_MEGABYTES=10240
    export WALE_DOWNLOAD_SSH_KEY=/etc/wal-e/ssh/id_rsa
    export WALE_GPG_KEY_ID=<EMAIL>

---
# Backup Scripts ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-scripts
  namespace: backup-system
data:
  full-backup.sh: |
    #!/bin/bash
    set -euo pipefail
    
    source /etc/backup/backup.conf
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_NAME="full_backup_${TIMESTAMP}"
    BACKUP_PATH="/backup/${BACKUP_NAME}"
    
    echo "Starting full backup: ${BACKUP_NAME}"
    
    # Create backup directory
    mkdir -p "${BACKUP_PATH}"
    
    # Perform base backup with WAL-E
    envdir /etc/wal-e/env wal-e backup-push "${BACKUP_PATH}"
    
    # Create manifest
    cat > "${BACKUP_PATH}/manifest.json" << EOF
    {
      "backup_name": "${BACKUP_NAME}",
      "timestamp": "${TIMESTAMP}",
      "type": "full",
      "database": "${DB_NAME}",
      "compression": "${COMPRESSION_LEVEL}",
      "size_bytes": $(du -sb "${BACKUP_PATH}" | cut -f1),
      "wal_start": "$(envdir /etc/wal-e/env wal-e backup-list | tail -1 | awk '{print $1}')"
    }
    EOF
    
    # Upload to S3
    aws s3 sync "${BACKUP_PATH}" "s3://${S3_BUCKET}/full-backups/${BACKUP_NAME}/" \
      --storage-class "${S3_STORAGE_CLASS}"
    
    echo "Full backup completed: ${BACKUP_NAME}"

  incremental-backup.sh: |
    #!/bin/bash
    set -euo pipefail
    
    source /etc/backup/backup.conf
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_NAME="incremental_backup_${TIMESTAMP}"
    
    echo "Starting incremental backup: ${BACKUP_NAME}"
    
    # WAL-E handles incremental backups automatically through WAL archiving
    # Force a WAL switch to ensure recent changes are archived
    psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${POSTGRES_USER}" -d "${DB_NAME}" \
      -c "SELECT pg_switch_wal();"
    
    # Create incremental backup manifest
    LATEST_WAL=$(envdir /etc/wal-e/env wal-e wal-show | tail -1)
    
    cat > "/backup/incremental_${TIMESTAMP}.json" << EOF
    {
      "backup_name": "${BACKUP_NAME}",
      "timestamp": "${TIMESTAMP}",
      "type": "incremental",
      "database": "${DB_NAME}",
      "latest_wal": "${LATEST_WAL}"
    }
    EOF
    
    # Upload manifest
    aws s3 cp "/backup/incremental_${TIMESTAMP}.json" \
      "s3://${S3_BUCKET}/incremental-backups/"
    
    echo "Incremental backup completed: ${BACKUP_NAME}"

  restore.sh: |
    #!/bin/bash
    set -euo pipefail
    
    RESTORE_POINT="${1:-latest}"
    TARGET_DIR="${2:-/restore}"
    
    source /etc/backup/backup.conf
    
    echo "Starting restore to point: ${RESTORE_POINT}"
    
    # Create restore directory
    mkdir -p "${TARGET_DIR}"
    
    # Restore base backup
    if [ "${RESTORE_POINT}" = "latest" ]; then
      envdir /etc/wal-e/env wal-e backup-fetch "${TARGET_DIR}" LATEST
    else
      envdir /etc/wal-e/env wal-e backup-fetch "${TARGET_DIR}" "${RESTORE_POINT}"
    fi
    
    # Create recovery configuration
    cat > "${TARGET_DIR}/recovery.conf" << EOF
    restore_command = 'envdir /etc/wal-e/env wal-e wal-fetch "%f" "%p"'
    recovery_target_time = '${RESTORE_POINT}'
    recovery_target_timeline = 'latest'
    EOF
    
    echo "Restore completed to: ${TARGET_DIR}"
    echo "Start PostgreSQL with data directory: ${TARGET_DIR}"

  cleanup-old-backups.sh: |
    #!/bin/bash
    set -euo pipefail
    
    source /etc/backup/backup.conf
    
    echo "Starting backup cleanup..."
    
    # Delete old WAL files
    envdir /etc/wal-e/env wal-e delete --confirm retain "${BACKUP_RETENTION_DAYS}"
    
    # Cleanup S3 backups older than retention period
    CUTOFF_DATE=$(date -d "${BACKUP_RETENTION_DAYS} days ago" +%Y%m%d)
    
    aws s3 ls "s3://${S3_BUCKET}/full-backups/" | while read -r line; do
      BACKUP_DATE=$(echo "$line" | awk '{print $2}' | cut -d'_' -f3 | cut -d'T' -f1)
      if [[ "${BACKUP_DATE}" < "${CUTOFF_DATE}" ]]; then
        BACKUP_NAME=$(echo "$line" | awk '{print $2}')
        echo "Deleting old backup: ${BACKUP_NAME}"
        aws s3 rm "s3://${S3_BUCKET}/full-backups/${BACKUP_NAME}/" --recursive
      fi
    done
    
    echo "Backup cleanup completed"

---
# WAL-E Environment Configuration
apiVersion: v1
kind: Secret
metadata:
  name: wal-e-env
  namespace: backup-system
type: Opaque
data:
  AWS_ACCESS_KEY_ID: <BASE64_ENCODED_ACCESS_KEY>
  AWS_SECRET_ACCESS_KEY: <BASE64_ENCODED_SECRET_KEY>
  AWS_REGION: dXMtZWFzdC0x  # us-east-1
  WALE_S3_PREFIX: czM6Ly9lY29tbWVyY2UtYW5hbHl0aWNzLXdhbC93YWwtZQ==  # s3://ecommerce-analytics-wal/wal-e

---
# Full Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: full-backup
  namespace: backup-system
  labels:
    app: backup-system
    type: full-backup
spec:
  schedule: "0 2 * * 0"  # Weekly on Sunday at 2 AM
  timeZone: "UTC"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: backup-system
            type: full-backup
        spec:
          restartPolicy: OnFailure
          containers:
          - name: backup
            image: postgres:15-alpine
            command: ["/bin/bash", "/scripts/full-backup.sh"]
            env:
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: timescaledb-credentials
                  key: username
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: timescaledb-credentials
                  key: password
            envFrom:
            - secretRef:
                name: wal-e-env
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
            - name: backup-config
              mountPath: /etc/backup
            - name: backup-storage
              mountPath: /backup
            - name: wal-e-env-dir
              mountPath: /etc/wal-e/env
            resources:
              requests:
                memory: "1Gi"
                cpu: "500m"
              limits:
                memory: "2Gi"
                cpu: "1"
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-scripts
              defaultMode: 0755
          - name: backup-config
            configMap:
              name: backup-config
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          - name: wal-e-env-dir
            secret:
              secretName: wal-e-env

---
# Incremental Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: incremental-backup
  namespace: backup-system
  labels:
    app: backup-system
    type: incremental-backup
spec:
  schedule: "0 */6 * * *"  # Every 6 hours
  timeZone: "UTC"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 5
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: backup-system
            type: incremental-backup
        spec:
          restartPolicy: OnFailure
          containers:
          - name: backup
            image: postgres:15-alpine
            command: ["/bin/bash", "/scripts/incremental-backup.sh"]
            env:
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: timescaledb-credentials
                  key: username
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: timescaledb-credentials
                  key: password
            envFrom:
            - secretRef:
                name: wal-e-env
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
            - name: backup-config
              mountPath: /etc/backup
            - name: backup-storage
              mountPath: /backup
            - name: wal-e-env-dir
              mountPath: /etc/wal-e/env
            resources:
              requests:
                memory: "512Mi"
                cpu: "250m"
              limits:
                memory: "1Gi"
                cpu: "500m"
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-scripts
              defaultMode: 0755
          - name: backup-config
            configMap:
              name: backup-config
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          - name: wal-e-env-dir
            secret:
              secretName: wal-e-env

---
# Backup Cleanup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: backup-cleanup
  namespace: backup-system
  labels:
    app: backup-system
    type: cleanup
spec:
  schedule: "0 4 * * 1"  # Weekly on Monday at 4 AM
  timeZone: "UTC"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: backup-system
            type: cleanup
        spec:
          restartPolicy: OnFailure
          containers:
          - name: cleanup
            image: postgres:15-alpine
            command: ["/bin/bash", "/scripts/cleanup-old-backups.sh"]
            envFrom:
            - secretRef:
                name: wal-e-env
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
            - name: backup-config
              mountPath: /etc/backup
            - name: wal-e-env-dir
              mountPath: /etc/wal-e/env
            resources:
              requests:
                memory: "256Mi"
                cpu: "100m"
              limits:
                memory: "512Mi"
                cpu: "250m"
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-scripts
              defaultMode: 0755
          - name: backup-config
            configMap:
              name: backup-config
          - name: wal-e-env-dir
            secret:
              secretName: wal-e-env

---
# Backup Storage PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-storage-pvc
  namespace: backup-system
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard
  resources:
    requests:
      storage: 1Ti

---
# Disaster Recovery Job Template
apiVersion: batch/v1
kind: Job
metadata:
  name: disaster-recovery-template
  namespace: backup-system
  labels:
    app: backup-system
    type: disaster-recovery
spec:
  template:
    metadata:
      labels:
        app: backup-system
        type: disaster-recovery
    spec:
      restartPolicy: Never
      containers:
      - name: restore
        image: postgres:15-alpine
        command: ["/bin/bash", "/scripts/restore.sh"]
        args: ["latest", "/restore"]
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: password
        envFrom:
        - secretRef:
            name: wal-e-env
        volumeMounts:
        - name: backup-scripts
          mountPath: /scripts
        - name: backup-config
          mountPath: /etc/backup
        - name: restore-storage
          mountPath: /restore
        - name: wal-e-env-dir
          mountPath: /etc/wal-e/env
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
      volumes:
      - name: backup-scripts
        configMap:
          name: backup-scripts
          defaultMode: 0755
      - name: backup-config
        configMap:
          name: backup-config
      - name: restore-storage
        persistentVolumeClaim:
          claimName: restore-storage-pvc
      - name: wal-e-env-dir
        secret:
          secretName: wal-e-env

---
# Restore Storage PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: restore-storage-pvc
  namespace: backup-system
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 2Ti
