-- Database Performance Monitoring and Optimization Queries
-- Comprehensive monitoring views and functions for TimescaleDB performance

-- ============================================================================
-- PERFORMANCE MONITORING VIEWS
-- ============================================================================

-- Real-time query performance monitoring
CREATE OR REPLACE VIEW active_queries_monitor AS
SELECT 
  pid,
  now() - pg_stat_activity.query_start AS duration,
  query,
  state,
  wait_event_type,
  wait_event,
  client_addr,
  application_name,
  usename,
  datname
FROM pg_stat_activity 
WHERE state != 'idle' 
  AND query NOT LIKE '%pg_stat_activity%'
ORDER BY duration DESC;

-- Slow query analysis with execution plans
CREATE OR REPLACE VIEW slow_queries_detailed AS
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  stddev_time,
  rows,
  100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent,
  100.0 * local_blks_hit / nullif(local_blks_hit + local_blks_read, 0) AS local_hit_percent,
  100.0 * temp_blks_read / nullif(temp_blks_read + temp_blks_written, 0) AS temp_read_percent
FROM pg_stat_statements 
WHERE mean_time > 100  -- Queries taking more than 100ms on average
ORDER BY mean_time DESC
LIMIT 50;

-- Table bloat analysis
CREATE OR REPLACE VIEW table_bloat_analysis AS
SELECT 
  schemaname,
  tablename,
  n_live_tup,
  n_dead_tup,
  CASE 
    WHEN n_live_tup > 0 
    THEN round((n_dead_tup::float / n_live_tup::float) * 100, 2)
    ELSE 0 
  END as dead_tuple_percent,
  last_vacuum,
  last_autovacuum,
  last_analyze,
  last_autoanalyze,
  vacuum_count,
  autovacuum_count
FROM pg_stat_user_tables
ORDER BY dead_tuple_percent DESC;

-- Index usage statistics
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_tup_read,
  idx_tup_fetch,
  CASE 
    WHEN idx_tup_read > 0 
    THEN round((idx_tup_fetch::float / idx_tup_read::float) * 100, 2)
    ELSE 0 
  END as index_hit_rate,
  pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
ORDER BY idx_tup_read DESC;

-- TimescaleDB chunk performance
CREATE OR REPLACE VIEW chunk_performance_detailed AS
SELECT 
  h.hypertable_name,
  c.chunk_name,
  c.range_start,
  c.range_end,
  c.is_compressed,
  pg_size_pretty(ccs.before_compression_total_bytes) as uncompressed_size,
  pg_size_pretty(ccs.after_compression_total_bytes) as compressed_size,
  CASE 
    WHEN ccs.before_compression_total_bytes > 0 
    THEN round((1 - ccs.after_compression_total_bytes::float / ccs.before_compression_total_bytes::float) * 100, 2)
    ELSE 0 
  END as compression_ratio,
  pg_size_pretty(pg_total_relation_size(format('%I.%I', c.chunk_schema, c.chunk_name))) as total_size
FROM timescaledb_information.chunks c
JOIN timescaledb_information.hypertables h ON c.hypertable_name = h.hypertable_name
LEFT JOIN timescaledb_information.chunk_compression_stats ccs 
  ON c.chunk_name = ccs.chunk_name
ORDER BY c.range_start DESC;

-- Connection pool monitoring
CREATE OR REPLACE VIEW connection_pool_stats AS
SELECT 
  datname,
  usename,
  application_name,
  client_addr,
  state,
  COUNT(*) as connection_count,
  MAX(now() - state_change) as max_idle_time,
  AVG(now() - state_change) as avg_idle_time
FROM pg_stat_activity
WHERE state IS NOT NULL
GROUP BY datname, usename, application_name, client_addr, state
ORDER BY connection_count DESC;

-- ============================================================================
-- PERFORMANCE OPTIMIZATION FUNCTIONS
-- ============================================================================

-- Automatic index recommendation based on query patterns
CREATE OR REPLACE FUNCTION recommend_indexes()
RETURNS TABLE(
  table_name text,
  recommended_index text,
  reason text,
  estimated_benefit numeric
) AS $$
BEGIN
  RETURN QUERY
  WITH query_analysis AS (
    SELECT 
      regexp_replace(query, '\$\d+', '?', 'g') as normalized_query,
      calls,
      mean_time,
      total_time
    FROM pg_stat_statements
    WHERE calls > 10 AND mean_time > 50
  ),
  table_scans AS (
    SELECT 
      schemaname || '.' || tablename as full_table_name,
      seq_scan,
      seq_tup_read,
      idx_scan,
      CASE 
        WHEN seq_scan + idx_scan > 0 
        THEN seq_scan::float / (seq_scan + idx_scan)::float * 100
        ELSE 0 
      END as seq_scan_ratio
    FROM pg_stat_user_tables
    WHERE seq_scan > 100 AND seq_tup_read > 10000
  )
  SELECT 
    ts.full_table_name,
    'CREATE INDEX ON ' || ts.full_table_name || ' (tenant_id, created_at DESC)' as recommended_index,
    'High sequential scan ratio: ' || round(ts.seq_scan_ratio, 2) || '%' as reason,
    ts.seq_scan_ratio as estimated_benefit
  FROM table_scans ts
  WHERE ts.seq_scan_ratio > 20
  ORDER BY ts.seq_scan_ratio DESC;
END;
$$ LANGUAGE plpgsql;

-- Automated table maintenance
CREATE OR REPLACE FUNCTION optimize_table_maintenance()
RETURNS void AS $$
DECLARE
  table_record RECORD;
  maintenance_sql text;
BEGIN
  -- Vacuum tables with high dead tuple percentage
  FOR table_record IN 
    SELECT schemaname, tablename, dead_tuple_percent
    FROM table_bloat_analysis
    WHERE dead_tuple_percent > 20
  LOOP
    maintenance_sql := format('VACUUM ANALYZE %I.%I', table_record.schemaname, table_record.tablename);
    RAISE NOTICE 'Executing: %', maintenance_sql;
    EXECUTE maintenance_sql;
  END LOOP;
  
  -- Reindex tables with low index hit rates
  FOR table_record IN 
    SELECT DISTINCT schemaname, tablename
    FROM index_usage_stats
    WHERE index_hit_rate < 80 AND idx_tup_read > 1000
  LOOP
    maintenance_sql := format('REINDEX TABLE %I.%I', table_record.schemaname, table_record.tablename);
    RAISE NOTICE 'Executing: %', maintenance_sql;
    EXECUTE maintenance_sql;
  END LOOP;
  
  -- Update table statistics
  ANALYZE;
END;
$$ LANGUAGE plpgsql;

-- Query performance analyzer
CREATE OR REPLACE FUNCTION analyze_query_performance(
  query_text text,
  explain_analyze boolean DEFAULT false
)
RETURNS TABLE(
  execution_plan text,
  execution_time numeric,
  planning_time numeric,
  total_cost numeric,
  recommendations text[]
) AS $$
DECLARE
  plan_result text;
  recommendations text[] := ARRAY[]::text[];
BEGIN
  -- Get execution plan
  IF explain_analyze THEN
    EXECUTE 'EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) ' || query_text INTO plan_result;
  ELSE
    EXECUTE 'EXPLAIN (FORMAT TEXT) ' || query_text INTO plan_result;
  END IF;
  
  -- Analyze plan for recommendations
  IF plan_result LIKE '%Seq Scan%' THEN
    recommendations := array_append(recommendations, 'Consider adding indexes to avoid sequential scans');
  END IF;
  
  IF plan_result LIKE '%Sort%' AND plan_result LIKE '%external%' THEN
    recommendations := array_append(recommendations, 'Increase work_mem to avoid external sorts');
  END IF;
  
  IF plan_result LIKE '%Hash%' AND plan_result LIKE '%Batches%' THEN
    recommendations := array_append(recommendations, 'Increase work_mem to avoid hash batching');
  END IF;
  
  RETURN QUERY SELECT 
    plan_result,
    0::numeric, -- Would extract from EXPLAIN ANALYZE output
    0::numeric, -- Would extract from EXPLAIN ANALYZE output  
    0::numeric, -- Would extract from EXPLAIN output
    recommendations;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- AUTOMATED PERFORMANCE ALERTS
-- ============================================================================

-- Function to check for performance issues
CREATE OR REPLACE FUNCTION check_performance_alerts()
RETURNS TABLE(
  alert_type text,
  severity text,
  message text,
  metric_value numeric,
  threshold numeric
) AS $$
BEGIN
  -- Check for long-running queries
  RETURN QUERY
  SELECT 
    'long_running_query'::text,
    'warning'::text,
    'Query running for ' || extract(epoch from duration) || ' seconds'::text,
    extract(epoch from duration)::numeric,
    300::numeric -- 5 minutes threshold
  FROM active_queries_monitor
  WHERE duration > interval '5 minutes';
  
  -- Check for high dead tuple percentage
  RETURN QUERY
  SELECT 
    'table_bloat'::text,
    CASE WHEN dead_tuple_percent > 50 THEN 'critical' ELSE 'warning' END::text,
    'Table ' || schemaname || '.' || tablename || ' has ' || dead_tuple_percent || '% dead tuples'::text,
    dead_tuple_percent::numeric,
    25::numeric -- 25% threshold
  FROM table_bloat_analysis
  WHERE dead_tuple_percent > 25;
  
  -- Check for low cache hit ratio
  RETURN QUERY
  SELECT 
    'low_cache_hit'::text,
    'warning'::text,
    'Query has low cache hit ratio: ' || hit_percent || '%'::text,
    hit_percent::numeric,
    90::numeric -- 90% threshold
  FROM slow_queries_detailed
  WHERE hit_percent < 90 AND calls > 100;
  
  -- Check for unused indexes
  RETURN QUERY
  SELECT 
    'unused_index'::text,
    'info'::text,
    'Index ' || schemaname || '.' || indexname || ' appears unused'::text,
    idx_tup_read::numeric,
    100::numeric -- 100 reads threshold
  FROM index_usage_stats
  WHERE idx_tup_read < 100;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- PERFORMANCE REPORTING
-- ============================================================================

-- Daily performance report
CREATE OR REPLACE FUNCTION generate_daily_performance_report()
RETURNS text AS $$
DECLARE
  report text := '';
  metric_record RECORD;
BEGIN
  report := report || E'=== DAILY PERFORMANCE REPORT ===\n';
  report := report || 'Generated: ' || now() || E'\n\n';
  
  -- Database size summary
  report := report || E'DATABASE SIZE:\n';
  FOR metric_record IN 
    SELECT 
      pg_size_pretty(pg_database_size(current_database())) as db_size,
      pg_size_pretty(sum(pg_total_relation_size(c.oid))) as table_size
    FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE c.relkind = 'r' AND n.nspname NOT IN ('information_schema', 'pg_catalog')
  LOOP
    report := report || '  Total Database Size: ' || metric_record.db_size || E'\n';
    report := report || '  Total Table Size: ' || metric_record.table_size || E'\n';
  END LOOP;
  
  -- Top slow queries
  report := report || E'\nTOP 5 SLOW QUERIES:\n';
  FOR metric_record IN 
    SELECT query, calls, mean_time, total_time
    FROM slow_queries_detailed
    LIMIT 5
  LOOP
    report := report || '  ' || round(metric_record.mean_time, 2) || 'ms avg (' || 
              metric_record.calls || ' calls): ' || 
              left(metric_record.query, 80) || E'...\n';
  END LOOP;
  
  -- Compression stats
  report := report || E'\nCOMPRESSION STATS:\n';
  FOR metric_record IN 
    SELECT 
      hypertable_name,
      count(*) as total_chunks,
      count(*) FILTER (WHERE is_compressed) as compressed_chunks,
      avg(compression_ratio) FILTER (WHERE compression_ratio > 0) as avg_compression
    FROM chunk_performance_detailed
    GROUP BY hypertable_name
  LOOP
    report := report || '  ' || metric_record.hypertable_name || ': ' ||
              metric_record.compressed_chunks || '/' || metric_record.total_chunks ||
              ' chunks compressed (avg: ' || round(metric_record.avg_compression, 1) || '%)\n';
  END LOOP;
  
  -- Performance alerts
  report := report || E'\nPERFORMANCE ALERTS:\n';
  FOR metric_record IN 
    SELECT alert_type, severity, message
    FROM check_performance_alerts()
    WHERE severity IN ('warning', 'critical')
  LOOP
    report := report || '  [' || upper(metric_record.severity) || '] ' || 
              metric_record.alert_type || ': ' || metric_record.message || E'\n';
  END LOOP;
  
  RETURN report;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- AUTOMATED OPTIMIZATION SCHEDULER
-- ============================================================================

-- Schedule automated maintenance tasks
SELECT cron.schedule(
  'daily-performance-check',
  '0 6 * * *',  -- Daily at 6 AM
  'SELECT optimize_table_maintenance();'
);

SELECT cron.schedule(
  'weekly-performance-report',
  '0 8 * * 1',  -- Weekly on Monday at 8 AM
  'SELECT generate_daily_performance_report();'
);

-- Create performance monitoring materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_performance_metrics AS
SELECT 
  current_date as report_date,
  (SELECT count(*) FROM active_queries_monitor WHERE duration > interval '1 minute') as long_queries,
  (SELECT avg(mean_time) FROM slow_queries_detailed WHERE calls > 10) as avg_query_time,
  (SELECT avg(hit_percent) FROM slow_queries_detailed WHERE calls > 10) as avg_cache_hit,
  (SELECT count(*) FROM table_bloat_analysis WHERE dead_tuple_percent > 25) as bloated_tables,
  (SELECT pg_database_size(current_database())) as database_size,
  (SELECT count(*) FROM check_performance_alerts() WHERE severity = 'critical') as critical_alerts
WITH NO DATA;

-- Refresh performance metrics daily
SELECT cron.schedule(
  'refresh-performance-metrics',
  '0 7 * * *',  -- Daily at 7 AM
  'REFRESH MATERIALIZED VIEW daily_performance_metrics;'
);
