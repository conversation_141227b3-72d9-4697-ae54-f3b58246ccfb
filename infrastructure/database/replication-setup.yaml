# TimescaleDB High Availability and Replication Setup
# Implements streaming replication with automatic failover

apiVersion: v1
kind: Namespace
metadata:
  name: database
  labels:
    app.kubernetes.io/name: timescaledb
    app.kubernetes.io/component: database

---
# Primary Database Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: timescaledb-primary-config
  namespace: database
data:
  postgresql.conf: |
    # Connection settings
    listen_addresses = '*'
    port = 5432
    max_connections = 500
    
    # Memory settings
    shared_buffers = 2GB
    effective_cache_size = 6GB
    work_mem = 32MB
    maintenance_work_mem = 512MB
    
    # WAL settings for replication
    wal_level = replica
    max_wal_senders = 10
    max_replication_slots = 10
    wal_keep_segments = 64
    wal_sender_timeout = 60s
    
    # Checkpoint settings
    checkpoint_completion_target = 0.9
    checkpoint_timeout = 15min
    max_wal_size = 4GB
    min_wal_size = 1GB
    wal_buffers = 64MB
    
    # Query planner
    random_page_cost = 1.1
    effective_io_concurrency = 200
    default_statistics_target = 500
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'pg_log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_rotation_age = 1d
    log_rotation_size = 100MB
    log_min_duration_statement = 1000
    log_checkpoints = on
    log_connections = on
    log_disconnections = on
    log_lock_waits = on
    log_statement = 'ddl'
    
    # TimescaleDB settings
    shared_preload_libraries = 'timescaledb,pg_stat_statements'
    timescaledb.max_background_workers = 16
    
    # Performance monitoring
    pg_stat_statements.max = 10000
    pg_stat_statements.track = all
    pg_stat_statements.track_utility = on
    pg_stat_statements.save = on
    
    # Autovacuum tuning
    autovacuum = on
    autovacuum_max_workers = 6
    autovacuum_naptime = 15s
    autovacuum_vacuum_threshold = 25
    autovacuum_analyze_threshold = 10
    autovacuum_vacuum_scale_factor = 0.1
    autovacuum_analyze_scale_factor = 0.05
    autovacuum_vacuum_cost_delay = 10ms
    autovacuum_vacuum_cost_limit = 1000

  pg_hba.conf: |
    # TYPE  DATABASE        USER            ADDRESS                 METHOD
    local   all             all                                     trust
    host    all             all             127.0.0.1/32            md5
    host    all             all             ::1/128                 md5
    host    all             all             10.0.0.0/8              md5
    host    replication     replicator      10.0.0.0/8              md5
    host    replication     replicator      0.0.0.0/0               md5

---
# Replica Database Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: timescaledb-replica-config
  namespace: database
data:
  postgresql.conf: |
    # Inherit most settings from primary
    include_if_exists = '/etc/postgresql/postgresql-primary.conf'
    
    # Replica-specific settings
    hot_standby = on
    max_standby_archive_delay = 30s
    max_standby_streaming_delay = 30s
    wal_receiver_status_interval = 10s
    hot_standby_feedback = on
    wal_receiver_timeout = 60s
    
    # Read-only optimizations
    default_transaction_isolation = 'read committed'
    
  recovery.conf: |
    standby_mode = 'on'
    primary_conninfo = 'host=timescaledb-primary port=5432 user=replicator'
    trigger_file = '/tmp/postgresql.trigger'
    recovery_target_timeline = 'latest'

---
# Primary Database StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: timescaledb-primary
  namespace: database
  labels:
    app: timescaledb
    role: primary
spec:
  serviceName: timescaledb-primary
  replicas: 1
  selector:
    matchLabels:
      app: timescaledb
      role: primary
  template:
    metadata:
      labels:
        app: timescaledb
        role: primary
    spec:
      containers:
      - name: timescaledb
        image: timescale/timescaledb-ha:pg15-latest
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          value: "ecommerce_analytics"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: password
        - name: POSTGRES_REPLICATION_USER
          value: "replicator"
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: replication-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: config-volume
          mountPath: /etc/postgresql
        - name: backup-storage
          mountPath: /backup
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "8Gi"
            cpu: "4"
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U $POSTGRES_USER -d $POSTGRES_DB
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U $POSTGRES_USER -d $POSTGRES_DB
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: timescaledb-primary-config
      - name: backup-storage
        persistentVolumeClaim:
          claimName: timescaledb-backup-pvc
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 1Ti

---
# Read Replica StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: timescaledb-replica
  namespace: database
  labels:
    app: timescaledb
    role: replica
spec:
  serviceName: timescaledb-replica
  replicas: 2
  selector:
    matchLabels:
      app: timescaledb
      role: replica
  template:
    metadata:
      labels:
        app: timescaledb
        role: replica
    spec:
      containers:
      - name: timescaledb
        image: timescale/timescaledb-ha:pg15-latest
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: password
        - name: POSTGRES_REPLICATION_USER
          value: "replicator"
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: replication-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        - name: POSTGRES_MASTER_SERVICE
          value: "timescaledb-primary"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: config-volume
          mountPath: /etc/postgresql
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U $PGUSER
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U $PGUSER
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: timescaledb-replica-config
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 1Ti

---
# Primary Database Service
apiVersion: v1
kind: Service
metadata:
  name: timescaledb-primary
  namespace: database
  labels:
    app: timescaledb
    role: primary
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  selector:
    app: timescaledb
    role: primary

---
# Read Replica Service
apiVersion: v1
kind: Service
metadata:
  name: timescaledb-replica
  namespace: database
  labels:
    app: timescaledb
    role: replica
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  selector:
    app: timescaledb
    role: replica

---
# Database Credentials Secret
apiVersion: v1
kind: Secret
metadata:
  name: timescaledb-credentials
  namespace: database
type: Opaque
data:
  username: cG9zdGdyZXM=  # postgres (base64)
  password: <BASE64_ENCODED_PASSWORD>
  replication-password: <BASE64_ENCODED_REPLICATION_PASSWORD>

---
# Backup Storage PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: timescaledb-backup-pvc
  namespace: database
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard
  resources:
    requests:
      storage: 500Gi

---
# Database Connection Pooler (PgBouncer)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgbouncer
  namespace: database
  labels:
    app: pgbouncer
spec:
  replicas: 3
  selector:
    matchLabels:
      app: pgbouncer
  template:
    metadata:
      labels:
        app: pgbouncer
    spec:
      containers:
      - name: pgbouncer
        image: pgbouncer/pgbouncer:latest
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: DATABASES_HOST
          value: "timescaledb-primary"
        - name: DATABASES_PORT
          value: "5432"
        - name: DATABASES_USER
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: username
        - name: DATABASES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: password
        - name: DATABASES_DBNAME
          value: "ecommerce_analytics"
        - name: POOL_MODE
          value: "transaction"
        - name: SERVER_RESET_QUERY
          value: "DISCARD ALL"
        - name: MAX_CLIENT_CONN
          value: "1000"
        - name: DEFAULT_POOL_SIZE
          value: "25"
        - name: MIN_POOL_SIZE
          value: "5"
        - name: RESERVE_POOL_SIZE
          value: "5"
        - name: RESERVE_POOL_TIMEOUT
          value: "5"
        - name: MAX_DB_CONNECTIONS
          value: "100"
        - name: MAX_USER_CONNECTIONS
          value: "100"
        - name: SERVER_ROUND_ROBIN
          value: "1"
        - name: LOG_CONNECTIONS
          value: "1"
        - name: LOG_DISCONNECTIONS
          value: "1"
        - name: LOG_POOLER_ERRORS
          value: "1"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          tcpSocket:
            port: 5432
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          tcpSocket:
            port: 5432
          initialDelaySeconds: 5
          periodSeconds: 5

---
# PgBouncer Service
apiVersion: v1
kind: Service
metadata:
  name: pgbouncer
  namespace: database
  labels:
    app: pgbouncer
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  selector:
    app: pgbouncer

---
# Database Monitoring with Prometheus Exporter
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-exporter
  namespace: database
  labels:
    app: postgres-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-exporter
  template:
    metadata:
      labels:
        app: postgres-exporter
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9187"
    spec:
      containers:
      - name: postgres-exporter
        image: prometheuscommunity/postgres-exporter:latest
        ports:
        - containerPort: 9187
          name: metrics
        env:
        - name: DATA_SOURCE_NAME
          value: "***************************************************************************/ecommerce_analytics?sslmode=disable"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timescaledb-credentials
              key: password
        - name: PG_EXPORTER_EXTEND_QUERY_PATH
          value: "/etc/postgres_exporter/queries.yaml"
        volumeMounts:
        - name: queries-config
          mountPath: /etc/postgres_exporter
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: queries-config
        configMap:
          name: postgres-exporter-queries

---
# Custom Queries for Postgres Exporter
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-exporter-queries
  namespace: database
data:
  queries.yaml: |
    # TimescaleDB specific metrics
    timescaledb_hypertable_size:
      query: |
        SELECT 
          schemaname,
          tablename,
          pg_size_pretty(hypertable_size(format('%I.%I', schemaname, tablename))) as size_pretty,
          hypertable_size(format('%I.%I', schemaname, tablename)) as size_bytes
        FROM timescaledb_information.hypertables
      metrics:
        - schemaname:
            usage: "LABEL"
            description: "Schema name"
        - tablename:
            usage: "LABEL"
            description: "Table name"
        - size_bytes:
            usage: "GAUGE"
            description: "Hypertable size in bytes"
    
    # Chunk compression stats
    timescaledb_compression_stats:
      query: |
        SELECT 
          chunk_schema,
          chunk_name,
          compression_status,
          before_compression_total_bytes,
          after_compression_total_bytes,
          CASE 
            WHEN before_compression_total_bytes > 0 
            THEN (1 - after_compression_total_bytes::float / before_compression_total_bytes::float) * 100
            ELSE 0 
          END as compression_ratio
        FROM chunk_compression_stats('clicks')
        UNION ALL
        SELECT 
          chunk_schema,
          chunk_name,
          compression_status,
          before_compression_total_bytes,
          after_compression_total_bytes,
          CASE 
            WHEN before_compression_total_bytes > 0 
            THEN (1 - after_compression_total_bytes::float / before_compression_total_bytes::float) * 100
            ELSE 0 
          END as compression_ratio
        FROM chunk_compression_stats('orders')
      metrics:
        - chunk_schema:
            usage: "LABEL"
            description: "Chunk schema"
        - chunk_name:
            usage: "LABEL"
            description: "Chunk name"
        - compression_status:
            usage: "LABEL"
            description: "Compression status"
        - compression_ratio:
            usage: "GAUGE"
            description: "Compression ratio percentage"
