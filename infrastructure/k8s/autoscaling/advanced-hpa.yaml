# Advanced Horizontal Pod Autoscaler with Custom Metrics
# Scales based on CPU, memory, and custom business metrics

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: analytics-service-hpa
  namespace: production
  labels:
    app.kubernetes.io/name: analytics-service
    app.kubernetes.io/component: autoscaler
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analytics-service
  
  minReplicas: 3
  maxReplicas: 50
  
  metrics:
  # CPU utilization
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  
  # Memory utilization
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  
  # Custom metric: API requests per second
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  
  # Custom metric: Database query latency
  - type: Pods
    pods:
      metric:
        name: database_query_latency_p95
      target:
        type: AverageValue
        averageValue: "500m"  # 500ms
  
  # Custom metric: Queue depth
  - type: Pods
    pods:
      metric:
        name: analytics_queue_depth
      target:
        type: AverageValue
        averageValue: "50"
  
  # External metric: CloudWatch custom metric
  - type: External
    external:
      metric:
        name: tenant_active_users
        selector:
          matchLabels:
            service: analytics
      target:
        type: AverageValue
        averageValue: "1000"

  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 5 minutes
      policies:
      - type: Percent
        value: 10  # Scale down by max 10% of current replicas
        periodSeconds: 60
      - type: Pods
        value: 2   # Scale down by max 2 pods
        periodSeconds: 60
      selectPolicy: Min  # Use the policy that results in fewer pods being removed
    
    scaleUp:
      stabilizationWindowSeconds: 60   # 1 minute
      policies:
      - type: Percent
        value: 50  # Scale up by max 50% of current replicas
        periodSeconds: 60
      - type: Pods
        value: 5   # Scale up by max 5 pods
        periodSeconds: 60
      selectPolicy: Max  # Use the policy that results in more pods being added

---
# Vertical Pod Autoscaler for right-sizing containers
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: analytics-service-vpa
  namespace: production
  labels:
    app.kubernetes.io/name: analytics-service
    app.kubernetes.io/component: vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analytics-service
  
  updatePolicy:
    updateMode: "Auto"  # Automatically apply recommendations
    
  resourcePolicy:
    containerPolicies:
    - containerName: analytics-service
      minAllowed:
        cpu: 100m
        memory: 256Mi
      maxAllowed:
        cpu: 4
        memory: 8Gi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits

---
# Custom Metrics API Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: custom-metrics-config
  namespace: production
data:
  prometheus-adapter-config.yaml: |
    rules:
    # HTTP requests per second
    - seriesQuery: 'http_requests_total{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^http_requests_total"
        as: "http_requests_per_second"
      metricsQuery: 'rate(http_requests_total{<<.LabelMatchers>>}[2m])'
    
    # Database query latency P95
    - seriesQuery: 'database_query_duration_seconds{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^database_query_duration_seconds"
        as: "database_query_latency_p95"
      metricsQuery: 'histogram_quantile(0.95, rate(database_query_duration_seconds_bucket{<<.LabelMatchers>>}[5m]))'
    
    # Queue depth
    - seriesQuery: 'analytics_queue_size{namespace!="",pod!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
      name:
        matches: "^analytics_queue_size"
        as: "analytics_queue_depth"
      metricsQuery: 'analytics_queue_size{<<.LabelMatchers>>}'
    
    # Memory usage percentage
    - seriesQuery: 'container_memory_usage_bytes{namespace!="",pod!="",container!=""}'
      resources:
        overrides:
          namespace: {resource: "namespace"}
          pod: {resource: "pod"}
          container: {resource: "container"}
      name:
        matches: "^container_memory_usage_bytes"
        as: "memory_usage_percentage"
      metricsQuery: '(container_memory_usage_bytes{<<.LabelMatchers>>} / container_spec_memory_limit_bytes{<<.LabelMatchers>>}) * 100'

---
# Pod Disruption Budget to ensure availability during scaling
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: analytics-service-pdb
  namespace: production
  labels:
    app.kubernetes.io/name: analytics-service
    app.kubernetes.io/component: pdb
spec:
  selector:
    matchLabels:
      app: analytics-service
  minAvailable: 2  # Always keep at least 2 pods running
  # Alternative: maxUnavailable: 25%

---
# KEDA ScaledObject for event-driven autoscaling
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: analytics-service-keda
  namespace: production
  labels:
    app.kubernetes.io/name: analytics-service
    app.kubernetes.io/component: keda-scaler
spec:
  scaleTargetRef:
    name: analytics-service
  
  pollingInterval: 30   # Check metrics every 30 seconds
  cooldownPeriod: 300   # Wait 5 minutes before scaling down
  idleReplicaCount: 2   # Scale to 2 replicas when no activity
  minReplicaCount: 3
  maxReplicaCount: 50
  
  triggers:
  # Scale based on Kafka message lag
  - type: kafka
    metadata:
      bootstrapServers: kafka-cluster:9092
      consumerGroup: analytics-consumer-group
      topic: analytics-events
      lagThreshold: "100"
      offsetResetPolicy: latest
  
  # Scale based on Redis queue length
  - type: redis
    metadata:
      address: redis-cluster:6379
      listName: analytics_queue
      listLength: "50"
      databaseIndex: "0"
  
  # Scale based on Prometheus metrics
  - type: prometheus
    metadata:
      serverAddress: http://prometheus:9090
      metricName: analytics_processing_time
      threshold: "500"
      query: avg(analytics_processing_time_seconds{service="analytics"})

---
# Cluster Autoscaler configuration for node scaling
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-autoscaler-status
  namespace: kube-system
  labels:
    app.kubernetes.io/name: cluster-autoscaler
data:
  nodes.max: "100"
  nodes.min: "3"
  scale-down-delay-after-add: "10m"
  scale-down-unneeded-time: "10m"
  scale-down-utilization-threshold: "0.5"
  skip-nodes-with-local-storage: "false"
  skip-nodes-with-system-pods: "false"

---
# Node affinity rules for optimal pod placement
apiVersion: v1
kind: ConfigMap
metadata:
  name: pod-affinity-config
  namespace: production
data:
  affinity-rules.yaml: |
    # Prefer to schedule analytics pods on compute-optimized nodes
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        preference:
          matchExpressions:
          - key: node.kubernetes.io/instance-type
            operator: In
            values: ["c5.large", "c5.xlarge", "c5.2xlarge", "c5.4xlarge"]
      - weight: 80
        preference:
          matchExpressions:
          - key: topology.kubernetes.io/zone
            operator: In
            values: ["us-east-1a", "us-east-1b", "us-east-1c"]
    
    # Pod anti-affinity to spread replicas across nodes
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app
              operator: In
              values: ["analytics-service"]
          topologyKey: kubernetes.io/hostname
      - weight: 50
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app
              operator: In
              values: ["analytics-service"]
          topologyKey: topology.kubernetes.io/zone

---
# Resource quotas for autoscaling limits
apiVersion: v1
kind: ResourceQuota
metadata:
  name: autoscaling-quota
  namespace: production
spec:
  hard:
    requests.cpu: "200"      # Total CPU requests across all pods
    requests.memory: "400Gi" # Total memory requests across all pods
    limits.cpu: "400"        # Total CPU limits across all pods
    limits.memory: "800Gi"   # Total memory limits across all pods
    pods: "200"              # Maximum number of pods
    persistentvolumeclaims: "50"
    services.loadbalancers: "10"

---
# Priority classes for different workload types
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: high-priority-analytics
value: 1000
globalDefault: false
description: "High priority for critical analytics workloads"

---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: medium-priority-analytics
value: 500
globalDefault: false
description: "Medium priority for standard analytics workloads"

---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: low-priority-analytics
value: 100
globalDefault: false
description: "Low priority for batch analytics workloads"
