# Multi-tenant Namespace Template
# This template creates isolated namespaces for each tenant with proper RBAC and resource quotas

apiVersion: v1
kind: Namespace
metadata:
  name: tenant-${TENANT_ID}
  labels:
    app.kubernetes.io/name: ecommerce-analytics
    app.kubernetes.io/component: tenant-namespace
    tenant.ecommerce.io/id: ${TENANT_ID}
    tenant.ecommerce.io/plan: ${TENANT_PLAN}
    tenant.ecommerce.io/region: ${TENANT_REGION}
    security.kubernetes.io/isolation: strict
  annotations:
    tenant.ecommerce.io/created-at: ${CREATION_TIMESTAMP}
    tenant.ecommerce.io/owner: ${TENANT_OWNER}
    tenant.ecommerce.io/billing-id: ${BILLING_ID}
    scheduler.alpha.kubernetes.io/node-selector: tenant-tier=${TENANT_PLAN}

---
# Resource Quota for Tenant Namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: tenant-${TENANT_ID}-quota
  namespace: tenant-${TENANT_ID}
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
spec:
  hard:
    # Compute Resources
    requests.cpu: ${CPU_REQUESTS_LIMIT}
    requests.memory: ${MEMORY_REQUESTS_LIMIT}
    limits.cpu: ${CPU_LIMITS_LIMIT}
    limits.memory: ${MEMORY_LIMITS_LIMIT}
    
    # Storage
    requests.storage: ${STORAGE_LIMIT}
    persistentvolumeclaims: ${PVC_COUNT_LIMIT}
    
    # Object Counts
    pods: ${POD_COUNT_LIMIT}
    services: ${SERVICE_COUNT_LIMIT}
    secrets: ${SECRET_COUNT_LIMIT}
    configmaps: ${CONFIGMAP_COUNT_LIMIT}
    
    # Network
    services.loadbalancers: ${LB_COUNT_LIMIT}
    services.nodeports: ${NODEPORT_COUNT_LIMIT}

---
# Network Policy for Tenant Isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-${TENANT_ID}-isolation
  namespace: tenant-${TENANT_ID}
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  # Ingress Rules
  ingress:
  # Allow traffic from same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          tenant.ecommerce.io/id: ${TENANT_ID}
  
  # Allow traffic from shared services namespace
  - from:
    - namespaceSelector:
        matchLabels:
          app.kubernetes.io/name: ecommerce-analytics
          app.kubernetes.io/component: shared-services
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 9092  # Kafka
  
  # Allow traffic from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          app.kubernetes.io/name: ingress-nginx
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: ingress-nginx
  
  # Allow traffic from monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          app.kubernetes.io/name: monitoring
    ports:
    - protocol: TCP
      port: 8080  # Metrics endpoint
  
  # Egress Rules
  egress:
  # Allow traffic to same namespace
  - to:
    - namespaceSelector:
        matchLabels:
          tenant.ecommerce.io/id: ${TENANT_ID}
  
  # Allow traffic to shared services
  - to:
    - namespaceSelector:
        matchLabels:
          app.kubernetes.io/name: ecommerce-analytics
          app.kubernetes.io/component: shared-services
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 9092  # Kafka
  
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS to external services (e-commerce platforms)
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
# Service Account for Tenant Applications
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-${TENANT_ID}-app
  namespace: tenant-${TENANT_ID}
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
automountServiceAccountToken: true

---
# Role for Tenant Applications
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tenant-${TENANT_ID}-app-role
  namespace: tenant-${TENANT_ID}
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
rules:
# Allow reading own namespace resources
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]

# Allow creating and updating own resources
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["create", "update", "patch"]

# Allow reading events for debugging
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch"]

# Allow accessing metrics
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods", "nodes"]
  verbs: ["get", "list"]

---
# Role Binding for Tenant Applications
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tenant-${TENANT_ID}-app-binding
  namespace: tenant-${TENANT_ID}
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
subjects:
- kind: ServiceAccount
  name: tenant-${TENANT_ID}-app
  namespace: tenant-${TENANT_ID}
roleRef:
  kind: Role
  name: tenant-${TENANT_ID}-app-role
  apiGroup: rbac.authorization.k8s.io

---
# Pod Security Policy for Tenant
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: tenant-${TENANT_ID}-psp
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 2000

---
# Limit Range for Tenant Resources
apiVersion: v1
kind: LimitRange
metadata:
  name: tenant-${TENANT_ID}-limits
  namespace: tenant-${TENANT_ID}
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
spec:
  limits:
  # Container limits
  - type: Container
    default:
      cpu: ${DEFAULT_CPU_LIMIT}
      memory: ${DEFAULT_MEMORY_LIMIT}
    defaultRequest:
      cpu: ${DEFAULT_CPU_REQUEST}
      memory: ${DEFAULT_MEMORY_REQUEST}
    max:
      cpu: ${MAX_CPU_LIMIT}
      memory: ${MAX_MEMORY_LIMIT}
    min:
      cpu: ${MIN_CPU_REQUEST}
      memory: ${MIN_MEMORY_REQUEST}
  
  # Pod limits
  - type: Pod
    max:
      cpu: ${POD_MAX_CPU}
      memory: ${POD_MAX_MEMORY}
  
  # PVC limits
  - type: PersistentVolumeClaim
    max:
      storage: ${PVC_MAX_STORAGE}
    min:
      storage: ${PVC_MIN_STORAGE}

---
# Priority Class for Tenant Workloads
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: tenant-${TENANT_ID}-priority
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
value: ${TENANT_PRIORITY_VALUE}
globalDefault: false
description: "Priority class for tenant ${TENANT_ID} workloads based on plan ${TENANT_PLAN}"

---
# Horizontal Pod Autoscaler Template
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tenant-${TENANT_ID}-hpa
  namespace: tenant-${TENANT_ID}
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tenant-${TENANT_ID}-analytics
  minReplicas: ${MIN_REPLICAS}
  maxReplicas: ${MAX_REPLICAS}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: ${CPU_TARGET_UTILIZATION}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: ${MEMORY_TARGET_UTILIZATION}
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
