# Comprehensive Monitoring Stack with Prometheus, Grafana, and AlertManager
# Includes multi-tenant monitoring with proper isolation

apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
  labels:
    app.kubernetes.io/name: monitoring
    app.kubernetes.io/component: observability

---
# Prometheus Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'ecommerce-analytics-prod'
        environment: 'production'
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
    # Kubernetes API Server
    - job_name: 'kubernetes-apiservers'
      kubernetes_sd_configs:
      - role: endpoints
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https
    
    # Kubernetes Nodes
    - job_name: 'kubernetes-nodes'
      kubernetes_sd_configs:
      - role: node
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - target_label: __address__
        replacement: kubernetes.default.svc:443
      - source_labels: [__meta_kubernetes_node_name]
        regex: (.+)
        target_label: __metrics_path__
        replacement: /api/v1/nodes/${1}/proxy/metrics
    
    # Kubernetes Pods
    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name
      - source_labels: [__meta_kubernetes_pod_label_tenant_ecommerce_io_id]
        action: replace
        target_label: tenant_id
    
    # Analytics Service
    - job_name: 'analytics-service'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - production
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app]
        action: keep
        regex: analytics-service
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        target_label: __address__
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
      - source_labels: [__meta_kubernetes_pod_label_tenant_ecommerce_io_id]
        action: replace
        target_label: tenant_id
    
    # Dashboard Service
    - job_name: 'dashboard-service'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - production
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app]
        action: keep
        regex: dashboard-service
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        target_label: __address__
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
    
    # Integration Service
    - job_name: 'integration-service'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - production
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app]
        action: keep
        regex: integration-service
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        target_label: __address__
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
    
    # PostgreSQL/TimescaleDB
    - job_name: 'postgres-exporter'
      static_configs:
      - targets: ['postgres-exporter:9187']
      relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'timescaledb-primary'
    
    # Redis
    - job_name: 'redis-exporter'
      static_configs:
      - targets: ['redis-exporter:9121']
    
    # Nginx Ingress
    - job_name: 'nginx-ingress'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - ingress-nginx
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
        action: keep
        regex: ingress-nginx
    
    # Node Exporter
    - job_name: 'node-exporter'
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      - source_labels: [__meta_kubernetes_endpoints_name]
        action: keep
        regex: node-exporter
    
    # kube-state-metrics
    - job_name: 'kube-state-metrics'
      static_configs:
      - targets: ['kube-state-metrics:8080']
    
    # cAdvisor
    - job_name: 'cadvisor'
      kubernetes_sd_configs:
      - role: node
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - target_label: __address__
        replacement: kubernetes.default.svc:443
      - source_labels: [__meta_kubernetes_node_name]
        regex: (.+)
        target_label: __metrics_path__
        replacement: /api/v1/nodes/${1}/proxy/metrics/cadvisor

---
# Prometheus Alerting Rules
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: monitoring
data:
  analytics-rules.yml: |
    groups:
    - name: analytics.rules
      rules:
      # High-level SLI/SLO metrics
      - record: analytics:request_rate
        expr: sum(rate(http_requests_total{job="analytics-service"}[5m])) by (tenant_id)
      
      - record: analytics:error_rate
        expr: sum(rate(http_requests_total{job="analytics-service",status=~"5.."}[5m])) by (tenant_id) / sum(rate(http_requests_total{job="analytics-service"}[5m])) by (tenant_id)
      
      - record: analytics:latency_p95
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job="analytics-service"}[5m])) by (le, tenant_id))
      
      - record: analytics:database_query_rate
        expr: sum(rate(database_queries_total{service="analytics"}[5m])) by (tenant_id)
      
      - record: analytics:database_latency_p95
        expr: histogram_quantile(0.95, sum(rate(database_query_duration_seconds_bucket{service="analytics"}[5m])) by (le, tenant_id))
      
      # Resource utilization
      - record: analytics:cpu_utilization
        expr: sum(rate(container_cpu_usage_seconds_total{pod=~"analytics-service-.*"}[5m])) by (pod, tenant_id) / sum(container_spec_cpu_quota{pod=~"analytics-service-.*"} / container_spec_cpu_period{pod=~"analytics-service-.*"}) by (pod, tenant_id)
      
      - record: analytics:memory_utilization
        expr: sum(container_memory_usage_bytes{pod=~"analytics-service-.*"}) by (pod, tenant_id) / sum(container_spec_memory_limit_bytes{pod=~"analytics-service-.*"}) by (pod, tenant_id)
    
    - name: analytics.alerts
      rules:
      # SLO Alerts
      - alert: AnalyticsHighErrorRate
        expr: analytics:error_rate > 0.05
        for: 5m
        labels:
          severity: critical
          service: analytics
        annotations:
          summary: "High error rate in analytics service"
          description: "Analytics service error rate is {{ $value | humanizePercentage }} for tenant {{ $labels.tenant_id }}"
      
      - alert: AnalyticsHighLatency
        expr: analytics:latency_p95 > 2
        for: 5m
        labels:
          severity: warning
          service: analytics
        annotations:
          summary: "High latency in analytics service"
          description: "Analytics service P95 latency is {{ $value }}s for tenant {{ $labels.tenant_id }}"
      
      - alert: AnalyticsDatabaseSlowQueries
        expr: analytics:database_latency_p95 > 5
        for: 5m
        labels:
          severity: warning
          service: analytics
        annotations:
          summary: "Slow database queries in analytics service"
          description: "Database query P95 latency is {{ $value }}s for tenant {{ $labels.tenant_id }}"
      
      # Resource Alerts
      - alert: AnalyticsHighCPUUsage
        expr: analytics:cpu_utilization > 0.8
        for: 10m
        labels:
          severity: warning
          service: analytics
        annotations:
          summary: "High CPU usage in analytics service"
          description: "Analytics service CPU usage is {{ $value | humanizePercentage }} on pod {{ $labels.pod }}"
      
      - alert: AnalyticsHighMemoryUsage
        expr: analytics:memory_utilization > 0.9
        for: 5m
        labels:
          severity: critical
          service: analytics
        annotations:
          summary: "High memory usage in analytics service"
          description: "Analytics service memory usage is {{ $value | humanizePercentage }} on pod {{ $labels.pod }}"
      
      # Infrastructure Alerts
      - alert: AnalyticsServiceDown
        expr: up{job="analytics-service"} == 0
        for: 1m
        labels:
          severity: critical
          service: analytics
        annotations:
          summary: "Analytics service is down"
          description: "Analytics service instance {{ $labels.instance }} is down"
      
      - alert: AnalyticsLowReplicas
        expr: kube_deployment_status_replicas_available{deployment="analytics-service"} < 2
        for: 5m
        labels:
          severity: warning
          service: analytics
        annotations:
          summary: "Low number of analytics service replicas"
          description: "Only {{ $value }} analytics service replicas are available"

  database-rules.yml: |
    groups:
    - name: database.rules
      rules:
      # TimescaleDB specific metrics
      - record: timescaledb:connection_utilization
        expr: pg_stat_database_numbackends / pg_settings_max_connections
      
      - record: timescaledb:query_rate
        expr: rate(pg_stat_database_xact_commit[5m]) + rate(pg_stat_database_xact_rollback[5m])
      
      - record: timescaledb:cache_hit_ratio
        expr: pg_stat_database_blks_hit / (pg_stat_database_blks_hit + pg_stat_database_blks_read)
      
      - record: timescaledb:replication_lag
        expr: pg_replication_lag_seconds
    
    - name: database.alerts
      rules:
      - alert: DatabaseHighConnections
        expr: timescaledb:connection_utilization > 0.8
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "High database connection usage"
          description: "Database connection usage is {{ $value | humanizePercentage }}"
      
      - alert: DatabaseLowCacheHitRatio
        expr: timescaledb:cache_hit_ratio < 0.9
        for: 10m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "Low database cache hit ratio"
          description: "Database cache hit ratio is {{ $value | humanizePercentage }}"
      
      - alert: DatabaseReplicationLag
        expr: timescaledb:replication_lag > 60
        for: 5m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "High database replication lag"
          description: "Database replication lag is {{ $value }}s"

---
# AlertManager Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.sendgrid.net:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: 'apikey'
      smtp_auth_password: '${SENDGRID_API_KEY}'
    
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default'
      routes:
      - match:
          severity: critical
        receiver: 'critical-alerts'
        group_wait: 5s
        repeat_interval: 30m
      - match:
          service: analytics
        receiver: 'analytics-team'
      - match:
          service: database
        receiver: 'database-team'
      - match_re:
          tenant_id: '.+'
        receiver: 'tenant-alerts'
    
    receivers:
    - name: 'default'
      email_configs:
      - to: '<EMAIL>'
        subject: '[{{ .Status | toUpper }}] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}
    
    - name: 'critical-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          CRITICAL ALERT TRIGGERED
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Time: {{ .StartsAt }}
          {{ end }}
      slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#critical-alerts'
        title: 'Critical Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          {{ end }}
    
    - name: 'analytics-team'
      email_configs:
      - to: '<EMAIL>'
        subject: '[Analytics] {{ .GroupLabels.alertname }}'
    
    - name: 'database-team'
      email_configs:
      - to: '<EMAIL>'
        subject: '[Database] {{ .GroupLabels.alertname }}'
    
    - name: 'tenant-alerts'
      webhook_configs:
      - url: 'http://tenant-notification-service:8080/alerts'
        send_resolved: true
        http_config:
          bearer_token: '${TENANT_WEBHOOK_TOKEN}'

---
# Grafana Datasource Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: monitoring
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus:9090
      isDefault: true
      editable: true
    - name: Loki
      type: loki
      access: proxy
      url: http://loki:3100
      editable: true
    - name: Jaeger
      type: jaeger
      access: proxy
      url: http://jaeger-query:16686
      editable: true
    - name: TimescaleDB
      type: postgres
      access: proxy
      url: timescaledb-primary:5432
      database: ecommerce_analytics
      user: grafana_readonly
      secureJsonData:
        password: '${GRAFANA_DB_PASSWORD}'
      jsonData:
        sslmode: require
        postgresVersion: 1300
        timescaledb: true
