#!/bin/bash

# Multi-tenant Provisioning Script
# Creates isolated Kubernetes namespaces and resources for new tenants

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMPLATE_DIR="${SCRIPT_DIR}/../k8s/multi-tenant"
TENANT_CONFIG_DIR="${SCRIPT_DIR}/../config/tenants"

# Default values
DEFAULT_REGION="us-east-1"
DEFAULT_PLAN="professional"
DEFAULT_CPU_REQUESTS="500m"
DEFAULT_MEMORY_REQUESTS="1Gi"
DEFAULT_STORAGE_LIMIT="10Gi"

# Logging function
log() {
    local level=$1
    shift
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] [$level] $*" >&2
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Provision a new tenant environment in Kubernetes cluster.

OPTIONS:
    -t, --tenant-id TENANT_ID       Unique tenant identifier (required)
    -p, --plan PLAN                 Tenant plan: basic|professional|enterprise (default: professional)
    -r, --region REGION             AWS region (default: us-east-1)
    -o, --owner OWNER               Tenant owner email (required)
    -b, --billing-id BILLING_ID     Billing system ID (required)
    -d, --dry-run                   Show what would be created without applying
    -v, --verbose                   Enable verbose logging
    -h, --help                      Show this help message

EXAMPLES:
    # Provision enterprise tenant
    $0 -t acme-corp -p enterprise -o <EMAIL> -b bill_123

    # Dry run for basic tenant
    $0 -t startup-inc -p basic -o <EMAIL> -b bill_456 --dry-run

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--tenant-id)
                TENANT_ID="$2"
                shift 2
                ;;
            -p|--plan)
                TENANT_PLAN="$2"
                shift 2
                ;;
            -r|--region)
                TENANT_REGION="$2"
                shift 2
                ;;
            -o|--owner)
                TENANT_OWNER="$2"
                shift 2
                ;;
            -b|--billing-id)
                BILLING_ID="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done

    # Validate required parameters
    if [[ -z "${TENANT_ID:-}" ]]; then
        log "ERROR" "Tenant ID is required"
        usage
        exit 1
    fi

    if [[ -z "${TENANT_OWNER:-}" ]]; then
        log "ERROR" "Tenant owner is required"
        usage
        exit 1
    fi

    if [[ -z "${BILLING_ID:-}" ]]; then
        log "ERROR" "Billing ID is required"
        usage
        exit 1
    fi

    # Set defaults
    TENANT_PLAN="${TENANT_PLAN:-$DEFAULT_PLAN}"
    TENANT_REGION="${TENANT_REGION:-$DEFAULT_REGION}"
    DRY_RUN="${DRY_RUN:-false}"
    VERBOSE="${VERBOSE:-false}"
}

# Validate tenant ID format
validate_tenant_id() {
    if [[ ! "$TENANT_ID" =~ ^[a-z0-9][a-z0-9-]*[a-z0-9]$ ]]; then
        log "ERROR" "Invalid tenant ID format. Must be lowercase alphanumeric with hyphens, starting and ending with alphanumeric"
        exit 1
    fi

    if [[ ${#TENANT_ID} -gt 63 ]]; then
        log "ERROR" "Tenant ID too long. Maximum 63 characters"
        exit 1
    fi
}

# Check if tenant already exists
check_tenant_exists() {
    if kubectl get namespace "tenant-${TENANT_ID}" &>/dev/null; then
        log "ERROR" "Tenant namespace already exists: tenant-${TENANT_ID}"
        exit 1
    fi
}

# Get plan-specific resource limits
get_plan_limits() {
    case "$TENANT_PLAN" in
        basic)
            CPU_REQUESTS_LIMIT="2"
            MEMORY_REQUESTS_LIMIT="4Gi"
            CPU_LIMITS_LIMIT="4"
            MEMORY_LIMITS_LIMIT="8Gi"
            STORAGE_LIMIT="50Gi"
            POD_COUNT_LIMIT="20"
            SERVICE_COUNT_LIMIT="10"
            SECRET_COUNT_LIMIT="20"
            CONFIGMAP_COUNT_LIMIT="20"
            LB_COUNT_LIMIT="2"
            NODEPORT_COUNT_LIMIT="0"
            PVC_COUNT_LIMIT="5"
            DEFAULT_CPU_LIMIT="500m"
            DEFAULT_MEMORY_LIMIT="1Gi"
            DEFAULT_CPU_REQUEST="100m"
            DEFAULT_MEMORY_REQUEST="256Mi"
            MAX_CPU_LIMIT="1"
            MAX_MEMORY_LIMIT="2Gi"
            MIN_REPLICAS="1"
            MAX_REPLICAS="3"
            TENANT_PRIORITY_VALUE="100"
            ;;
        professional)
            CPU_REQUESTS_LIMIT="8"
            MEMORY_REQUESTS_LIMIT="16Gi"
            CPU_LIMITS_LIMIT="16"
            MEMORY_LIMITS_LIMIT="32Gi"
            STORAGE_LIMIT="200Gi"
            POD_COUNT_LIMIT="50"
            SERVICE_COUNT_LIMIT="25"
            SECRET_COUNT_LIMIT="50"
            CONFIGMAP_COUNT_LIMIT="50"
            LB_COUNT_LIMIT="5"
            NODEPORT_COUNT_LIMIT="2"
            PVC_COUNT_LIMIT="15"
            DEFAULT_CPU_LIMIT="1"
            DEFAULT_MEMORY_LIMIT="2Gi"
            DEFAULT_CPU_REQUEST="250m"
            DEFAULT_MEMORY_REQUEST="512Mi"
            MAX_CPU_LIMIT="4"
            MAX_MEMORY_LIMIT="8Gi"
            MIN_REPLICAS="2"
            MAX_REPLICAS="10"
            TENANT_PRIORITY_VALUE="200"
            ;;
        enterprise)
            CPU_REQUESTS_LIMIT="32"
            MEMORY_REQUESTS_LIMIT="64Gi"
            CPU_LIMITS_LIMIT="64"
            MEMORY_LIMITS_LIMIT="128Gi"
            STORAGE_LIMIT="1Ti"
            POD_COUNT_LIMIT="200"
            SERVICE_COUNT_LIMIT="100"
            SECRET_COUNT_LIMIT="200"
            CONFIGMAP_COUNT_LIMIT="200"
            LB_COUNT_LIMIT="20"
            NODEPORT_COUNT_LIMIT="10"
            PVC_COUNT_LIMIT="50"
            DEFAULT_CPU_LIMIT="2"
            DEFAULT_MEMORY_LIMIT="4Gi"
            DEFAULT_CPU_REQUEST="500m"
            DEFAULT_MEMORY_REQUEST="1Gi"
            MAX_CPU_LIMIT="16"
            MAX_MEMORY_LIMIT="32Gi"
            MIN_REPLICAS="3"
            MAX_REPLICAS="50"
            TENANT_PRIORITY_VALUE="500"
            ;;
        *)
            log "ERROR" "Invalid tenant plan: $TENANT_PLAN. Must be basic, professional, or enterprise"
            exit 1
            ;;
    esac

    # Common limits
    MIN_CPU_REQUEST="50m"
    MIN_MEMORY_REQUEST="128Mi"
    POD_MAX_CPU="$MAX_CPU_LIMIT"
    POD_MAX_MEMORY="$MAX_MEMORY_LIMIT"
    PVC_MAX_STORAGE="100Gi"
    PVC_MIN_STORAGE="1Gi"
    CPU_TARGET_UTILIZATION="70"
    MEMORY_TARGET_UTILIZATION="80"
}

# Generate tenant configuration
generate_tenant_config() {
    local config_file="${TENANT_CONFIG_DIR}/${TENANT_ID}.yaml"
    
    mkdir -p "$TENANT_CONFIG_DIR"
    
    cat > "$config_file" << EOF
# Tenant Configuration for ${TENANT_ID}
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-${TENANT_ID}-config
  namespace: tenant-${TENANT_ID}
  labels:
    tenant.ecommerce.io/id: ${TENANT_ID}
data:
  tenant_id: "${TENANT_ID}"
  tenant_plan: "${TENANT_PLAN}"
  tenant_region: "${TENANT_REGION}"
  tenant_owner: "${TENANT_OWNER}"
  billing_id: "${BILLING_ID}"
  created_at: "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
  
  # Database configuration
  database_name: "tenant_${TENANT_ID//-/_}"
  database_schema: "tenant_${TENANT_ID//-/_}"
  
  # Analytics configuration
  analytics_retention_days: "$(case $TENANT_PLAN in basic) echo 90;; professional) echo 365;; enterprise) echo 1095;; esac)"
  max_api_requests_per_hour: "$(case $TENANT_PLAN in basic) echo 1000;; professional) echo 10000;; enterprise) echo 100000;; esac)"
  max_data_export_size_mb: "$(case $TENANT_PLAN in basic) echo 100;; professional) echo 1000;; enterprise) echo 10000;; esac)"
  
  # Feature flags
  advanced_analytics_enabled: "$(case $TENANT_PLAN in basic) echo false;; *) echo true;; esac)"
  real_time_streaming_enabled: "$(case $TENANT_PLAN in enterprise) echo true;; *) echo false;; esac)"
  custom_integrations_enabled: "$(case $TENANT_PLAN in enterprise) echo true;; *) echo false;; esac)"
EOF

    log "INFO" "Generated tenant configuration: $config_file"
}

# Apply tenant resources
apply_tenant_resources() {
    local temp_file
    temp_file=$(mktemp)
    
    # Substitute variables in template
    envsubst < "${TEMPLATE_DIR}/namespace-template.yaml" > "$temp_file"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "DRY RUN: Would apply the following resources:"
        echo "---"
        cat "$temp_file"
        echo "---"
    else
        log "INFO" "Applying tenant resources for ${TENANT_ID}..."
        kubectl apply -f "$temp_file"
        
        # Apply tenant configuration
        kubectl apply -f "${TENANT_CONFIG_DIR}/${TENANT_ID}.yaml"
        
        log "INFO" "Successfully created tenant namespace and resources"
    fi
    
    rm -f "$temp_file"
}

# Create tenant database schema
create_tenant_database() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "DRY RUN: Would create database schema for tenant ${TENANT_ID}"
        return
    fi

    log "INFO" "Creating database schema for tenant ${TENANT_ID}..."
    
    # Get database connection details from shared services
    local db_host db_name db_user db_password
    db_host=$(kubectl get secret -n shared-services postgres-credentials -o jsonpath='{.data.host}' | base64 -d)
    db_name=$(kubectl get secret -n shared-services postgres-credentials -o jsonpath='{.data.database}' | base64 -d)
    db_user=$(kubectl get secret -n shared-services postgres-credentials -o jsonpath='{.data.username}' | base64 -d)
    db_password=$(kubectl get secret -n shared-services postgres-credentials -o jsonpath='{.data.password}' | base64 -d)
    
    # Create tenant-specific database schema
    PGPASSWORD="$db_password" psql -h "$db_host" -U "$db_user" -d "$db_name" << EOF
-- Create tenant schema
CREATE SCHEMA IF NOT EXISTS tenant_${TENANT_ID//-/_};

-- Set search path for tenant
ALTER ROLE ${db_user} SET search_path TO tenant_${TENANT_ID//-/_}, public;

-- Grant permissions
GRANT USAGE ON SCHEMA tenant_${TENANT_ID//-/_} TO ${db_user};
GRANT CREATE ON SCHEMA tenant_${TENANT_ID//-/_} TO ${db_user};

-- Create tenant-specific tables (if needed)
-- This would typically be handled by application migrations
EOF

    log "INFO" "Database schema created successfully"
}

# Verify tenant deployment
verify_deployment() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "DRY RUN: Skipping deployment verification"
        return
    fi

    log "INFO" "Verifying tenant deployment..."
    
    # Wait for namespace to be ready
    kubectl wait --for=condition=Ready namespace/tenant-${TENANT_ID} --timeout=60s
    
    # Check resource quota
    kubectl get resourcequota -n tenant-${TENANT_ID}
    
    # Check network policy
    kubectl get networkpolicy -n tenant-${TENANT_ID}
    
    # Check service account
    kubectl get serviceaccount -n tenant-${TENANT_ID}
    
    log "INFO" "Tenant deployment verified successfully"
}

# Main function
main() {
    log "INFO" "Starting tenant provisioning process..."
    
    parse_args "$@"
    validate_tenant_id
    check_tenant_exists
    get_plan_limits
    
    # Set environment variables for template substitution
    export TENANT_ID TENANT_PLAN TENANT_REGION TENANT_OWNER BILLING_ID
    export CREATION_TIMESTAMP=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    export CPU_REQUESTS_LIMIT MEMORY_REQUESTS_LIMIT CPU_LIMITS_LIMIT MEMORY_LIMITS_LIMIT
    export STORAGE_LIMIT POD_COUNT_LIMIT SERVICE_COUNT_LIMIT SECRET_COUNT_LIMIT
    export CONFIGMAP_COUNT_LIMIT LB_COUNT_LIMIT NODEPORT_COUNT_LIMIT PVC_COUNT_LIMIT
    export DEFAULT_CPU_LIMIT DEFAULT_MEMORY_LIMIT DEFAULT_CPU_REQUEST DEFAULT_MEMORY_REQUEST
    export MAX_CPU_LIMIT MAX_MEMORY_LIMIT MIN_CPU_REQUEST MIN_MEMORY_REQUEST
    export POD_MAX_CPU POD_MAX_MEMORY PVC_MAX_STORAGE PVC_MIN_STORAGE
    export MIN_REPLICAS MAX_REPLICAS TENANT_PRIORITY_VALUE
    export CPU_TARGET_UTILIZATION MEMORY_TARGET_UTILIZATION
    
    if [[ "$VERBOSE" == "true" ]]; then
        log "INFO" "Tenant configuration:"
        log "INFO" "  ID: $TENANT_ID"
        log "INFO" "  Plan: $TENANT_PLAN"
        log "INFO" "  Region: $TENANT_REGION"
        log "INFO" "  Owner: $TENANT_OWNER"
        log "INFO" "  Billing ID: $BILLING_ID"
        log "INFO" "  CPU Limit: $CPU_LIMITS_LIMIT"
        log "INFO" "  Memory Limit: $MEMORY_LIMITS_LIMIT"
        log "INFO" "  Storage Limit: $STORAGE_LIMIT"
    fi
    
    generate_tenant_config
    apply_tenant_resources
    create_tenant_database
    verify_deployment
    
    if [[ "$DRY_RUN" == "false" ]]; then
        log "INFO" "Tenant provisioning completed successfully!"
        log "INFO" "Tenant ${TENANT_ID} is ready for use"
        log "INFO" "Namespace: tenant-${TENANT_ID}"
        log "INFO" "Plan: ${TENANT_PLAN}"
        log "INFO" "Resource limits applied based on plan"
    else
        log "INFO" "DRY RUN completed. No resources were created."
    fi
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
