# Advanced Security Policies for Production Kubernetes Deployment
# Implements comprehensive security controls including Pod Security Standards,
# Network Policies, RBAC, and compliance frameworks

apiVersion: v1
kind: Namespace
metadata:
  name: security-system
  labels:
    app.kubernetes.io/name: security-system
    security.kubernetes.io/enforce: restricted

---
# Pod Security Standards - Restricted Policy
apiVersion: v1
kind: ConfigMap
metadata:
  name: pod-security-standards
  namespace: security-system
data:
  restricted-policy.yaml: |
    apiVersion: kyverno.io/v1
    kind: ClusterPolicy
    metadata:
      name: pod-security-restricted
      annotations:
        policies.kyverno.io/title: Pod Security Standards (Restricted)
        policies.kyverno.io/category: Pod Security Standards
        policies.kyverno.io/severity: high
        policies.kyverno.io/description: >-
          Implements the restricted Pod Security Standards profile.
    spec:
      validationFailureAction: enforce
      background: true
      rules:
      - name: check-securitycontext
        match:
          any:
          - resources:
              kinds:
              - Pod
        validate:
          message: "Pod must run as non-root user"
          pattern:
            spec:
              securityContext:
                runAsNonRoot: true
                runAsUser: ">0"
                fsGroup: ">0"
              containers:
              - name: "*"
                securityContext:
                  allowPrivilegeEscalation: false
                  readOnlyRootFilesystem: true
                  runAsNonRoot: true
                  capabilities:
                    drop:
                    - ALL
      
      - name: check-volumes
        match:
          any:
          - resources:
              kinds:
              - Pod
        validate:
          message: "Only allowed volume types permitted"
          pattern:
            spec:
              volumes:
              - name: "*"
                =(configMap): {}
                =(secret): {}
                =(emptyDir): {}
                =(persistentVolumeClaim): {}
                =(projected): {}
                =(downwardAPI): {}

---
# Network Security Policies
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-analytics-service
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: analytics-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from dashboard service
  - from:
    - podSelector:
        matchLabels:
          app: dashboard-service
    ports:
    - protocol: TCP
      port: 8080
  # Allow traffic from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
  egress:
  # Allow traffic to database
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
  # Allow HTTPS to external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443

---
# Advanced RBAC Configuration
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: security-auditor
rules:
# Read-only access to security-related resources
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "statefulsets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["policy"]
  resources: ["podsecuritypolicies"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["rbac.authorization.k8s.io"]
  resources: ["roles", "rolebindings", "clusterroles", "clusterrolebindings"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: compliance-officer
rules:
# Compliance-specific permissions
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["audit.k8s.io"]
  resources: ["events"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods", "nodes"]
  verbs: ["get", "list"]

---
# Security Monitoring and Alerting
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-monitoring-rules
  namespace: security-system
data:
  security-alerts.yaml: |
    groups:
    - name: security.rules
      rules:
      # Privileged container detection
      - alert: PrivilegedContainerRunning
        expr: kube_pod_container_status_running{container=~".*"} and on(pod, namespace) kube_pod_spec_containers_security_context_privileged == 1
        for: 0m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "Privileged container detected"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is running a privileged container"
      
      # Root user detection
      - alert: ContainerRunningAsRoot
        expr: kube_pod_container_status_running{container=~".*"} and on(pod, namespace) kube_pod_spec_containers_security_context_run_as_user == 0
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Container running as root user"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is running as root user"
      
      # Excessive failed login attempts
      - alert: ExcessiveFailedLogins
        expr: increase(failed_login_attempts_total[5m]) > 10
        for: 1m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "Excessive failed login attempts"
          description: "More than 10 failed login attempts in the last 5 minutes from {{ $labels.source_ip }}"
      
      # Suspicious API access patterns
      - alert: SuspiciousAPIAccess
        expr: rate(http_requests_total{status=~"4..|5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "High error rate detected"
          description: "High error rate ({{ $value }}) detected for {{ $labels.method }} {{ $labels.endpoint }}"
      
      # Data exfiltration detection
      - alert: LargeDataTransfer
        expr: rate(http_response_size_bytes[5m]) > 100000000  # 100MB/5min
        for: 5m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "Large data transfer detected"
          description: "Large data transfer rate detected: {{ $value }} bytes/sec"

---
# Compliance Scanning Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: compliance-scan-config
  namespace: security-system
data:
  compliance-profiles.yaml: |
    # CIS Kubernetes Benchmark
    cis-kubernetes:
      name: "CIS Kubernetes Benchmark v1.6.1"
      rules:
        - id: "1.1.1"
          description: "Ensure that the API server pod specification file permissions are set to 644 or more restrictive"
          check: "file_permissions"
          target: "/etc/kubernetes/manifests/kube-apiserver.yaml"
          expected: "644"
        
        - id: "1.2.1"
          description: "Ensure that the --anonymous-auth argument is set to false"
          check: "process_argument"
          process: "kube-apiserver"
          argument: "--anonymous-auth"
          expected: "false"
        
        - id: "2.1.1"
          description: "Ensure that the --cert-file and --key-file arguments are set as appropriate"
          check: "process_argument"
          process: "etcd"
          argument: "--cert-file"
          required: true
    
    # SOC 2 Type II Controls
    soc2-type2:
      name: "SOC 2 Type II Security Controls"
      controls:
        - id: "CC6.1"
          description: "Logical and physical access controls"
          checks:
            - "rbac_enabled"
            - "network_policies_enforced"
            - "pod_security_standards"
        
        - id: "CC6.2"
          description: "Authentication and authorization"
          checks:
            - "strong_authentication"
            - "mfa_enabled"
            - "session_management"
        
        - id: "CC6.3"
          description: "System access monitoring"
          checks:
            - "audit_logging_enabled"
            - "security_monitoring"
            - "incident_response"

---
# Data Encryption Configuration
apiVersion: v1
kind: Secret
metadata:
  name: encryption-keys
  namespace: security-system
type: Opaque
data:
  # These would be actual encryption keys in production
  database-encryption-key: <BASE64_ENCODED_KEY>
  application-encryption-key: <BASE64_ENCODED_KEY>
  backup-encryption-key: <BASE64_ENCODED_KEY>

---
# Secrets Management with External Secrets Operator
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: aws-secrets-manager
  namespace: security-system
spec:
  provider:
    aws:
      service: SecretsManager
      region: us-east-1
      auth:
        jwt:
          serviceAccountRef:
            name: external-secrets-sa

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: database-credentials
  namespace: production
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets-manager
    kind: SecretStore
  target:
    name: database-credentials
    creationPolicy: Owner
  data:
  - secretKey: username
    remoteRef:
      key: prod/database/credentials
      property: username
  - secretKey: password
    remoteRef:
      key: prod/database/credentials
      property: password

---
# Security Scanning CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: security-scan
  namespace: security-system
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: security-scanner
          containers:
          - name: trivy-scanner
            image: aquasec/trivy:latest
            command:
            - /bin/sh
            - -c
            - |
              # Scan all images in the cluster
              kubectl get pods --all-namespaces -o jsonpath='{range .items[*]}{.spec.containers[*].image}{"\n"}{end}' | sort -u | while read image; do
                echo "Scanning image: $image"
                trivy image --format json --output /reports/$(echo $image | tr '/' '_' | tr ':' '_').json $image
              done
              
              # Scan cluster configuration
              trivy k8s --format json --output /reports/cluster-scan.json cluster
            volumeMounts:
            - name: reports
              mountPath: /reports
            - name: kubeconfig
              mountPath: /root/.kube
          volumes:
          - name: reports
            persistentVolumeClaim:
              claimName: security-reports-pvc
          - name: kubeconfig
            secret:
              secretName: scanner-kubeconfig
          restartPolicy: OnFailure

---
# Admission Controller for Security Policies
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingAdmissionWebhook
metadata:
  name: security-policy-validator
webhooks:
- name: security.ecommerce-analytics.com
  clientConfig:
    service:
      name: security-policy-webhook
      namespace: security-system
      path: "/validate"
  rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  - operations: ["CREATE", "UPDATE"]
    apiGroups: ["apps"]
    apiVersions: ["v1"]
    resources: ["deployments", "statefulsets"]
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail

---
# Security Policy Webhook Service
apiVersion: v1
kind: Service
metadata:
  name: security-policy-webhook
  namespace: security-system
spec:
  selector:
    app: security-policy-webhook
  ports:
  - port: 443
    targetPort: 8443
    protocol: TCP

---
# Security Metrics Collection
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-metrics-config
  namespace: security-system
data:
  metrics.yaml: |
    # Security-specific metrics to collect
    metrics:
      - name: failed_authentication_attempts
        type: counter
        description: "Number of failed authentication attempts"
        labels: ["source_ip", "user_agent", "endpoint"]
      
      - name: privilege_escalation_attempts
        type: counter
        description: "Number of privilege escalation attempts"
        labels: ["user_id", "target_resource", "action"]
      
      - name: data_access_violations
        type: counter
        description: "Number of unauthorized data access attempts"
        labels: ["user_id", "resource", "tenant_id"]
      
      - name: encryption_key_rotations
        type: counter
        description: "Number of encryption key rotations"
        labels: ["key_type", "rotation_reason"]
      
      - name: compliance_violations
        type: counter
        description: "Number of compliance policy violations"
        labels: ["policy", "severity", "resource_type"]

---
# Incident Response Automation
apiVersion: v1
kind: ConfigMap
metadata:
  name: incident-response-playbooks
  namespace: security-system
data:
  security-incident-response.yaml: |
    playbooks:
      - name: "Privilege Escalation Detection"
        trigger: "PrivilegeEscalationAttempt"
        actions:
          - type: "isolate_user"
            parameters:
              user_id: "{{ .Labels.user_id }}"
              duration: "1h"
          - type: "notify_security_team"
            parameters:
              severity: "high"
              message: "Privilege escalation attempt detected"
          - type: "collect_forensics"
            parameters:
              target: "{{ .Labels.source_ip }}"
              duration: "24h"
      
      - name: "Data Exfiltration Response"
        trigger: "LargeDataTransfer"
        actions:
          - type: "rate_limit_user"
            parameters:
              user_id: "{{ .Labels.user_id }}"
              limit: "1MB/min"
              duration: "30m"
          - type: "alert_compliance_team"
            parameters:
              severity: "critical"
              data_volume: "{{ .Value }}"
          - type: "audit_data_access"
            parameters:
              user_id: "{{ .Labels.user_id }}"
              timeframe: "24h"
