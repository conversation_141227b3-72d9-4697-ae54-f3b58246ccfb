# Advanced Threat Detection and Incident Response System
# Implements real-time threat detection, automated response, and forensic capabilities

apiVersion: v1
kind: Namespace
metadata:
  name: threat-detection
  labels:
    app.kubernetes.io/name: threat-detection
    security.ecommerce.io/component: siem

---
# Threat Detection Engine
apiVersion: apps/v1
kind: Deployment
metadata:
  name: threat-detection-engine
  namespace: threat-detection
  labels:
    app: threat-detection-engine
spec:
  replicas: 2
  selector:
    matchLabels:
      app: threat-detection-engine
  template:
    metadata:
      labels:
        app: threat-detection-engine
    spec:
      serviceAccountName: threat-detector
      containers:
      - name: detection-engine
        image: ecommerce-analytics/threat-detector:latest
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: ELASTICSEARCH_URL
          value: "http://elasticsearch:9200"
        - name: KAFKA_BROKERS
          value: "kafka:9092"
        - name: REDIS_URL
          value: "redis://redis:6379"
        - name: ML_MODEL_ENDPOINT
          value: "http://ml-service:8080/predict"
        - name: ALERT_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: threat-detection-secrets
              key: alert-webhook-url
        volumeMounts:
        - name: detection-rules
          mountPath: /etc/detection-rules
        - name: ml-models
          mountPath: /models
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: detection-rules
        configMap:
          name: threat-detection-rules
      - name: ml-models
        persistentVolumeClaim:
          claimName: ml-models-pvc

---
# Threat Detection Rules
apiVersion: v1
kind: ConfigMap
metadata:
  name: threat-detection-rules
  namespace: threat-detection
data:
  detection-rules.yaml: |
    rules:
      # Authentication-based threats
      - id: "AUTH001"
        name: "Brute Force Attack"
        category: "authentication"
        severity: "high"
        description: "Multiple failed login attempts from same source"
        query: |
          SELECT source_ip, COUNT(*) as attempts
          FROM auth_logs 
          WHERE event_type = 'login_failed' 
            AND timestamp > NOW() - INTERVAL '5 minutes'
          GROUP BY source_ip 
          HAVING COUNT(*) > 10
        actions:
          - "block_ip"
          - "alert_security_team"
          - "increase_monitoring"
      
      - id: "AUTH002"
        name: "Credential Stuffing"
        category: "authentication"
        severity: "high"
        description: "Multiple failed logins across different accounts from same source"
        query: |
          SELECT source_ip, COUNT(DISTINCT username) as unique_users
          FROM auth_logs 
          WHERE event_type = 'login_failed' 
            AND timestamp > NOW() - INTERVAL '10 minutes'
          GROUP BY source_ip 
          HAVING COUNT(DISTINCT username) > 20
        actions:
          - "block_ip"
          - "alert_security_team"
          - "trigger_captcha"
      
      # Data access threats
      - id: "DATA001"
        name: "Unusual Data Access Pattern"
        category: "data_access"
        severity: "medium"
        description: "User accessing unusual amount of data"
        query: |
          SELECT user_id, SUM(data_size_bytes) as total_accessed
          FROM data_access_logs 
          WHERE timestamp > NOW() - INTERVAL '1 hour'
          GROUP BY user_id 
          HAVING SUM(data_size_bytes) > *********  -- 100MB
        actions:
          - "alert_security_team"
          - "increase_user_monitoring"
          - "require_additional_auth"
      
      - id: "DATA002"
        name: "Cross-Tenant Data Access"
        category: "data_access"
        severity: "critical"
        description: "User attempting to access data from different tenant"
        query: |
          SELECT user_id, user_tenant_id, accessed_tenant_id
          FROM data_access_logs 
          WHERE user_tenant_id != accessed_tenant_id
            AND timestamp > NOW() - INTERVAL '5 minutes'
        actions:
          - "block_user"
          - "alert_security_team"
          - "trigger_incident_response"
      
      # Network-based threats
      - id: "NET001"
        name: "DDoS Attack"
        category: "network"
        severity: "high"
        description: "High volume of requests from multiple sources"
        query: |
          SELECT COUNT(*) as request_count
          FROM access_logs 
          WHERE timestamp > NOW() - INTERVAL '1 minute'
          HAVING COUNT(*) > 10000
        actions:
          - "enable_rate_limiting"
          - "alert_security_team"
          - "scale_infrastructure"
      
      # Anomaly detection using ML
      - id: "ML001"
        name: "Behavioral Anomaly"
        category: "anomaly"
        severity: "medium"
        description: "ML model detected unusual user behavior"
        ml_model: "user_behavior_anomaly"
        threshold: 0.8
        actions:
          - "alert_security_team"
          - "increase_user_monitoring"
          - "log_detailed_activity"

  response-actions.yaml: |
    actions:
      block_ip:
        type: "network"
        implementation: "iptables"
        parameters:
          duration: "1h"
          rule: "DROP"
        
      block_user:
        type: "application"
        implementation: "user_management"
        parameters:
          duration: "24h"
          notify_user: true
        
      alert_security_team:
        type: "notification"
        implementation: "webhook"
        parameters:
          webhook_url: "${ALERT_WEBHOOK_URL}"
          severity_mapping:
            critical: "@channel"
            high: "@security-team"
            medium: "#security-alerts"
        
      trigger_incident_response:
        type: "workflow"
        implementation: "incident_management"
        parameters:
          playbook: "security_incident"
          auto_assign: true
          escalation_timeout: "15m"
        
      enable_rate_limiting:
        type: "infrastructure"
        implementation: "nginx_rate_limit"
        parameters:
          rate: "100r/m"
          burst: "200"
          duration: "30m"

---
# Behavioral Analytics ML Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: behavioral-analytics
  namespace: threat-detection
  labels:
    app: behavioral-analytics
spec:
  replicas: 1
  selector:
    matchLabels:
      app: behavioral-analytics
  template:
    metadata:
      labels:
        app: behavioral-analytics
    spec:
      containers:
      - name: ml-service
        image: ecommerce-analytics/behavioral-ml:latest
        ports:
        - containerPort: 8080
        env:
        - name: MODEL_PATH
          value: "/models/user_behavior_model.pkl"
        - name: FEATURE_STORE_URL
          value: "http://feature-store:8080"
        - name: PREDICTION_THRESHOLD
          value: "0.8"
        volumeMounts:
        - name: ml-models
          mountPath: /models
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: ml-models
        persistentVolumeClaim:
          claimName: ml-models-pvc

---
# Incident Response Automation
apiVersion: apps/v1
kind: Deployment
metadata:
  name: incident-response-orchestrator
  namespace: threat-detection
  labels:
    app: incident-response-orchestrator
spec:
  replicas: 1
  selector:
    matchLabels:
      app: incident-response-orchestrator
  template:
    metadata:
      labels:
        app: incident-response-orchestrator
    spec:
      serviceAccountName: incident-responder
      containers:
      - name: orchestrator
        image: ecommerce-analytics/incident-orchestrator:latest
        ports:
        - containerPort: 8080
        env:
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: SLACK_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: threat-detection-secrets
              key: slack-webhook-url
        - name: PAGERDUTY_API_KEY
          valueFrom:
            secretKeyRef:
              name: threat-detection-secrets
              key: pagerduty-api-key
        volumeMounts:
        - name: playbooks
          mountPath: /etc/playbooks
        - name: forensics-storage
          mountPath: /forensics
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: playbooks
        configMap:
          name: incident-response-playbooks
      - name: forensics-storage
        persistentVolumeClaim:
          claimName: forensics-pvc

---
# Incident Response Playbooks
apiVersion: v1
kind: ConfigMap
metadata:
  name: incident-response-playbooks
  namespace: threat-detection
data:
  security-incident.yaml: |
    playbook:
      name: "Security Incident Response"
      version: "1.0"
      trigger_conditions:
        - "severity >= high"
        - "category in [authentication, data_access, network]"
      
      phases:
        - name: "Detection and Analysis"
          timeout: "15m"
          steps:
            - name: "Collect Initial Evidence"
              action: "collect_logs"
              parameters:
                timeframe: "1h"
                sources: ["auth_logs", "access_logs", "audit_logs"]
            
            - name: "Assess Impact"
              action: "impact_assessment"
              parameters:
                affected_systems: "auto_detect"
                data_classification: "check"
            
            - name: "Classify Incident"
              action: "classify_incident"
              parameters:
                severity_matrix: "nist_framework"
        
        - name: "Containment"
          timeout: "30m"
          steps:
            - name: "Isolate Affected Systems"
              action: "network_isolation"
              condition: "severity == critical"
              parameters:
                isolation_type: "network_segment"
            
            - name: "Block Malicious IPs"
              action: "ip_blocking"
              parameters:
                duration: "24h"
                scope: "global"
            
            - name: "Disable Compromised Accounts"
              action: "account_disable"
              parameters:
                require_approval: false
                notification: true
        
        - name: "Eradication and Recovery"
          timeout: "2h"
          steps:
            - name: "Remove Malicious Artifacts"
              action: "artifact_removal"
              parameters:
                scan_type: "comprehensive"
            
            - name: "Patch Vulnerabilities"
              action: "vulnerability_patching"
              parameters:
                priority: "critical_high"
            
            - name: "Restore Services"
              action: "service_restoration"
              parameters:
                validation_required: true
        
        - name: "Post-Incident Activities"
          timeout: "1w"
          steps:
            - name: "Forensic Analysis"
              action: "forensic_collection"
              parameters:
                preserve_evidence: true
                analysis_depth: "detailed"
            
            - name: "Lessons Learned"
              action: "post_incident_review"
              parameters:
                stakeholders: ["security_team", "engineering", "management"]
            
            - name: "Update Security Controls"
              action: "control_updates"
              parameters:
                based_on_findings: true

---
# Forensics Collection Service
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: forensics-collector
  namespace: threat-detection
  labels:
    app: forensics-collector
spec:
  selector:
    matchLabels:
      app: forensics-collector
  template:
    metadata:
      labels:
        app: forensics-collector
    spec:
      serviceAccountName: forensics-collector
      hostNetwork: true
      hostPID: true
      containers:
      - name: collector
        image: ecommerce-analytics/forensics-collector:latest
        securityContext:
          privileged: true
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: COLLECTION_ENDPOINT
          value: "http://forensics-storage:8080/upload"
        volumeMounts:
        - name: host-root
          mountPath: /host
          readOnly: true
        - name: host-proc
          mountPath: /host/proc
          readOnly: true
        - name: host-sys
          mountPath: /host/sys
          readOnly: true
        - name: forensics-temp
          mountPath: /tmp/forensics
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
      volumes:
      - name: host-root
        hostPath:
          path: /
      - name: host-proc
        hostPath:
          path: /proc
      - name: host-sys
        hostPath:
          path: /sys
      - name: forensics-temp
        emptyDir:
          sizeLimit: 10Gi

---
# Threat Intelligence Feed
apiVersion: batch/v1
kind: CronJob
metadata:
  name: threat-intel-update
  namespace: threat-detection
spec:
  schedule: "0 */6 * * *"  # Every 6 hours
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: intel-updater
            image: ecommerce-analytics/threat-intel:latest
            command:
            - /bin/sh
            - -c
            - |
              echo "Updating threat intelligence feeds..."
              
              # Update IP reputation lists
              python3 /app/update_ip_reputation.py \
                --sources="alienvault,emergingthreats,malwaredomainlist" \
                --output=/data/ip_reputation.json
              
              # Update malware signatures
              python3 /app/update_malware_signatures.py \
                --output=/data/malware_signatures.json
              
              # Update IOCs (Indicators of Compromise)
              python3 /app/update_iocs.py \
                --sources="misp,opencti,threatconnect" \
                --output=/data/iocs.json
              
              # Reload detection engine
              curl -X POST http://threat-detection-engine:8080/reload-intel
              
              echo "Threat intelligence update completed"
            env:
            - name: THREAT_INTEL_API_KEY
              valueFrom:
                secretKeyRef:
                  name: threat-detection-secrets
                  key: threat-intel-api-key
            volumeMounts:
            - name: intel-data
              mountPath: /data
            resources:
              requests:
                memory: "256Mi"
                cpu: "100m"
              limits:
                memory: "512Mi"
                cpu: "250m"
          volumes:
          - name: intel-data
            persistentVolumeClaim:
              claimName: threat-intel-pvc
          restartPolicy: OnFailure

---
# Storage for ML Models and Forensics
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ml-models-pvc
  namespace: threat-detection
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: forensics-pvc
  namespace: threat-detection
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard
  resources:
    requests:
      storage: 500Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: threat-intel-pvc
  namespace: threat-detection
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: standard
  resources:
    requests:
      storage: 10Gi

---
# Service Accounts and RBAC
apiVersion: v1
kind: ServiceAccount
metadata:
  name: threat-detector
  namespace: threat-detection

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: incident-responder
  namespace: threat-detection

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: forensics-collector
  namespace: threat-detection

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: incident-responder-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "nodes"]
  verbs: ["get", "list", "watch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "update", "patch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies"]
  verbs: ["create", "update", "patch", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: incident-responder-binding
subjects:
- kind: ServiceAccount
  name: incident-responder
  namespace: threat-detection
roleRef:
  kind: ClusterRole
  name: incident-responder-role
  apiGroup: rbac.authorization.k8s.io
