# Production Environment Configuration

# Environment
environment = "prod"
aws_region  = "us-east-1"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"
private_subnet_cidrs = ["10.0.1.0/24", "10.0.2.0/24", "10.0.3.0/24"]
public_subnet_cidrs  = ["10.0.101.0/24", "10.0.102.0/24", "10.0.103.0/24"]
database_subnet_cidrs = ["10.0.201.0/24", "10.0.202.0/24", "10.0.203.0/24"]

# EKS Configuration
kubernetes_version = "1.27"
node_instance_types = ["t3.xlarge", "t3.2xlarge"]
node_group_min_size = 3
node_group_max_size = 20
node_group_desired_size = 5

# Spot instances for cost optimization
spot_instance_types = ["t3.large", "t3.xlarge", "t3.2xlarge"]
spot_max_size = 30
spot_desired_size = 5

# RDS Configuration
postgres_version = "14.9"
db_instance_class = "db.r5.xlarge"
db_allocated_storage = 500
db_max_allocated_storage = 5000
db_name = "ecommerce_analytics"
db_username = "postgres"
db_backup_retention_period = 30

# ElastiCache Configuration
redis_version = "7.0"
redis_node_type = "cache.r6g.large"
redis_num_cache_nodes = 3

# Domain Configuration
frontend_domain = "app.yourdomain.com"
api_domain = "api.yourdomain.com"

# Security Configuration
allowed_cidr_blocks = ["0.0.0.0/0"]
enable_waf = true
enable_guard_duty = true

# High Availability
multi_az_deployment = true
enable_auto_scaling = true

# Performance
enable_performance_insights = true
enable_enhanced_monitoring = true

# Monitoring
enable_detailed_monitoring = true
log_retention_days = 90

# Backup
backup_retention_days = 30
backup_schedule = "cron(0 2 * * ? *)"

# Compliance
enable_encryption_at_rest = true
enable_encryption_in_transit = true
enable_audit_logging = true

# Cost Optimization
enable_spot_instances = true
schedule_shutdown = false

# Notifications
notification_email = "<EMAIL>"