#!/bin/bash

# AWS CLI Configuration for Production Deployment
# Sets up AWS CLI with production profile and validates permissions

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROFILE_NAME="ecommerce-analytics-production"
AWS_REGION="us-east-1"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓ $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠ WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗ ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ $1${NC}"
}

# Display configuration options
display_options() {
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                AWS CLI CONFIGURATION OPTIONS                 ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  Choose your AWS authentication method:                      ║
║                                                              ║
║  1. AWS Access Keys (IAM User)                               ║
║     - Best for: Individual developers, CI/CD                 ║
║     - Requires: Access Key ID + Secret Access Key            ║
║                                                              ║
║  2. AWS SSO (Single Sign-On)                                 ║
║     - Best for: Enterprise environments                      ║
║     - Requires: SSO start URL + region                       ║
║                                                              ║
║  3. AWS IAM Role (AssumeRole)                                ║
║     - Best for: Cross-account access                         ║
║     - Requires: Role ARN + external ID                       ║
║                                                              ║
║  4. Use existing default profile                             ║
║     - Use currently configured AWS credentials               ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
}

# Configure AWS Access Keys
configure_access_keys() {
    info "Configuring AWS CLI with Access Keys..."
    
    echo
    echo "Please provide your AWS credentials:"
    read -p "AWS Access Key ID: " access_key_id
    read -s -p "AWS Secret Access Key: " secret_access_key
    echo
    
    # Configure the profile
    aws configure set aws_access_key_id "$access_key_id" --profile "$PROFILE_NAME"
    aws configure set aws_secret_access_key "$secret_access_key" --profile "$PROFILE_NAME"
    aws configure set region "$AWS_REGION" --profile "$PROFILE_NAME"
    aws configure set output "json" --profile "$PROFILE_NAME"
    
    log "AWS profile configured: $PROFILE_NAME"
}

# Configure AWS SSO
configure_sso() {
    info "Configuring AWS CLI with SSO..."
    
    echo
    echo "Please provide your AWS SSO details:"
    read -p "SSO Start URL (e.g., https://my-company.awsapps.com/start): " sso_start_url
    read -p "SSO Region (e.g., us-east-1): " sso_region
    read -p "Account ID: " account_id
    read -p "Role Name (e.g., PowerUserAccess): " role_name
    
    # Configure SSO profile
    aws configure sso --profile "$PROFILE_NAME" << EOF
$sso_start_url
$sso_region
$account_id
$role_name
$AWS_REGION
json
EOF
    
    log "AWS SSO profile configured: $PROFILE_NAME"
    
    # Login to SSO
    info "Logging in to AWS SSO..."
    aws sso login --profile "$PROFILE_NAME"
}

# Configure IAM Role
configure_iam_role() {
    info "Configuring AWS CLI with IAM Role..."
    
    echo
    echo "Please provide your IAM Role details:"
    read -p "Role ARN (e.g., arn:aws:iam::************:role/TerraformDeployRole): " role_arn
    read -p "External ID (optional): " external_id
    read -p "Source Profile (profile with permissions to assume role): " source_profile
    
    # Configure role profile
    aws configure set role_arn "$role_arn" --profile "$PROFILE_NAME"
    aws configure set source_profile "$source_profile" --profile "$PROFILE_NAME"
    aws configure set region "$AWS_REGION" --profile "$PROFILE_NAME"
    aws configure set output "json" --profile "$PROFILE_NAME"
    
    if [[ -n "$external_id" ]]; then
        aws configure set external_id "$external_id" --profile "$PROFILE_NAME"
    fi
    
    log "AWS IAM Role profile configured: $PROFILE_NAME"
}

# Use existing default profile
use_default_profile() {
    info "Using existing default AWS profile..."
    
    # Copy default profile to production profile
    local default_access_key=$(aws configure get aws_access_key_id)
    local default_secret_key=$(aws configure get aws_secret_access_key)
    local default_region=$(aws configure get region)
    
    if [[ -z "$default_access_key" ]]; then
        error "No default AWS profile found. Please configure AWS CLI first."
    fi
    
    aws configure set aws_access_key_id "$default_access_key" --profile "$PROFILE_NAME"
    aws configure set aws_secret_access_key "$default_secret_key" --profile "$PROFILE_NAME"
    aws configure set region "${default_region:-$AWS_REGION}" --profile "$PROFILE_NAME"
    aws configure set output "json" --profile "$PROFILE_NAME"
    
    log "Production profile created from default profile"
}

# Validate AWS configuration
validate_configuration() {
    info "Validating AWS configuration..."
    
    # Test AWS CLI access
    if ! aws sts get-caller-identity --profile "$PROFILE_NAME" &> /dev/null; then
        error "AWS CLI validation failed. Please check your credentials."
    fi
    
    local account_id=$(aws sts get-caller-identity --query Account --output text --profile "$PROFILE_NAME")
    local user_arn=$(aws sts get-caller-identity --query Arn --output text --profile "$PROFILE_NAME")
    local region=$(aws configure get region --profile "$PROFILE_NAME")
    
    log "AWS CLI validation successful"
    log "Account ID: $account_id"
    log "User/Role: $user_arn"
    log "Region: $region"
    log "Profile: $PROFILE_NAME"
}

# Check required IAM permissions
check_permissions() {
    info "Checking required IAM permissions..."
    
    local required_permissions=(
        "s3:CreateBucket"
        "s3:ListBucket"
        "s3:GetObject"
        "s3:PutObject"
        "dynamodb:CreateTable"
        "dynamodb:DescribeTable"
        "ec2:DescribeVpcs"
        "eks:DescribeCluster"
        "rds:DescribeDBInstances"
        "iam:GetRole"
        "secretsmanager:CreateSecret"
        "secretsmanager:GetSecretValue"
    )
    
    local permissions_ok=true
    
    # Test S3 permissions
    if aws s3 ls --profile "$PROFILE_NAME" &> /dev/null; then
        log "S3 permissions: OK"
    else
        warn "S3 permissions: Limited or missing"
        permissions_ok=false
    fi
    
    # Test EC2 permissions
    if aws ec2 describe-vpcs --max-items 1 --profile "$PROFILE_NAME" &> /dev/null; then
        log "EC2 permissions: OK"
    else
        warn "EC2 permissions: Limited or missing"
        permissions_ok=false
    fi
    
    # Test IAM permissions
    if aws iam get-user --profile "$PROFILE_NAME" &> /dev/null || aws sts get-caller-identity --profile "$PROFILE_NAME" | grep -q "assumed-role"; then
        log "IAM permissions: OK"
    else
        warn "IAM permissions: Limited or missing"
        permissions_ok=false
    fi
    
    if [[ "$permissions_ok" == true ]]; then
        log "All required permissions appear to be available"
    else
        warn "Some permissions may be missing. Deployment may fail."
        warn "Ensure your user/role has the following policies:"
        echo "  - PowerUserAccess (recommended) OR"
        echo "  - Custom policy with EC2, S3, RDS, EKS, IAM, Secrets Manager permissions"
    fi
}

# Set environment variables
set_environment_variables() {
    info "Setting environment variables..."
    
    # Export AWS profile for subsequent scripts
    export AWS_PROFILE="$PROFILE_NAME"
    export AWS_REGION="$AWS_REGION"
    
    # Create environment file for scripts
    cat > .env.aws << EOF
# AWS Configuration for Production Deployment
export AWS_PROFILE="$PROFILE_NAME"
export AWS_REGION="$AWS_REGION"
export AWS_DEFAULT_REGION="$AWS_REGION"
EOF
    
    log "Environment variables configured"
    log "AWS_PROFILE=$PROFILE_NAME"
    log "AWS_REGION=$AWS_REGION"
    
    echo
    info "To use this configuration in new terminal sessions, run:"
    echo "  source .env.aws"
}

# Main execution
main() {
    display_options
    
    echo
    read -p "Choose your authentication method (1-4): " choice
    echo
    
    case $choice in
        1)
            configure_access_keys
            ;;
        2)
            configure_sso
            ;;
        3)
            configure_iam_role
            ;;
        4)
            use_default_profile
            ;;
        *)
            error "Invalid choice. Please run the script again."
            ;;
    esac
    
    echo
    validate_configuration
    echo
    check_permissions
    echo
    set_environment_variables
    
    echo
    log "AWS CLI configuration completed successfully!"
    log "You can now proceed with Terraform backend setup"
    
    cat << EOF

╔══════════════════════════════════════════════════════════════╗
║                        NEXT STEPS                            ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  1. Source the environment variables:                        ║
║     source .env.aws                                          ║
║                                                              ║
║  2. Set up Terraform backend:                                ║
║     ./scripts/setup-terraform-backend.sh                     ║
║                                                              ║
║  3. Validate infrastructure:                                 ║
║     ./scripts/validate-infrastructure.sh                     ║
║                                                              ║
║  4. Run complete deployment:                                 ║
║     ./scripts/deploy-production.sh                           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

EOF
}

# Execute main function
main "$@"
