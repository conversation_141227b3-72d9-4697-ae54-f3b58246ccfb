#!/bin/bash

# Production Deployment Orchestration Script
# Coordinates the complete production deployment process

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="production"
AWS_REGION="${AWS_REGION:-us-east-1}"
TERRAFORM_DIR="infrastructure/terraform"
K8S_DIR="infrastructure/k8s/production"
DRY_RUN="${DRY_RUN:-false}"
SKIP_TESTS="${SKIP_TESTS:-false}"
SKIP_VALIDATION="${SKIP_VALIDATION:-false}"

# Deployment phases
PHASE_VALIDATION=false
PHASE_SECRETS=false
PHASE_INFRASTRUCTURE=false
PHASE_APPLICATIONS=false
PHASE_VERIFICATION=false

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓ $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠ WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗ ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ $1${NC}"
}

# Display banner
display_banner() {
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║        🚀 E-COMMERCE ANALYTICS SAAS PRODUCTION DEPLOYMENT    ║
║                                                              ║
║  This script will deploy your complete infrastructure and    ║
║  applications to production. Please ensure you have:        ║
║                                                              ║
║  ✓ AWS credentials configured                                ║
║  ✓ Domain names and DNS configured                           ║
║  ✓ SSL certificates ready                                    ║
║  ✓ External service API keys available                       ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
}

# Confirm deployment
confirm_deployment() {
    if [[ "$DRY_RUN" == "true" ]]; then
        warn "DRY RUN MODE - No actual changes will be made"
        return 0
    fi
    
    echo
    warn "You are about to deploy to PRODUCTION environment!"
    warn "This will create real AWS resources and incur costs."
    echo
    read -p "Are you sure you want to continue? (yes/no): " -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        info "Deployment cancelled by user"
        exit 0
    fi
    
    log "Deployment confirmed by user"
}

# Phase 1: Validation
phase_validation() {
    info "Phase 1: Infrastructure Validation"
    
    if [[ "$SKIP_VALIDATION" == "true" ]]; then
        warn "Skipping validation phase"
        PHASE_VALIDATION=true
        return 0
    fi
    
    # Run infrastructure validation
    if ./scripts/validate-infrastructure.sh; then
        log "Infrastructure validation passed"
        PHASE_VALIDATION=true
    else
        error "Infrastructure validation failed. Please fix issues before proceeding."
    fi
    
    # Run tests if not skipped
    if [[ "$SKIP_TESTS" == "false" ]]; then
        info "Running complete test suite..."
        if ./scripts/run-complete-tests.sh; then
            log "All tests passed"
        else
            error "Tests failed. Please fix issues before proceeding."
        fi
    else
        warn "Skipping test suite"
    fi
}

# Phase 2: Secrets Management
phase_secrets() {
    info "Phase 2: Secrets Management"
    
    # Setup production secrets
    if ./scripts/setup-production-secrets.sh; then
        log "Production secrets configured"
        PHASE_SECRETS=true
    else
        error "Failed to configure production secrets"
    fi
    
    # Verify secrets are accessible
    info "Verifying secrets accessibility..."
    local required_secrets=(
        "ecommerce-analytics/production/database"
        "ecommerce-analytics/production/redis"
        "ecommerce-analytics/production/application"
    )
    
    for secret in "${required_secrets[@]}"; do
        if aws secretsmanager describe-secret --secret-id "$secret" --region "$AWS_REGION" &> /dev/null; then
            log "Secret verified: $secret"
        else
            error "Secret not found: $secret"
        fi
    done
}

# Phase 3: Infrastructure Deployment
phase_infrastructure() {
    info "Phase 3: Infrastructure Deployment"
    
    cd "$TERRAFORM_DIR"
    
    # Initialize Terraform with backend
    info "Initializing Terraform..."
    if [[ "$DRY_RUN" == "true" ]]; then
        terraform init -backend=false
    else
        terraform init
    fi
    
    # Plan infrastructure changes
    info "Planning infrastructure changes..."
    terraform plan -var-file="environments/production.tfvars" -out=tfplan
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "DRY RUN: Infrastructure plan completed"
        PHASE_INFRASTRUCTURE=true
        cd - > /dev/null
        return 0
    fi
    
    # Apply infrastructure changes
    info "Applying infrastructure changes..."
    echo
    warn "This will create real AWS resources and incur costs!"
    read -p "Proceed with infrastructure deployment? (yes/no): " -r
    echo
    
    if [[ $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        if terraform apply tfplan; then
            log "Infrastructure deployment completed"
            PHASE_INFRASTRUCTURE=true
        else
            error "Infrastructure deployment failed"
        fi
    else
        error "Infrastructure deployment cancelled by user"
    fi
    
    cd - > /dev/null
}

# Phase 4: Application Deployment
phase_applications() {
    info "Phase 4: Application Deployment"
    
    # Update kubeconfig for the new EKS cluster
    info "Updating kubeconfig..."
    aws eks update-kubeconfig --name "ecommerce-analytics-production-cluster" --region "$AWS_REGION"
    
    # Install External Secrets Operator
    info "Installing External Secrets Operator..."
    if ! kubectl get namespace external-secrets &> /dev/null; then
        kubectl create namespace external-secrets
    fi
    
    helm repo add external-secrets https://charts.external-secrets.io
    helm repo update
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "DRY RUN: Would install External Secrets Operator"
    else
        helm upgrade --install external-secrets external-secrets/external-secrets \
            --namespace external-secrets \
            --create-namespace \
            --wait
    fi
    
    # Deploy Kubernetes manifests
    info "Deploying Kubernetes manifests..."
    cd "$K8S_DIR"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        kubectl apply --dry-run=client -k .
        log "DRY RUN: Kubernetes manifests validated"
    else
        kubectl apply -k .
        log "Kubernetes manifests applied"
    fi
    
    cd - > /dev/null
    
    # Wait for deployments to be ready
    if [[ "$DRY_RUN" == "false" ]]; then
        info "Waiting for deployments to be ready..."
        local services=("dashboard" "analytics" "integration")
        
        for service in "${services[@]}"; do
            info "Waiting for $service deployment..."
            kubectl rollout status deployment/"$service" -n production --timeout=600s
            log "$service deployment is ready"
        done
    fi
    
    PHASE_APPLICATIONS=true
}

# Phase 5: Verification
phase_verification() {
    info "Phase 5: Deployment Verification"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "DRY RUN: Skipping verification phase"
        PHASE_VERIFICATION=true
        return 0
    fi
    
    # Check pod status
    info "Checking pod status..."
    kubectl get pods -n production
    
    # Check service endpoints
    info "Checking service endpoints..."
    kubectl get services -n production
    
    # Run health checks
    info "Running health checks..."
    local api_url=$(kubectl get service dashboard-service -n production -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
    
    if [[ -n "$api_url" ]]; then
        info "Testing API endpoint: http://$api_url/health"
        
        # Wait for load balancer to be ready
        sleep 60
        
        if curl -f "http://$api_url/health" &> /dev/null; then
            log "Health check passed"
        else
            warn "Health check failed - this may be normal for initial deployment"
        fi
    else
        warn "Load balancer endpoint not yet available"
    fi
    
    # Generate deployment summary
    generate_deployment_summary
    
    PHASE_VERIFICATION=true
}

# Generate deployment summary
generate_deployment_summary() {
    info "Generating deployment summary..."
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local git_commit=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    
    cat > deployment-summary.md << EOF
# Production Deployment Summary

**Deployed:** $timestamp  
**Environment:** $ENVIRONMENT  
**Git Commit:** $git_commit  
**Deployed By:** $(whoami)  

## Deployment Status

| Phase | Status |
|-------|--------|
| Validation | $([ "$PHASE_VALIDATION" == true ] && echo "✅ COMPLETED" || echo "❌ FAILED") |
| Secrets | $([ "$PHASE_SECRETS" == true ] && echo "✅ COMPLETED" || echo "❌ FAILED") |
| Infrastructure | $([ "$PHASE_INFRASTRUCTURE" == true ] && echo "✅ COMPLETED" || echo "❌ FAILED") |
| Applications | $([ "$PHASE_APPLICATIONS" == true ] && echo "✅ COMPLETED" || echo "❌ FAILED") |
| Verification | $([ "$PHASE_VERIFICATION" == true ] && echo "✅ COMPLETED" || echo "❌ FAILED") |

## Infrastructure Resources

- **EKS Cluster:** ecommerce-analytics-production-cluster
- **RDS Database:** ecommerce-analytics-production-database
- **ElastiCache:** ecommerce-analytics-production-redis
- **Load Balancer:** $(kubectl get service dashboard-service -n production -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "Not available")

## Next Steps

1. **Configure DNS:** Point your domain to the load balancer
2. **SSL Certificates:** Configure SSL/TLS certificates
3. **Monitoring:** Set up alerts and monitoring dashboards
4. **Backup Verification:** Verify backup systems are working
5. **Performance Testing:** Run load tests against production

## Important Notes

- All secrets are stored in AWS Secrets Manager
- Database backups are configured with 30-day retention
- Auto-scaling is enabled for all services
- Monitoring and logging are configured

## Support

For issues or questions, check:
- CloudWatch logs for application errors
- Kubernetes events: \`kubectl get events -n production\`
- Infrastructure status: \`terraform show\`

EOF
    
    log "Deployment summary generated: deployment-summary.md"
}

# Rollback function
rollback_deployment() {
    error "Deployment failed! Initiating rollback..."
    
    # This is a basic rollback - in production you'd want more sophisticated rollback logic
    warn "Manual rollback may be required for infrastructure changes"
    warn "Check AWS Console and Kubernetes resources"
    
    exit 1
}

# Main execution
main() {
    display_banner
    confirm_deployment
    
    # Set trap for rollback on error
    trap rollback_deployment ERR
    
    echo
    phase_validation
    echo
    phase_secrets
    echo
    phase_infrastructure
    echo
    phase_applications
    echo
    phase_verification
    
    echo
    log "🎉 Production deployment completed successfully!"
    log "Review deployment-summary.md for next steps"
    
    # Remove error trap
    trap - ERR
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-validation)
            SKIP_VALIDATION=true
            shift
            ;;
        --help)
            cat << EOF
Usage: $0 [OPTIONS]

Options:
    --dry-run           Run in dry-run mode (no actual changes)
    --skip-tests        Skip the testing phase
    --skip-validation   Skip the validation phase
    --help              Show this help message

Environment Variables:
    AWS_REGION          AWS region (default: us-east-1)
    DRY_RUN            Enable dry-run mode (default: false)
    SKIP_TESTS         Skip tests (default: false)
    SKIP_VALIDATION    Skip validation (default: false)

EOF
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            ;;
    esac
done

# Execute main function
main "$@"
