#!/bin/bash

# Install Prerequisites for Production Deployment
# This script installs AWS CLI, Helm, and other required tools

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓ $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠ WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗ ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install AWS CLI
install_aws_cli() {
    if command_exists aws; then
        log "AWS CLI is already installed"
        aws --version
        return 0
    fi
    
    info "Installing AWS CLI..."
    
    # Download and install AWS CLI v2
    cd /tmp
    curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
    unzip -q awscliv2.zip
    sudo ./aws/install
    
    # Verify installation
    if command_exists aws; then
        log "AWS CLI installed successfully"
        aws --version
    else
        error "AWS CLI installation failed"
    fi
    
    # Cleanup
    rm -rf awscliv2.zip aws/
    cd - > /dev/null
}

# Install Helm
install_helm() {
    if command_exists helm; then
        log "Helm is already installed"
        helm version --short
        return 0
    fi
    
    info "Installing Helm..."
    
    # Download and install Helm
    curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    
    # Verify installation
    if command_exists helm; then
        log "Helm installed successfully"
        helm version --short
    else
        error "Helm installation failed"
    fi
}

# Install Artillery (for load testing)
install_artillery() {
    if command_exists artillery; then
        log "Artillery is already installed"
        return 0
    fi
    
    info "Installing Artillery..."
    npm install -g artillery
    
    if command_exists artillery; then
        log "Artillery installed successfully"
    else
        warn "Artillery installation failed - you can install it later with: npm install -g artillery"
    fi
}

# Install K6 (for load testing)
install_k6() {
    if command_exists k6; then
        log "K6 is already installed"
        return 0
    fi
    
    info "Installing K6..."
    
    # Add K6 repository and install
    sudo gpg -k
    sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
    echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
    sudo apt-get update
    sudo apt-get install k6
    
    if command_exists k6; then
        log "K6 installed successfully"
    else
        warn "K6 installation failed - you can install it manually later"
    fi
}

# Configure AWS CLI
configure_aws_cli() {
    info "Checking AWS CLI configuration..."
    
    if aws sts get-caller-identity &> /dev/null; then
        log "AWS CLI is already configured"
        local account_id=$(aws sts get-caller-identity --query Account --output text)
        local user_arn=$(aws sts get-caller-identity --query Arn --output text)
        log "Account ID: $account_id"
        log "User/Role: $user_arn"
        return 0
    fi
    
    warn "AWS CLI is not configured"
    echo
    echo "Please configure AWS CLI with your credentials:"
    echo "1. Run: aws configure"
    echo "2. Enter your AWS Access Key ID"
    echo "3. Enter your AWS Secret Access Key"
    echo "4. Enter your default region (e.g., us-east-1)"
    echo "5. Enter your default output format (json)"
    echo
    echo "Or if you're using AWS SSO:"
    echo "1. Run: aws configure sso"
    echo "2. Follow the prompts to configure SSO"
    echo
    read -p "Would you like to configure AWS CLI now? (y/n): " -r
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        aws configure
        
        # Verify configuration
        if aws sts get-caller-identity &> /dev/null; then
            log "AWS CLI configured successfully"
        else
            error "AWS CLI configuration failed"
        fi
    else
        warn "AWS CLI configuration skipped - you'll need to configure it before deployment"
    fi
}

# Check Docker
check_docker() {
    if command_exists docker; then
        log "Docker is installed"
        
        # Check if Docker daemon is running
        if docker info &> /dev/null; then
            log "Docker daemon is running"
        else
            warn "Docker daemon is not running. Please start Docker:"
            echo "  sudo systemctl start docker"
            echo "  sudo systemctl enable docker"
        fi
        
        # Check if user is in docker group
        if groups | grep -q docker; then
            log "User is in docker group"
        else
            warn "User is not in docker group. Add user to docker group:"
            echo "  sudo usermod -aG docker \$USER"
            echo "  Then log out and log back in"
        fi
    else
        error "Docker is not installed. Please install Docker first."
    fi
}

# Verify all tools
verify_installation() {
    info "Verifying all required tools..."
    
    local tools=("terraform" "aws" "kubectl" "jq" "helm" "docker")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if command_exists "$tool"; then
            log "$tool is available"
        else
            error "$tool is not available"
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -eq 0 ]]; then
        log "All required tools are installed! ✓"
        return 0
    else
        error "Missing tools: ${missing_tools[*]}"
        return 1
    fi
}

# Main execution
main() {
    info "Installing prerequisites for production deployment..."
    echo
    
    # Check existing tools
    info "Checking existing installations..."
    echo "Terraform: $(command_exists terraform && echo "✓ Installed" || echo "✗ Missing")"
    echo "AWS CLI: $(command_exists aws && echo "✓ Installed" || echo "✗ Missing")"
    echo "kubectl: $(command_exists kubectl && echo "✓ Installed" || echo "✗ Missing")"
    echo "jq: $(command_exists jq && echo "✓ Installed" || echo "✗ Missing")"
    echo "Helm: $(command_exists helm && echo "✓ Installed" || echo "✗ Missing")"
    echo "Docker: $(command_exists docker && echo "✓ Installed" || echo "✗ Missing")"
    echo
    
    # Install missing tools
    install_aws_cli
    echo
    install_helm
    echo
    check_docker
    echo
    install_artillery
    echo
    install_k6
    echo
    configure_aws_cli
    echo
    verify_installation
    
    echo
    log "Prerequisites installation completed!"
    log "You can now run: ./scripts/validate-infrastructure.sh"
}

# Execute main function
main "$@"
