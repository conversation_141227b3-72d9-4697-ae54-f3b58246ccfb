#!/bin/bash

# Complete Testing Suite Runner
# Executes all tests: unit, integration, E2E, and load testing

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="${ENVIRONMENT:-test}"
PARALLEL_JOBS="${PARALLEL_JOBS:-4}"
COVERAGE_THRESHOLD="${COVERAGE_THRESHOLD:-80}"
LOAD_TEST_DURATION="${LOAD_TEST_DURATION:-300}"

# Test results
UNIT_TESTS_PASSED=false
INTEGRATION_TESTS_PASSED=false
E2E_TESTS_PASSED=false
LOAD_TESTS_PASSED=false

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓ $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠ WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗ ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    info "Checking testing prerequisites..."
    
    # Check Node.js and npm
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        error "npm is not installed"
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        error "Docker is not running"
        exit 1
    fi
    
    log "Prerequisites check passed"
}

# Setup test environment
setup_test_environment() {
    info "Setting up test environment..."
    
    # Create test environment file
    cat > .env.test << EOF
NODE_ENV=test
LOG_LEVEL=error

# Test Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_analytics_test
DB_USER=postgres
DB_PASSWORD=test_password

# Test Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=test_password
REDIS_DB=1

# Test JWT
JWT_SECRET=test_jwt_secret_for_testing_only_not_for_production
JWT_EXPIRES_IN=1h

# Test API URLs
VITE_API_URL=http://localhost:3001/api
VITE_ANALYTICS_URL=http://localhost:3002/api
VITE_INTEGRATION_URL=http://localhost:3003/api

# Disable external services in tests
ENABLE_EXTERNAL_APIS=false
ENABLE_WEBHOOKS=false
ENABLE_EMAIL=false
EOF
    
    # Start test services with Docker Compose
    info "Starting test services..."
    docker-compose -f docker-compose.test.yml up -d postgres redis
    
    # Wait for services to be ready
    info "Waiting for test services to be ready..."
    sleep 10
    
    # Check if services are healthy
    if docker-compose -f docker-compose.test.yml ps | grep -q "Up (healthy)"; then
        log "Test services are ready"
    else
        warn "Some test services may not be fully ready"
    fi
    
    log "Test environment setup completed"
}

# Install dependencies
install_dependencies() {
    info "Installing dependencies..."
    
    # Install root dependencies
    npm ci
    
    # Install testing dependencies
    cd testing && npm ci && cd ..
    
    # Install service dependencies
    local services=("dashboard" "analytics" "integration")
    for service in "${services[@]}"; do
        if [[ -d "services/$service" ]]; then
            info "Installing dependencies for $service..."
            cd "services/$service" && npm ci && cd ../..
        fi
    done
    
    log "Dependencies installed"
}

# Run unit tests
run_unit_tests() {
    info "Running unit tests..."
    
    cd testing
    
    # Run unit tests with coverage
    if npm run test:unit -- --coverage --maxWorkers=$PARALLEL_JOBS; then
        log "Unit tests passed"
        UNIT_TESTS_PASSED=true
        
        # Check coverage threshold
        local coverage=$(cat coverage/coverage-summary.json | jq '.total.lines.pct')
        if (( $(echo "$coverage >= $COVERAGE_THRESHOLD" | bc -l) )); then
            log "Coverage threshold met: ${coverage}% >= ${COVERAGE_THRESHOLD}%"
        else
            warn "Coverage below threshold: ${coverage}% < ${COVERAGE_THRESHOLD}%"
        fi
    else
        error "Unit tests failed"
        UNIT_TESTS_PASSED=false
    fi
    
    cd ..
}

# Run integration tests
run_integration_tests() {
    info "Running integration tests..."
    
    cd testing
    
    # Run database migrations for testing
    npm run test:setup
    
    # Run integration tests
    if npm run test:integration -- --maxWorkers=$PARALLEL_JOBS; then
        log "Integration tests passed"
        INTEGRATION_TESTS_PASSED=true
    else
        error "Integration tests failed"
        INTEGRATION_TESTS_PASSED=false
    fi
    
    cd ..
}

# Run E2E tests
run_e2e_tests() {
    info "Running E2E tests..."
    
    # Start application services
    info "Starting application services for E2E tests..."
    npm run build
    npm run start:test &
    local app_pid=$!
    
    # Wait for application to be ready
    sleep 30
    
    # Check if application is responding
    if curl -f http://localhost:3001/health &> /dev/null; then
        log "Application is ready for E2E tests"
    else
        warn "Application may not be fully ready"
    fi
    
    cd testing
    
    # Run E2E tests
    if npm run test:e2e; then
        log "E2E tests passed"
        E2E_TESTS_PASSED=true
    else
        error "E2E tests failed"
        E2E_TESTS_PASSED=false
    fi
    
    cd ..
    
    # Stop application services
    kill $app_pid 2>/dev/null || true
    sleep 5
}

# Run load tests
run_load_tests() {
    info "Running load tests..."
    
    # Check if Artillery is installed
    if ! command -v artillery &> /dev/null; then
        info "Installing Artillery..."
        npm install -g artillery
    fi
    
    # Check if K6 is installed
    if ! command -v k6 &> /dev/null; then
        warn "K6 is not installed. Skipping K6 load tests."
        warn "Install K6 from: https://k6.io/docs/getting-started/installation/"
    fi
    
    # Start application for load testing
    info "Starting application for load testing..."
    npm run start:test &
    local app_pid=$!
    sleep 30
    
    # Run Artillery load tests
    info "Running Artillery load tests..."
    cd performance/load-testing
    
    if artillery run artillery-config.yml --output artillery-report.json; then
        log "Artillery load tests completed"
        
        # Generate HTML report
        artillery report artillery-report.json --output artillery-report.html
        log "Artillery report generated: artillery-report.html"
        
        LOAD_TESTS_PASSED=true
    else
        error "Artillery load tests failed"
        LOAD_TESTS_PASSED=false
    fi
    
    # Run K6 load tests if available
    if command -v k6 &> /dev/null; then
        info "Running K6 load tests..."
        if k6 run k6-config.js; then
            log "K6 load tests completed"
        else
            warn "K6 load tests failed"
        fi
    fi
    
    cd ../..
    
    # Stop application
    kill $app_pid 2>/dev/null || true
    sleep 5
}

# Cleanup test environment
cleanup_test_environment() {
    info "Cleaning up test environment..."
    
    # Stop test services
    docker-compose -f docker-compose.test.yml down -v
    
    # Remove test environment file
    rm -f .env.test
    
    # Kill any remaining processes
    pkill -f "node.*test" 2>/dev/null || true
    
    log "Test environment cleaned up"
}

# Generate test report
generate_test_report() {
    info "Generating test report..."
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local total_tests=4
    local passed_tests=0
    
    [[ "$UNIT_TESTS_PASSED" == true ]] && ((passed_tests++))
    [[ "$INTEGRATION_TESTS_PASSED" == true ]] && ((passed_tests++))
    [[ "$E2E_TESTS_PASSED" == true ]] && ((passed_tests++))
    [[ "$LOAD_TESTS_PASSED" == true ]] && ((passed_tests++))
    
    cat > test-report.md << EOF
# Complete Testing Suite Report

**Generated:** $timestamp  
**Environment:** $ENVIRONMENT  
**Coverage Threshold:** $COVERAGE_THRESHOLD%  

## Summary

**Tests Passed:** $passed_tests/$total_tests

## Test Results

| Test Suite | Status | Details |
|------------|--------|---------|
| Unit Tests | $([ "$UNIT_TESTS_PASSED" == true ] && echo "✅ PASSED" || echo "❌ FAILED") | Coverage report: testing/coverage/index.html |
| Integration Tests | $([ "$INTEGRATION_TESTS_PASSED" == true ] && echo "✅ PASSED" || echo "❌ FAILED") | Database and API integration tests |
| E2E Tests | $([ "$E2E_TESTS_PASSED" == true ] && echo "✅ PASSED" || echo "❌ FAILED") | Full user workflow tests |
| Load Tests | $([ "$LOAD_TESTS_PASSED" == true ] && echo "✅ PASSED" || echo "❌ FAILED") | Performance and stress tests |

## Deployment Readiness

$(if [[ $passed_tests -eq $total_tests ]]; then
    echo "🚀 **READY FOR DEPLOYMENT** - All tests passed!"
elif [[ $passed_tests -ge 3 ]]; then
    echo "⚠️ **CONDITIONAL DEPLOYMENT** - Most tests passed, review failures"
else
    echo "❌ **NOT READY** - Multiple test failures, do not deploy"
fi)

## Next Steps

$(if [[ $passed_tests -eq $total_tests ]]; then
    cat << 'NEXT_STEPS'
1. Review test coverage reports
2. Proceed with infrastructure deployment
3. Run production health checks after deployment
NEXT_STEPS
else
    echo "1. Fix failing tests"
    echo "2. Re-run test suite"
    echo "3. Ensure all tests pass before deployment"
fi)

## Test Artifacts

- Unit Test Coverage: \`testing/coverage/index.html\`
- Integration Test Results: \`testing/reports/integration-report.html\`
- E2E Test Screenshots: \`testing/screenshots/\`
- Load Test Report: \`performance/load-testing/artillery-report.html\`

EOF
    
    log "Test report generated: test-report.md"
}

# Main execution
main() {
    info "Starting complete testing suite..."
    
    # Set trap for cleanup
    trap cleanup_test_environment EXIT
    
    check_prerequisites
    setup_test_environment
    install_dependencies
    
    echo
    run_unit_tests
    echo
    run_integration_tests
    echo
    run_e2e_tests
    echo
    run_load_tests
    echo
    
    generate_test_report
    
    # Final status
    local total_passed=0
    [[ "$UNIT_TESTS_PASSED" == true ]] && ((total_passed++))
    [[ "$INTEGRATION_TESTS_PASSED" == true ]] && ((total_passed++))
    [[ "$E2E_TESTS_PASSED" == true ]] && ((total_passed++))
    [[ "$LOAD_TESTS_PASSED" == true ]] && ((total_passed++))
    
    echo
    info "Testing suite completed: $total_passed/4 test suites passed"
    
    if [[ $total_passed -eq 4 ]]; then
        log "All tests passed! Ready for deployment 🚀"
        exit 0
    else
        error "Some tests failed. Review the results before deployment."
        exit 1
    fi
}

# Execute main function
main "$@"
