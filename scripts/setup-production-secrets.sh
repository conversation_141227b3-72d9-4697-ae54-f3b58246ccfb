#!/bin/bash

# Production Secrets Setup Script
# This script sets up AWS Secrets Manager with all required secrets for production deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION="${AWS_REGION:-us-east-1}"
PROJECT_NAME="ecommerce-analytics"
ENVIRONMENT="production"

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        error "AWS CLI is not installed. Please install it first."
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        error "AWS credentials not configured. Please run 'aws configure' first."
    fi
    
    # Check jq
    if ! command -v jq &> /dev/null; then
        error "jq is not installed. Please install it first."
    fi
    
    # Check openssl
    if ! command -v openssl &> /dev/null; then
        error "openssl is not installed. Please install it first."
    fi
    
    log "Prerequisites check passed ✓"
}

# Generate secure random password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Generate JWT secret
generate_jwt_secret() {
    openssl rand -base64 64 | tr -d "=+/" | cut -c1-64
}

# Generate encryption key
generate_encryption_key() {
    openssl rand -hex 32
}

# Create or update secret in AWS Secrets Manager
create_secret() {
    local secret_name="$1"
    local secret_value="$2"
    local description="$3"
    
    log "Creating/updating secret: $secret_name"
    
    # Check if secret exists
    if aws secretsmanager describe-secret --secret-id "$secret_name" --region "$AWS_REGION" &> /dev/null; then
        # Update existing secret
        aws secretsmanager update-secret \
            --secret-id "$secret_name" \
            --secret-string "$secret_value" \
            --region "$AWS_REGION" > /dev/null
        log "Updated existing secret: $secret_name"
    else
        # Create new secret
        aws secretsmanager create-secret \
            --name "$secret_name" \
            --description "$description" \
            --secret-string "$secret_value" \
            --region "$AWS_REGION" > /dev/null
        log "Created new secret: $secret_name"
    fi
}

# Setup database secrets
setup_database_secrets() {
    log "Setting up database secrets..."
    
    local db_password=$(generate_password 32)
    local db_secret=$(cat <<EOF
{
    "username": "postgres",
    "password": "$db_password",
    "engine": "postgres",
    "host": "${PROJECT_NAME}-${ENVIRONMENT}-database.cluster-xyz.${AWS_REGION}.rds.amazonaws.com",
    "port": 5432,
    "dbname": "ecommerce_analytics"
}
EOF
)
    
    create_secret "${PROJECT_NAME}/${ENVIRONMENT}/database" "$db_secret" "Database credentials for ${PROJECT_NAME} ${ENVIRONMENT}"
    
    log "Database secrets configured ✓"
}

# Setup Redis secrets
setup_redis_secrets() {
    log "Setting up Redis secrets..."
    
    local redis_password=$(generate_password 32)
    local redis_secret=$(cat <<EOF
{
    "auth_token": "$redis_password",
    "host": "${PROJECT_NAME}-${ENVIRONMENT}-redis.xyz.cache.amazonaws.com",
    "port": 6379
}
EOF
)
    
    create_secret "${PROJECT_NAME}/${ENVIRONMENT}/redis" "$redis_secret" "Redis credentials for ${PROJECT_NAME} ${ENVIRONMENT}"
    
    log "Redis secrets configured ✓"
}

# Setup application secrets
setup_application_secrets() {
    log "Setting up application secrets..."
    
    local jwt_secret=$(generate_jwt_secret)
    local jwt_refresh_secret=$(generate_jwt_secret)
    local encryption_key=$(generate_encryption_key)
    local api_encryption_key=$(generate_encryption_key)
    local session_secret=$(generate_password 64)
    local cookie_secret=$(generate_password 32)
    
    local app_secret=$(cat <<EOF
{
    "jwt_secret": "$jwt_secret",
    "jwt_refresh_secret": "$jwt_refresh_secret",
    "encryption_key": "$encryption_key",
    "api_encryption_key": "$api_encryption_key",
    "session_secret": "$session_secret",
    "cookie_secret": "$cookie_secret"
}
EOF
)
    
    create_secret "${PROJECT_NAME}/${ENVIRONMENT}/application" "$app_secret" "Application secrets for ${PROJECT_NAME} ${ENVIRONMENT}"
    
    log "Application secrets configured ✓"
}

# Setup external service secrets (placeholders)
setup_external_secrets() {
    log "Setting up external service secrets..."
    
    # Shopify secrets
    local shopify_secret=$(cat <<EOF
{
    "api_key": "REPLACE_WITH_ACTUAL_SHOPIFY_API_KEY",
    "secret": "REPLACE_WITH_ACTUAL_SHOPIFY_SECRET",
    "webhook_secret": "REPLACE_WITH_ACTUAL_SHOPIFY_WEBHOOK_SECRET"
}
EOF
)
    
    create_secret "${PROJECT_NAME}/${ENVIRONMENT}/shopify" "$shopify_secret" "Shopify integration secrets"
    
    # WooCommerce secrets
    local woocommerce_secret=$(cat <<EOF
{
    "consumer_key": "REPLACE_WITH_ACTUAL_WOOCOMMERCE_KEY",
    "consumer_secret": "REPLACE_WITH_ACTUAL_WOOCOMMERCE_SECRET"
}
EOF
)
    
    create_secret "${PROJECT_NAME}/${ENVIRONMENT}/woocommerce" "$woocommerce_secret" "WooCommerce integration secrets"
    
    # Email secrets
    local email_secret=$(cat <<EOF
{
    "smtp_host": "smtp.gmail.com",
    "smtp_port": "587",
    "smtp_user": "REPLACE_WITH_ACTUAL_EMAIL",
    "smtp_password": "REPLACE_WITH_ACTUAL_APP_PASSWORD",
    "from_email": "<EMAIL>"
}
EOF
)
    
    create_secret "${PROJECT_NAME}/${ENVIRONMENT}/email" "$email_secret" "Email service secrets"
    
    # AWS S3 secrets
    local s3_secret=$(cat <<EOF
{
    "access_key_id": "REPLACE_WITH_ACTUAL_ACCESS_KEY",
    "secret_access_key": "REPLACE_WITH_ACTUAL_SECRET_KEY",
    "bucket_name": "${PROJECT_NAME}-${ENVIRONMENT}-storage",
    "region": "$AWS_REGION"
}
EOF
)
    
    create_secret "${PROJECT_NAME}/${ENVIRONMENT}/s3" "$s3_secret" "S3 storage secrets"
    
    log "External service secrets configured ✓"
    warn "Remember to update external service secrets with actual values!"
}

# Setup monitoring secrets
setup_monitoring_secrets() {
    log "Setting up monitoring secrets..."
    
    local monitoring_secret=$(cat <<EOF
{
    "sentry_dsn": "REPLACE_WITH_ACTUAL_SENTRY_DSN",
    "grafana_admin_password": "$(generate_password 16)",
    "prometheus_basic_auth": "$(generate_password 16)"
}
EOF
)
    
    create_secret "${PROJECT_NAME}/${ENVIRONMENT}/monitoring" "$monitoring_secret" "Monitoring service secrets"
    
    log "Monitoring secrets configured ✓"
}

# Generate summary report
generate_summary() {
    log "Generating secrets summary..."
    
    cat > secrets-summary.md << EOF
# Production Secrets Summary

Generated on: $(date)
AWS Region: $AWS_REGION
Project: $PROJECT_NAME
Environment: $ENVIRONMENT

## Created Secrets in AWS Secrets Manager:

1. **${PROJECT_NAME}/${ENVIRONMENT}/database**
   - Database credentials (username, password, connection details)

2. **${PROJECT_NAME}/${ENVIRONMENT}/redis**
   - Redis authentication and connection details

3. **${PROJECT_NAME}/${ENVIRONMENT}/application**
   - JWT secrets, encryption keys, session secrets

4. **${PROJECT_NAME}/${ENVIRONMENT}/shopify**
   - Shopify integration credentials (NEEDS MANUAL UPDATE)

5. **${PROJECT_NAME}/${ENVIRONMENT}/woocommerce**
   - WooCommerce integration credentials (NEEDS MANUAL UPDATE)

6. **${PROJECT_NAME}/${ENVIRONMENT}/email**
   - Email service configuration (NEEDS MANUAL UPDATE)

7. **${PROJECT_NAME}/${ENVIRONMENT}/s3**
   - S3 storage credentials (NEEDS MANUAL UPDATE)

8. **${PROJECT_NAME}/${ENVIRONMENT}/monitoring**
   - Monitoring service credentials (NEEDS MANUAL UPDATE)

## Next Steps:

1. Update external service secrets with actual values:
   \`\`\`bash
   aws secretsmanager update-secret --secret-id "${PROJECT_NAME}/${ENVIRONMENT}/shopify" --secret-string '{"api_key":"actual_key","secret":"actual_secret","webhook_secret":"actual_webhook_secret"}'
   \`\`\`

2. Configure External Secrets Operator in Kubernetes to sync these secrets

3. Update Terraform variables with actual domain names and notification emails

4. Proceed with infrastructure deployment

## Security Notes:

- All secrets are encrypted at rest in AWS Secrets Manager
- Use IAM roles and policies to control access
- Rotate secrets regularly (recommended: every 90 days)
- Monitor secret access through CloudTrail

EOF
    
    log "Summary report generated: secrets-summary.md"
}

# Main execution
main() {
    log "Starting production secrets setup for $PROJECT_NAME..."
    
    check_prerequisites
    setup_database_secrets
    setup_redis_secrets
    setup_application_secrets
    setup_external_secrets
    setup_monitoring_secrets
    generate_summary
    
    log "Production secrets setup completed successfully! ✓"
    log "Please review secrets-summary.md for next steps"
}

# Execute main function
main "$@"
