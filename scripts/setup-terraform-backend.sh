#!/bin/bash

# Setup Terraform Backend Infrastructure
# Creates S3 bucket and DynamoDB table for Terraform state management

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION="${AWS_REGION:-us-east-1}"
PROJECT_NAME="ecommerce-analytics"
BUCKET_NAME="${PROJECT_NAME}-terraform-state"
DYNAMODB_TABLE="terraform-state-lock"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓ $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠ WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗ ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ $1${NC}"
}

# Check AWS CLI configuration
check_aws_config() {
    info "Checking AWS CLI configuration..."
    
    if ! aws sts get-caller-identity &> /dev/null; then
        error "AWS CLI is not configured. Please run 'aws configure' first."
    fi
    
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    local user_arn=$(aws sts get-caller-identity --query Arn --output text)
    
    log "AWS CLI is configured"
    log "Account ID: $account_id"
    log "User/Role: $user_arn"
    log "Region: $AWS_REGION"
}

# Create S3 bucket for Terraform state
create_s3_bucket() {
    info "Creating S3 bucket for Terraform state..."
    
    # Check if bucket already exists
    if aws s3 ls "s3://$BUCKET_NAME" &> /dev/null; then
        log "S3 bucket already exists: $BUCKET_NAME"
        return 0
    fi
    
    # Create bucket
    if [[ "$AWS_REGION" == "us-east-1" ]]; then
        # us-east-1 doesn't need LocationConstraint
        aws s3 mb "s3://$BUCKET_NAME" --region "$AWS_REGION"
    else
        aws s3api create-bucket \
            --bucket "$BUCKET_NAME" \
            --region "$AWS_REGION" \
            --create-bucket-configuration LocationConstraint="$AWS_REGION"
    fi
    
    log "S3 bucket created: $BUCKET_NAME"
    
    # Enable versioning
    aws s3api put-bucket-versioning \
        --bucket "$BUCKET_NAME" \
        --versioning-configuration Status=Enabled
    
    log "S3 bucket versioning enabled"
    
    # Enable server-side encryption
    aws s3api put-bucket-encryption \
        --bucket "$BUCKET_NAME" \
        --server-side-encryption-configuration '{
            "Rules": [
                {
                    "ApplyServerSideEncryptionByDefault": {
                        "SSEAlgorithm": "AES256"
                    }
                }
            ]
        }'
    
    log "S3 bucket encryption enabled"
    
    # Block public access
    aws s3api put-public-access-block \
        --bucket "$BUCKET_NAME" \
        --public-access-block-configuration \
            BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true
    
    log "S3 bucket public access blocked"
}

# Create DynamoDB table for state locking
create_dynamodb_table() {
    info "Creating DynamoDB table for Terraform state locking..."
    
    # Check if table already exists
    if aws dynamodb describe-table --table-name "$DYNAMODB_TABLE" --region "$AWS_REGION" &> /dev/null; then
        log "DynamoDB table already exists: $DYNAMODB_TABLE"
        return 0
    fi
    
    # Create table
    aws dynamodb create-table \
        --table-name "$DYNAMODB_TABLE" \
        --attribute-definitions AttributeName=LockID,AttributeType=S \
        --key-schema AttributeName=LockID,KeyType=HASH \
        --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
        --region "$AWS_REGION"
    
    log "DynamoDB table created: $DYNAMODB_TABLE"
    
    # Wait for table to be active
    info "Waiting for DynamoDB table to be active..."
    aws dynamodb wait table-exists --table-name "$DYNAMODB_TABLE" --region "$AWS_REGION"
    
    log "DynamoDB table is active"
}

# Verify backend setup
verify_backend() {
    info "Verifying Terraform backend setup..."
    
    # Check S3 bucket
    if aws s3 ls "s3://$BUCKET_NAME" &> /dev/null; then
        log "S3 bucket is accessible: $BUCKET_NAME"
    else
        error "S3 bucket is not accessible: $BUCKET_NAME"
    fi
    
    # Check DynamoDB table
    if aws dynamodb describe-table --table-name "$DYNAMODB_TABLE" --region "$AWS_REGION" &> /dev/null; then
        log "DynamoDB table is accessible: $DYNAMODB_TABLE"
    else
        error "DynamoDB table is not accessible: $DYNAMODB_TABLE"
    fi
    
    # Test write permissions
    info "Testing S3 write permissions..."
    echo "test" | aws s3 cp - "s3://$BUCKET_NAME/test-file.txt"
    aws s3 rm "s3://$BUCKET_NAME/test-file.txt"
    log "S3 write permissions confirmed"
    
    # Test DynamoDB write permissions
    info "Testing DynamoDB write permissions..."
    aws dynamodb put-item \
        --table-name "$DYNAMODB_TABLE" \
        --item '{"LockID":{"S":"test-lock"}}' \
        --region "$AWS_REGION"
    
    aws dynamodb delete-item \
        --table-name "$DYNAMODB_TABLE" \
        --key '{"LockID":{"S":"test-lock"}}' \
        --region "$AWS_REGION"
    
    log "DynamoDB write permissions confirmed"
}

# Generate backend configuration
generate_backend_config() {
    info "Generating Terraform backend configuration..."
    
    cat > infrastructure/terraform/backend.tf << EOF
# Terraform Backend Configuration
# This file configures the S3 backend for Terraform state storage

terraform {
  backend "s3" {
    bucket         = "$BUCKET_NAME"
    key            = "production/terraform.tfstate"
    region         = "$AWS_REGION"
    encrypt        = true
    dynamodb_table = "$DYNAMODB_TABLE"
    
    # Optional: Enable state locking and consistency checking
    # versioning is enabled on the S3 bucket
  }
}
EOF
    
    log "Backend configuration generated: infrastructure/terraform/backend.tf"
}

# Display summary
display_summary() {
    info "Terraform backend setup completed successfully!"
    
    cat << EOF

╔══════════════════════════════════════════════════════════════╗
║                    TERRAFORM BACKEND SUMMARY                 ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  S3 Bucket:        $BUCKET_NAME                    ║
║  DynamoDB Table:   $DYNAMODB_TABLE                          ║
║  AWS Region:       $AWS_REGION                              ║
║                                                              ║
║  Features Enabled:                                           ║
║  ✓ S3 Versioning                                             ║
║  ✓ S3 Encryption (AES256)                                    ║
║  ✓ S3 Public Access Blocked                                  ║
║  ✓ DynamoDB State Locking                                    ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

Next Steps:
1. Review the generated backend configuration: infrastructure/terraform/backend.tf
2. Initialize Terraform: cd infrastructure/terraform && terraform init
3. Plan your infrastructure: terraform plan -var-file=environments/production.tfvars
4. Apply when ready: terraform apply

EOF
}

# Main execution
main() {
    info "Setting up Terraform backend infrastructure..."
    echo
    
    check_aws_config
    echo
    create_s3_bucket
    echo
    create_dynamodb_table
    echo
    verify_backend
    echo
    generate_backend_config
    echo
    display_summary
}

# Execute main function
main "$@"
