#!/bin/bash

# Phase 1 Database Setup Script
# Enhanced Analytics Database Implementation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-ecommerce_analytics}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD:-}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if PostgreSQL is running
check_postgres() {
    print_status "Checking PostgreSQL connection..."
    
    if ! command -v psql &> /dev/null; then
        print_error "psql command not found. Please install PostgreSQL client."
        exit 1
    fi
    
    # Test connection
    if ! PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "SELECT 1;" &> /dev/null; then
        print_error "Cannot connect to PostgreSQL. Please check your connection settings."
        print_error "Host: $DB_HOST, Port: $DB_PORT, User: $DB_USER"
        exit 1
    fi
    
    print_success "PostgreSQL connection successful"
}

# Function to check if TimescaleDB is installed
check_timescaledb() {
    print_status "Checking TimescaleDB availability..."
    
    TIMESCALEDB_AVAILABLE=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -t -c "SELECT COUNT(*) FROM pg_available_extensions WHERE name = 'timescaledb';" | xargs)
    
    if [ "$TIMESCALEDB_AVAILABLE" -eq 0 ]; then
        print_error "TimescaleDB extension is not available."
        print_error "Please install TimescaleDB first: https://docs.timescale.com/install/"
        exit 1
    fi
    
    print_success "TimescaleDB extension is available"
}

# Function to create database if it doesn't exist
create_database() {
    print_status "Checking if database '$DB_NAME' exists..."
    
    DB_EXISTS=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -t -c "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME';" | xargs)
    
    if [ -z "$DB_EXISTS" ]; then
        print_status "Creating database '$DB_NAME'..."
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "CREATE DATABASE $DB_NAME;"
        print_success "Database '$DB_NAME' created successfully"
    else
        print_success "Database '$DB_NAME' already exists"
    fi
}

# Function to execute SQL file
execute_sql_file() {
    local sql_file="$1"
    local description="$2"
    
    if [ ! -f "$sql_file" ]; then
        print_error "SQL file not found: $sql_file"
        exit 1
    fi
    
    print_status "$description"
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$sql_file"; then
        print_success "$description completed"
    else
        print_error "$description failed"
        exit 1
    fi
}

# Function to run the enhanced analytics setup
run_enhanced_setup() {
    print_status "Running enhanced analytics database setup..."
    
    # Change to database directory
    cd "$(dirname "$0")/../database"
    
    # Execute the setup script
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "setup_enhanced_analytics.sql"; then
        print_success "Enhanced analytics setup completed"
    else
        print_error "Enhanced analytics setup failed"
        exit 1
    fi
}

# Function to verify the setup
verify_setup() {
    print_status "Verifying database setup..."
    
    # Check if TimescaleDB is enabled
    TIMESCALEDB_ENABLED=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM pg_extension WHERE extname = 'timescaledb';" | xargs)
    
    if [ "$TIMESCALEDB_ENABLED" -eq 0 ]; then
        print_error "TimescaleDB extension is not enabled in the database"
        exit 1
    fi
    
    # Check if hypertables were created
    HYPERTABLES_COUNT=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM timescaledb_information.hypertables WHERE hypertable_schema = 'public';" | xargs)
    
    if [ "$HYPERTABLES_COUNT" -lt 6 ]; then
        print_warning "Expected 6 hypertables, found $HYPERTABLES_COUNT"
    else
        print_success "All hypertables created successfully ($HYPERTABLES_COUNT found)"
    fi
    
    # Check if continuous aggregates were created
    CAGGS_COUNT=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM timescaledb_information.continuous_aggregates;" | xargs)
    
    if [ "$CAGGS_COUNT" -lt 6 ]; then
        print_warning "Expected 6 continuous aggregates, found $CAGGS_COUNT"
    else
        print_success "All continuous aggregates created successfully ($CAGGS_COUNT found)"
    fi
    
    # Check if sample data was inserted
    SAMPLE_TENANTS=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM tenants;" | xargs)
    
    if [ "$SAMPLE_TENANTS" -gt 0 ]; then
        print_success "Sample data inserted successfully ($SAMPLE_TENANTS tenants found)"
    else
        print_warning "No sample data found"
    fi
    
    print_success "Database setup verification completed"
}

# Function to display performance recommendations
show_performance_recommendations() {
    print_status "Performance Recommendations:"
    echo ""
    echo "1. PostgreSQL Configuration:"
    echo "   - Update postgresql.conf with the optimized settings from database/postgresql.conf"
    echo "   - Restart PostgreSQL after configuration changes"
    echo ""
    echo "2. Monitoring:"
    echo "   - Monitor query performance with pg_stat_statements"
    echo "   - Check TimescaleDB compression ratios regularly"
    echo "   - Monitor continuous aggregate refresh policies"
    echo ""
    echo "3. Performance Targets (Phase 1):"
    echo "   - Query response time: <100ms for dashboard queries"
    echo "   - Data ingestion: 10,000+ events/second"
    echo "   - Storage compression: 70% ratio"
    echo ""
    echo "4. Next Steps:"
    echo "   - Update Analytics Service to use enhanced schema"
    echo "   - Implement Fresh frontend components"
    echo "   - Set up monitoring and alerting"
    echo ""
}

# Main execution
main() {
    echo ""
    print_status "=== Phase 1: Enhanced Analytics Database Setup ==="
    echo ""
    
    # Pre-flight checks
    check_postgres
    check_timescaledb
    
    # Database setup
    create_database
    
    # Run enhanced analytics setup
    run_enhanced_setup
    
    # Verify setup
    verify_setup
    
    # Show recommendations
    show_performance_recommendations
    
    print_success "=== Phase 1 Database Setup Completed Successfully ==="
    echo ""
    print_status "You can now:"
    print_status "1. Start the enhanced Analytics Service: cd services/analytics-deno && deno run --allow-all src/main.ts"
    print_status "2. Test the new endpoints: curl http://localhost:3002/api/enhanced-analytics/health"
    print_status "3. View dashboard metrics: curl http://localhost:3002/api/enhanced-analytics/dashboard?range=7d"
    echo ""
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --verify-only  Only run verification checks"
        echo ""
        echo "Environment Variables:"
        echo "  DB_HOST        Database host (default: localhost)"
        echo "  DB_PORT        Database port (default: 5432)"
        echo "  DB_NAME        Database name (default: ecommerce_analytics)"
        echo "  DB_USER        Database user (default: postgres)"
        echo "  DB_PASSWORD    Database password"
        echo ""
        exit 0
        ;;
    --verify-only)
        check_postgres
        verify_setup
        exit 0
        ;;
    *)
        main
        ;;
esac
