#!/bin/bash

# Phase 1 Complete Setup and Start Script
# Enhanced Analytics Platform Implementation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-ecommerce_analytics}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD:-}"

ANALYTICS_PORT="${ANALYTICS_PORT:-3002}"
DASHBOARD_PORT="${DASHBOARD_PORT:-8000}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[PHASE 1]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking prerequisites..."
    
    # Check if Deno is installed
    if ! command -v deno &> /dev/null; then
        print_error "Deno is not installed. Please install Deno first: https://deno.land/manual/getting_started/installation"
        exit 1
    fi
    
    # Check Deno version (should be 2.0+)
    DENO_VERSION=$(deno --version | head -n1 | cut -d' ' -f2)
    print_status "Deno version: $DENO_VERSION"
    
    # Check if PostgreSQL client is available
    if ! command -v psql &> /dev/null; then
        print_error "PostgreSQL client (psql) is not installed."
        exit 1
    fi
    
    # Check if Docker is running (for Redis)
    if ! command -v docker &> /dev/null; then
        print_warning "Docker not found. Redis may not be available."
    elif ! docker info &> /dev/null; then
        print_warning "Docker is not running. Redis may not be available."
    fi
    
    print_success "Prerequisites check completed"
}

# Function to setup database
setup_database() {
    print_header "Setting up enhanced analytics database..."
    
    # Run the database setup script
    if [ -f "scripts/setup_phase1_database.sh" ]; then
        chmod +x scripts/setup_phase1_database.sh
        ./scripts/setup_phase1_database.sh
    else
        print_error "Database setup script not found: scripts/setup_phase1_database.sh"
        exit 1
    fi
    
    print_success "Database setup completed"
}

# Function to start Redis (if Docker is available)
start_redis() {
    print_header "Starting Redis for caching..."
    
    if command -v docker &> /dev/null && docker info &> /dev/null; then
        # Check if Redis container is already running
        if docker ps | grep -q "redis-analytics"; then
            print_status "Redis container already running"
        else
            print_status "Starting Redis container..."
            docker run -d \
                --name redis-analytics \
                --restart unless-stopped \
                -p 6379:6379 \
                redis:7-alpine \
                redis-server --appendonly yes
            
            # Wait for Redis to be ready
            sleep 3
            print_success "Redis started successfully"
        fi
    else
        print_warning "Docker not available. Skipping Redis setup."
        print_warning "Analytics service will work without Redis but caching will be disabled."
    fi
}

# Function to start Analytics Service
start_analytics_service() {
    print_header "Starting Enhanced Analytics Service..."
    
    cd services/analytics-deno
    
    # Check if deno.json exists
    if [ ! -f "deno.json" ]; then
        print_error "deno.json not found in analytics service directory"
        exit 1
    fi
    
    # Set environment variables
    export DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
    export REDIS_URL="redis://localhost:6379"
    export PORT="${ANALYTICS_PORT}"
    export NODE_ENV="development"
    
    print_status "Starting Analytics Service on port ${ANALYTICS_PORT}..."
    print_status "Database: ${DATABASE_URL}"
    
    # Start the service in background
    nohup deno run --allow-all src/main.ts > ../../logs/analytics-service.log 2>&1 &
    ANALYTICS_PID=$!
    echo $ANALYTICS_PID > ../../logs/analytics-service.pid
    
    # Wait for service to start
    sleep 5
    
    # Check if service is running
    if kill -0 $ANALYTICS_PID 2>/dev/null; then
        print_success "Analytics Service started successfully (PID: $ANALYTICS_PID)"
        
        # Test the health endpoint
        if curl -s "http://localhost:${ANALYTICS_PORT}/api/enhanced-analytics/health" > /dev/null; then
            print_success "Analytics Service health check passed"
        else
            print_warning "Analytics Service health check failed"
        fi
    else
        print_error "Failed to start Analytics Service"
        exit 1
    fi
    
    cd ../..
}

# Function to start Dashboard Service
start_dashboard_service() {
    print_header "Starting Fresh Dashboard Service..."
    
    cd services/dashboard-fresh
    
    # Check if deno.json exists
    if [ ! -f "deno.json" ]; then
        print_error "deno.json not found in dashboard service directory"
        exit 1
    fi
    
    # Set environment variables
    export ANALYTICS_SERVICE_URL="http://localhost:${ANALYTICS_PORT}"
    export PORT="${DASHBOARD_PORT}"
    export NODE_ENV="development"
    
    print_status "Starting Dashboard Service on port ${DASHBOARD_PORT}..."
    print_status "Analytics Service URL: ${ANALYTICS_SERVICE_URL}"
    
    # Start the service in background
    nohup deno run -A --watch=static/,routes/ dev.ts > ../../logs/dashboard-service.log 2>&1 &
    DASHBOARD_PID=$!
    echo $DASHBOARD_PID > ../../logs/dashboard-service.pid
    
    # Wait for service to start
    sleep 8
    
    # Check if service is running
    if kill -0 $DASHBOARD_PID 2>/dev/null; then
        print_success "Dashboard Service started successfully (PID: $DASHBOARD_PID)"
        
        # Test the health endpoint
        if curl -s "http://localhost:${DASHBOARD_PORT}/api/health" > /dev/null; then
            print_success "Dashboard Service health check passed"
        else
            print_warning "Dashboard Service health check failed"
        fi
    else
        print_error "Failed to start Dashboard Service"
        exit 1
    fi
    
    cd ../..
}

# Function to create logs directory
setup_logging() {
    print_status "Setting up logging..."
    
    mkdir -p logs
    touch logs/analytics-service.log
    touch logs/dashboard-service.log
    
    print_success "Logging setup completed"
}

# Function to display service status
show_service_status() {
    print_header "Service Status Summary"
    echo ""
    
    # Analytics Service
    if [ -f "logs/analytics-service.pid" ]; then
        ANALYTICS_PID=$(cat logs/analytics-service.pid)
        if kill -0 $ANALYTICS_PID 2>/dev/null; then
            print_success "✅ Analytics Service (PID: $ANALYTICS_PID) - http://localhost:${ANALYTICS_PORT}"
        else
            print_error "❌ Analytics Service - Not running"
        fi
    else
        print_error "❌ Analytics Service - PID file not found"
    fi
    
    # Dashboard Service
    if [ -f "logs/dashboard-service.pid" ]; then
        DASHBOARD_PID=$(cat logs/dashboard-service.pid)
        if kill -0 $DASHBOARD_PID 2>/dev/null; then
            print_success "✅ Dashboard Service (PID: $DASHBOARD_PID) - http://localhost:${DASHBOARD_PORT}"
        else
            print_error "❌ Dashboard Service - Not running"
        fi
    else
        print_error "❌ Dashboard Service - PID file not found"
    fi
    
    # Redis
    if command -v docker &> /dev/null && docker ps | grep -q "redis-analytics"; then
        print_success "✅ Redis Cache - Running in Docker"
    else
        print_warning "⚠️ Redis Cache - Not available"
    fi
    
    # Database
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        print_success "✅ PostgreSQL Database - Connected"
    else
        print_error "❌ PostgreSQL Database - Connection failed"
    fi
    
    echo ""
    print_header "Phase 1 Implementation URLs:"
    echo ""
    echo "🚀 Enhanced Analytics Dashboard: http://localhost:${DASHBOARD_PORT}/analytics/enhanced"
    echo "📊 Analytics API Health: http://localhost:${ANALYTICS_PORT}/api/enhanced-analytics/health"
    echo "📈 Dashboard Metrics API: http://localhost:${ANALYTICS_PORT}/api/enhanced-analytics/dashboard"
    echo "⚡ Real-time Revenue API: http://localhost:${ANALYTICS_PORT}/api/enhanced-analytics/realtime/revenue"
    echo ""
    print_header "Performance Targets (Phase 1):"
    echo "• Query response time: <100ms for dashboard queries"
    echo "• Data ingestion: 10,000+ events/second"
    echo "• Storage compression: 70% ratio"
    echo ""
    print_status "Logs available in:"
    echo "• Analytics Service: logs/analytics-service.log"
    echo "• Dashboard Service: logs/dashboard-service.log"
    echo ""
}

# Function to stop services
stop_services() {
    print_header "Stopping Phase 1 services..."
    
    # Stop Analytics Service
    if [ -f "logs/analytics-service.pid" ]; then
        ANALYTICS_PID=$(cat logs/analytics-service.pid)
        if kill -0 $ANALYTICS_PID 2>/dev/null; then
            kill $ANALYTICS_PID
            print_status "Analytics Service stopped"
        fi
        rm -f logs/analytics-service.pid
    fi
    
    # Stop Dashboard Service
    if [ -f "logs/dashboard-service.pid" ]; then
        DASHBOARD_PID=$(cat logs/dashboard-service.pid)
        if kill -0 $DASHBOARD_PID 2>/dev/null; then
            kill $DASHBOARD_PID
            print_status "Dashboard Service stopped"
        fi
        rm -f logs/dashboard-service.pid
    fi
    
    # Stop Redis container
    if command -v docker &> /dev/null && docker ps | grep -q "redis-analytics"; then
        docker stop redis-analytics > /dev/null 2>&1
        docker rm redis-analytics > /dev/null 2>&1
        print_status "Redis container stopped"
    fi
    
    print_success "All services stopped"
}

# Main execution
main() {
    echo ""
    print_header "=== Phase 1: Enhanced Analytics Platform Setup ==="
    echo ""
    
    case "${1:-start}" in
        "start")
            check_prerequisites
            setup_logging
            setup_database
            start_redis
            start_analytics_service
            start_dashboard_service
            show_service_status
            ;;
        "stop")
            stop_services
            ;;
        "status")
            show_service_status
            ;;
        "restart")
            stop_services
            sleep 2
            main start
            ;;
        "logs")
            if [ -n "$2" ]; then
                case "$2" in
                    "analytics")
                        tail -f logs/analytics-service.log
                        ;;
                    "dashboard")
                        tail -f logs/dashboard-service.log
                        ;;
                    *)
                        print_error "Unknown log type. Use: analytics, dashboard"
                        ;;
                esac
            else
                print_status "Available logs: analytics, dashboard"
                print_status "Usage: $0 logs [analytics|dashboard]"
            fi
            ;;
        "help"|"--help"|"-h")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  start     Start all Phase 1 services (default)"
            echo "  stop      Stop all services"
            echo "  restart   Restart all services"
            echo "  status    Show service status"
            echo "  logs      Show logs (analytics|dashboard)"
            echo "  help      Show this help message"
            echo ""
            echo "Environment Variables:"
            echo "  DB_HOST        Database host (default: localhost)"
            echo "  DB_PORT        Database port (default: 5432)"
            echo "  DB_NAME        Database name (default: ecommerce_analytics)"
            echo "  DB_USER        Database user (default: postgres)"
            echo "  DB_PASSWORD    Database password"
            echo "  ANALYTICS_PORT Analytics service port (default: 3002)"
            echo "  DASHBOARD_PORT Dashboard service port (default: 8000)"
            echo ""
            ;;
        *)
            print_error "Unknown command: $1"
            print_status "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Handle script execution
main "$@"
