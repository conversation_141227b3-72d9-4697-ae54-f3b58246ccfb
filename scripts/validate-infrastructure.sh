#!/bin/bash

# Infrastructure Validation Script
# Validates Terraform configuration, AWS resources, and deployment readiness

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TERRAFORM_DIR="infrastructure/terraform"
ENVIRONMENT="${ENVIRONMENT:-production}"
AWS_REGION="${AWS_REGION:-us-east-1}"

# Counters
CHECKS_PASSED=0
CHECKS_FAILED=0
WARNINGS=0

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓ $1${NC}"
    ((CHECKS_PASSED++))
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠ WARNING: $1${NC}"
    ((WARNINGS++))
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗ ERROR: $1${NC}"
    ((CHECKS_FAILED++))
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Validate prerequisites
validate_prerequisites() {
    info "Validating prerequisites..."
    
    # Check required tools
    local required_tools=("terraform" "aws" "kubectl" "jq" "helm")
    
    for tool in "${required_tools[@]}"; do
        if command_exists "$tool"; then
            log "$tool is installed"
        else
            error "$tool is not installed"
        fi
    done
    
    # Check AWS credentials
    if aws sts get-caller-identity &> /dev/null; then
        local account_id=$(aws sts get-caller-identity --query Account --output text)
        local user_arn=$(aws sts get-caller-identity --query Arn --output text)
        log "AWS credentials configured (Account: $account_id, User: $user_arn)"
    else
        error "AWS credentials not configured"
    fi
    
    # Check Terraform version
    if command_exists terraform; then
        local tf_version=$(terraform version -json | jq -r '.terraform_version')
        if [[ $(echo "$tf_version 1.0.0" | tr " " "\n" | sort -V | head -n1) == "1.0.0" ]]; then
            log "Terraform version $tf_version is compatible"
        else
            error "Terraform version $tf_version is too old (minimum: 1.0.0)"
        fi
    fi
}

# Validate Terraform configuration
validate_terraform() {
    info "Validating Terraform configuration..."
    
    cd "$TERRAFORM_DIR"
    
    # Initialize Terraform
    if terraform init -backend=false &> /dev/null; then
        log "Terraform initialization successful"
    else
        error "Terraform initialization failed"
        cd - > /dev/null
        return 1
    fi
    
    # Validate configuration
    if terraform validate &> /dev/null; then
        log "Terraform configuration is valid"
    else
        error "Terraform configuration validation failed"
        terraform validate
    fi
    
    # Check if tfvars file exists
    if [[ -f "environments/${ENVIRONMENT}.tfvars" ]]; then
        log "Environment configuration file exists: environments/${ENVIRONMENT}.tfvars"
    else
        error "Environment configuration file missing: environments/${ENVIRONMENT}.tfvars"
    fi
    
    # Format check
    if terraform fmt -check &> /dev/null; then
        log "Terraform files are properly formatted"
    else
        warn "Terraform files need formatting (run: terraform fmt)"
    fi
    
    cd - > /dev/null
}

# Validate AWS resources and permissions
validate_aws_resources() {
    info "Validating AWS resources and permissions..."
    
    # Check S3 bucket for Terraform state
    local state_bucket="ecommerce-analytics-terraform-state"
    if aws s3 ls "s3://$state_bucket" &> /dev/null; then
        log "Terraform state S3 bucket exists: $state_bucket"
    else
        warn "Terraform state S3 bucket does not exist: $state_bucket"
        info "Create it with: aws s3 mb s3://$state_bucket --region $AWS_REGION"
    fi
    
    # Check DynamoDB table for state locking
    local lock_table="terraform-state-lock"
    if aws dynamodb describe-table --table-name "$lock_table" --region "$AWS_REGION" &> /dev/null; then
        log "Terraform state lock DynamoDB table exists: $lock_table"
    else
        warn "Terraform state lock DynamoDB table does not exist: $lock_table"
    fi
    
    # Check ECR repositories
    local services=("dashboard" "analytics" "integration" "error-tracking" "admin")
    for service in "${services[@]}"; do
        if aws ecr describe-repositories --repository-names "$service" --region "$AWS_REGION" &> /dev/null; then
            log "ECR repository exists: $service"
        else
            warn "ECR repository does not exist: $service"
        fi
    done
    
    # Check IAM permissions (basic check)
    if aws iam get-user &> /dev/null || aws sts get-caller-identity | grep -q "assumed-role"; then
        log "IAM permissions appear to be configured"
    else
        error "IAM permissions check failed"
    fi
}

# Validate Kubernetes configuration
validate_kubernetes() {
    info "Validating Kubernetes configuration..."
    
    # Check if kubectl is configured
    if kubectl cluster-info &> /dev/null; then
        local cluster_name=$(kubectl config current-context)
        log "kubectl is configured (current context: $cluster_name)"
    else
        warn "kubectl is not configured or cluster is not accessible"
        return 0
    fi
    
    # Check Kubernetes manifests
    local k8s_dir="infrastructure/k8s/production"
    if [[ -d "$k8s_dir" ]]; then
        log "Kubernetes manifests directory exists: $k8s_dir"
        
        # Validate YAML files
        local yaml_files=$(find "$k8s_dir" -name "*.yaml" -o -name "*.yml")
        for file in $yaml_files; do
            if kubectl apply --dry-run=client -f "$file" &> /dev/null; then
                log "Kubernetes manifest is valid: $(basename $file)"
            else
                error "Kubernetes manifest validation failed: $(basename $file)"
            fi
        done
    else
        error "Kubernetes manifests directory not found: $k8s_dir"
    fi
}

# Validate secrets configuration
validate_secrets() {
    info "Validating secrets configuration..."
    
    # Check if External Secrets Operator CRDs exist
    if kubectl get crd externalsecrets.external-secrets.io &> /dev/null; then
        log "External Secrets Operator CRDs are installed"
    else
        warn "External Secrets Operator CRDs not found"
    fi
    
    # Check AWS Secrets Manager access
    if aws secretsmanager list-secrets --region "$AWS_REGION" &> /dev/null; then
        log "AWS Secrets Manager access confirmed"
    else
        error "Cannot access AWS Secrets Manager"
    fi
    
    # Check for required secrets (if they exist)
    local required_secrets=(
        "ecommerce-analytics/production/database"
        "ecommerce-analytics/production/redis"
        "ecommerce-analytics/production/application"
    )
    
    for secret in "${required_secrets[@]}"; do
        if aws secretsmanager describe-secret --secret-id "$secret" --region "$AWS_REGION" &> /dev/null; then
            log "Required secret exists: $secret"
        else
            warn "Required secret not found: $secret"
        fi
    done
}

# Validate CI/CD configuration
validate_cicd() {
    info "Validating CI/CD configuration..."
    
    # Check GitHub Actions workflows
    local workflows_dir=".github/workflows"
    if [[ -d "$workflows_dir" ]]; then
        log "GitHub Actions workflows directory exists"
        
        local workflow_files=$(find "$workflows_dir" -name "*.yml" -o -name "*.yaml")
        for file in $workflow_files; do
            if [[ -f "$file" ]]; then
                log "Workflow file exists: $(basename $file)"
            fi
        done
    else
        warn "GitHub Actions workflows directory not found"
    fi
    
    # Check Docker files
    local services=("dashboard" "analytics" "integration")
    for service in "${services[@]}"; do
        local dockerfile="services/$service/Dockerfile"
        if [[ -f "$dockerfile" ]]; then
            log "Dockerfile exists for $service"
        else
            warn "Dockerfile not found for $service"
        fi
    done
}

# Validate testing configuration
validate_testing() {
    info "Validating testing configuration..."
    
    # Check Jest configuration
    if [[ -f "testing/jest.config.js" ]]; then
        log "Jest configuration exists"
    else
        warn "Jest configuration not found"
    fi
    
    # Check load testing configuration
    if [[ -f "performance/load-testing/artillery-config.yml" ]]; then
        log "Artillery load testing configuration exists"
    else
        warn "Artillery load testing configuration not found"
    fi
    
    if [[ -f "performance/load-testing/k6-config.js" ]]; then
        log "K6 load testing configuration exists"
    else
        warn "K6 load testing configuration not found"
    fi
    
    # Check test directories
    local test_dirs=("testing/unit" "testing/integration" "testing/e2e")
    for dir in "${test_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            log "Test directory exists: $dir"
        else
            warn "Test directory not found: $dir"
        fi
    done
}

# Generate validation report
generate_report() {
    info "Generating validation report..."
    
    cat > infrastructure-validation-report.md << EOF
# Infrastructure Validation Report

Generated on: $(date)
Environment: $ENVIRONMENT
AWS Region: $AWS_REGION

## Summary

- ✅ Checks Passed: $CHECKS_PASSED
- ⚠️  Warnings: $WARNINGS
- ❌ Checks Failed: $CHECKS_FAILED

## Validation Status

$(if [[ $CHECKS_FAILED -eq 0 ]]; then
    echo "🎉 **READY FOR DEPLOYMENT** - All critical checks passed!"
elif [[ $CHECKS_FAILED -le 2 ]]; then
    echo "⚠️  **NEEDS ATTENTION** - Some issues need to be resolved before deployment"
else
    echo "❌ **NOT READY** - Multiple critical issues found"
fi)

## Next Steps

$(if [[ $CHECKS_FAILED -eq 0 ]]; then
    cat << 'NEXT_STEPS'
1. Run the secrets setup script: `./scripts/setup-production-secrets.sh`
2. Execute Terraform plan: `cd infrastructure/terraform && terraform plan -var-file=environments/production.tfvars`
3. Run the complete testing suite
4. Proceed with deployment
NEXT_STEPS
else
    echo "1. Address the failed checks listed above"
    echo "2. Re-run this validation script"
    echo "3. Proceed only when all critical checks pass"
fi)

## Warnings to Address

$(if [[ $WARNINGS -gt 0 ]]; then
    echo "- Review all warnings above"
    echo "- Consider addressing warnings before production deployment"
    echo "- Some warnings may be acceptable for initial deployment"
else
    echo "No warnings found."
fi)

EOF
    
    log "Validation report generated: infrastructure-validation-report.md"
}

# Main execution
main() {
    info "Starting infrastructure validation for $ENVIRONMENT environment..."
    echo
    
    validate_prerequisites
    echo
    validate_terraform
    echo
    validate_aws_resources
    echo
    validate_kubernetes
    echo
    validate_secrets
    echo
    validate_cicd
    echo
    validate_testing
    echo
    generate_report
    
    echo
    info "Infrastructure validation completed!"
    info "Checks passed: $CHECKS_PASSED | Warnings: $WARNINGS | Failed: $CHECKS_FAILED"
    
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        log "Infrastructure is ready for deployment! 🚀"
        exit 0
    else
        error "Infrastructure validation failed. Please address the issues above."
        exit 1
    fi
}

# Execute main function
main "$@"
