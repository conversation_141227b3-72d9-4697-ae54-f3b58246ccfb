# Multi-stage Dockerfile for Deno Admin Service
FROM denoland/deno:2.4.0 AS base

# Set working directory
WORKDIR /app

# Create non-root user for security
RUN groupadd -r deno && useradd -r -g deno -s /bin/false deno

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Development stage
FROM base AS development

# Copy configuration files
COPY deno.json ./

# Copy source code
COPY . .

# Cache dependencies
RUN deno cache --import-map=deno.json src/main.ts

# Create necessary directories and set permissions
RUN mkdir -p logs uploads && \
    chown -R deno:deno /app

USER deno

# Expose port
EXPOSE 3004

# Start development server with watch mode
CMD ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "--watch", "src/main.ts"]

# Production stage
FROM base AS production

# Set production environment variables
ENV DENO_ENV=production
ENV NODE_ENV=production

# Copy configuration files
COPY deno.json ./

# Copy source code
COPY src/ ./src/
COPY healthcheck.ts ./

# Cache dependencies and compile for faster startup
RUN deno cache --import-map=deno.json src/main.ts && \
    deno cache --import-map=deno.json healthcheck.ts

# Create necessary directories and set permissions
RUN mkdir -p logs uploads /tmp && \
    chown -R deno:deno /app /tmp

# Switch to non-root user
USER deno

# Expose port
EXPOSE 3004

# Health check using our custom script
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD deno run --allow-net --allow-env healthcheck.ts || exit 1

# Start the application with optimized flags
CMD ["deno", "run", \
     "--allow-net", \
     "--allow-env", \
     "--allow-read", \
     "--no-prompt", \
     "--quiet", \
     "src/main.ts"]

# Optimized stage for minimal size
FROM base AS optimized

# Set production environment
ENV DENO_ENV=production
ENV NODE_ENV=production

# Copy only necessary files
COPY deno.json ./
COPY src/ ./src/
COPY healthcheck.ts ./

# Compile to binary for fastest startup
RUN deno compile \
    --allow-net \
    --allow-env \
    --allow-read \
    --output=admin-service \
    src/main.ts

# Create minimal runtime directories
RUN mkdir -p logs uploads && \
    chown -R deno:deno /app

USER deno

EXPOSE 3004

# Use compiled binary for fastest startup
CMD ["./admin-service"]

# Default target is production
FROM production
