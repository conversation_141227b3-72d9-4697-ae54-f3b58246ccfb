{"name": "@ecommerce-analytics/admin-service-deno", "version": "1.0.0", "exports": "./src/main.ts", "tasks": {"dev": "deno run --allow-net --allow-env --allow-read --watch src/main.ts", "start": "deno run --allow-net --allow-env --allow-read src/main.ts", "test": "deno test --allow-all tests/", "test:watch": "deno test --allow-all --watch tests/", "test:coverage": "deno test --allow-all --coverage=coverage tests/", "lint": "deno lint src/ tests/", "fmt": "deno fmt src/ tests/", "check": "deno check src/main.ts", "cache": "deno cache --import-map=import_map.json src/main.ts", "compile": "deno compile --allow-net --allow-env --allow-read --output=admin-service src/main.ts"}, "imports": {"@oak/oak": "jsr:@oak/oak@^17.1.0", "@std/assert": "jsr:@std/assert@^1.0.0", "@std/testing": "jsr:@std/testing@^1.0.0", "@std/log": "jsr:@std/log@^0.224.0", "@std/dotenv": "jsr:@std/dotenv@^0.225.0", "@std/crypto": "jsr:@std/crypto@^1.0.0", "@std/encoding": "jsr:@std/encoding@^1.0.0", "postgres": "https://deno.land/x/postgres@v0.19.3/mod.ts", "redis": "https://deno.land/x/redis@v0.32.3/mod.ts", "djwt": "https://deno.land/x/djwt@v3.0.2/mod.ts", "bcrypt": "https://deno.land/x/bcrypt@v0.4.1/mod.ts", "zod": "https://deno.land/x/zod@v3.22.4/mod.ts", "cors": "https://deno.land/x/cors@v1.2.2/mod.ts"}, "compilerOptions": {"allowJs": true, "lib": ["deno.window"], "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true}, "fmt": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve", "include": ["src/", "tests/"], "exclude": ["coverage/"]}, "lint": {"include": ["src/", "tests/"], "exclude": ["coverage/"], "rules": {"tags": ["recommended"], "include": ["ban-untagged-todo"], "exclude": ["no-unused-vars"]}}, "test": {"include": ["tests/"], "exclude": ["coverage/"]}}