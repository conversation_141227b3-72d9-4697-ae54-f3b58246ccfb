#!/usr/bin/env deno run --allow-net --allow-env

/**
 * Health check script for Deno admin service
 * Used by <PERSON>er healthcheck to verify service is running properly
 */

const HEALTH_CHECK_URL = `http://localhost:${Deno.env.get("ADMIN_PORT") || "3004"}/api/health`;
const TIMEOUT_MS = 5000;

async function healthCheck(): Promise<void> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);
    
    const response = await fetch(HEALTH_CHECK_URL, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Docker-Healthcheck/1.0'
      }
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error(`Health check failed with status: ${response.status}`);
    }
    
    const healthData = await response.json();
    
    if (healthData.status !== 'healthy') {
      throw new Error(`Service unhealthy: ${healthData.status}`);
    }
    
    console.log('✅ Health check passed');
    Deno.exit(0);
    
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    Deno.exit(1);
  }
}

// Run health check
await healthCheck();
