import { load } from "@std/dotenv";

// Load environment variables
await load({ export: true });

export interface DatabaseConfig {
  host: string;
  port: number;
  name: string;
  user: string;
  password: string;
  ssl: boolean;
  maxConnections: number;
  idleTimeout: number;
  connectionTimeout: number;
}

export interface RedisConfig {
  host: string;
  port: number;
  password: string;
  db: number;
  keyPrefix: string;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  enableOfflineQueue: boolean;
}

export interface JWTConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
  issuer: string;
  audience: string;
}

export interface CorsConfig {
  origins: string[];
}

export interface RateLimitConfig {
  windowMs: number;
  max: number;
}

export interface LoggingConfig {
  level: string;
  file: string;
  maxSize: string;
  maxFiles: number;
  format: string;
}

export interface UploadConfig {
  maxSize: number;
  allowedTypes: string[];
  destination: string;
}

export interface MonitoringConfig {
  interval: number;
  retentionDays: number;
  alertThresholds: {
    cpu: number;
    memory: number;
    disk: number;
    responseTime: number;
    errorRate: number;
  };
}

export interface BackupConfig {
  schedule: string;
  retention: number;
  compression: boolean;
  encryption: boolean;
  destination: string;
  s3: {
    enabled: boolean;
    bucket?: string;
    region: string;
    accessKeyId?: string;
    secretAccessKey?: string;
  };
}

export interface NotificationConfig {
  email: {
    enabled: boolean;
    host?: string;
    port: number;
    secure: boolean;
    user?: string;
    password?: string;
    from: string;
  };
  slack: {
    enabled: boolean;
    webhookUrl?: string;
    channel: string;
    username: string;
  };
  webhook: {
    enabled: boolean;
    url?: string;
    secret?: string;
  };
}

export interface ServiceConfig {
  analytics: {
    url: string;
    timeout: number;
  };
  dashboard: {
    url: string;
    timeout: number;
  };
  integration: {
    url: string;
    timeout: number;
  };
}

export interface SecurityConfig {
  bcryptRounds: number;
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  passwordMinLength: number;
  requirePasswordChange: boolean;
}

export interface FeatureConfig {
  systemMetrics: boolean;
  userManagement: boolean;
  logViewing: boolean;
  backupManagement: boolean;
  analyticsOverview: boolean;
  realTimeMonitoring: boolean;
  alertManagement: boolean;
  reportGeneration: boolean;
}

export interface Config {
  env: string;
  port: number;
  serveStatic: boolean;
  database: DatabaseConfig;
  redis: RedisConfig;
  jwt: JWTConfig;
  cors: CorsConfig;
  rateLimit: RateLimitConfig;
  logging: LoggingConfig;
  upload: UploadConfig;
  monitoring: MonitoringConfig;
  backup: BackupConfig;
  notifications: NotificationConfig;
  services: ServiceConfig;
  security: SecurityConfig;
  features: FeatureConfig;
}

const config: Config = {
  // Server configuration
  env: Deno.env.get("NODE_ENV") || "development",
  port: parseInt(Deno.env.get("ADMIN_PORT") || "3004"),
  serveStatic: Deno.env.get("SERVE_STATIC") === "true",

  // Database configuration
  database: {
    host: Deno.env.get("DB_HOST") || "localhost",
    port: parseInt(Deno.env.get("DB_PORT") || "5432"),
    name: Deno.env.get("DB_NAME") || "ecommerce_analytics",
    user: Deno.env.get("DB_USER") || "postgres",
    password: Deno.env.get("DB_PASSWORD") || "password",
    ssl: Deno.env.get("DB_SSL") === "true",
    maxConnections: parseInt(Deno.env.get("DB_MAX_CONNECTIONS") || "20"),
    idleTimeout: parseInt(Deno.env.get("DB_IDLE_TIMEOUT") || "30000"),
    connectionTimeout: parseInt(Deno.env.get("DB_CONNECTION_TIMEOUT") || "5000"),
  },

  // Redis configuration
  redis: {
    host: Deno.env.get("REDIS_HOST") || "localhost",
    port: parseInt(Deno.env.get("REDIS_PORT") || "6379"),
    password: Deno.env.get("REDIS_PASSWORD") || "",
    db: parseInt(Deno.env.get("REDIS_DB") || "0"),
    keyPrefix: Deno.env.get("REDIS_KEY_PREFIX") || "admin:",
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableOfflineQueue: false,
  },

  // JWT configuration
  jwt: {
    secret: Deno.env.get("JWT_SECRET") || "change-in-production",
    expiresIn: Deno.env.get("JWT_EXPIRES_IN") || "24h",
    refreshExpiresIn: Deno.env.get("JWT_REFRESH_EXPIRES_IN") || "7d",
    issuer: Deno.env.get("JWT_ISSUER") || "ecommerce-analytics-admin",
    audience: Deno.env.get("JWT_AUDIENCE") || "admin-dashboard",
  },

  // CORS configuration
  cors: {
    origins: (Deno.env.get("CORS_ORIGIN") || "http://localhost:3000").split(","),
  },

  // Rate limiting
  rateLimit: {
    windowMs: parseInt(Deno.env.get("RATE_LIMIT_WINDOW") || "900000"), // 15 minutes
    max: parseInt(Deno.env.get("RATE_LIMIT_MAX") || "100"), // requests per window
  },

  // Logging configuration
  logging: {
    level: Deno.env.get("LOG_LEVEL") || "info",
    file: Deno.env.get("LOG_FILE") || "logs/admin.log",
    maxSize: Deno.env.get("LOG_MAX_SIZE") || "10m",
    maxFiles: parseInt(Deno.env.get("LOG_MAX_FILES") || "5"),
    format: Deno.env.get("LOG_FORMAT") || "json",
  },

  // File upload configuration
  upload: {
    maxSize: parseInt(Deno.env.get("UPLOAD_MAX_SIZE") || "52428800"), // 50MB
    allowedTypes: (Deno.env.get("UPLOAD_ALLOWED_TYPES") || 
      "image/jpeg,image/png,text/csv,application/json").split(","),
    destination: Deno.env.get("UPLOAD_DESTINATION") || "uploads/",
  },

  // System monitoring
  monitoring: {
    interval: parseInt(Deno.env.get("MONITORING_INTERVAL") || "60000"), // 1 minute
    retentionDays: parseInt(Deno.env.get("MONITORING_RETENTION_DAYS") || "30"),
    alertThresholds: {
      cpu: parseInt(Deno.env.get("ALERT_CPU_THRESHOLD") || "80"),
      memory: parseInt(Deno.env.get("ALERT_MEMORY_THRESHOLD") || "85"),
      disk: parseInt(Deno.env.get("ALERT_DISK_THRESHOLD") || "90"),
      responseTime: parseInt(Deno.env.get("ALERT_RESPONSE_TIME_THRESHOLD") || "5000"),
      errorRate: parseInt(Deno.env.get("ALERT_ERROR_RATE_THRESHOLD") || "5"),
    },
  },

  // Backup configuration
  backup: {
    schedule: Deno.env.get("BACKUP_SCHEDULE") || "0 2 * * *", // Daily at 2 AM
    retention: parseInt(Deno.env.get("BACKUP_RETENTION_DAYS") || "30"),
    compression: Deno.env.get("BACKUP_COMPRESSION") === "true",
    encryption: Deno.env.get("BACKUP_ENCRYPTION") === "true",
    destination: Deno.env.get("BACKUP_DESTINATION") || "/opt/backups",
    s3: {
      enabled: Deno.env.get("BACKUP_S3_ENABLED") === "true",
      bucket: Deno.env.get("BACKUP_S3_BUCKET"),
      region: Deno.env.get("BACKUP_S3_REGION") || "us-east-1",
      accessKeyId: Deno.env.get("AWS_ACCESS_KEY_ID"),
      secretAccessKey: Deno.env.get("AWS_SECRET_ACCESS_KEY"),
    },
  },

  // Notification configuration
  notifications: {
    email: {
      enabled: Deno.env.get("EMAIL_ENABLED") === "true",
      host: Deno.env.get("EMAIL_HOST"),
      port: parseInt(Deno.env.get("EMAIL_PORT") || "587"),
      secure: Deno.env.get("EMAIL_SECURE") === "true",
      user: Deno.env.get("EMAIL_USER"),
      password: Deno.env.get("EMAIL_PASSWORD"),
      from: Deno.env.get("EMAIL_FROM") || "<EMAIL>",
    },
    slack: {
      enabled: Deno.env.get("SLACK_ENABLED") === "true",
      webhookUrl: Deno.env.get("SLACK_WEBHOOK_URL"),
      channel: Deno.env.get("SLACK_CHANNEL") || "#alerts",
      username: Deno.env.get("SLACK_USERNAME") || "Analytics Admin",
    },
    webhook: {
      enabled: Deno.env.get("WEBHOOK_ENABLED") === "true",
      url: Deno.env.get("WEBHOOK_URL"),
      secret: Deno.env.get("WEBHOOK_SECRET"),
    },
  },

  // External services
  services: {
    analytics: {
      url: Deno.env.get("ANALYTICS_SERVICE_URL") || "http://localhost:3002",
      timeout: parseInt(Deno.env.get("ANALYTICS_SERVICE_TIMEOUT") || "30000"),
    },
    dashboard: {
      url: Deno.env.get("DASHBOARD_SERVICE_URL") || "http://localhost:3001",
      timeout: parseInt(Deno.env.get("DASHBOARD_SERVICE_TIMEOUT") || "30000"),
    },
    integration: {
      url: Deno.env.get("INTEGRATION_SERVICE_URL") || "http://localhost:3003",
      timeout: parseInt(Deno.env.get("INTEGRATION_SERVICE_TIMEOUT") || "30000"),
    },
  },

  // Security configuration
  security: {
    bcryptRounds: parseInt(Deno.env.get("BCRYPT_ROUNDS") || "12"),
    sessionTimeout: parseInt(Deno.env.get("SESSION_TIMEOUT") || "1800000"), // 30 minutes
    maxLoginAttempts: parseInt(Deno.env.get("MAX_LOGIN_ATTEMPTS") || "5"),
    lockoutDuration: parseInt(Deno.env.get("LOCKOUT_DURATION") || "900000"), // 15 minutes
    passwordMinLength: parseInt(Deno.env.get("PASSWORD_MIN_LENGTH") || "8"),
    requirePasswordChange: Deno.env.get("REQUIRE_PASSWORD_CHANGE") === "true",
  },

  // Feature flags
  features: {
    systemMetrics: Deno.env.get("FEATURE_SYSTEM_METRICS") !== "false",
    userManagement: Deno.env.get("FEATURE_USER_MANAGEMENT") !== "false",
    logViewing: Deno.env.get("FEATURE_LOG_VIEWING") !== "false",
    backupManagement: Deno.env.get("FEATURE_BACKUP_MANAGEMENT") !== "false",
    analyticsOverview: Deno.env.get("FEATURE_ANALYTICS_OVERVIEW") !== "false",
    realTimeMonitoring: Deno.env.get("FEATURE_REAL_TIME_MONITORING") !== "false",
    alertManagement: Deno.env.get("FEATURE_ALERT_MANAGEMENT") !== "false",
    reportGeneration: Deno.env.get("FEATURE_REPORT_GENERATION") !== "false",
  },
};

// Validate required configuration
function validateConfig(): void {
  const required = [
    "database.password",
    "jwt.secret",
  ];

  const missing = required.filter((key) => {
    const value = key.split(".").reduce((obj: any, k) => obj?.[k], config);
    return !value || (typeof value === "string" && value.trim() === "");
  });

  if (missing.length > 0) {
    throw new Error(`Missing required configuration: ${missing.join(", ")}`);
  }

  // Warn about default values in production
  if (config.env === "production") {
    const warnings: string[] = [];

    if (config.jwt.secret === "change-in-production") {
      warnings.push("JWT secret is using default value");
    }

    if (config.database.password === "password") {
      warnings.push("Database password is using default value");
    }

    if (warnings.length > 0) {
      console.warn("Production configuration warnings:", warnings);
    }
  }
}

// Validate configuration on load
validateConfig();

export default config;
