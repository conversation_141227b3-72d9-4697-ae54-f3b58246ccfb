import { Context, Middleware } from "@oak/oak";
import { getLogger } from "@std/log";
import config from "../config/index.ts";

const logger = getLogger();

export interface AppError extends Error {
  status?: number;
  statusCode?: number;
  code?: string;
  details?: any;
}

export const errorHandler: Middleware = async (ctx: Context, next) => {
  try {
    await next();
  } catch (error) {
    const appError = error as AppError;
    
    // Determine status code
    const status = appError.status || appError.statusCode || 500;
    
    // Log error
    if (status >= 500) {
      logger.error("Server error:", {
        error: appError.message,
        stack: appError.stack,
        url: ctx.request.url.toString(),
        method: ctx.request.method,
        userAgent: ctx.request.headers.get("user-agent"),
        ip: ctx.request.ip,
      });
    } else {
      logger.warn("Client error:", {
        error: appError.message,
        status,
        url: ctx.request.url.toString(),
        method: ctx.request.method,
        ip: ctx.request.ip,
      });
    }

    // Set response status
    ctx.response.status = status;

    // Prepare error response
    const errorResponse: any = {
      error: true,
      message: appError.message || "Internal Server Error",
      timestamp: new Date().toISOString(),
      path: ctx.request.url.pathname,
      method: ctx.request.method,
    };

    // Add error code if available
    if (appError.code) {
      errorResponse.code = appError.code;
    }

    // Add details in development mode
    if (config.env === "development") {
      errorResponse.stack = appError.stack;
      if (appError.details) {
        errorResponse.details = appError.details;
      }
    }

    // Add request ID if available
    const requestId = ctx.state.requestId;
    if (requestId) {
      errorResponse.requestId = requestId;
    }

    ctx.response.body = errorResponse;
  }
};

// Custom error classes
export class ValidationError extends Error {
  status = 400;
  code = "VALIDATION_ERROR";
  
  constructor(message: string, public details?: any) {
    super(message);
    this.name = "ValidationError";
  }
}

export class AuthenticationError extends Error {
  status = 401;
  code = "AUTHENTICATION_ERROR";
  
  constructor(message: string = "Authentication required") {
    super(message);
    this.name = "AuthenticationError";
  }
}

export class AuthorizationError extends Error {
  status = 403;
  code = "AUTHORIZATION_ERROR";
  
  constructor(message: string = "Insufficient permissions") {
    super(message);
    this.name = "AuthorizationError";
  }
}

export class NotFoundError extends Error {
  status = 404;
  code = "NOT_FOUND";
  
  constructor(message: string = "Resource not found") {
    super(message);
    this.name = "NotFoundError";
  }
}

export class ConflictError extends Error {
  status = 409;
  code = "CONFLICT";
  
  constructor(message: string = "Resource conflict") {
    super(message);
    this.name = "ConflictError";
  }
}

export class RateLimitError extends Error {
  status = 429;
  code = "RATE_LIMIT_EXCEEDED";
  
  constructor(message: string = "Rate limit exceeded") {
    super(message);
    this.name = "RateLimitError";
  }
}

export class DatabaseError extends Error {
  status = 500;
  code = "DATABASE_ERROR";
  
  constructor(message: string = "Database operation failed") {
    super(message);
    this.name = "DatabaseError";
  }
}

export class ExternalServiceError extends Error {
  status = 502;
  code = "EXTERNAL_SERVICE_ERROR";
  
  constructor(message: string = "External service unavailable") {
    super(message);
    this.name = "ExternalServiceError";
  }
}

// Error factory functions
export function createValidationError(message: string, details?: any): ValidationError {
  return new ValidationError(message, details);
}

export function createAuthenticationError(message?: string): AuthenticationError {
  return new AuthenticationError(message);
}

export function createAuthorizationError(message?: string): AuthorizationError {
  return new AuthorizationError(message);
}

export function createNotFoundError(message?: string): NotFoundError {
  return new NotFoundError(message);
}

export function createConflictError(message?: string): ConflictError {
  return new ConflictError(message);
}

export function createRateLimitError(message?: string): RateLimitError {
  return new RateLimitError(message);
}

export function createDatabaseError(message?: string): DatabaseError {
  return new DatabaseError(message);
}

export function createExternalServiceError(message?: string): ExternalServiceError {
  return new ExternalServiceError(message);
}

// Async error wrapper
export function asyncHandler(
  fn: (ctx: Context, next: () => Promise<unknown>) => Promise<void>
): Middleware {
  return async (ctx: Context, next) => {
    try {
      await fn(ctx, next);
    } catch (error) {
      throw error;
    }
  };
}

// Validation helper
export function validateRequired(value: any, fieldName: string): void {
  if (value === undefined || value === null || value === "") {
    throw createValidationError(`${fieldName} is required`);
  }
}

export function validateEmail(email: string): void {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw createValidationError("Invalid email format");
  }
}

export function validatePassword(password: string): void {
  if (password.length < config.security.passwordMinLength) {
    throw createValidationError(
      `Password must be at least ${config.security.passwordMinLength} characters long`
    );
  }
}

export function validateUUID(uuid: string, fieldName: string = "ID"): void {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(uuid)) {
    throw createValidationError(`Invalid ${fieldName} format`);
  }
}
