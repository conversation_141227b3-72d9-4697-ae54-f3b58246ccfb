import { Context, Middleware } from "@oak/oak";
import { getLogger } from "@std/log";

const logger = getLogger();

export const loggingMiddleware: Middleware = async (ctx: Context, next) => {
  const start = Date.now();
  const requestId = crypto.randomUUID();
  
  // Add request ID to state for use in other middleware
  ctx.state.requestId = requestId;
  
  // Log request
  logger.info("Request started", {
    requestId,
    method: ctx.request.method,
    url: ctx.request.url.toString(),
    userAgent: ctx.request.headers.get("user-agent"),
    ip: ctx.request.ip,
  });
  
  await next();
  
  // Log response
  const duration = Date.now() - start;
  logger.info("Request completed", {
    requestId,
    method: ctx.request.method,
    url: ctx.request.url.toString(),
    status: ctx.response.status,
    duration: `${duration}ms`,
    ip: ctx.request.ip,
  });
};
