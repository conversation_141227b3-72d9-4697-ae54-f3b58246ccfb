import { Context, Middleware } from "@oak/oak";

export const securityMiddleware: Middleware = async (ctx: Context, next) => {
  // Security headers
  ctx.response.headers.set("X-Content-Type-Options", "nosniff");
  ctx.response.headers.set("X-Frame-Options", "DENY");
  ctx.response.headers.set("X-XSS-Protection", "1; mode=block");
  ctx.response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  ctx.response.headers.set("Permissions-Policy", "geolocation=(), microphone=(), camera=()");
  
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "script-src 'self'",
    "img-src 'self' data: https:",
    "connect-src 'self' ws: wss:",
  ].join("; ");
  
  ctx.response.headers.set("Content-Security-Policy", csp);
  
  // Remove server information
  ctx.response.headers.delete("Server");
  
  await next();
};
