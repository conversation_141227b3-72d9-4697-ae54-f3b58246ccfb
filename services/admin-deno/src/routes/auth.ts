import { Router } from "@oak/oak";
import { create } from "djwt";
import { hash, compare } from "bcrypt";
import { 
  getUserByEmail, 
  updateUserLoginAttempts, 
  updateUserLastLogin 
} from "../utils/database.ts";
import { 
  incrementLoginAttempts, 
  getLoginAttempts, 
  clearLoginAttempts 
} from "../utils/redis.ts";
import { 
  createAuthenticationError, 
  createValidationError,
  validateRequired,
  validateEmail,
  validatePassword,
} from "../middleware/errorHandler.ts";
import { authRateLimitMiddleware } from "../middleware/rateLimit.ts";
import config from "../config/index.ts";

const router = new Router();

// Apply auth rate limiting to all auth routes
router.use("/api/auth", authRateLimitMiddleware);

router.post("/api/auth/login", async (ctx) => {
  const body = await ctx.request.body.json();
  
  // Validate input
  validateRequired(body.email, "email");
  validateRequired(body.password, "password");
  validateEmail(body.email);
  
  const { email, password } = body;
  const identifier = ctx.request.ip;
  
  try {
    // Check login attempts
    const attempts = await getLoginAttempts(identifier);
    if (attempts >= config.security.maxLoginAttempts) {
      throw createAuthenticationError("Too many failed login attempts. Please try again later.");
    }
    
    // Get user from database
    const user = await getUserByEmail(email.toLowerCase());
    if (!user) {
      await incrementLoginAttempts(identifier);
      throw createAuthenticationError("Invalid email or password");
    }
    
    // Check if user is locked
    if (user.locked_until && new Date(user.locked_until) > new Date()) {
      throw createAuthenticationError("Account is temporarily locked");
    }
    
    // Verify password
    const isValidPassword = await compare(password, user.password_hash);
    if (!isValidPassword) {
      await incrementLoginAttempts(identifier);
      
      // Update user login attempts in database
      const newAttempts = user.login_attempts + 1;
      let lockedUntil: Date | undefined;
      
      if (newAttempts >= config.security.maxLoginAttempts) {
        lockedUntil = new Date(Date.now() + config.security.lockoutDuration);
      }
      
      await updateUserLoginAttempts(user.id, newAttempts, lockedUntil);
      
      throw createAuthenticationError("Invalid email or password");
    }
    
    // Clear login attempts on successful login
    await clearLoginAttempts(identifier);
    await updateUserLastLogin(user.id);
    
    // Create JWT token
    const key = await crypto.subtle.importKey(
      "raw",
      new TextEncoder().encode(config.jwt.secret),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign", "verify"]
    );
    
    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
      iss: config.jwt.issuer,
      aud: config.jwt.audience,
    };
    
    const token = await create({ alg: "HS256", typ: "JWT" }, payload, key);
    
    ctx.response.body = {
      success: true,
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        lastLogin: user.last_login,
      },
    };
    
  } catch (error) {
    throw error;
  }
});

router.post("/api/auth/logout", async (ctx) => {
  // In a stateless JWT system, logout is handled client-side
  // But we can add token blacklisting here if needed
  
  ctx.response.body = {
    success: true,
    message: "Logged out successfully",
  };
});

router.post("/api/auth/refresh", async (ctx) => {
  // For now, we'll require re-authentication
  // In the future, we can implement refresh tokens
  
  throw createAuthenticationError("Token refresh not implemented. Please log in again.");
});

router.get("/api/auth/me", async (ctx) => {
  // This route would typically be protected by auth middleware
  // For now, return an error since we don't have the user in context
  
  throw createAuthenticationError("Authentication required");
});

export default router;
