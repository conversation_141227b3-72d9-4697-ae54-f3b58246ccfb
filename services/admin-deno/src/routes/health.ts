import { Router } from "@oak/oak";
import { checkDatabaseHealth } from "../utils/database.ts";
import { checkRedisHealth } from "../utils/redis.ts";
import config from "../config/index.ts";

const router = new Router();

router.get("/api/health", async (ctx) => {
  const startTime = Date.now();
  
  // Check database health
  const dbHealthy = await checkDatabaseHealth();
  
  // Check Redis health
  const redisHealthy = await checkRedisHealth();
  
  // Get system information
  const systemInfo = {
    pid: Deno.pid,
    version: Deno.version,
    memory: Deno.systemMemoryInfo(),
    uptime: Date.now() - startTime,
  };
  
  const health = {
    status: dbHealthy && redisHealthy ? "healthy" : "unhealthy",
    timestamp: new Date().toISOString(),
    service: "admin-service-deno",
    version: "1.0.0",
    environment: config.env,
    checks: {
      database: {
        status: dbHealthy ? "healthy" : "unhealthy",
        responseTime: Date.now() - startTime,
      },
      redis: {
        status: redisHealthy ? "healthy" : "unhealthy",
        responseTime: Date.now() - startTime,
      },
    },
    system: systemInfo,
  };
  
  ctx.response.status = health.status === "healthy" ? 200 : 503;
  ctx.response.body = health;
});

router.get("/api/health/ready", async (ctx) => {
  const dbHealthy = await checkDatabaseHealth();
  const redisHealthy = await checkRedisHealth();
  
  if (dbHealthy && redisHealthy) {
    ctx.response.status = 200;
    ctx.response.body = { status: "ready" };
  } else {
    ctx.response.status = 503;
    ctx.response.body = { 
      status: "not ready",
      database: dbHealthy,
      redis: redisHealthy,
    };
  }
});

router.get("/api/health/live", (ctx) => {
  ctx.response.status = 200;
  ctx.response.body = { status: "alive" };
});

export default router;
