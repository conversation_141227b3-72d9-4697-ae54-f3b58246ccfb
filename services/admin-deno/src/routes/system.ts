import { Router } from "@oak/oak";
import { 
  getSystemMetrics, 
  recordSystemMetric,
  getOrganizationStats,
  getUserStats 
} from "../utils/database.ts";
import { requireAdmin } from "../middleware/auth.ts";

const router = new Router();

// Apply admin role requirement to all system routes
router.use("/api/system", requireAdmin());

router.get("/api/system/metrics", async (ctx) => {
  const url = new URL(ctx.request.url);
  const metricName = url.searchParams.get("metric");
  const hours = parseInt(url.searchParams.get("hours") || "24");
  
  const metrics = await getSystemMetrics(metricName || undefined, hours);
  
  ctx.response.body = {
    success: true,
    data: metrics,
    count: metrics.length,
  };
});

router.post("/api/system/metrics", async (ctx) => {
  const body = await ctx.request.body.json();
  
  const { name, value, unit, metadata } = body;
  
  if (!name || value === undefined || !unit) {
    ctx.response.status = 400;
    ctx.response.body = {
      error: "Missing required fields: name, value, unit",
    };
    return;
  }
  
  await recordSystemMetric(name, value, unit, metadata);
  
  ctx.response.body = {
    success: true,
    message: "Metric recorded successfully",
  };
});

router.get("/api/system/stats", async (ctx) => {
  // Get system information using Deno APIs
  const systemInfo = Deno.systemMemoryInfo();
  
  // Get database stats
  const orgStats = await getOrganizationStats();
  const userStats = await getUserStats();
  
  const stats = {
    system: {
      memory: {
        total: systemInfo.total,
        free: systemInfo.free,
        available: systemInfo.available,
        used: systemInfo.total - systemInfo.free,
        usagePercent: ((systemInfo.total - systemInfo.free) / systemInfo.total) * 100,
      },
      process: {
        pid: Deno.pid,
        version: Deno.version,
        uptime: Date.now(), // This would need to be tracked from startup
      },
    },
    database: {
      organizations: orgStats,
      users: userStats,
    },
    timestamp: new Date().toISOString(),
  };
  
  ctx.response.body = {
    success: true,
    data: stats,
  };
});

router.get("/api/system/health", async (ctx) => {
  // This is similar to the health endpoint but with more detailed admin info
  const { checkDatabaseHealth } = await import("../utils/database.ts");
  const { checkRedisHealth } = await import("../utils/redis.ts");
  
  const dbHealthy = await checkDatabaseHealth();
  const redisHealthy = await checkRedisHealth();
  
  const health = {
    overall: dbHealthy && redisHealthy ? "healthy" : "unhealthy",
    services: {
      database: dbHealthy ? "healthy" : "unhealthy",
      redis: redisHealthy ? "healthy" : "unhealthy",
    },
    timestamp: new Date().toISOString(),
  };
  
  ctx.response.body = {
    success: true,
    data: health,
  };
});

export default router;
