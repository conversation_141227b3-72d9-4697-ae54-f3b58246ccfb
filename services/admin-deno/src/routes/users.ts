import { Router } from "@oak/oak";
import { requireAdmin } from "../middleware/auth.ts";

const router = new Router();

// Apply admin role requirement to all user routes
router.use("/api/users", requireAdmin());

router.get("/api/users", async (ctx) => {
  // This would fetch users from the database
  // For now, return a placeholder response
  
  ctx.response.body = {
    success: true,
    data: [],
    message: "User management endpoints not yet implemented",
  };
});

router.get("/api/users/:id", async (ctx) => {
  const { id } = ctx.params;
  
  ctx.response.body = {
    success: true,
    data: null,
    message: `User ${id} endpoint not yet implemented`,
  };
});

export default router;
