import { Client, Pool } from "postgres";
import { getLogger } from "@std/log";
import config from "../config/index.ts";

const logger = getLogger();

let pool: Pool;

export async function initializeDatabase(): Promise<void> {
  try {
    // Create connection pool
    pool = new Pool({
      user: config.database.user,
      password: config.database.password,
      database: config.database.name,
      hostname: config.database.host,
      port: config.database.port,
      tls: config.database.ssl ? { enabled: true } : undefined,
    }, config.database.maxConnections);

    // Test connection
    const client = await pool.connect();
    try {
      await client.queryObject("SELECT 1");
    } finally {
      client.end();
    }

    logger.info("Database pool initialized successfully");
  } catch (error) {
    logger.error("Failed to initialize database:", error);
    throw error;
  }
}

export function getPool(): Pool {
  if (!pool) {
    throw new Error("Database pool not initialized. Call initializeDatabase() first.");
  }
  return pool;
}

export async function getClient(): Promise<Client> {
  return await getPool().connect();
}

// Multi-tenant query helper
export async function queryWithTenant<T = unknown>(
  query: string,
  tenantId: string,
  params: unknown[] = []
): Promise<T[]> {
  const client = await getClient();
  try {
    // Ensure tenant_id is included in the query
    const tenantParams = [tenantId, ...params];
    const result = await client.queryObject<T>(query, tenantParams);
    return result.rows;
  } finally {
    client.end();
  }
}

// Generic query helper
export async function query<T = unknown>(
  sql: string,
  params: unknown[] = []
): Promise<T[]> {
  const client = await getClient();
  try {
    const result = await client.queryObject<T>(sql, params);
    return result.rows;
  } finally {
    client.end();
  }
}

// Transaction helper
export async function transaction<T>(
  callback: (client: Client) => Promise<T>
): Promise<T> {
  const client = await getClient();
  try {
    await client.queryObject("BEGIN");
    const result = await callback(client);
    await client.queryObject("COMMIT");
    return result;
  } catch (error) {
    await client.queryObject("ROLLBACK");
    throw error;
  } finally {
    client.end();
  }
}

// Health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const client = await getClient();
    try {
      await client.queryObject("SELECT 1");
      return true;
    } finally {
      client.end();
    }
  } catch (error) {
    logger.error("Database health check failed:", error);
    return false;
  }
}

// Close pool
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    logger.info("Database pool closed");
  }
}

// Admin-specific database operations
export interface User {
  id: string;
  email: string;
  password_hash: string;
  role: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
  login_attempts: number;
  locked_until?: Date;
}

export interface Organization {
  id: string;
  name: string;
  domain: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  settings: Record<string, unknown>;
}

export interface SystemMetric {
  id: string;
  metric_name: string;
  metric_value: number;
  metric_unit: string;
  recorded_at: Date;
  metadata?: Record<string, unknown>;
}

// User operations
export async function getUserByEmail(email: string): Promise<User | null> {
  const users = await query<User>(
    "SELECT * FROM admin_users WHERE email = $1 AND is_active = true",
    [email]
  );
  return users[0] || null;
}

export async function getUserById(id: string): Promise<User | null> {
  const users = await query<User>(
    "SELECT * FROM admin_users WHERE id = $1 AND is_active = true",
    [id]
  );
  return users[0] || null;
}

export async function updateUserLoginAttempts(
  userId: string,
  attempts: number,
  lockedUntil?: Date
): Promise<void> {
  await query(
    `UPDATE admin_users 
     SET login_attempts = $2, locked_until = $3, updated_at = NOW() 
     WHERE id = $1`,
    [userId, attempts, lockedUntil]
  );
}

export async function updateUserLastLogin(userId: string): Promise<void> {
  await query(
    `UPDATE admin_users 
     SET last_login = NOW(), login_attempts = 0, locked_until = NULL, updated_at = NOW() 
     WHERE id = $1`,
    [userId]
  );
}

// Organization operations
export async function getAllOrganizations(): Promise<Organization[]> {
  return await query<Organization>(
    "SELECT * FROM organizations ORDER BY created_at DESC"
  );
}

export async function getOrganizationById(id: string): Promise<Organization | null> {
  const orgs = await query<Organization>(
    "SELECT * FROM organizations WHERE id = $1",
    [id]
  );
  return orgs[0] || null;
}

// System metrics operations
export async function recordSystemMetric(
  name: string,
  value: number,
  unit: string,
  metadata?: Record<string, unknown>
): Promise<void> {
  await query(
    `INSERT INTO system_metrics (metric_name, metric_value, metric_unit, metadata, recorded_at)
     VALUES ($1, $2, $3, $4, NOW())`,
    [name, value, unit, metadata ? JSON.stringify(metadata) : null]
  );
}

export async function getSystemMetrics(
  metricName?: string,
  hours: number = 24
): Promise<SystemMetric[]> {
  const baseQuery = `
    SELECT * FROM system_metrics 
    WHERE recorded_at >= NOW() - INTERVAL '${hours} hours'
  `;
  
  if (metricName) {
    return await query<SystemMetric>(
      baseQuery + " AND metric_name = $1 ORDER BY recorded_at DESC",
      [metricName]
    );
  } else {
    return await query<SystemMetric>(
      baseQuery + " ORDER BY recorded_at DESC"
    );
  }
}

// Analytics aggregation queries
export async function getOrganizationStats(): Promise<{
  total: number;
  active: number;
  inactive: number;
}> {
  const result = await query<{
    total: number;
    active: number;
    inactive: number;
  }>(`
    SELECT 
      COUNT(*) as total,
      COUNT(*) FILTER (WHERE is_active = true) as active,
      COUNT(*) FILTER (WHERE is_active = false) as inactive
    FROM organizations
  `);
  
  return result[0] || { total: 0, active: 0, inactive: 0 };
}

export async function getUserStats(): Promise<{
  total: number;
  active: number;
  locked: number;
}> {
  const result = await query<{
    total: number;
    active: number;
    locked: number;
  }>(`
    SELECT 
      COUNT(*) as total,
      COUNT(*) FILTER (WHERE is_active = true AND (locked_until IS NULL OR locked_until < NOW())) as active,
      COUNT(*) FILTER (WHERE locked_until IS NOT NULL AND locked_until > NOW()) as locked
    FROM admin_users
  `);
  
  return result[0] || { total: 0, active: 0, locked: 0 };
}
