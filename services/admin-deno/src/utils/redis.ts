import { connect, Redis } from "redis";
import { getLogger } from "@std/log";
import config from "../config/index.ts";

const logger = getLogger();

let redis: Redis;

export async function initializeRedis(): Promise<void> {
  try {
    redis = await connect({
      hostname: config.redis.host,
      port: config.redis.port,
      password: config.redis.password || undefined,
      db: config.redis.db,
    });

    // Test connection
    await redis.ping();
    logger.info("Redis connection established successfully");
  } catch (error) {
    logger.error("Failed to initialize Redis:", error);
    throw error;
  }
}

export function getRedis(): Redis {
  if (!redis) {
    throw new Error("Redis not initialized. Call initializeRedis() first.");
  }
  return redis;
}

// Helper functions with key prefixing
function getKey(key: string): string {
  return `${config.redis.keyPrefix}${key}`;
}

// Cache operations
export async function set(
  key: string,
  value: string | number | object,
  ttlSeconds?: number
): Promise<void> {
  const redisClient = getRedis();
  const serializedValue = typeof value === "object" ? JSON.stringify(value) : String(value);
  
  if (ttlSeconds) {
    await redisClient.setex(getKey(key), ttlSeconds, serializedValue);
  } else {
    await redisClient.set(getKey(key), serializedValue);
  }
}

export async function get(key: string): Promise<string | null> {
  const redisClient = getRedis();
  return await redisClient.get(getKey(key));
}

export async function getObject<T = unknown>(key: string): Promise<T | null> {
  const value = await get(key);
  if (!value) return null;
  
  try {
    return JSON.parse(value) as T;
  } catch (error) {
    logger.error(`Failed to parse JSON for key ${key}:`, error);
    return null;
  }
}

export async function del(key: string): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.del(getKey(key));
}

export async function exists(key: string): Promise<boolean> {
  const redisClient = getRedis();
  const result = await redisClient.exists(getKey(key));
  return result === 1;
}

export async function expire(key: string, ttlSeconds: number): Promise<boolean> {
  const redisClient = getRedis();
  const result = await redisClient.expire(getKey(key), ttlSeconds);
  return result === 1;
}

export async function ttl(key: string): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.ttl(getKey(key));
}

// List operations
export async function lpush(key: string, ...values: string[]): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.lpush(getKey(key), ...values);
}

export async function rpush(key: string, ...values: string[]): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.rpush(getKey(key), ...values);
}

export async function lpop(key: string): Promise<string | null> {
  const redisClient = getRedis();
  return await redisClient.lpop(getKey(key));
}

export async function rpop(key: string): Promise<string | null> {
  const redisClient = getRedis();
  return await redisClient.rpop(getKey(key));
}

export async function llen(key: string): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.llen(getKey(key));
}

export async function lrange(key: string, start: number, stop: number): Promise<string[]> {
  const redisClient = getRedis();
  return await redisClient.lrange(getKey(key), start, stop);
}

// Set operations
export async function sadd(key: string, ...members: string[]): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.sadd(getKey(key), ...members);
}

export async function srem(key: string, ...members: string[]): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.srem(getKey(key), ...members);
}

export async function sismember(key: string, member: string): Promise<boolean> {
  const redisClient = getRedis();
  const result = await redisClient.sismember(getKey(key), member);
  return result === 1;
}

export async function smembers(key: string): Promise<string[]> {
  const redisClient = getRedis();
  return await redisClient.smembers(getKey(key));
}

// Hash operations
export async function hset(key: string, field: string, value: string): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.hset(getKey(key), field, value);
}

export async function hget(key: string, field: string): Promise<string | null> {
  const redisClient = getRedis();
  return await redisClient.hget(getKey(key), field);
}

export async function hgetall(key: string): Promise<Record<string, string>> {
  const redisClient = getRedis();
  const result = await redisClient.hgetall(getKey(key));
  // Convert array result to object if needed
  if (Array.isArray(result)) {
    const obj: Record<string, string> = {};
    for (let i = 0; i < result.length; i += 2) {
      const field = result[i];
      const value = result[i + 1];
      if (field !== undefined && value !== undefined) {
        obj[field] = value;
      }
    }
    return obj;
  }
  return result as Record<string, string>;
}

export async function hdel(key: string, ...fields: string[]): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.hdel(getKey(key), ...fields);
}

// Increment operations
export async function incr(key: string): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.incr(getKey(key));
}

export async function incrby(key: string, increment: number): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.incrby(getKey(key), increment);
}

export async function decr(key: string): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.decr(getKey(key));
}

export async function decrby(key: string, decrement: number): Promise<number> {
  const redisClient = getRedis();
  return await redisClient.decrby(getKey(key), decrement);
}

// Session management
export async function setSession(
  sessionId: string,
  sessionData: object,
  ttlSeconds: number = config.security.sessionTimeout / 1000
): Promise<void> {
  await set(`session:${sessionId}`, sessionData, ttlSeconds);
}

export async function getSession<T = unknown>(sessionId: string): Promise<T | null> {
  return await getObject<T>(`session:${sessionId}`);
}

export async function deleteSession(sessionId: string): Promise<void> {
  await del(`session:${sessionId}`);
}

export async function refreshSession(
  sessionId: string,
  ttlSeconds: number = config.security.sessionTimeout / 1000
): Promise<boolean> {
  return await expire(`session:${sessionId}`, ttlSeconds);
}

// Rate limiting
export async function incrementRateLimit(
  identifier: string,
  windowSeconds: number = config.rateLimit.windowMs / 1000
): Promise<number> {
  const key = `rate_limit:${identifier}`;
  const redisClient = getRedis();
  
  const current = await redisClient.incr(getKey(key));
  if (current === 1) {
    await redisClient.expire(getKey(key), windowSeconds);
  }
  
  return current;
}

export async function getRateLimitCount(identifier: string): Promise<number> {
  const value = await get(`rate_limit:${identifier}`);
  return value ? parseInt(value, 10) : 0;
}

// Login attempt tracking
export async function incrementLoginAttempts(
  identifier: string,
  windowSeconds: number = config.security.lockoutDuration / 1000
): Promise<number> {
  const key = `login_attempts:${identifier}`;
  const redisClient = getRedis();
  
  const current = await redisClient.incr(getKey(key));
  if (current === 1) {
    await redisClient.expire(getKey(key), windowSeconds);
  }
  
  return current;
}

export async function getLoginAttempts(identifier: string): Promise<number> {
  const value = await get(`login_attempts:${identifier}`);
  return value ? parseInt(value, 10) : 0;
}

export async function clearLoginAttempts(identifier: string): Promise<void> {
  await del(`login_attempts:${identifier}`);
}

// Cache invalidation patterns
export async function invalidatePattern(pattern: string): Promise<number> {
  const redisClient = getRedis();
  const keys = await redisClient.keys(getKey(pattern));
  
  if (keys.length === 0) return 0;
  
  return await redisClient.del(...keys);
}

// Health check
export async function checkRedisHealth(): Promise<boolean> {
  try {
    const redisClient = getRedis();
    const result = await redisClient.ping();
    return result === "PONG";
  } catch (error) {
    logger.error("Redis health check failed:", error);
    return false;
  }
}

// Close connection
export function closeRedis(): void {
  if (redis) {
    redis.close();
    logger.info("Redis connection closed");
  }
}
