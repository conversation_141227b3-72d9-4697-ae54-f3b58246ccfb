import { assertEquals } from "@std/assert";

Deno.test("Basic Deno functionality", () => {
  assertEquals(1 + 1, 2);
});

Deno.test("Environment variables", () => {
  // Test that we can read environment variables
  const nodeEnv = Deno.env.get("NODE_ENV") || "development";
  assertEquals(typeof nodeEnv, "string");
});

Deno.test("Configuration loading", async () => {
  // Test that configuration can be loaded
  const config = await import("../src/config/index.ts");
  assertEquals(typeof config.default.port, "number");
  assertEquals(typeof config.default.env, "string");
});

Deno.test("Middleware imports", async () => {
  // Test that middleware can be imported
  const errorHandler = await import("../src/middleware/errorHandler.ts");
  assertEquals(typeof errorHandler.errorHandler, "function");
  
  const auth = await import("../src/middleware/auth.ts");
  assertEquals(typeof auth.authMiddleware, "function");
});

Deno.test("Route imports", async () => {
  // Test that routes can be imported
  const health = await import("../src/routes/health.ts");
  assertEquals(typeof health.default, "object");
  
  const authRoutes = await import("../src/routes/auth.ts");
  assertEquals(typeof authRoutes.default, "object");
});
