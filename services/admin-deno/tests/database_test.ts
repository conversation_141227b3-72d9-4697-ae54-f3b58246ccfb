import { assertEquals, assertExists } from "@std/assert";

Deno.test("Database utility imports", async () => {
  // Test that database utilities can be imported
  const db = await import("../src/utils/database.ts");
  
  // Check that all expected functions are exported
  assertExists(db.initializeDatabase);
  assertExists(db.getPool);
  assertExists(db.getClient);
  assertExists(db.query);
  assertExists(db.queryWithTenant);
  assertExists(db.transaction);
  assertExists(db.checkDatabaseHealth);
  assertExists(db.getUserByEmail);
  assertExists(db.getUserById);
  assertExists(db.getAllOrganizations);
  assertExists(db.recordSystemMetric);
  assertExists(db.getSystemMetrics);
});

Deno.test("Redis utility imports", async () => {
  // Test that Redis utilities can be imported
  const redis = await import("../src/utils/redis.ts");
  
  // Check that all expected functions are exported
  assertExists(redis.initializeRedis);
  assertExists(redis.getRedis);
  assertExists(redis.set);
  assertExists(redis.get);
  assertExists(redis.getObject);
  assertExists(redis.del);
  assertExists(redis.exists);
  assertExists(redis.setSession);
  assertExists(redis.getSession);
  assertExists(redis.deleteSession);
  assertExists(redis.incrementRateLimit);
  assertExists(redis.checkRedisHealth);
});

Deno.test("Database configuration", async () => {
  const config = await import("../src/config/index.ts");
  
  // Test database configuration structure
  assertEquals(typeof config.default.database.host, "string");
  assertEquals(typeof config.default.database.port, "number");
  assertEquals(typeof config.default.database.name, "string");
  assertEquals(typeof config.default.database.user, "string");
  assertEquals(typeof config.default.database.maxConnections, "number");
});

Deno.test("Multi-tenant query structure", async () => {
  // Test that multi-tenant query function has correct signature
  const db = await import("../src/utils/database.ts");

  // This should not throw an error when imported
  assertEquals(typeof db.queryWithTenant, "function");

  // Test that specific database functions exist
  assertExists(db.getUserByEmail);
  assertExists(db.getOrganizationById);
  assertExists(db.recordSystemMetric);
});

Deno.test("Database pool configuration", async () => {
  const { Pool } = await import("postgres");
  const config = await import("../src/config/index.ts");
  
  // Test that we can create a pool configuration object
  const poolConfig = {
    user: config.default.database.user,
    password: config.default.database.password,
    database: config.default.database.name,
    hostname: config.default.database.host,
    port: config.default.database.port,
    tls: config.default.database.ssl ? { enabled: true } : undefined,
  };
  
  assertEquals(typeof poolConfig.user, "string");
  assertEquals(typeof poolConfig.hostname, "string");
  assertEquals(typeof poolConfig.port, "number");
  
  // Test that Pool constructor exists
  assertEquals(typeof Pool, "function");
});
