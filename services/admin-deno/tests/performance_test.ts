import { assertEquals, assert } from "@std/assert";

// Performance testing utilities
class PerformanceTimer {
  private startTime: number = 0;
  
  start(): void {
    this.startTime = performance.now();
  }
  
  end(): number {
    return performance.now() - this.startTime;
  }
}

class MemoryTracker {
  getMemoryUsage(): Deno.MemoryUsage {
    return Deno.memoryUsage();
  }
  
  formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}

// Benchmark configuration loading
Deno.test("Performance - Configuration loading", async () => {
  const timer = new PerformanceTimer();
  
  timer.start();
  const config = await import("../src/config/index.ts");
  const loadTime = timer.end();
  
  console.log(`Configuration loading time: ${loadTime.toFixed(2)}ms`);
  
  // Configuration should load quickly (under 100ms)
  assert(loadTime < 100, `Configuration loading too slow: ${loadTime}ms`);
  
  // Verify configuration is properly loaded
  assertEquals(typeof config.default.port, "number");
  assertEquals(typeof config.default.env, "string");
});

// Benchmark module imports
Deno.test("Performance - Module import speed", async () => {
  const timer = new PerformanceTimer();
  const importTimes: Record<string, number> = {};
  
  // Test database utils import
  timer.start();
  await import("../src/utils/database.ts");
  importTimes.database = timer.end();
  
  // Test Redis utils import
  timer.start();
  await import("../src/utils/redis.ts");
  importTimes.redis = timer.end();
  
  // Test middleware imports
  timer.start();
  await import("../src/middleware/errorHandler.ts");
  importTimes.errorHandler = timer.end();
  
  timer.start();
  await import("../src/middleware/auth.ts");
  importTimes.auth = timer.end();
  
  // Test route imports
  timer.start();
  await import("../src/routes/health.ts");
  importTimes.health = timer.end();
  
  timer.start();
  await import("../src/routes/auth.ts");
  importTimes.authRoutes = timer.end();
  
  console.log("Module import times:");
  for (const [module, time] of Object.entries(importTimes)) {
    console.log(`  ${module}: ${time.toFixed(2)}ms`);
  }
  
  // All imports should be fast (under 50ms each)
  for (const [module, time] of Object.entries(importTimes)) {
    assert(time < 50, `${module} import too slow: ${time}ms`);
  }
});

// Benchmark application startup simulation
Deno.test("Performance - Application startup simulation", async () => {
  const timer = new PerformanceTimer();
  const memory = new MemoryTracker();
  
  const initialMemory = memory.getMemoryUsage();
  console.log(`Initial memory usage: ${memory.formatBytes(initialMemory.rss)}`);
  
  timer.start();
  
  // Simulate application startup by importing main components
  const [
    config,
    { Application },
    errorHandler,
    auth,
    healthRoutes,
    authRoutes
  ] = await Promise.all([
    import("../src/config/index.ts"),
    import("@oak/oak"),
    import("../src/middleware/errorHandler.ts"),
    import("../src/middleware/auth.ts"),
    import("../src/routes/health.ts"),
    import("../src/routes/auth.ts")
  ]);
  
  // Create application instance
  const app = new Application();
  
  // Add middleware (simulation)
  app.use(errorHandler.errorHandler);
  app.use(healthRoutes.default.routes());
  app.use(authRoutes.default.routes());
  
  const startupTime = timer.end();
  const finalMemory = memory.getMemoryUsage();
  
  console.log(`Startup time: ${startupTime.toFixed(2)}ms`);
  console.log(`Final memory usage: ${memory.formatBytes(finalMemory.rss)}`);
  console.log(`Memory increase: ${memory.formatBytes(finalMemory.rss - initialMemory.rss)}`);
  
  // Startup should be fast (target: under 1000ms)
  assert(startupTime < 1000, `Startup too slow: ${startupTime}ms`);
  
  // Memory usage should be reasonable (under 100MB increase)
  const memoryIncrease = finalMemory.rss - initialMemory.rss;
  assert(memoryIncrease < 100 * 1024 * 1024, `Memory usage too high: ${memory.formatBytes(memoryIncrease)}`);
});

// Benchmark JSON parsing performance
Deno.test("Performance - JSON parsing", () => {
  const timer = new PerformanceTimer();
  
  // Create test data
  const testData = {
    user: {
      id: "test-user-id",
      email: "<EMAIL>",
      role: "admin",
      permissions: ["read", "write", "delete"],
      metadata: {
        lastLogin: new Date().toISOString(),
        preferences: {
          theme: "dark",
          language: "en",
          notifications: true
        }
      }
    },
    organizations: Array.from({ length: 100 }, (_, i) => ({
      id: `org-${i}`,
      name: `Organization ${i}`,
      domain: `org${i}.com`,
      settings: {
        features: ["analytics", "reporting", "api"],
        limits: {
          users: 100,
          requests: 10000,
          storage: "10GB"
        }
      }
    }))
  };
  
  // Test JSON stringify performance
  timer.start();
  const jsonString = JSON.stringify(testData);
  const stringifyTime = timer.end();
  
  // Test JSON parse performance
  timer.start();
  const parsedData = JSON.parse(jsonString);
  const parseTime = timer.end();
  
  console.log(`JSON stringify time: ${stringifyTime.toFixed(2)}ms`);
  console.log(`JSON parse time: ${parseTime.toFixed(2)}ms`);
  console.log(`JSON size: ${(jsonString.length / 1024).toFixed(2)}KB`);
  
  // JSON operations should be fast
  assert(stringifyTime < 10, `JSON stringify too slow: ${stringifyTime}ms`);
  assert(parseTime < 10, `JSON parse too slow: ${parseTime}ms`);
  
  // Verify data integrity
  assertEquals(parsedData.user.email, testData.user.email);
  assertEquals(parsedData.organizations.length, 100);
});

// Benchmark crypto operations
Deno.test("Performance - Crypto operations", async () => {
  const timer = new PerformanceTimer();
  
  // Test password hashing (bcrypt simulation)
  timer.start();
  const password = "test-password-123";
  const encoder = new TextEncoder();
  const data = encoder.encode(password);
  
  // Simulate crypto operations using Web Crypto API
  const key = await crypto.subtle.importKey(
    "raw",
    data,
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign", "verify"]
  );
  
  const signature = await crypto.subtle.sign("HMAC", key, data);
  const cryptoTime = timer.end();
  
  console.log(`Crypto operations time: ${cryptoTime.toFixed(2)}ms`);
  
  // Crypto operations should complete reasonably fast
  assert(cryptoTime < 100, `Crypto operations too slow: ${cryptoTime}ms`);
  assert(signature.byteLength > 0, "Crypto signature should be generated");
});

// Benchmark concurrent operations
Deno.test("Performance - Concurrent operations", async () => {
  const timer = new PerformanceTimer();
  
  // Simulate concurrent API calls
  const concurrentTasks = Array.from({ length: 10 }, async (_, i) => {
    // Simulate async work
    await new Promise(resolve => setTimeout(resolve, Math.random() * 10));
    
    // Simulate JSON processing
    const data = { id: i, timestamp: Date.now(), data: `test-data-${i}` };
    const json = JSON.stringify(data);
    const parsed = JSON.parse(json);
    
    return parsed;
  });
  
  timer.start();
  const results = await Promise.all(concurrentTasks);
  const concurrentTime = timer.end();
  
  console.log(`Concurrent operations time: ${concurrentTime.toFixed(2)}ms`);
  console.log(`Operations completed: ${results.length}`);
  
  // Concurrent operations should be efficient
  assert(concurrentTime < 100, `Concurrent operations too slow: ${concurrentTime}ms`);
  assertEquals(results.length, 10);
  
  // Verify all operations completed successfully
  results.forEach((result, index) => {
    assertEquals(result.id, index);
    assertEquals(typeof result.timestamp, "number");
  });
});

// System information benchmark
Deno.test("Performance - System information", () => {
  const timer = new PerformanceTimer();
  const memory = new MemoryTracker();
  
  timer.start();
  
  // Get system information
  const memoryUsage = memory.getMemoryUsage();
  const version = Deno.version;
  const pid = Deno.pid;
  
  const sysInfoTime = timer.end();
  
  console.log("System Information:");
  console.log(`  Deno version: ${version.deno}`);
  console.log(`  V8 version: ${version.v8}`);
  console.log(`  TypeScript version: ${version.typescript}`);
  console.log(`  Process ID: ${pid}`);
  console.log(`  Memory usage: ${memory.formatBytes(memoryUsage.rss)}`);
  console.log(`  Heap used: ${memory.formatBytes(memoryUsage.heapUsed)}`);
  console.log(`  System info time: ${sysInfoTime.toFixed(2)}ms`);
  
  // System info should be available quickly
  assert(sysInfoTime < 10, `System info too slow: ${sysInfoTime}ms`);
  
  // Verify system info is available
  assert(memoryUsage.rss > 0, "Memory usage should be reported");
  assert(typeof pid === "number", "Process ID should be available");
  assert(version.deno.length > 0, "Deno version should be available");
});
