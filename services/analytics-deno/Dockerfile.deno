# Multi-stage Dockerfile for Analytics Service (Deno 2)

# Base stage with Deno
FROM denoland/deno:2.4.0 AS base
WORKDIR /app

# Development stage
FROM base AS development
COPY deno.json deno.lock* ./
RUN deno cache --import-map=deno.json src/main.ts || true
COPY . .
EXPOSE 3002
CMD ["deno", "task", "dev"]

# Production dependencies stage
FROM base AS deps
COPY deno.json deno.lock* ./
COPY src/ ./src/
RUN deno cache --import-map=deno.json src/main.ts

# Production stage
FROM base AS production
COPY --from=deps /app /app
COPY . .

# Create non-root user
RUN groupadd -r analytics && useradd -r -g analytics analytics
RUN chown -R analytics:analytics /app
USER analytics

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD deno run --allow-net healthcheck.ts

EXPOSE 3002
CMD ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "--allow-write", "src/main.ts"]

# Test stage
FROM deps AS test
COPY . .
RUN deno task test
RUN deno task lint
RUN deno check src/main.ts
