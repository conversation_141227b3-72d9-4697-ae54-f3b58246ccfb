#!/usr/bin/env deno run --allow-net --allow-env

/**
 * Health check script for Docker container
 */

const PORT = Deno.env.get("PORT") || "3002";
const HOST = Deno.env.get("HOST") || "localhost";

async function healthCheck(): Promise<void> {
  try {
    const response = await fetch(`http://${HOST}:${PORT}/health`, {
      method: "GET",
      headers: {
        "User-Agent": "Docker-Health-Check",
      },
    });

    if (!response.ok) {
      console.error(`Health check failed: HTTP ${response.status}`);
      Deno.exit(1);
    }

    const data = await response.json();
    
    if (!data.success) {
      console.error("Health check failed: Service reports unhealthy");
      Deno.exit(1);
    }

    console.log("Health check passed");
    Deno.exit(0);
  } catch (error) {
    console.error(`Health check failed: ${error.message}`);
    Deno.exit(1);
  }
}

// Run health check
healthCheck();
