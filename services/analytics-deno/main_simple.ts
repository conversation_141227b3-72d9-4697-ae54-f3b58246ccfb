import { Application, Router } from "@oak/oak";
import { Pool } from "postgres";

console.log("Starting Analytics Service...");

// Database configuration
const dbConfig = {
  hostname: Deno.env.get("DB_HOST") || "localhost",
  port: parseInt(Deno.env.get("DB_PORT") || "5432"),
  database: Deno.env.get("DB_NAME") || "ecommerce_analytics",
  user: Deno.env.get("DB_USER") || "postgres",
  password: Deno.env.get("DB_PASSWORD") || "password",
};

console.log("Database config:", { ...dbConfig, password: "***" });

// Initialize database pool
const pool = new Pool(dbConfig, 5);

// Test database connection
try {
  console.log("Testing database connection...");
  const client = await pool.connect();
  await client.queryObject("SELECT 1");
  client.release();
  console.log("Database connection successful");
} catch (error) {
  console.error("Database connection failed:", error);
  Deno.exit(1);
}

// Create Oak application
const app = new Application();
const router = new Router();

// Health check route
router.get("/health", (ctx) => {
  ctx.response.body = {
    success: true,
    service: "analytics-service",
    status: "healthy",
    timestamp: new Date().toISOString(),
  };
});

// API routes
const apiRouter = new Router({ prefix: "/api" });

apiRouter.get("/test", async (ctx) => {
  try {
    const client = await pool.connect();
    const result = await client.queryObject("SELECT COUNT(*)::int as count FROM customer_events");
    client.release();

    ctx.response.body = {
      success: true,
      data: result.rows[0],
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Database query error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Database query failed",
    };
  }
});

// Register routes
app.use(router.routes());
app.use(router.allowedMethods());
app.use(apiRouter.routes());
app.use(apiRouter.allowedMethods());

// Start server
const port = parseInt(Deno.env.get("PORT") || "3002");
console.log(`Starting server on port ${port}...`);

await app.listen({ 
  hostname: "0.0.0.0", 
  port: port 
});
