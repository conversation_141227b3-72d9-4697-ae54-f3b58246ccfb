import { Application, Router } from "@oak/oak";
import { Pool } from "postgres";
import { connect, Redis } from "redis";

console.log("🚀 Starting Enhanced Analytics Service...");

// Configuration from environment variables
const config = {
  port: parseInt(Deno.env.get("PORT") || "3002"),
  host: Deno.env.get("HOST") || "0.0.0.0",
  database: {
    hostname: Deno.env.get("DB_HOST") || "localhost",
    port: parseInt(Deno.env.get("DB_PORT") || "5432"),
    database: Deno.env.get("DB_NAME") || "ecommerce_analytics",
    user: Deno.env.get("DB_USER") || "postgres",
    password: Deno.env.get("DB_PASSWORD") || "password",
  },
  redis: {
    hostname: Deno.env.get("REDIS_HOST") || "localhost",
    port: parseInt(Deno.env.get("REDIS_PORT") || "6379"),
  },
};

console.log("📊 Configuration loaded:", {
  port: config.port,
  host: config.host,
  database: { ...config.database, password: "***" },
  redis: config.redis,
});

// Initialize database pool
console.log("🗄️  Initializing database connection...");
const pool = new Pool(config.database, 10);

// Test database connection
try {
  const client = await pool.connect();
  await client.queryObject("SELECT 1");
  client.release();
  console.log("✅ Database connection successful");
} catch (error) {
  console.error("❌ Database connection failed:", error);
  Deno.exit(1);
}

// Initialize Redis connection
console.log("🔴 Initializing Redis connection...");
let redisClient: Redis;
try {
  redisClient = await connect(config.redis);
  await redisClient.ping();
  console.log("✅ Redis connection successful");
} catch (error) {
  console.error("❌ Redis connection failed:", error);
  Deno.exit(1);
}

// Create Oak application
const app = new Application();

// Global error handler
app.addEventListener("error", (evt) => {
  console.error("🚨 Unhandled application error:", evt.error?.message);
});

// Basic middleware for CORS
app.use(async (ctx, next) => {
  ctx.response.headers.set("Access-Control-Allow-Origin", "*");
  ctx.response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  ctx.response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
  
  if (ctx.request.method === "OPTIONS") {
    ctx.response.status = 200;
    return;
  }
  
  await next();
});

// Request logging middleware
app.use(async (ctx, next) => {
  const start = Date.now();
  await next();
  const ms = Date.now() - start;
  console.log(`${ctx.request.method} ${ctx.request.url.pathname} - ${ctx.response.status} - ${ms}ms`);
});

// Main router
const router = new Router();

// Health check endpoints
router.get("/health", (ctx) => {
  ctx.response.body = {
    success: true,
    service: "analytics-service",
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
  };
});

router.get("/ready", async (ctx) => {
  try {
    // Test database
    const client = await pool.connect();
    await client.queryObject("SELECT 1");
    client.release();
    
    // Test Redis
    await redisClient.ping();
    
    ctx.response.body = {
      success: true,
      service: "analytics-service",
      status: "ready",
      checks: {
        database: "healthy",
        redis: "healthy",
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    ctx.response.status = 503;
    ctx.response.body = {
      success: false,
      service: "analytics-service",
      status: "not ready",
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
});

// API router
const apiRouter = new Router({ prefix: "/api" });

// Enhanced analytics endpoints
apiRouter.get("/enhanced-analytics/dashboard", async (ctx) => {
  try {
    const tenantId = ctx.request.url.searchParams.get("tenantId");
    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "tenantId parameter is required",
      };
      return;
    }

    const client = await pool.connect();
    
    // Get dashboard metrics using TimescaleDB continuous aggregates
    const metricsQuery = `
      SELECT
        day,
        total_events::int,
        unique_customers::int,
        purchases::int,
        total_revenue::float
      FROM daily_customer_metrics
      WHERE tenant_id = $1
        AND day >= NOW() - INTERVAL '30 days'
      ORDER BY day DESC
      LIMIT 30
    `;
    
    const metrics = await client.queryObject(metricsQuery, [tenantId]);
    
    // Get real-time stats
    const realtimeQuery = `
      SELECT 
        COUNT(*)::int as total_events_today,
        COUNT(DISTINCT customer_id)::int as unique_customers_today,
        COUNT(*) FILTER (WHERE event_type = 'purchase')::int as purchases_today,
        COALESCE(SUM(revenue), 0)::float as revenue_today
      FROM customer_events 
      WHERE tenant_id = $1 
        AND timestamp >= CURRENT_DATE
    `;
    
    const realtime = await client.queryObject(realtimeQuery, [tenantId]);
    
    client.release();
    
    ctx.response.body = {
      success: true,
      data: {
        historical: metrics.rows,
        realtime: realtime.rows[0],
        period: "30 days",
        generated_at: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Dashboard metrics error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to fetch dashboard metrics",
      timestamp: new Date().toISOString(),
    };
  }
});

// Customer events endpoint
apiRouter.get("/enhanced-analytics/events", async (ctx) => {
  try {
    const tenantId = ctx.request.url.searchParams.get("tenantId");
    const limit = parseInt(ctx.request.url.searchParams.get("limit") || "100");
    
    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "tenantId parameter is required",
      };
      return;
    }

    const client = await pool.connect();
    
    const eventsQuery = `
      SELECT 
        id,
        customer_id,
        event_type,
        event_source,
        revenue,
        utm_source,
        utm_medium,
        utm_campaign,
        timestamp
      FROM customer_events 
      WHERE tenant_id = $1 
      ORDER BY timestamp DESC 
      LIMIT $2
    `;
    
    const events = await client.queryObject(eventsQuery, [tenantId, limit]);
    client.release();
    
    ctx.response.body = {
      success: true,
      data: events.rows,
      count: events.rows.length,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Events query error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to fetch events",
      timestamp: new Date().toISOString(),
    };
  }
});

// Test endpoint
apiRouter.get("/test", async (ctx) => {
  try {
    const client = await pool.connect();
    const result = await client.queryObject("SELECT COUNT(*)::int as count FROM customer_events");
    client.release();
    
    ctx.response.body = {
      success: true,
      data: result.rows[0],
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Test query error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Database query failed",
    };
  }
});

// Register routes
app.use(router.routes());
app.use(router.allowedMethods());
app.use(apiRouter.routes());
app.use(apiRouter.allowedMethods());

// 404 handler
app.use((ctx) => {
  ctx.response.status = 404;
  ctx.response.body = {
    success: false,
    error: "Endpoint not found",
    timestamp: new Date().toISOString(),
  };
});

// Graceful shutdown
async function gracefulShutdown(signal: string): Promise<void> {
  console.log(`\n🛑 Received ${signal}, starting graceful shutdown...`);
  
  try {
    await pool.end();
    redisClient.close();
    console.log("✅ Graceful shutdown completed");
    Deno.exit(0);
  } catch (error) {
    console.error("❌ Error during graceful shutdown:", error);
    Deno.exit(1);
  }
}

// Setup signal handlers
Deno.addSignalListener("SIGINT", () => gracefulShutdown("SIGINT"));
Deno.addSignalListener("SIGTERM", () => gracefulShutdown("SIGTERM"));

// Start server
console.log(`🚀 Starting server on ${config.host}:${config.port}...`);

await app.listen({ 
  hostname: config.host, 
  port: config.port 
});
