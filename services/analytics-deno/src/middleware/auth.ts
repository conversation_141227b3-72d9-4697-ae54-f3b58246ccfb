import { Context, Middleware } from "@oak/oak";
import { verify } from "djwt";
import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { errors } from "./errorHandler.ts";

export interface User {
  id: string;
  email: string;
  tenantId: string;
  role: string;
  permissions: string[];
}

export interface JWTPayload {
  sub: string;
  email: string;
  tenant_id: string;
  role: string;
  permissions: string[];
  iat: number;
  exp: number;
  iss: string;
}

/**
 * JWT authentication middleware
 */
export const authMiddleware: Middleware = async (ctx: Context, next) => {
  try {
    const token = extractToken(ctx);
    
    if (!token) {
      throw errors.unauthorized("Authentication token required");
    }

    // Verify JWT token
    const key = await crypto.subtle.importKey(
      "raw",
      new TextEncoder().encode(config.jwt.secret),
      { name: "<PERSON><PERSON>", hash: "SHA-256" },
      false,
      ["verify"]
    );

    const payload = await verify(token, key) as unknown as JWTPayload;
    
    // Validate token claims
    if (payload.iss !== config.jwt.issuer) {
      throw errors.unauthorized("Invalid token issuer");
    }

    // Create user object
    const user: User = {
      id: payload.sub,
      email: payload.email,
      tenantId: payload.tenant_id,
      role: payload.role,
      permissions: payload.permissions || [],
    };

    // Add user to context state
    ctx.state.user = user;
    ctx.state.tenantId = user.tenantId;

    logger.debug("User authenticated successfully", {
      userId: user.id,
      tenantId: user.tenantId,
      role: user.role,
    });

    await next();
  } catch (error) {
    const err = error as { name?: string; status?: number };
    if (err.name === "JWTExpired") {
      throw errors.unauthorized("Token has expired");
    } else if (err.name === "JWTInvalid") {
      throw errors.unauthorized("Invalid token");
    } else if (err.status === 401) {
      throw error;
    } else {
      logger.error("Authentication error", error as Error);
      throw errors.unauthorized("Authentication failed");
    }
  }
};

/**
 * Optional authentication middleware (doesn't throw if no token)
 */
export const optionalAuthMiddleware: Middleware = async (ctx: Context, next) => {
  try {
    const token = extractToken(ctx);
    
    if (token) {
      const key = await crypto.subtle.importKey(
        "raw",
        new TextEncoder().encode(config.jwt.secret),
        { name: "HMAC", hash: "SHA-256" },
        false,
        ["verify"]
      );

      const payload = await verify(token, key) as unknown as JWTPayload;
      
      if (payload.iss === config.jwt.issuer) {
        const user: User = {
          id: payload.sub,
          email: payload.email,
          tenantId: payload.tenant_id,
          role: payload.role,
          permissions: payload.permissions || [],
        };

        ctx.state.user = user;
        ctx.state.tenantId = user.tenantId;
      }
    }
  } catch (error) {
    // Log but don't throw for optional auth
    logger.debug("Optional authentication failed", {
      error: (error as Error).message,
    });
  }

  await next();
};

/**
 * Role-based authorization middleware
 */
export function requireRole(requiredRole: string): Middleware {
  return async (ctx: Context, next) => {
    const user = ctx.state.user as User;
    
    if (!user) {
      throw errors.unauthorized("Authentication required");
    }

    if (user.role !== requiredRole && user.role !== "admin") {
      throw errors.forbidden(`Role '${requiredRole}' required`);
    }

    await next();
  };
}

/**
 * Permission-based authorization middleware
 */
export function requirePermission(requiredPermission: string): Middleware {
  return async (ctx: Context, next) => {
    const user = ctx.state.user as User;
    
    if (!user) {
      throw errors.unauthorized("Authentication required");
    }

    if (!user.permissions.includes(requiredPermission) && user.role !== "admin") {
      throw errors.forbidden(`Permission '${requiredPermission}' required`);
    }

    await next();
  };
}

/**
 * Tenant isolation middleware
 */
export const tenantIsolationMiddleware: Middleware = async (ctx: Context, next) => {
  const user = ctx.state.user as User;
  const requestedTenantId = ctx.request.headers.get("x-tenant-id") || 
                           ctx.request.url.searchParams.get("tenant_id");

  if (user && requestedTenantId && user.tenantId !== requestedTenantId) {
    // Allow admin users to access other tenants
    if (user.role !== "admin") {
      throw errors.forbidden("Access to this tenant is not allowed");
    }
  }

  // Ensure tenant ID is set in context
  if (user) {
    ctx.state.tenantId = requestedTenantId || user.tenantId;
  } else if (requestedTenantId) {
    ctx.state.tenantId = requestedTenantId;
  }

  await next();
};

/**
 * API key authentication middleware
 */
export const apiKeyAuthMiddleware: Middleware = async (ctx: Context, next) => {
  const apiKey = ctx.request.headers.get("x-api-key");
  
  if (!apiKey) {
    throw errors.unauthorized("API key required");
  }

  // TODO(@analytics-team): Implement API key validation against database
  // For now, just check if it's not empty
  if (apiKey.length < 10) {
    throw errors.unauthorized("Invalid API key");
  }

  // Set a basic context for API key authentication
  ctx.state.apiKey = apiKey;
  ctx.state.authType = "api_key";

  logger.debug("API key authentication successful", {
    apiKey: apiKey.substring(0, 8) + "...",
  });

  await next();
};

/**
 * Extract JWT token from request headers
 */
function extractToken(ctx: Context): string | null {
  const authHeader = ctx.request.headers.get("authorization");
  
  if (!authHeader) {
    return null;
  }

  const parts = authHeader.split(" ");
  
  if (parts.length !== 2 || parts[0] !== "Bearer") {
    return null;
  }

  return parts[1];
}

/**
 * Check if user has any of the required roles
 */
export function hasAnyRole(user: User, roles: string[]): boolean {
  return roles.includes(user.role) || user.role === "admin";
}

/**
 * Check if user has any of the required permissions
 */
export function hasAnyPermission(user: User, permissions: string[]): boolean {
  return user.role === "admin" ||
         permissions.some(permission => user.permissions.includes(permission));
}

/**
 * Simplified tenant access validation for enhanced analytics
 */
export const validateTenantAccess: Middleware = async (ctx: Context, next) => {
  try {
    // For Phase 1, use simplified tenant validation
    // In production, this would integrate with your existing auth system

    const tenantId = ctx.request.headers.get("x-tenant-id") ||
                     ctx.request.url.searchParams.get("tenant_id") ||
                     "00000000-0000-0000-0000-000000000001"; // Default demo tenant

    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: "Tenant ID required",
        message: "Please provide tenant_id in query params or x-tenant-id header",
      };
      return;
    }

    // Set tenant context
    ctx.state.tenantId = tenantId;

    logger.debug("Tenant access validated", { tenantId });

    await next();
  } catch (error) {
    logger.error("Tenant validation error", error as Error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Tenant validation failed",
      message: (error as Error).message,
    };
  }
};
