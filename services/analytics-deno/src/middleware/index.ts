import { Application, Middleware } from "@oak/oak";
import { oakCors } from "cors";
import { config } from "../config/config.ts";
import { logger, createRequestLogger } from "../utils/logger.ts";
import { errorHandler } from "./errorHandler.ts";
import { rateLimiter } from "./rateLimiter.ts";
import { authMiddleware } from "./auth.ts";
import { metricsMiddleware } from "./metrics.ts";
import { compressionMiddleware } from "./compression.ts";
import { securityHeaders } from "./security.ts";

/**
 * Setup all middleware for the Oak application
 */
export function setupMiddleware(app: Application): void {
  logger.info("Setting up middleware stack");

  // Security headers (first)
  app.use(securityHeaders);

  // CORS configuration
  app.use(oakCors({
    origin: config.cors.origins,
    credentials: config.cors.credentials,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "X-Tenant-ID",
      "X-API-Key",
    ],
  }));

  // Compression middleware
  app.use(compressionMiddleware);

  // Request logging
  app.use(createRequestLogger() as Middleware);

  // Rate limiting
  app.use(rateLimiter);

  // Metrics collection
  app.use(metricsMiddleware);

  // Body parsing is handled automatically by Oak

  // Authentication middleware (applied selectively in routes)
  // app.use(authMiddleware); // This will be applied per route

  // Error handling (last)
  app.use(errorHandler);

  logger.info("Middleware stack setup completed");
}

// Export individual middleware for selective use
export {
  errorHandler,
  rateLimiter,
  authMiddleware,
  metricsMiddleware,
  compressionMiddleware,
  securityHeaders,
};
