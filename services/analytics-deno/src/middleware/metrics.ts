import { Context, Middleware } from "@oak/oak";
import { logger } from "../utils/logger.ts";

// Metrics storage
interface Metrics {
  httpRequests: Map<string, number>;
  httpRequestDuration: Map<string, number[]>;
  httpRequestsInProgress: number;
  httpRequestErrors: Map<string, number>;
  startTime: number;
}

const metrics: Metrics = {
  httpRequests: new Map(),
  httpRequestDuration: new Map(),
  httpRequestsInProgress: 0,
  httpRequestErrors: new Map(),
  startTime: Date.now(),
};

/**
 * Metrics collection middleware
 */
export const metricsMiddleware: Middleware = async (ctx: Context, next) => {
  const startTime = Date.now();
  const method = ctx.request.method;
  const route = getRoutePattern(ctx);
  const key = `${method} ${route}`;

  // Increment in-progress counter
  metrics.httpRequestsInProgress++;

  try {
    await next();

    // Record successful request
    recordRequest(key, Date.now() - startTime, ctx.response.status);
  } catch (error) {
    // Record failed request
    recordRequest(key, Date.now() - startTime, 500, true);
    throw error;
  } finally {
    // Decrement in-progress counter
    metrics.httpRequestsInProgress--;
  }
};

/**
 * Record HTTP request metrics
 */
function recordRequest(key: string, duration: number, status: number, isError = false): void {
  // Count total requests
  metrics.httpRequests.set(key, (metrics.httpRequests.get(key) || 0) + 1);

  // Record duration
  if (!metrics.httpRequestDuration.has(key)) {
    metrics.httpRequestDuration.set(key, []);
  }
  metrics.httpRequestDuration.get(key)!.push(duration);

  // Keep only last 1000 durations per endpoint
  const durations = metrics.httpRequestDuration.get(key)!;
  if (durations.length > 1000) {
    durations.splice(0, durations.length - 1000);
  }

  // Count errors
  if (isError || status >= 400) {
    const errorKey = `${key}_${status}`;
    metrics.httpRequestErrors.set(errorKey, (metrics.httpRequestErrors.get(errorKey) || 0) + 1);
  }
}

/**
 * Get route pattern from context
 */
function getRoutePattern(ctx: Context): string {
  const pathname = ctx.request.url.pathname;
  
  // Replace dynamic segments with placeholders
  return pathname
    .replace(/\/\d+/g, "/:id")
    .replace(/\/[a-f0-9-]{36}/g, "/:uuid")
    .replace(/\/[a-f0-9]{24}/g, "/:objectid");
}

/**
 * Generate Prometheus-style metrics
 */
export function generateMetrics(): string {
  const lines: string[] = [];
  const now = Date.now();
  const uptime = (now - metrics.startTime) / 1000;

  // Process uptime
  lines.push("# HELP process_uptime_seconds Process uptime in seconds");
  lines.push("# TYPE process_uptime_seconds gauge");
  lines.push(`process_uptime_seconds ${uptime}`);
  lines.push("");

  // HTTP requests total
  lines.push("# HELP http_requests_total Total number of HTTP requests");
  lines.push("# TYPE http_requests_total counter");
  for (const [key, count] of metrics.httpRequests) {
    const [method, route] = key.split(" ", 2);
    lines.push(`http_requests_total{method="${method}",route="${route}"} ${count}`);
  }
  lines.push("");

  // HTTP requests in progress
  lines.push("# HELP http_requests_in_progress Number of HTTP requests currently being processed");
  lines.push("# TYPE http_requests_in_progress gauge");
  lines.push(`http_requests_in_progress ${metrics.httpRequestsInProgress}`);
  lines.push("");

  // HTTP request duration
  lines.push("# HELP http_request_duration_seconds HTTP request duration in seconds");
  lines.push("# TYPE http_request_duration_seconds histogram");
  for (const [key, durations] of metrics.httpRequestDuration) {
    const [method, route] = key.split(" ", 2);
    const sorted = durations.slice().sort((a, b) => a - b);
    
    if (sorted.length > 0) {
      const sum = sorted.reduce((a, b) => a + b, 0) / 1000; // Convert to seconds
      const count = sorted.length;
      
      // Calculate percentiles
      const p50 = sorted[Math.floor(sorted.length * 0.5)] / 1000;
      const p90 = sorted[Math.floor(sorted.length * 0.9)] / 1000;
      const p95 = sorted[Math.floor(sorted.length * 0.95)] / 1000;
      const p99 = sorted[Math.floor(sorted.length * 0.99)] / 1000;

      lines.push(`http_request_duration_seconds_sum{method="${method}",route="${route}"} ${sum}`);
      lines.push(`http_request_duration_seconds_count{method="${method}",route="${route}"} ${count}`);
      lines.push(`http_request_duration_seconds{method="${method}",route="${route}",quantile="0.5"} ${p50}`);
      lines.push(`http_request_duration_seconds{method="${method}",route="${route}",quantile="0.9"} ${p90}`);
      lines.push(`http_request_duration_seconds{method="${method}",route="${route}",quantile="0.95"} ${p95}`);
      lines.push(`http_request_duration_seconds{method="${method}",route="${route}",quantile="0.99"} ${p99}`);
    }
  }
  lines.push("");

  // HTTP errors
  lines.push("# HELP http_errors_total Total number of HTTP errors");
  lines.push("# TYPE http_errors_total counter");
  for (const [key, count] of metrics.httpRequestErrors) {
    const parts = key.split("_");
    const status = parts.pop();
    const routeKey = parts.join("_");
    const [method, route] = routeKey.split(" ", 2);
    lines.push(`http_errors_total{method="${method}",route="${route}",status="${status}"} ${count}`);
  }
  lines.push("");

  return lines.join("\n");
}

/**
 * Get current metrics summary
 */
export function getMetricsSummary(): any {
  const summary: any = {
    uptime: (Date.now() - metrics.startTime) / 1000,
    requestsInProgress: metrics.httpRequestsInProgress,
    totalRequests: 0,
    totalErrors: 0,
    endpoints: {},
  };

  // Calculate totals and endpoint stats
  for (const [key, count] of metrics.httpRequests) {
    summary.totalRequests += count;
    
    const durations = metrics.httpRequestDuration.get(key) || [];
    const avgDuration = durations.length > 0 
      ? durations.reduce((a, b) => a + b, 0) / durations.length 
      : 0;

    summary.endpoints[key] = {
      requests: count,
      avgDuration: Math.round(avgDuration),
      errors: 0,
    };
  }

  // Add error counts
  for (const [key, count] of metrics.httpRequestErrors) {
    summary.totalErrors += count;
    
    const parts = key.split("_");
    parts.pop(); // Remove status code
    const routeKey = parts.join("_");
    
    if (summary.endpoints[routeKey]) {
      summary.endpoints[routeKey].errors += count;
    }
  }

  return summary;
}

/**
 * Reset metrics (useful for testing)
 */
export function resetMetrics(): void {
  metrics.httpRequests.clear();
  metrics.httpRequestDuration.clear();
  metrics.httpRequestErrors.clear();
  metrics.httpRequestsInProgress = 0;
  metrics.startTime = Date.now();
  
  logger.info("Metrics reset");
}

/**
 * Custom metric recording
 */
export function recordCustomMetric(name: string, value: number, labels: Record<string, string> = {}): void {
  logger.debug("Custom metric recorded", {
    metric: name,
    value,
    labels,
  });
  
  // In a real implementation, you might want to store these in a separate structure
  // For now, just log them
}
