import { Context, Middleware } from "@oak/oak";
import { config } from "../config/config.ts";

/**
 * Security headers middleware
 */
export const securityHeaders: Middleware = async (ctx: Context, next) => {
  await next();

  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self' ws: wss:",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ].join("; ");

  // Set security headers
  ctx.response.headers.set("Content-Security-Policy", csp);
  ctx.response.headers.set("X-Content-Type-Options", "nosniff");
  ctx.response.headers.set("X-Frame-Options", "DENY");
  ctx.response.headers.set("X-XSS-Protection", "1; mode=block");
  ctx.response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  ctx.response.headers.set("Permissions-Policy", "geolocation=(), microphone=(), camera=()");
  
  // HSTS (only in production with HTTPS)
  if (config.nodeEnv === "production") {
    ctx.response.headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
  }

  // Remove server information
  ctx.response.headers.delete("server");
  ctx.response.headers.delete("x-powered-by");
  
  // Set custom server header
  ctx.response.headers.set("X-Service", "analytics-service");
  ctx.response.headers.set("X-Version", "1.0.0");
};
