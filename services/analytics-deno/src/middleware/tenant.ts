// Tenant validation middleware for multi-tenant data isolation
// Ensures proper tenant access control and data security

import { Context, Middleware } from "@oak/oak";
import { query } from "../utils/database.ts";
import { logger } from "../utils/logger.ts";

export interface TenantInfo {
  id: string;
  name: string;
  status: string;
  plan: string;
  features: string[];
}

/**
 * Middleware to validate tenant access and ensure data isolation
 */
export const validateTenantAccess: Middleware = async (ctx: Context, next) => {
  try {
    const tenantId = ctx.request.url.searchParams.get("tenant_id") || 
                    ctx.state.user?.tenantId ||
                    ctx.request.headers.get("x-tenant-id");

    if (!tenantId) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: {
          code: "MISSING_TENANT_ID",
          message: "Tenant ID is required for this operation",
        },
        timestamp: new Date().toISOString(),
      };
      return;
    }

    // Validate tenant ID format (UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(tenantId)) {
      ctx.response.status = 400;
      ctx.response.body = {
        success: false,
        error: {
          code: "INVALID_TENANT_ID",
          message: "Tenant ID must be a valid UUID",
        },
        timestamp: new Date().toISOString(),
      };
      return;
    }

    // Check if tenant exists and is active
    const tenantQuery = `
      SELECT 
        id,
        name,
        status,
        subscription_plan as plan,
        features
      FROM tenants 
      WHERE id = $1 AND status = 'active'
    `;

    try {
      const tenantResult = await query(tenantQuery, [tenantId], tenantId);
      
      if (!tenantResult || tenantResult.length === 0) {
        ctx.response.status = 403;
        ctx.response.body = {
          success: false,
          error: {
            code: "TENANT_NOT_FOUND",
            message: "Tenant not found or inactive",
          },
          timestamp: new Date().toISOString(),
        };
        return;
      }

      const tenant = tenantResult[0] as any;
      
      // Add tenant info to context state for use in route handlers
      ctx.state.tenant = {
        id: tenant.id,
        name: tenant.name,
        status: tenant.status,
        plan: tenant.plan,
        features: tenant.features || [],
      } as TenantInfo;

      // Log tenant access for audit purposes
      logger.info("Tenant access validated", {
        tenantId: tenant.id,
        tenantName: tenant.name,
        userId: ctx.state.user?.id,
        endpoint: ctx.request.url.pathname,
        method: ctx.request.method,
      });

      await next();
    } catch (dbError) {
      logger.error("Database error during tenant validation", {
        tenantId,
        error: dbError.message,
        stack: dbError.stack,
      });

      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: {
          code: "TENANT_VALIDATION_ERROR",
          message: "Failed to validate tenant access",
        },
        timestamp: new Date().toISOString(),
      };
    }
  } catch (error) {
    logger.error("Error in tenant validation middleware", {
      error: error.message,
      stack: error.stack,
    });

    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: {
        code: "MIDDLEWARE_ERROR",
        message: "Internal server error during tenant validation",
      },
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Middleware to check if tenant has access to specific features
 */
export function requireFeature(featureName: string): Middleware {
  return async (ctx: Context, next) => {
    const tenant = ctx.state.tenant as TenantInfo;
    
    if (!tenant) {
      ctx.response.status = 403;
      ctx.response.body = {
        success: false,
        error: {
          code: "TENANT_NOT_VALIDATED",
          message: "Tenant validation required before feature check",
        },
        timestamp: new Date().toISOString(),
      };
      return;
    }

    // Check if tenant has access to the required feature
    if (!tenant.features.includes(featureName)) {
      ctx.response.status = 403;
      ctx.response.body = {
        success: false,
        error: {
          code: "FEATURE_NOT_AVAILABLE",
          message: `Feature '${featureName}' is not available for your subscription plan`,
          availableFeatures: tenant.features,
          currentPlan: tenant.plan,
        },
        timestamp: new Date().toISOString(),
      };
      return;
    }

    await next();
  };
}

/**
 * Middleware to check subscription plan requirements
 */
export function requirePlan(requiredPlans: string[]): Middleware {
  return async (ctx: Context, next) => {
    const tenant = ctx.state.tenant as TenantInfo;
    
    if (!tenant) {
      ctx.response.status = 403;
      ctx.response.body = {
        success: false,
        error: {
          code: "TENANT_NOT_VALIDATED",
          message: "Tenant validation required before plan check",
        },
        timestamp: new Date().toISOString(),
      };
      return;
    }

    // Check if tenant's plan is in the required plans list
    if (!requiredPlans.includes(tenant.plan)) {
      ctx.response.status = 403;
      ctx.response.body = {
        success: false,
        error: {
          code: "PLAN_UPGRADE_REQUIRED",
          message: `This feature requires one of the following plans: ${requiredPlans.join(", ")}`,
          currentPlan: tenant.plan,
          requiredPlans,
        },
        timestamp: new Date().toISOString(),
      };
      return;
    }

    await next();
  };
}

/**
 * Utility function to get tenant ID from various sources
 */
export function getTenantId(ctx: Context): string | null {
  return ctx.request.url.searchParams.get("tenant_id") || 
         ctx.state.user?.tenantId ||
         ctx.request.headers.get("x-tenant-id") ||
         null;
}

/**
 * Utility function to ensure tenant data isolation in queries
 */
export function addTenantFilter(baseQuery: string, tenantId: string): string {
  // Add tenant_id filter to WHERE clause if not already present
  if (baseQuery.toLowerCase().includes("where")) {
    return baseQuery.replace(/where/i, `WHERE tenant_id = '${tenantId}' AND`);
  } else {
    return baseQuery + ` WHERE tenant_id = '${tenantId}'`;
  }
}

/**
 * Middleware for development/testing that bypasses tenant validation
 */
export const bypassTenantValidation: Middleware = async (ctx: Context, next) => {
  if (Deno.env.get("NODE_ENV") === "development" || Deno.env.get("BYPASS_TENANT_VALIDATION") === "true") {
    // Set a default test tenant for development
    ctx.state.tenant = {
      id: "00000000-0000-0000-0000-000000000000",
      name: "Test Tenant",
      status: "active",
      plan: "enterprise",
      features: ["cohort_analysis", "clv_calculation", "funnel_analysis", "predictive_analytics"],
    } as TenantInfo;

    logger.warn("Tenant validation bypassed for development", {
      endpoint: ctx.request.url.pathname,
      method: ctx.request.method,
    });
  }

  await next();
};
