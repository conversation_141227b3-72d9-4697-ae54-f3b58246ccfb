import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { authMiddleware } from "../middleware/auth.ts";
import { createEndpointRateLimiter } from "../middleware/rateLimiter.ts";
import { errors } from "../middleware/errorHandler.ts";

// Validation schemas using Zod (replacing <PERSON><PERSON>)
const summaryQuerySchema = z.object({
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  platform: z.string().optional(),
  link_id: z.string().optional(),
  integration_id: z.string().optional(),
});

const linkAnalyticsParamsSchema = z.object({
  linkId: z.string().uuid(),
});

const linkAnalyticsQuerySchema = z.object({
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  granularity: z.enum(["hour", "day", "week", "month"]).default("day"),
});

const timeSeriesQuerySchema = z.object({
  metric: z.enum(["clicks", "conversions", "revenue"]).default("clicks"),
  granularity: z.enum(["hour", "day", "week", "month"]).default("day"),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  link_id: z.string().optional(),
  platform: z.string().optional(),
});

const attributionParamsSchema = z.object({
  trackingId: z.string(),
});

const attributionQuerySchema = z.object({
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  model: z.enum(["first_touch", "last_touch", "linear", "time_decay"]).default("last_touch"),
});

// Create router
const router = new Router();

// Apply authentication middleware to all routes
router.use(authMiddleware);

// Apply rate limiting
router.use(createEndpointRateLimiter("analytics", 100, 60000)); // 100 requests per minute

// GET /analytics/summary - Get analytics summary with caching
router.get("/summary", async (ctx) => {
  try {
    logger.info("Analytics summary endpoint called", { 
      query: Object.fromEntries(ctx.request.url.searchParams),
      tenantId: ctx.state.tenantId,
    });
    
    // Validate query parameters
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = summaryQuerySchema.parse(queryParams);

    // TODO(@analytics-team): Implement AnalyticsService
    // const analyticsService = getAnalyticsService();
    // const result = await analyticsService.getSummary({
    //   ...validatedQuery,
    //   tenantId: ctx.state.tenantId,
    // });

    // Mock response for now
    const result = {
      total_clicks: 1250,
      total_conversions: 89,
      total_links: 45,
      total_revenue: 12450.75,
      avg_order_value: 139.90,
      conversion_rate: 7.12,
      period: {
        from: validatedQuery.date_from || "2024-01-01",
        to: validatedQuery.date_to || "2024-01-31",
      },
    };

    ctx.response.body = {
      success: true,
      data: result,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid query parameters", error.errors);
    }
    logger.error("Analytics summary error", error as Error);
    throw error;
  }
});

// GET /analytics/links/:linkId - Get analytics for specific link
router.get("/links/:linkId", async (ctx) => {
  try {
    // Validate parameters
    const params = linkAnalyticsParamsSchema.parse(ctx.params);
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = linkAnalyticsQuerySchema.parse(queryParams);

    logger.debug("Fetching link analytics", {
      linkId: params.linkId,
      ...validatedQuery,
      tenantId: ctx.state.tenantId,
    });

    // TODO(@analytics-team): Implement AnalyticsService
    // const analyticsService = getAnalyticsService();
    // const analytics = await analyticsService.getLinkAnalytics(params.linkId, {
    //   ...validatedQuery,
    //   tenantId: ctx.state.tenantId,
    // });

    // Mock response for now
    const analytics = {
      link_id: params.linkId,
      metrics: {
        clicks: 245,
        conversions: 18,
        revenue: 2150.50,
        conversion_rate: 7.35,
      },
      time_series: [
        { date: "2024-01-01", clicks: 12, conversions: 1, revenue: 125.00 },
        { date: "2024-01-02", clicks: 15, conversions: 2, revenue: 250.00 },
      ],
      top_sources: [
        { source: "google", clicks: 120, conversions: 8 },
        { source: "facebook", clicks: 85, conversions: 6 },
      ],
    };

    ctx.response.body = {
      success: true,
      data: analytics,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid parameters", error.errors);
    }
    logger.error("Link analytics error", error as Error);
    throw error;
  }
});

// GET /analytics/attribution/:trackingId - Get attribution data
router.get("/attribution/:trackingId", async (ctx) => {
  try {
    // Validate parameters
    const params = attributionParamsSchema.parse(ctx.params);
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = attributionQuerySchema.parse(queryParams);

    logger.debug("Fetching attribution data", {
      trackingId: params.trackingId,
      ...validatedQuery,
      tenantId: ctx.state.tenantId,
    });

    // TODO(@analytics-team): Implement AnalyticsService
    // const analyticsService = getAnalyticsService();
    // const attribution = await analyticsService.getAttribution(params.trackingId, {
    //   ...validatedQuery,
    //   tenantId: ctx.state.tenantId,
    // });

    // Mock response for now
    const attribution = {
      tracking_id: params.trackingId,
      model: validatedQuery.model,
      touchpoints: [
        {
          timestamp: "2024-01-01T10:00:00Z",
          source: "google",
          medium: "cpc",
          campaign: "winter-sale",
          attribution_weight: 0.4,
        },
        {
          timestamp: "2024-01-02T14:30:00Z",
          source: "facebook",
          medium: "social",
          campaign: "retargeting",
          attribution_weight: 0.6,
        },
      ],
      conversion: {
        timestamp: "2024-01-02T15:00:00Z",
        value: 125.00,
        order_id: "order_123",
      },
    };

    ctx.response.body = {
      success: true,
      data: attribution,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid parameters", error.errors);
    }
    logger.error("Attribution data error", error as Error);
    throw error;
  }
});

// GET /analytics/time-series - Get time series data
router.get("/time-series", async (ctx) => {
  try {
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = timeSeriesQuerySchema.parse(queryParams);

    logger.debug("Fetching time series data", {
      ...validatedQuery,
      tenantId: ctx.state.tenantId,
    });

    // TODO(@analytics-team): Implement AnalyticsService
    // const analyticsService = getAnalyticsService();
    // const timeSeries = await analyticsService.getTimeSeries({
    //   ...validatedQuery,
    //   tenantId: ctx.state.tenantId,
    // });

    // Mock response for now
    const timeSeries = {
      metric: validatedQuery.metric,
      granularity: validatedQuery.granularity,
      data: [
        { timestamp: "2024-01-01T00:00:00Z", value: 125 },
        { timestamp: "2024-01-02T00:00:00Z", value: 142 },
        { timestamp: "2024-01-03T00:00:00Z", value: 98 },
      ],
      summary: {
        total: 365,
        average: 121.67,
        trend: "increasing",
      },
    };

    ctx.response.body = {
      success: true,
      data: timeSeries,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid query parameters", error.errors);
    }
    logger.error("Time series data error", error as Error);
    throw error;
  }
});

export { router as analyticsRouter };
