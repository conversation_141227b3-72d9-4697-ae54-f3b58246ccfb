// Enhanced Cohort Analysis API Routes - Phase 2 Implementation
// RESTful endpoints for advanced cohort analysis with performance optimization

import { Router } from "@oak/oak";
import { CohortAnalysisService, CohortAnalysisOptions } from "../services/cohortAnalysisService.ts";
import { authenticateToken } from "../middleware/auth.ts";
import { validateTenantAccess } from "../middleware/tenant.ts";
import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

const router = new Router();
const cohortService = new CohortAnalysisService();

// Validation schemas
const CohortAnalysisQuerySchema = z.object({
  tenant_id: z.string().uuid(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  cohort_type: z.enum(['acquisition', 'behavioral', 'value']).default('acquisition'),
  granularity: z.enum(['daily', 'weekly', 'monthly']).default('monthly'),
  include_projections: z.string().transform(val => val === 'true').default('true'),
});

const CohortComparisonSchema = z.object({
  tenant_id: z.string().uuid(),
  cohort_ids: z.array(z.string()),
  metrics: z.array(z.enum(['retention', 'revenue', 'churn'])).default(['retention']),
});

/**
 * GET /api/enhanced-analytics/cohorts/analysis
 * Advanced cohort analysis with predictive capabilities
 */
router.get("/analysis", authenticateToken, validateTenantAccess, async (ctx) => {
  try {
    const startTime = performance.now();
    
    // Validate query parameters
    const queryParams = CohortAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      cohort_type: ctx.request.url.searchParams.get("cohort_type"),
      granularity: ctx.request.url.searchParams.get("granularity"),
      include_projections: ctx.request.url.searchParams.get("include_projections"),
    });

    // Set default date range if not provided (last 12 months)
    const dateTo = queryParams.date_to || new Date().toISOString();
    const dateFrom = queryParams.date_from || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString();

    const options: CohortAnalysisOptions = {
      tenantId: queryParams.tenant_id,
      dateFrom,
      dateTo,
      cohortType: queryParams.cohort_type,
      granularity: queryParams.granularity,
      includeProjections: queryParams.include_projections,
    };

    // Perform cohort analysis
    const result = await cohortService.analyzeCohorts(options);
    
    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: result,
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        recordCount: result.segments.length,
        cohortCount: result.overview.totalCohorts,
        performanceTarget: "< 500ms",
        performanceStatus: executionTime < 500 ? "OPTIMAL" : "NEEDS_OPTIMIZATION",
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Enhanced cohort analysis error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to perform cohort analysis",
      details: error.message,
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * GET /api/enhanced-analytics/cohorts/retention-curves
 * Get detailed retention curves for visualization
 */
router.get("/retention-curves", authenticateToken, validateTenantAccess, async (ctx) => {
  try {
    const startTime = performance.now();
    
    const queryParams = CohortAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      cohort_type: ctx.request.url.searchParams.get("cohort_type"),
      granularity: ctx.request.url.searchParams.get("granularity"),
      include_projections: ctx.request.url.searchParams.get("include_projections"),
    });

    const dateTo = queryParams.date_to || new Date().toISOString();
    const dateFrom = queryParams.date_from || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString();

    const options: CohortAnalysisOptions = {
      tenantId: queryParams.tenant_id,
      dateFrom,
      dateTo,
      cohortType: queryParams.cohort_type,
      granularity: queryParams.granularity,
      includeProjections: queryParams.include_projections,
    };

    // Get full analysis but focus on retention curves
    const result = await cohortService.analyzeCohorts(options);
    
    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: {
        retentionCurves: result.retentionCurves,
        overview: {
          avgRetentionRate: result.overview.avgRetentionRate,
          bestPerformingCohort: result.overview.bestPerformingCohort,
          retentionTrend: result.predictiveInsights.retentionTrend,
        },
        chartConfig: {
          xAxis: "Period (Months)",
          yAxis: "Retention Rate (%)",
          chartType: "line",
          showTrendLine: true,
        },
      },
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        cohortCount: Object.keys(result.retentionCurves).length,
        dataPoints: Object.values(result.retentionCurves).reduce((sum, curve) => sum + curve.length, 0),
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Retention curves error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to generate retention curves",
      details: error.message,
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * GET /api/enhanced-analytics/cohorts/segments
 * Get cohort segments with advanced filtering
 */
router.get("/segments", authenticateToken, validateTenantAccess, async (ctx) => {
  try {
    const startTime = performance.now();
    
    const queryParams = CohortAnalysisQuerySchema.parse({
      tenant_id: ctx.request.url.searchParams.get("tenant_id"),
      date_from: ctx.request.url.searchParams.get("date_from"),
      date_to: ctx.request.url.searchParams.get("date_to"),
      cohort_type: ctx.request.url.searchParams.get("cohort_type"),
      granularity: ctx.request.url.searchParams.get("granularity"),
      include_projections: ctx.request.url.searchParams.get("include_projections"),
    });

    // Additional filtering parameters
    const minCustomers = parseInt(ctx.request.url.searchParams.get("min_customers") || "0");
    const minRevenue = parseFloat(ctx.request.url.searchParams.get("min_revenue") || "0");
    const sortBy = ctx.request.url.searchParams.get("sort_by") || "totalRevenue";
    const sortOrder = ctx.request.url.searchParams.get("sort_order") || "desc";

    const dateTo = queryParams.date_to || new Date().toISOString();
    const dateFrom = queryParams.date_from || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString();

    const options: CohortAnalysisOptions = {
      tenantId: queryParams.tenant_id,
      dateFrom,
      dateTo,
      cohortType: queryParams.cohort_type,
      granularity: queryParams.granularity,
      includeProjections: queryParams.include_projections,
    };

    const result = await cohortService.analyzeCohorts(options);
    
    // Apply additional filtering and sorting
    let filteredSegments = result.segments.filter(segment => 
      segment.customerCount >= minCustomers && segment.totalRevenue >= minRevenue
    );

    // Sort segments
    filteredSegments.sort((a, b) => {
      const aValue = a[sortBy as keyof typeof a] as number;
      const bValue = b[sortBy as keyof typeof b] as number;
      return sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
    });

    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: {
        segments: filteredSegments,
        summary: {
          totalSegments: filteredSegments.length,
          totalCustomers: filteredSegments.reduce((sum, s) => sum + s.customerCount, 0),
          totalRevenue: filteredSegments.reduce((sum, s) => sum + s.totalRevenue, 0),
          avgChurnProbability: filteredSegments.length > 0 
            ? filteredSegments.reduce((sum, s) => sum + s.churnProbability, 0) / filteredSegments.length 
            : 0,
        },
        filters: {
          minCustomers,
          minRevenue,
          sortBy,
          sortOrder,
        },
      },
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        originalSegmentCount: result.segments.length,
        filteredSegmentCount: filteredSegments.length,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Cohort segments error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to retrieve cohort segments",
      details: error.message,
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * POST /api/enhanced-analytics/cohorts/compare
 * Compare multiple cohorts with detailed metrics
 */
router.post("/compare", authenticateToken, validateTenantAccess, async (ctx) => {
  try {
    const startTime = performance.now();
    
    const body = await ctx.request.body().value;
    const comparisonParams = CohortComparisonSchema.parse(body);

    // For now, return a placeholder response
    // This would be implemented with specific cohort comparison logic
    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      data: {
        comparison: {
          cohorts: comparisonParams.cohort_ids,
          metrics: comparisonParams.metrics,
          results: "Cohort comparison feature coming soon in Phase 2.1",
        },
      },
      metadata: {
        executionTime: `${executionTime.toFixed(2)}ms`,
        feature: "COMING_SOON",
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Cohort comparison error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to compare cohorts",
      details: error.message,
      timestamp: new Date().toISOString(),
    };
  }
});

/**
 * GET /api/enhanced-analytics/cohorts/health
 * Health check for cohort analysis service
 */
router.get("/health", async (ctx) => {
  try {
    const startTime = performance.now();
    
    // Quick health check query
    const healthCheck = await cohortService.analyzeCohorts({
      tenantId: "00000000-0000-0000-0000-000000000000", // Test tenant
      dateFrom: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      dateTo: new Date().toISOString(),
      cohortType: 'acquisition',
      granularity: 'monthly',
      includeProjections: false,
    });
    
    const executionTime = performance.now() - startTime;
    
    ctx.response.status = 200;
    ctx.response.body = {
      success: true,
      service: "Enhanced Cohort Analysis",
      status: "healthy",
      performance: {
        responseTime: `${executionTime.toFixed(2)}ms`,
        target: "< 500ms",
        status: executionTime < 500 ? "OPTIMAL" : "DEGRADED",
      },
      features: {
        cohortSegmentation: "active",
        retentionAnalysis: "active",
        predictiveInsights: "active",
        retentionCurves: "active",
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Cohort analysis health check failed:", error);
    ctx.response.status = 503;
    ctx.response.body = {
      success: false,
      service: "Enhanced Cohort Analysis",
      status: "unhealthy",
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
});

export default router;
