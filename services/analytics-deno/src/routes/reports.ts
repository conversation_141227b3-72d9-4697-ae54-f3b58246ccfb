import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { authMiddleware } from "../middleware/auth.ts";
import { createEndpointRateLimiter } from "../middleware/rateLimiter.ts";
import { errors } from "../middleware/errorHandler.ts";

// Validation schemas
const performanceQuerySchema = z.object({
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  metric: z.enum(["clicks", "conversions", "revenue", "ctr", "conversion_rate"]).default("clicks"),
  group_by: z.enum(["day", "week", "month", "source", "campaign"]).default("day"),
});

const conversionFunnelQuerySchema = z.object({
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  funnel_id: z.string().optional(),
  include_segments: z.boolean().default(false),
});

const generateReportSchema = z.object({
  type: z.enum(["performance", "conversion", "attribution", "cohort", "custom"]),
  format: z.enum(["json", "csv", "pdf"]).default("json"),
  date_from: z.string(),
  date_to: z.string(),
  filters: z.record(z.unknown()).optional(),
  template_id: z.string().optional(),
});

const scheduleReportSchema = z.object({
  name: z.string().min(1).max(100),
  type: z.enum(["performance", "conversion", "attribution", "cohort"]),
  format: z.enum(["csv", "pdf"]),
  frequency: z.enum(["daily", "weekly", "monthly"]),
  recipients: z.array(z.string().email()),
  filters: z.record(z.unknown()).optional(),
  template_id: z.string().optional(),
});

const exportReportSchema = z.object({
  report_id: z.string(),
  format: z.enum(["csv", "pdf", "xlsx"]),
  include_raw_data: z.boolean().default(false),
});

// Create router
const router = new Router();

// Apply authentication middleware
router.use(authMiddleware);

// Apply rate limiting
router.use(createEndpointRateLimiter("reports", 50, 60000)); // 50 requests per minute

// GET /reports/performance - Get performance reports
router.get("/performance", async (ctx) => {
  try {
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = performanceQuerySchema.parse(queryParams);

    logger.info("Performance report requested", {
      ...validatedQuery,
      tenantId: ctx.state.tenantId,
    });

    // TODO(@analytics-team): Implement ReportsService
    // const reportsService = getReportsService();
    // const report = await reportsService.getPerformanceReport({
    //   ...validatedQuery,
    //   tenantId: ctx.state.tenantId,
    // });

    // Mock response
    const report = {
      period: {
        from: validatedQuery.date_from || "2024-01-01",
        to: validatedQuery.date_to || "2024-01-31",
      },
      metric: validatedQuery.metric,
      group_by: validatedQuery.group_by,
      data: [
        {
          label: "2024-01-01",
          value: 1250,
          change_percent: 12.5,
          previous_value: 1111,
        },
        {
          label: "2024-01-02",
          value: 1380,
          change_percent: 10.4,
          previous_value: 1250,
        },
      ],
      summary: {
        total: 2630,
        average: 1315,
        best_performing: "2024-01-02",
        trend: "increasing",
      },
    };

    ctx.response.body = {
      success: true,
      data: report,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid query parameters", error.errors);
    }
    logger.error("Performance report error", error as Error);
    throw error;
  }
});

// GET /reports/conversion-funnel - Get conversion funnel analysis
router.get("/conversion-funnel", async (ctx) => {
  try {
    const queryParams = Object.fromEntries(ctx.request.url.searchParams);
    const validatedQuery = conversionFunnelQuerySchema.parse(queryParams);

    logger.info("Conversion funnel report requested", {
      ...validatedQuery,
      tenantId: ctx.state.tenantId,
    });

    // TODO(@analytics-team): Implement ReportsService
    // const reportsService = getReportsService();
    // const funnel = await reportsService.getConversionFunnel({
    //   ...validatedQuery,
    //   tenantId: ctx.state.tenantId,
    // });

    // Mock response
    const funnel = {
      funnel_id: validatedQuery.funnel_id || "default",
      period: {
        from: validatedQuery.date_from || "2024-01-01",
        to: validatedQuery.date_to || "2024-01-31",
      },
      steps: [
        {
          step: 1,
          name: "Link Click",
          users: 10000,
          conversion_rate: 100,
          drop_off_rate: 0,
        },
        {
          step: 2,
          name: "Page View",
          users: 8500,
          conversion_rate: 85,
          drop_off_rate: 15,
        },
        {
          step: 3,
          name: "Add to Cart",
          users: 2550,
          conversion_rate: 30,
          drop_off_rate: 70,
        },
        {
          step: 4,
          name: "Purchase",
          users: 765,
          conversion_rate: 30,
          drop_off_rate: 70,
        },
      ],
      overall_conversion_rate: 7.65,
      biggest_drop_off: {
        from_step: 2,
        to_step: 3,
        drop_off_rate: 70,
      },
    };

    ctx.response.body = {
      success: true,
      data: funnel,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid query parameters", error.errors);
    }
    logger.error("Conversion funnel error", error as Error);
    throw error;
  }
});

// POST /reports/generate - Generate a custom report
router.post("/generate", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    const validatedBody = generateReportSchema.parse(body);

    logger.info("Custom report generation requested", {
      type: validatedBody.type,
      format: validatedBody.format,
      tenantId: ctx.state.tenantId,
    });

    // TODO(@analytics-team): Implement ReportsService
    // const reportsService = getReportsService();
    // const report = await reportsService.generateReport({
    //   ...validatedBody,
    //   tenantId: ctx.state.tenantId,
    // });

    // Mock response
    const report = {
      report_id: `report_${Date.now()}`,
      type: validatedBody.type,
      format: validatedBody.format,
      status: "completed",
      generated_at: new Date().toISOString(),
      download_url: `/api/reports/download/report_${Date.now()}`,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
    };

    ctx.response.body = {
      success: true,
      data: report,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid request body", error.errors);
    }
    logger.error("Report generation error", error as Error);
    throw error;
  }
});

// GET /reports/templates - Get available report templates
router.get("/templates", async (ctx) => {
  try {
    logger.info("Report templates requested", {
      tenantId: ctx.state.tenantId,
    });

    // TODO(@analytics-team): Implement ReportsService
    // const reportsService = getReportsService();
    // const templates = await reportsService.getTemplates(ctx.state.tenantId);

    // Mock response
    const templates = [
      {
        id: "performance_summary",
        name: "Performance Summary",
        description: "Overview of key performance metrics",
        type: "performance",
        supported_formats: ["json", "csv", "pdf"],
      },
      {
        id: "conversion_analysis",
        name: "Conversion Analysis",
        description: "Detailed conversion funnel analysis",
        type: "conversion",
        supported_formats: ["json", "csv", "pdf"],
      },
      {
        id: "attribution_report",
        name: "Attribution Report",
        description: "Multi-touch attribution analysis",
        type: "attribution",
        supported_formats: ["json", "csv"],
      },
    ];

    ctx.response.body = {
      success: true,
      data: templates,
    };
  } catch (error) {
    logger.error("Report templates error", error as Error);
    throw error;
  }
});

// POST /reports/schedule - Schedule a recurring report
router.post("/schedule", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    const validatedBody = scheduleReportSchema.parse(body);

    logger.info("Report scheduling requested", {
      name: validatedBody.name,
      frequency: validatedBody.frequency,
      tenantId: ctx.state.tenantId,
    });

    // TODO(@analytics-team): Implement ReportsService
    // const reportsService = getReportsService();
    // const schedule = await reportsService.scheduleReport({
    //   ...validatedBody,
    //   tenantId: ctx.state.tenantId,
    // });

    // Mock response
    const schedule = {
      schedule_id: `schedule_${Date.now()}`,
      name: validatedBody.name,
      type: validatedBody.type,
      frequency: validatedBody.frequency,
      next_run: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      status: "active",
      created_at: new Date().toISOString(),
    };

    ctx.response.body = {
      success: true,
      data: schedule,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid request body", error.errors);
    }
    logger.error("Report scheduling error", error as Error);
    throw error;
  }
});

// GET /reports/scheduled - Get scheduled reports
router.get("/scheduled", async (ctx) => {
  try {
    logger.info("Scheduled reports requested", {
      tenantId: ctx.state.tenantId,
    });

    // TODO(@analytics-team): Implement ReportsService
    // const reportsService = getReportsService();
    // const schedules = await reportsService.getScheduledReports(ctx.state.tenantId);

    // Mock response
    const schedules = [
      {
        schedule_id: "schedule_1",
        name: "Weekly Performance Report",
        type: "performance",
        frequency: "weekly",
        next_run: "2024-01-08T09:00:00Z",
        status: "active",
        last_run: "2024-01-01T09:00:00Z",
      },
      {
        schedule_id: "schedule_2",
        name: "Monthly Attribution Analysis",
        type: "attribution",
        frequency: "monthly",
        next_run: "2024-02-01T09:00:00Z",
        status: "active",
        last_run: "2024-01-01T09:00:00Z",
      },
    ];

    ctx.response.body = {
      success: true,
      data: schedules,
    };
  } catch (error) {
    logger.error("Scheduled reports error", error as Error);
    throw error;
  }
});

export { router as reportsRouter };
