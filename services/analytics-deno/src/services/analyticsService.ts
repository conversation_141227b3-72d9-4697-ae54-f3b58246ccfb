import { query, queryOne, transaction } from "../utils/database.ts";
import { set, get, getJSON } from "../utils/redis.ts";
import { logger } from "../utils/logger.ts";

// Database result types
interface SummaryResult {
  total_clicks: string;
  total_conversions: string;
  total_links: string;
  total_revenue: string;
  avg_order_value: string;
  conversion_rate: string;
}

interface MetricsResult {
  clicks: string;
  conversions: string;
  revenue: string;
  conversion_rate: string;
}

interface TimeSeriesRow {
  period: string;
  clicks: string;
  conversions: string;
  revenue: string;
}

interface SourceRow {
  source: string;
  clicks: string;
  conversions: string;
}

interface TimeSeriesDataRow {
  timestamp: string;
  value: string;
}

export interface AnalyticsSummaryOptions {
  dateFrom?: string;
  dateTo?: string;
  platform?: string;
  linkId?: string;
  integrationId?: string;
  tenantId: string;
}

export interface LinkAnalyticsOptions {
  dateFrom?: string;
  dateTo?: string;
  granularity?: "hour" | "day" | "week" | "month";
  tenantId: string;
}

export interface TimeSeriesOptions {
  metric?: "clicks" | "conversions" | "revenue";
  granularity?: "hour" | "day" | "week" | "month";
  dateFrom?: string;
  dateTo?: string;
  linkId?: string;
  platform?: string;
  tenantId: string;
}

export interface AttributionOptions {
  dateFrom?: string;
  dateTo?: string;
  model?: "first_touch" | "last_touch" | "linear" | "time_decay";
  tenantId: string;
}

export class AnalyticsService {
  private cacheTTL = {
    short: 300, // 5 minutes
    medium: 1800, // 30 minutes
    long: 3600, // 1 hour
  };

  /**
   * Get analytics summary with caching
   */
  async getSummary(options: AnalyticsSummaryOptions) {
    const startTime = Date.now();
    
    try {
      const cacheKey = `analytics:summary:${JSON.stringify(options)}`;
      
      // Try cache first
      const cached = await getJSON(cacheKey, options.tenantId);
      if (cached) {
        logger.debug("Analytics summary cache hit", { cacheKey });
        return cached;
      }

      // Build query conditions
      const conditions: string[] = [];
      const params: unknown[] = [];
      let paramIndex = 1;

      // Date range filter
      if (options.dateFrom) {
        conditions.push(`c.clicked_at >= $${paramIndex}`);
        params.push(options.dateFrom);
        paramIndex++;
      }

      if (options.dateTo) {
        conditions.push(`c.clicked_at <= $${paramIndex}`);
        params.push(options.dateTo);
        paramIndex++;
      }

      // Platform filter
      if (options.platform) {
        conditions.push(`c.platform = $${paramIndex}`);
        params.push(options.platform);
        paramIndex++;
      }

      // Link filter
      if (options.linkId) {
        conditions.push(`c.link_id = $${paramIndex}`);
        params.push(options.linkId);
        paramIndex++;
      }

      // Integration filter
      if (options.integrationId) {
        conditions.push(`i.id = $${paramIndex}`);
        params.push(options.integrationId);
        paramIndex++;
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Get summary metrics
      const summaryQuery = `
        SELECT 
          COUNT(DISTINCT c.id) as total_clicks,
          COUNT(DISTINCT a.id) as total_conversions,
          COUNT(DISTINCT l.id) as total_links,
          COALESCE(SUM(o.total_amount), 0) as total_revenue,
          COALESCE(AVG(o.total_amount), 0) as avg_order_value,
          CASE 
            WHEN COUNT(DISTINCT c.id) > 0 
            THEN (COUNT(DISTINCT a.id)::float / COUNT(DISTINCT c.id)::float * 100)
            ELSE 0 
          END as conversion_rate
        FROM clicks c
        LEFT JOIN links l ON c.link_id = l.id
        LEFT JOIN attributions a ON c.id = a.click_id
        LEFT JOIN orders o ON a.order_id = o.id
        LEFT JOIN integrations i ON o.integration_id = i.id
        ${whereClause}
      `;

      const summaryResult = await queryOne(summaryQuery, params, options.tenantId);

      const summaryData = summaryResult as SummaryResult | null;
      const result = {
        total_clicks: parseInt(summaryData?.total_clicks || "0"),
        total_conversions: parseInt(summaryData?.total_conversions || "0"),
        total_links: parseInt(summaryData?.total_links || "0"),
        total_revenue: parseFloat(summaryData?.total_revenue || "0"),
        avg_order_value: parseFloat(summaryData?.avg_order_value || "0"),
        conversion_rate: parseFloat(summaryData?.conversion_rate || "0"),
        period: {
          from: options.dateFrom || null,
          to: options.dateTo || null,
        },
        generated_at: new Date().toISOString(),
      };

      // Cache the result
      await set(cacheKey, result, this.cacheTTL.short, options.tenantId);

      const duration = Date.now() - startTime;
      logger.info("Analytics summary generated", {
        duration,
        tenantId: options.tenantId,
        cached: false,
      });

      return result;
    } catch (error) {
      logger.error("Failed to get analytics summary", error as Error);
      throw error;
    }
  }

  /**
   * Get analytics for a specific link
   */
  async getLinkAnalytics(linkId: string, options: LinkAnalyticsOptions) {
    const startTime = Date.now();
    
    try {
      const cacheKey = `analytics:link:${linkId}:${JSON.stringify(options)}`;
      
      const cached = await getJSON(cacheKey, options.tenantId);
      if (cached) {
        logger.debug("Link analytics cache hit", { linkId, cacheKey });
        return cached;
      }

      // Build time series query based on granularity
      const timeFormat = this.getTimeFormat(options.granularity || "day");
      
      const conditions: string[] = [`c.link_id = $1`];
      const params: unknown[] = [linkId];
      let paramIndex = 2;

      if (options.dateFrom) {
        conditions.push(`c.clicked_at >= $${paramIndex}`);
        params.push(options.dateFrom);
        paramIndex++;
      }

      if (options.dateTo) {
        conditions.push(`c.clicked_at <= $${paramIndex}`);
        params.push(options.dateTo);
        paramIndex++;
      }

      const whereClause = `WHERE ${conditions.join(' AND ')}`;

      // Get link metrics
      const metricsQuery = `
        SELECT 
          COUNT(DISTINCT c.id) as clicks,
          COUNT(DISTINCT a.id) as conversions,
          COALESCE(SUM(o.total_amount), 0) as revenue,
          CASE 
            WHEN COUNT(DISTINCT c.id) > 0 
            THEN (COUNT(DISTINCT a.id)::float / COUNT(DISTINCT c.id)::float * 100)
            ELSE 0 
          END as conversion_rate
        FROM clicks c
        LEFT JOIN attributions a ON c.id = a.click_id
        LEFT JOIN orders o ON a.order_id = o.id
        ${whereClause}
      `;

      const metricsResult = await queryOne(metricsQuery, params, options.tenantId);

      // Get time series data
      const timeSeriesQuery = `
        SELECT 
          ${timeFormat} as period,
          COUNT(DISTINCT c.id) as clicks,
          COUNT(DISTINCT a.id) as conversions,
          COALESCE(SUM(o.total_amount), 0) as revenue
        FROM clicks c
        LEFT JOIN attributions a ON c.id = a.click_id
        LEFT JOIN orders o ON a.order_id = o.id
        ${whereClause}
        GROUP BY ${timeFormat}
        ORDER BY ${timeFormat}
      `;

      const timeSeriesResult = await query(timeSeriesQuery, params, options.tenantId);

      // Get top sources
      const sourcesQuery = `
        SELECT 
          c.referrer_domain as source,
          COUNT(DISTINCT c.id) as clicks,
          COUNT(DISTINCT a.id) as conversions
        FROM clicks c
        LEFT JOIN attributions a ON c.id = a.click_id
        ${whereClause}
        GROUP BY c.referrer_domain
        ORDER BY clicks DESC
        LIMIT 10
      `;

      const sourcesResult = await query(sourcesQuery, params, options.tenantId);

      const metricsData = metricsResult as MetricsResult | null;
      const result = {
        link_id: linkId,
        metrics: {
          clicks: parseInt(metricsData?.clicks || "0"),
          conversions: parseInt(metricsData?.conversions || "0"),
          revenue: parseFloat(metricsData?.revenue || "0"),
          conversion_rate: parseFloat(metricsData?.conversion_rate || "0"),
        },
        time_series: (timeSeriesResult as TimeSeriesRow[]).map((row) => ({
          date: row.period,
          clicks: parseInt(row.clicks),
          conversions: parseInt(row.conversions),
          revenue: parseFloat(row.revenue),
        })),
        top_sources: (sourcesResult as SourceRow[]).map((row) => ({
          source: row.source || "direct",
          clicks: parseInt(row.clicks),
          conversions: parseInt(row.conversions),
        })),
        generated_at: new Date().toISOString(),
      };

      // Cache the result
      await set(cacheKey, result, this.cacheTTL.medium, options.tenantId);

      const duration = Date.now() - startTime;
      logger.info("Link analytics generated", {
        linkId,
        duration,
        tenantId: options.tenantId,
      });

      return result;
    } catch (error) {
      logger.error("Failed to get link analytics", error as Error);
      throw error;
    }
  }

  /**
   * Get time series data
   */
  async getTimeSeries(options: TimeSeriesOptions) {
    const startTime = Date.now();
    
    try {
      const cacheKey = `analytics:time-series:${JSON.stringify(options)}`;
      
      const cached = await getJSON(cacheKey, options.tenantId);
      if (cached) {
        logger.debug("Time series cache hit", { cacheKey });
        return cached;
      }

      const timeFormat = this.getTimeFormat(options.granularity || "day");
      const metric = this.getMetricColumn(options.metric || "clicks");
      
      const conditions: string[] = [];
      const params: unknown[] = [];
      let paramIndex = 1;

      if (options.dateFrom) {
        conditions.push(`c.clicked_at >= $${paramIndex}`);
        params.push(options.dateFrom);
        paramIndex++;
      }

      if (options.dateTo) {
        conditions.push(`c.clicked_at <= $${paramIndex}`);
        params.push(options.dateTo);
        paramIndex++;
      }

      if (options.linkId) {
        conditions.push(`c.link_id = $${paramIndex}`);
        params.push(options.linkId);
        paramIndex++;
      }

      if (options.platform) {
        conditions.push(`c.platform = $${paramIndex}`);
        params.push(options.platform);
        paramIndex++;
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      const timeSeriesQuery = `
        SELECT 
          ${timeFormat} as timestamp,
          ${metric} as value
        FROM clicks c
        LEFT JOIN attributions a ON c.id = a.click_id
        LEFT JOIN orders o ON a.order_id = o.id
        ${whereClause}
        GROUP BY ${timeFormat}
        ORDER BY ${timeFormat}
      `;

      const timeSeriesResult = await query(timeSeriesQuery, params, options.tenantId);

      // Calculate summary statistics
      const timeSeriesData = timeSeriesResult as TimeSeriesDataRow[];
      const values = timeSeriesData.map((row) => parseFloat(row.value));
      const total = values.reduce((sum, val) => sum + val, 0);
      const average = values.length > 0 ? total / values.length : 0;
      const trend = this.calculateTrend(values);

      const result = {
        metric: options.metric || "clicks",
        granularity: options.granularity || "day",
        data: timeSeriesData.map((row) => ({
          timestamp: row.timestamp,
          value: parseFloat(row.value),
        })),
        summary: {
          total,
          average: Math.round(average * 100) / 100,
          trend,
        },
        generated_at: new Date().toISOString(),
      };

      // Cache the result
      await set(cacheKey, result, this.cacheTTL.medium, options.tenantId);

      const duration = Date.now() - startTime;
      logger.info("Time series generated", {
        metric: options.metric,
        duration,
        tenantId: options.tenantId,
      });

      return result;
    } catch (error) {
      logger.error("Failed to get time series", error as Error);
      throw error;
    }
  }

  /**
   * Get time format for SQL based on granularity
   */
  private getTimeFormat(granularity: string): string {
    switch (granularity) {
      case "hour":
        return "DATE_TRUNC('hour', c.clicked_at)";
      case "day":
        return "DATE_TRUNC('day', c.clicked_at)";
      case "week":
        return "DATE_TRUNC('week', c.clicked_at)";
      case "month":
        return "DATE_TRUNC('month', c.clicked_at)";
      default:
        return "DATE_TRUNC('day', c.clicked_at)";
    }
  }

  /**
   * Get metric column for SQL based on metric type
   */
  private getMetricColumn(metric: string): string {
    switch (metric) {
      case "clicks":
        return "COUNT(DISTINCT c.id)";
      case "conversions":
        return "COUNT(DISTINCT a.id)";
      case "revenue":
        return "COALESCE(SUM(o.total_amount), 0)";
      default:
        return "COUNT(DISTINCT c.id)";
    }
  }

  /**
   * Calculate trend from time series values
   */
  private calculateTrend(values: number[]): string {
    if (values.length < 2) return "stable";
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    
    const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    if (changePercent > 5) return "increasing";
    if (changePercent < -5) return "decreasing";
    return "stable";
  }
}
