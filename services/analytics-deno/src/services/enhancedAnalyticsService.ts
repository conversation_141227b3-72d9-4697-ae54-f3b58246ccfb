import { query, queryOne, transaction } from "../utils/database.ts";
import { set, get, getJSON } from "../utils/redis.ts";
import { logger } from "../utils/logger.ts";

// Enhanced Analytics Service for Phase 1 Implementation
// Implements business metrics and KPIs from the enhanced schema

export interface DashboardMetricsOptions {
  tenantId: string;
  timeRange?: string; // '7d', '30d', '90d', '1y'
  dateFrom?: string;
  dateTo?: string;
}

export interface CustomerAnalyticsOptions {
  tenantId: string;
  customerId?: string;
  segment?: string;
  timeRange?: string;
}

export interface RevenueAnalyticsOptions {
  tenantId: string;
  platformId?: string;
  timeRange?: string;
  granularity?: 'hour' | 'day' | 'week' | 'month';
}

export interface AttributionAnalyticsOptions {
  tenantId: string;
  modelType?: 'first_touch' | 'last_touch' | 'linear' | 'time_decay';
  timeRange?: string;
}

export interface SocialMediaAnalyticsOptions {
  tenantId: string;
  platform?: string;
  accountId?: string;
  timeRange?: string;
}

export class EnhancedAnalyticsService {
  private cacheTTL = {
    realtime: 60,     // 1 minute for real-time data
    short: 300,       // 5 minutes for dashboard data
    medium: 1800,     // 30 minutes for detailed analytics
    long: 3600,       // 1 hour for historical data
  };

  /**
   * Get comprehensive dashboard metrics using continuous aggregates
   */
  async getDashboardMetrics(options: DashboardMetricsOptions) {
    const startTime = Date.now();
    
    try {
      const cacheKey = `dashboard:metrics:${JSON.stringify(options)}`;
      
      // Try cache first
      const cached = await getJSON(cacheKey, options.tenantId);
      if (cached) {
        logger.debug("Dashboard metrics cache hit", { cacheKey });
        return cached;
      }

      const { dateFrom, dateTo } = this.getDateRange(options.timeRange || '30d');
      
      // Use continuous aggregates for performance
      const metricsQuery = `
        WITH revenue_metrics AS (
          SELECT 
            SUM(total_revenue) as total_revenue,
            SUM(total_orders) as total_orders,
            SUM(unique_customers) as unique_customers,
            AVG(avg_order_value) as avg_order_value
          FROM daily_order_metrics
          WHERE tenant_id = $1 
            AND day >= $2 
            AND day <= $3
        ),
        customer_metrics AS (
          SELECT 
            SUM(total_events) as total_events,
            SUM(unique_customers) as active_customers,
            SUM(purchases) as total_purchases,
            SUM(unique_sessions) as total_sessions
          FROM daily_customer_metrics
          WHERE tenant_id = $1 
            AND day >= $2 
            AND day <= $3
        ),
        link_metrics AS (
          SELECT 
            SUM(total_clicks) as total_clicks,
            SUM(unique_clicks) as unique_clicks,
            COUNT(DISTINCT link_id) as active_links
          FROM daily_link_metrics
          WHERE tenant_id = $1 
            AND day >= $2 
            AND day <= $3
        ),
        social_metrics AS (
          SELECT 
            SUM(CASE WHEN metric_type = 'impressions' THEN total_value ELSE 0 END) as total_impressions,
            SUM(CASE WHEN metric_type = 'engagement' THEN total_value ELSE 0 END) as total_engagement,
            SUM(CASE WHEN metric_type = 'reach' THEN total_value ELSE 0 END) as total_reach
          FROM daily_social_metrics
          WHERE tenant_id = $1 
            AND day >= $2 
            AND day <= $3
        )
        SELECT 
          rm.total_revenue,
          rm.total_orders,
          rm.unique_customers,
          rm.avg_order_value,
          cm.total_events,
          cm.active_customers,
          cm.total_purchases,
          cm.total_sessions,
          lm.total_clicks,
          lm.unique_clicks,
          lm.active_links,
          sm.total_impressions,
          sm.total_engagement,
          sm.total_reach,
          CASE 
            WHEN lm.total_clicks > 0 
            THEN (rm.total_orders::float / lm.total_clicks::float * 100)
            ELSE 0 
          END as conversion_rate,
          CASE 
            WHEN sm.total_impressions > 0 
            THEN (sm.total_engagement::float / sm.total_impressions::float * 100)
            ELSE 0 
          END as engagement_rate
        FROM revenue_metrics rm
        CROSS JOIN customer_metrics cm
        CROSS JOIN link_metrics lm
        CROSS JOIN social_metrics sm
      `;

      const metricsResult = await queryOne(
        metricsQuery, 
        [options.tenantId, dateFrom, dateTo], 
        options.tenantId
      );

      // Get trend data for comparison
      const previousPeriod = this.getPreviousPeriod(dateFrom, dateTo);
      const trendsQuery = `
        SELECT 
          SUM(total_revenue) as prev_revenue,
          SUM(total_orders) as prev_orders,
          SUM(unique_customers) as prev_customers
        FROM daily_order_metrics
        WHERE tenant_id = $1 
          AND day >= $2 
          AND day <= $3
      `;

      const trendsResult = await queryOne(
        trendsQuery,
        [options.tenantId, previousPeriod.from, previousPeriod.to],
        options.tenantId
      );

      // Calculate percentage changes
      const currentRevenue = parseFloat(metricsResult?.total_revenue || '0');
      const prevRevenue = parseFloat(trendsResult?.prev_revenue || '0');
      const revenueChange = prevRevenue > 0 ? ((currentRevenue - prevRevenue) / prevRevenue) * 100 : 0;

      const currentOrders = parseInt(metricsResult?.total_orders || '0');
      const prevOrders = parseInt(trendsResult?.prev_orders || '0');
      const ordersChange = prevOrders > 0 ? ((currentOrders - prevOrders) / prevOrders) * 100 : 0;

      const currentCustomers = parseInt(metricsResult?.unique_customers || '0');
      const prevCustomers = parseInt(trendsResult?.prev_customers || '0');
      const customersChange = prevCustomers > 0 ? ((currentCustomers - prevCustomers) / prevCustomers) * 100 : 0;

      const result = {
        overview: {
          totalRevenue: currentRevenue,
          revenueChange: Math.round(revenueChange * 100) / 100,
          totalOrders: currentOrders,
          ordersChange: Math.round(ordersChange * 100) / 100,
          totalCustomers: currentCustomers,
          customersChange: Math.round(customersChange * 100) / 100,
          avgOrderValue: parseFloat(metricsResult?.avg_order_value || '0'),
          conversionRate: parseFloat(metricsResult?.conversion_rate || '0'),
          engagementRate: parseFloat(metricsResult?.engagement_rate || '0'),
        },
        activity: {
          totalEvents: parseInt(metricsResult?.total_events || '0'),
          totalSessions: parseInt(metricsResult?.total_sessions || '0'),
          totalClicks: parseInt(metricsResult?.total_clicks || '0'),
          uniqueClicks: parseInt(metricsResult?.unique_clicks || '0'),
          activeLinks: parseInt(metricsResult?.active_links || '0'),
        },
        socialMedia: {
          totalImpressions: parseInt(metricsResult?.total_impressions || '0'),
          totalEngagement: parseInt(metricsResult?.total_engagement || '0'),
          totalReach: parseInt(metricsResult?.total_reach || '0'),
        },
        period: {
          from: dateFrom,
          to: dateTo,
          range: options.timeRange || '30d',
        },
        generatedAt: new Date().toISOString(),
      };

      // Cache the result
      await set(cacheKey, result, this.cacheTTL.short, options.tenantId);

      const duration = Date.now() - startTime;
      logger.info("Dashboard metrics generated", {
        duration,
        tenantId: options.tenantId,
        cached: false,
      });

      return result;
    } catch (error) {
      logger.error("Failed to get dashboard metrics", error as Error);
      throw error;
    }
  }

  /**
   * Get real-time revenue tracking (last 24 hours)
   */
  async getRealTimeRevenue(tenantId: string) {
    const startTime = Date.now();
    
    try {
      const cacheKey = `realtime:revenue:${tenantId}`;
      
      const cached = await getJSON(cacheKey, tenantId);
      if (cached) {
        return cached;
      }

      // Use hourly aggregates for real-time data
      const realtimeQuery = `
        SELECT 
          hour,
          total_revenue,
          total_orders,
          unique_customers,
          unique_sessions
        FROM hourly_customer_metrics
        WHERE tenant_id = $1 
          AND hour >= NOW() - INTERVAL '24 hours'
        ORDER BY hour DESC
      `;

      const realtimeResult = await query(realtimeQuery, [tenantId], tenantId);

      const result = {
        hourlyData: realtimeResult.map((row: any) => ({
          hour: row.hour,
          revenue: parseFloat(row.total_revenue || '0'),
          orders: parseInt(row.total_orders || '0'),
          customers: parseInt(row.unique_customers || '0'),
          sessions: parseInt(row.unique_sessions || '0'),
        })),
        summary: {
          last24Hours: {
            revenue: realtimeResult.reduce((sum: number, row: any) => sum + parseFloat(row.total_revenue || '0'), 0),
            orders: realtimeResult.reduce((sum: number, row: any) => sum + parseInt(row.total_orders || '0'), 0),
            customers: realtimeResult.reduce((sum: number, row: any) => sum + parseInt(row.unique_customers || '0'), 0),
          }
        },
        generatedAt: new Date().toISOString(),
      };

      // Cache for 1 minute (real-time data)
      await set(cacheKey, result, this.cacheTTL.realtime, tenantId);

      const duration = Date.now() - startTime;
      logger.info("Real-time revenue generated", {
        duration,
        tenantId,
        dataPoints: realtimeResult.length,
      });

      return result;
    } catch (error) {
      logger.error("Failed to get real-time revenue", error as Error);
      throw error;
    }
  }

  /**
   * Get customer lifetime value analysis
   */
  async getCustomerLifetimeValue(options: CustomerAnalyticsOptions) {
    const startTime = Date.now();
    
    try {
      const cacheKey = `clv:analysis:${JSON.stringify(options)}`;
      
      const cached = await getJSON(cacheKey, options.tenantId);
      if (cached) {
        return cached;
      }

      const { dateFrom, dateTo } = this.getDateRange(options.timeRange || '1y');

      // CLV calculation with cohort analysis
      const clvQuery = `
        WITH customer_metrics AS (
          SELECT 
            c.id,
            c.email,
            c.acquisition_date,
            c.lifetime_value,
            c.total_orders,
            c.total_spent,
            c.avg_order_value,
            c.churn_probability,
            DATE_TRUNC('month', c.acquisition_date) as cohort_month,
            EXTRACT(DAYS FROM (COALESCE(c.last_order_date, NOW()) - c.acquisition_date)) as customer_age_days
          FROM customers c
          WHERE c.tenant_id = $1
            AND c.acquisition_date >= $2
            AND c.acquisition_date <= $3
        ),
        clv_segments AS (
          SELECT 
            CASE 
              WHEN lifetime_value >= 1000 THEN 'VIP'
              WHEN lifetime_value >= 500 THEN 'High Value'
              WHEN lifetime_value >= 100 THEN 'Medium Value'
              ELSE 'Low Value'
            END as segment,
            COUNT(*) as customer_count,
            AVG(lifetime_value) as avg_clv,
            SUM(lifetime_value) as total_clv,
            AVG(total_orders) as avg_orders,
            AVG(churn_probability) as avg_churn_risk
          FROM customer_metrics
          GROUP BY segment
        ),
        cohort_analysis AS (
          SELECT 
            cohort_month,
            COUNT(*) as cohort_size,
            AVG(lifetime_value) as avg_cohort_clv,
            AVG(customer_age_days) as avg_customer_age
          FROM customer_metrics
          GROUP BY cohort_month
          ORDER BY cohort_month
        )
        SELECT 
          (SELECT json_agg(row_to_json(cs)) FROM clv_segments cs) as segments,
          (SELECT json_agg(row_to_json(ca)) FROM cohort_analysis ca) as cohorts,
          (SELECT AVG(lifetime_value) FROM customer_metrics) as overall_avg_clv,
          (SELECT COUNT(*) FROM customer_metrics) as total_customers
      `;

      const clvResult = await queryOne(clvQuery, [options.tenantId, dateFrom, dateTo], options.tenantId);

      const result = {
        segments: clvResult?.segments || [],
        cohorts: clvResult?.cohorts || [],
        overview: {
          averageClv: parseFloat(clvResult?.overall_avg_clv || '0'),
          totalCustomers: parseInt(clvResult?.total_customers || '0'),
        },
        period: {
          from: dateFrom,
          to: dateTo,
        },
        generatedAt: new Date().toISOString(),
      };

      // Cache for 30 minutes
      await set(cacheKey, result, this.cacheTTL.medium, options.tenantId);

      const duration = Date.now() - startTime;
      logger.info("CLV analysis generated", {
        duration,
        tenantId: options.tenantId,
      });

      return result;
    } catch (error) {
      logger.error("Failed to get CLV analysis", error as Error);
      throw error;
    }
  }

  /**
   * Get attribution analysis using multiple models
   */
  async getAttributionAnalysis(options: AttributionAnalyticsOptions) {
    const startTime = Date.now();
    
    try {
      const cacheKey = `attribution:analysis:${JSON.stringify(options)}`;
      
      const cached = await getJSON(cacheKey, options.tenantId);
      if (cached) {
        return cached;
      }

      const { dateFrom, dateTo } = this.getDateRange(options.timeRange || '30d');

      // Multi-touch attribution analysis
      const attributionQuery = `
        WITH attribution_data AS (
          SELECT 
            ct.channel,
            ct.touchpoint_type,
            ct.source,
            COUNT(*) as touchpoints,
            COUNT(DISTINCT ct.customer_id) as unique_customers,
            SUM(ct.touchpoint_value) as attributed_revenue,
            AVG(ct.touchpoint_value) FILTER (WHERE ct.touchpoint_value > 0) as avg_attribution_value
          FROM customer_touchpoints ct
          WHERE ct.tenant_id = $1
            AND ct.touchpoint_timestamp >= $2
            AND ct.touchpoint_timestamp <= $3
          GROUP BY ct.channel, ct.touchpoint_type, ct.source
        ),
        channel_performance AS (
          SELECT 
            channel,
            SUM(touchpoints) as total_touchpoints,
            SUM(unique_customers) as total_customers,
            SUM(attributed_revenue) as total_revenue,
            AVG(avg_attribution_value) as avg_value
          FROM attribution_data
          GROUP BY channel
          ORDER BY total_revenue DESC
        )
        SELECT 
          (SELECT json_agg(row_to_json(ad)) FROM attribution_data ad) as detailed_attribution,
          (SELECT json_agg(row_to_json(cp)) FROM channel_performance cp) as channel_performance
      `;

      const attributionResult = await queryOne(
        attributionQuery, 
        [options.tenantId, dateFrom, dateTo], 
        options.tenantId
      );

      const result = {
        detailedAttribution: attributionResult?.detailed_attribution || [],
        channelPerformance: attributionResult?.channel_performance || [],
        modelType: options.modelType || 'linear',
        period: {
          from: dateFrom,
          to: dateTo,
        },
        generatedAt: new Date().toISOString(),
      };

      // Cache for 30 minutes
      await set(cacheKey, result, this.cacheTTL.medium, options.tenantId);

      const duration = Date.now() - startTime;
      logger.info("Attribution analysis generated", {
        duration,
        tenantId: options.tenantId,
      });

      return result;
    } catch (error) {
      logger.error("Failed to get attribution analysis", error as Error);
      throw error;
    }
  }

  /**
   * Helper method to get date range based on time range string
   */
  private getDateRange(timeRange: string): { dateFrom: string; dateTo: string } {
    const now = new Date();
    const dateTo = now.toISOString();
    
    let dateFrom: Date;
    
    switch (timeRange) {
      case '7d':
        dateFrom = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        dateFrom = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        dateFrom = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        dateFrom = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        dateFrom = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    return {
      dateFrom: dateFrom.toISOString(),
      dateTo,
    };
  }

  /**
   * Helper method to get previous period for trend comparison
   */
  private getPreviousPeriod(dateFrom: string, dateTo: string): { from: string; to: string } {
    const fromDate = new Date(dateFrom);
    const toDate = new Date(dateTo);
    const periodLength = toDate.getTime() - fromDate.getTime();
    
    const prevTo = new Date(fromDate.getTime() - 1);
    const prevFrom = new Date(prevTo.getTime() - periodLength);
    
    return {
      from: prevFrom.toISOString(),
      to: prevTo.toISOString(),
    };
  }
}
