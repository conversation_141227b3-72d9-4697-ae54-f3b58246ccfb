import { Pool, PoolClient } from "postgres";
import { config } from "../config/config.ts";
import { logger } from "./logger.ts";

// Database pool instance
let pool: Pool | null = null;

// Performance monitoring
interface QueryStats {
  totalQueries: number;
  slowQueries: number;
  averageExecutionTime: number;
  lastSlowQuery?: {
    query: string;
    executionTime: number;
    timestamp: Date;
  };
}

const queryStats: QueryStats = {
  totalQueries: 0,
  slowQueries: 0,
  averageExecutionTime: 0,
};

const SLOW_QUERY_THRESHOLD = 1000; // 1 second

/**
 * Initialize database connection pool
 */
export async function initializeDatabase(): Promise<void> {
  try {
    pool = new Pool({
      hostname: config.database.host,
      port: config.database.port,
      database: config.database.database,
      user: config.database.username,
      password: config.database.password,
      connection: {
        attempts: 3,
      },
    }, config.database.maxConnections);

    // Test connection
    const client = await pool.connect();
    await client.queryObject("SELECT 1");
    client.release();

    logger.info("Database connection pool initialized successfully", {
      host: config.database.host,
      port: config.database.port,
      database: config.database.database,
      maxConnections: config.database.maxConnections,
    });
  } catch (error) {
    logger.error("Failed to initialize database connection pool", error as Error);
    throw error;
  }
}

/**
 * Get database pool instance
 */
export function getPool(): Pool {
  if (!pool) {
    throw new Error("Database pool not initialized. Call initializeDatabase() first.");
  }
  return pool;
}

/**
 * Execute a query with performance monitoring
 */
export async function query<T = unknown>(
  text: string,
  params: unknown[] = [],
  tenantId?: string,
): Promise<T[]> {
  if (!pool) {
    throw new Error("Database pool not initialized");
  }

  const startTime = Date.now();
  let client: PoolClient | null = null;

  try {
    client = await pool.connect();
    
    // Add tenant isolation if tenantId is provided
    let finalQuery = text;
    let finalParams = params;
    
    if (tenantId && !text.toLowerCase().includes("where")) {
      finalQuery += " WHERE tenant_id = $" + (params.length + 1);
      finalParams = [...params, tenantId];
    } else if (tenantId && text.toLowerCase().includes("where")) {
      finalQuery = text.replace(/where/i, `WHERE tenant_id = $${params.length + 1} AND`);
      finalParams = [...params, tenantId];
    }

    const result = await client.queryObject(finalQuery, finalParams);
    const executionTime = Date.now() - startTime;

    // Update performance stats
    updateQueryStats(finalQuery, executionTime);

    logger.debug("Database query executed", {
      query: finalQuery.substring(0, 100) + (finalQuery.length > 100 ? "..." : ""),
      executionTime,
      rowCount: result.rows.length,
      tenantId,
    });

    return result.rows as T[];
  } catch (error) {
    const executionTime = Date.now() - startTime;
    logger.error("Database query failed", {
      query: text.substring(0, 100) + (text.length > 100 ? "..." : ""),
      executionTime,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  } finally {
    if (client) {
      client.release();
    }
  }
}

/**
 * Execute a query and return a single row
 */
export async function queryOne<T = unknown>(
  text: string,
  params: unknown[] = [],
  tenantId?: string,
): Promise<T | null> {
  const results = await query<T>(text, params, tenantId);
  return results.length > 0 ? results[0] : null;
}

/**
 * Execute a transaction
 */
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>,
  tenantId?: string,
): Promise<T> {
  if (!pool) {
    throw new Error("Database pool not initialized");
  }

  const client = await pool.connect();
  
  try {
    await client.queryObject("BEGIN");
    
    // Set tenant context for the transaction if provided
    if (tenantId) {
      await client.queryObject("SET LOCAL app.current_tenant_id = $1", [tenantId]);
    }
    
    const result = await callback(client);
    await client.queryObject("COMMIT");
    
    logger.debug("Database transaction completed successfully", { tenantId });
    return result;
  } catch (error) {
    await client.queryObject("ROLLBACK");
    logger.error("Database transaction failed", { error: (error as Error).message, tenantId });
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Update query performance statistics
 */
function updateQueryStats(query: string, executionTime: number): void {
  queryStats.totalQueries++;
  
  // Update average execution time
  queryStats.averageExecutionTime = 
    (queryStats.averageExecutionTime * (queryStats.totalQueries - 1) + executionTime) / 
    queryStats.totalQueries;

  // Track slow queries
  if (executionTime > SLOW_QUERY_THRESHOLD) {
    queryStats.slowQueries++;
    queryStats.lastSlowQuery = {
      query: query.substring(0, 200),
      executionTime,
      timestamp: new Date(),
    };
    
    logger.warn("Slow query detected", {
      query: query.substring(0, 100),
      executionTime,
      threshold: SLOW_QUERY_THRESHOLD,
    });
  }
}

/**
 * Get query performance statistics
 */
export function getQueryPerformanceStats(): QueryStats {
  return { ...queryStats };
}

/**
 * Health check for database connection
 */
export async function healthCheck(): Promise<boolean> {
  try {
    if (!pool) {
      return false;
    }
    
    const client = await pool.connect();
    await client.queryObject("SELECT 1");
    client.release();
    return true;
  } catch (error) {
    logger.error("Database health check failed", error as Error);
    return false;
  }
}

/**
 * Close database connection pool
 */
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    logger.info("Database connection pool closed");
  }
}
