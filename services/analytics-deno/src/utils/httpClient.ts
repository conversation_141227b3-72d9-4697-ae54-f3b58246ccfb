import { logger } from "./logger.ts";

export interface HttpClientOptions {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
  retries?: number;
  retryDelay?: number;
}

export interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: unknown;
  params?: Record<string, string>;
  timeout?: number;
}

export interface HttpResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

export class HttpClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;
  private retries: number;
  private retryDelay: number;

  constructor(options: HttpClientOptions = {}) {
    this.baseURL = options.baseURL || "";
    this.timeout = options.timeout || 30000;
    this.defaultHeaders = options.headers || {};
    this.retries = options.retries || 3;
    this.retryDelay = options.retryDelay || 1000;
  }

  async request<T = unknown>(url: string, options: RequestOptions = {}): Promise<HttpResponse<T>> {
    const fullUrl = this.buildUrl(url, options.params);
    const requestOptions = this.buildRequestOptions(options);
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= this.retries; attempt++) {
      try {
        const startTime = Date.now();
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), options.timeout || this.timeout);
        
        const response = await fetch(fullUrl, {
          ...requestOptions,
          signal: controller.signal,
        });
        
        clearTimeout(timeoutId);
        const duration = Date.now() - startTime;
        
        const responseData = await this.parseResponse<T>(response);
        
        logger.debug("HTTP request completed", {
          method: options.method || "GET",
          url: fullUrl,
          status: response.status,
          duration,
          attempt: attempt + 1,
        });
        
        return {
          data: responseData,
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
        };
      } catch (error) {
        lastError = error as Error;
        
        logger.warn("HTTP request failed", {
          method: options.method || "GET",
          url: fullUrl,
          attempt: attempt + 1,
          maxAttempts: this.retries + 1,
          error: (error as Error).message,
        });
        
        if (attempt < this.retries && this.shouldRetry(error as Error)) {
          await this.delay(this.retryDelay * Math.pow(2, attempt));
          continue;
        }
        
        break;
      }
    }
    
    logger.error("HTTP request failed after all retries", {
      method: options.method || "GET",
      url: fullUrl,
      attempts: this.retries + 1,
      error: lastError?.message,
    });
    
    throw lastError;
  }

  get<T = unknown>(url: string, options: Omit<RequestOptions, "method"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "GET" });
  }

  post<T = unknown>(url: string, data?: unknown, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "POST", body: data });
  }

  put<T = unknown>(url: string, data?: unknown, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "PUT", body: data });
  }

  patch<T = unknown>(url: string, data?: unknown, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "PATCH", body: data });
  }

  delete<T = unknown>(url: string, options: Omit<RequestOptions, "method"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "DELETE" });
  }

  private buildUrl(url: string, params?: Record<string, string>): string {
    const fullUrl = url.startsWith("http") ? url : `${this.baseURL}${url}`;
    
    if (!params || Object.keys(params).length === 0) {
      return fullUrl;
    }
    
    const urlObj = new URL(fullUrl);
    Object.entries(params).forEach(([key, value]) => {
      urlObj.searchParams.set(key, value);
    });
    
    return urlObj.toString();
  }

  private buildRequestOptions(options: RequestOptions): RequestInit {
    const headers = {
      ...this.defaultHeaders,
      ...options.headers,
    };
    
    const requestOptions: RequestInit = {
      method: options.method || "GET",
      headers,
    };
    
    if (options.body !== undefined) {
      if (typeof options.body === "object" && options.body !== null) {
        requestOptions.body = JSON.stringify(options.body);
        headers["Content-Type"] = "application/json";
      } else {
        requestOptions.body = options.body as BodyInit;
      }
    }
    
    return requestOptions;
  }

  private async parseResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get("content-type") || "";
    
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      
      try {
        if (contentType.includes("application/json")) {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.error || errorMessage;
        } else {
          const errorText = await response.text();
          if (errorText) {
            errorMessage = errorText;
          }
        }
      } catch {
        // Ignore parsing errors for error responses
      }
      
      throw new Error(errorMessage);
    }
    
    if (contentType.includes("application/json")) {
      return await response.json();
    } else if (contentType.includes("text/")) {
      return await response.text() as unknown as T;
    } else {
      return await response.arrayBuffer() as unknown as T;
    }
  }

  private shouldRetry(error: Error): boolean {
    // Retry on network errors, timeouts, and 5xx status codes
    return (
      error.name === "AbortError" ||
      error.message.includes("fetch") ||
      error.message.includes("network") ||
      error.message.includes("timeout") ||
      error.message.includes("HTTP 5")
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Create default HTTP client instances for external services
export const linkTrackingClient = new HttpClient({
  baseURL: Deno.env.get("LINK_TRACKING_SERVICE_URL") || "http://link-tracking:8080",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

export const dashboardClient = new HttpClient({
  baseURL: Deno.env.get("DASHBOARD_SERVICE_URL") || "http://dashboard:3000",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

export const integrationClient = new HttpClient({
  baseURL: Deno.env.get("INTEGRATION_SERVICE_URL") || "http://integration:3001",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Export default instance
export const httpClient = new HttpClient();
