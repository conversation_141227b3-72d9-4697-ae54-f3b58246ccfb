import { logger, logWebSocketEvent } from "../utils/logger.ts";
import { config } from "../config/config.ts";

interface WebSocketClient {
  id: string;
  socket: WebSocket;
  userId?: string;
  tenantId?: string;
  subscriptions: Set<string>;
  lastHeartbeat: number;
  isAlive: boolean;
}

interface WebSocketMessage {
  type: string;
  data?: unknown;
  channels?: string[];
  timestamp?: string;
}

export class WebSocketServer {
  private clients: Map<string, WebSocketClient> = new Map();
  private heartbeatInterval?: number;
  private metricsInterval?: number;

  constructor() {
    if (config.websocket.enabled) {
      this.startHeartbeat();
      this.startMetricsCollection();
    }
  }

  /**
   * Handle new WebSocket connection
   */
  handleConnection(socket: WebSocket, clientId: string): void {
    if (!config.websocket.enabled) {
      socket.close(1000, "WebSocket disabled");
      return;
    }

    if (this.clients.size >= config.websocket.maxConnections) {
      socket.close(1008, "Maximum connections exceeded");
      return;
    }

    const client: WebSocketClient = {
      id: clientId,
      socket,
      subscriptions: new Set(),
      lastHeartbeat: Date.now(),
      isAlive: true,
    };

    this.clients.set(clientId, client);

    logWebSocketEvent("connection", clientId, {
      totalClients: this.clients.size,
    });

    // Set up event handlers
    socket.onmessage = (event) => {
      this.handleMessage(clientId, event.data);
    };

    socket.onclose = () => {
      this.handleDisconnection(clientId);
    };

    socket.onerror = (error) => {
      logger.error("WebSocket error", {
        clientId,
        error: error.toString(),
      });
    };

    // Send welcome message
    this.sendToClient(clientId, {
      type: "welcome",
      data: {
        clientId,
        serverTime: new Date().toISOString(),
        availableChannels: [
          "live_metrics",
          "click_events",
          "conversion_events",
          "alerts",
          "performance_updates",
        ],
      },
    });
  }

  /**
   * Handle incoming WebSocket message
   */
  private handleMessage(clientId: string, data: string): void {
    try {
      const message: WebSocketMessage = JSON.parse(data);
      const client = this.clients.get(clientId);

      if (!client) {
        return;
      }

      // Update heartbeat
      client.lastHeartbeat = Date.now();
      client.isAlive = true;

      logWebSocketEvent("message", clientId, {
        type: message.type,
      });

      switch (message.type) {
        case "authenticate":
          this.handleAuthentication(clientId, message);
          break;
        case "subscribe":
          this.handleSubscription(clientId, message);
          break;
        case "unsubscribe":
          this.handleUnsubscription(clientId, message);
          break;
        case "ping":
          this.handlePing(clientId);
          break;
        default:
          this.sendToClient(clientId, {
            type: "error",
            data: { message: "Unknown message type" },
          });
      }
    } catch (error) {
      logger.error("Failed to parse WebSocket message", {
        clientId,
        data: data.substring(0, 100),
        error: (error as Error).message,
      });

      this.sendToClient(clientId, {
        type: "error",
        data: { message: "Invalid message format" },
      });
    }
  }

  /**
   * Handle client authentication
   */
  private handleAuthentication(clientId: string, message: WebSocketMessage): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    // TODO(@analytics-team): Implement JWT token validation
    // For now, accept any authentication data
    const authData = message.data as { token?: string; userId?: string; tenantId?: string };
    
    if (authData.userId && authData.tenantId) {
      client.userId = authData.userId;
      client.tenantId = authData.tenantId;

      this.sendToClient(clientId, {
        type: "authenticated",
        data: {
          userId: client.userId,
          tenantId: client.tenantId,
        },
      });

      logWebSocketEvent("authenticated", clientId, {
        userId: client.userId,
        tenantId: client.tenantId,
      });
    } else {
      this.sendToClient(clientId, {
        type: "authentication_failed",
        data: { message: "Invalid credentials" },
      });
    }
  }

  /**
   * Handle channel subscription
   */
  private handleSubscription(clientId: string, message: WebSocketMessage): void {
    const client = this.clients.get(clientId);
    if (!client || !client.userId) {
      this.sendToClient(clientId, {
        type: "error",
        data: { message: "Must authenticate before subscribing" },
      });
      return;
    }

    const channels = message.channels || [];
    for (const channel of channels) {
      client.subscriptions.add(channel);
    }

    this.sendToClient(clientId, {
      type: "subscribed",
      data: { channels: Array.from(client.subscriptions) },
    });

    // Send initial data for subscribed channels
    if (client.subscriptions.has("live_metrics")) {
      this.sendLiveMetricsSnapshot(clientId);
    }

    logWebSocketEvent("subscribed", clientId, { channels });
  }

  /**
   * Handle channel unsubscription
   */
  private handleUnsubscription(clientId: string, message: WebSocketMessage): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    const channels = message.channels || [];
    for (const channel of channels) {
      client.subscriptions.delete(channel);
    }

    this.sendToClient(clientId, {
      type: "unsubscribed",
      data: { channels },
    });

    logWebSocketEvent("unsubscribed", clientId, { channels });
  }

  /**
   * Handle ping message
   */
  private handlePing(clientId: string): void {
    this.sendToClient(clientId, {
      type: "pong",
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Handle client disconnection
   */
  private handleDisconnection(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      logWebSocketEvent("disconnection", clientId, {
        userId: client.userId,
        tenantId: client.tenantId,
        subscriptions: Array.from(client.subscriptions),
      });
    }

    this.clients.delete(clientId);
  }

  /**
   * Send message to specific client
   */
  private sendToClient(clientId: string, message: WebSocketMessage): void {
    const client = this.clients.get(clientId);
    if (!client || client.socket.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      const messageWithTimestamp = {
        ...message,
        timestamp: message.timestamp || new Date().toISOString(),
      };

      client.socket.send(JSON.stringify(messageWithTimestamp));
    } catch (error) {
      logger.error("Failed to send WebSocket message", {
        clientId,
        error: (error as Error).message,
      });
      this.handleDisconnection(clientId);
    }
  }

  /**
   * Broadcast message to all clients matching filter
   */
  broadcast(message: WebSocketMessage, filter?: (client: WebSocketClient) => boolean): void {
    for (const [clientId, client] of this.clients) {
      if (!filter || filter(client)) {
        this.sendToClient(clientId, message);
      }
    }
  }

  /**
   * Send live metrics snapshot to client
   */
  private sendLiveMetricsSnapshot(clientId: string): void {
    const mockMetrics = this.generateMockMetrics();
    this.sendToClient(clientId, {
      type: "snapshot",
      data: {
        live_metrics: mockMetrics.metrics,
        trends: mockMetrics.trends,
      },
    });
  }

  /**
   * Generate mock metrics for demonstration
   */
  private generateMockMetrics() {
    return {
      metrics: {
        clicks_per_minute: Math.floor(Math.random() * 50) + 10,
        conversions_per_hour: Math.floor(Math.random() * 20) + 5,
        revenue_per_hour: Math.floor(Math.random() * 1000) + 200,
        active_links: Math.floor(Math.random() * 100) + 50,
      },
      trends: {
        clicks: Math.random() > 0.5 ? "up" : "down",
        conversions: Math.random() > 0.5 ? "up" : "down",
        revenue: Math.random() > 0.5 ? "up" : "down",
      },
    };
  }

  /**
   * Start heartbeat monitoring
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = Date.now();
      const timeout = config.websocket.heartbeatInterval * 2;

      for (const [clientId, client] of this.clients) {
        if (now - client.lastHeartbeat > timeout) {
          logger.warn("Client heartbeat timeout", { clientId });
          client.socket.close(1000, "Heartbeat timeout");
          this.handleDisconnection(clientId);
        }
      }
    }, config.websocket.heartbeatInterval);
  }

  /**
   * Start metrics collection and broadcasting
   */
  private startMetricsCollection(): void {
    this.metricsInterval = setInterval(() => {
      const metrics = this.generateMockMetrics();
      this.broadcast({
        type: "live_update",
        data: {
          metrics: metrics.metrics,
          trends: metrics.trends,
        },
      }, (client) => client.subscriptions.has("live_metrics"));
    }, config.analytics.metricsInterval);
  }

  /**
   * Get connected clients count
   */
  getConnectedClientsCount(): number {
    return this.clients.size;
  }

  /**
   * Cleanup and close all connections
   */
  close(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    for (const [clientId, client] of this.clients) {
      client.socket.close(1000, "Server shutdown");
    }

    this.clients.clear();
    logger.info("WebSocket server closed");
  }
}
