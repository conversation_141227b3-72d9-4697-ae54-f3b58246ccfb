import { Pool } from "postgres";

const pool = new Pool({
  hostname: "localhost",
  port: 5432,
  database: "ecommerce_analytics",
  user: "postgres",
  password: "password",
}, 5);

try {
  console.log("Testing database connection...");
  const client = await pool.connect();
  console.log("Connected to database");
  
  const result = await client.queryObject("SELECT 1 as test");
  console.log("Query result:", result);
  
  client.release();
  console.log("Connection released");
  
  await pool.end();
  console.log("Pool closed");
} catch (error) {
  console.error("Database connection error:", error);
}
