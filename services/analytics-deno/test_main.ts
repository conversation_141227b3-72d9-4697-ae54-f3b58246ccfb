import { Application } from "@oak/oak";

console.log("Starting test main...");

// Create Oak application
const app = new Application();

console.log("Oak application created");

// Simple route
app.use((ctx) => {
  ctx.response.body = { message: "Hello World" };
});

console.log("Route added");

// Start server
const port = 3002;
console.log(`Starting server on port ${port}...`);

await app.listen({ 
  hostname: "0.0.0.0", 
  port: port 
});
