import { connect } from "redis";

try {
  console.log("Testing Redis connection...");
  const redis = await connect({
    hostname: "localhost",
    port: 6379,
  });
  
  console.log("Connected to Redis");
  
  await redis.set("test", "hello");
  const value = await redis.get("test");
  console.log("Test value:", value);
  
  redis.close();
  console.log("Redis connection closed");
} catch (error) {
  console.error("Redis connection error:", error);
}
