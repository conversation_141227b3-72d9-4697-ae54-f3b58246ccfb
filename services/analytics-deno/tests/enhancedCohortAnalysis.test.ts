// Enhanced Cohort Analysis Tests - Phase 2 Implementation
// Comprehensive test suite for advanced cohort analysis functionality

import { assertEquals, assertExists, assert } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { CohortAnalysisService, CohortAnalysisOptions } from "../src/services/cohortAnalysisService.ts";

// Test configuration
const TEST_TENANT_ID = "00000000-0000-0000-0000-000000000000";
const TEST_DATE_FROM = "2023-01-01T00:00:00.000Z";
const TEST_DATE_TO = "2023-12-31T23:59:59.999Z";

// Utility function to get error message
function getErrorMessage(error: unknown): string {
  return error instanceof Error ? error.message : 'Unknown error';
}

// Utility function to check if error is database related
function isDatabaseError(error: unknown): boolean {
  const message = getErrorMessage(error);
  return message.includes("database") || message.includes("connection");
}

// Mock data for testing
const mockCohortOptions: CohortAnalysisOptions = {
  tenantId: TEST_TENANT_ID,
  dateFrom: TEST_DATE_FROM,
  dateTo: TEST_DATE_TO,
  cohortType: 'acquisition',
  granularity: 'monthly',
  includeProjections: true,
};

Deno.test("Enhanced Cohort Analysis Service", async (t) => {
  const cohortService = new CohortAnalysisService();

  await t.step("should initialize cohort analysis service", () => {
    assertExists(cohortService);
  });

  await t.step("should handle cohort analysis with valid parameters", async () => {
    try {
      const startTime = performance.now();
      const result = await cohortService.analyzeCohorts(mockCohortOptions);
      const executionTime = performance.now() - startTime;

      // Verify response structure
      assertExists(result);
      assertExists(result.segments);
      assertExists(result.retentionCurves);
      assertExists(result.overview);
      assertExists(result.predictiveInsights);
      assertExists(result.generatedAt);

      // Verify performance target (< 500ms)
      console.log(`Cohort analysis execution time: ${executionTime.toFixed(2)}ms`);
      assert(executionTime < 500, `Execution time ${executionTime.toFixed(2)}ms exceeds 500ms target`);

      // Verify data types
      assert(Array.isArray(result.segments), "Segments should be an array");
      assert(typeof result.retentionCurves === 'object', "Retention curves should be an object");
      assert(typeof result.overview === 'object', "Overview should be an object");
      assert(typeof result.predictiveInsights === 'object', "Predictive insights should be an object");

      console.log("✅ Cohort analysis completed successfully");
      console.log(`   - Segments found: ${result.segments.length}`);
      console.log(`   - Total cohorts: ${result.overview.totalCohorts}`);
      console.log(`   - Total customers: ${result.overview.totalCustomers}`);
      console.log(`   - Average retention rate: ${result.overview.avgRetentionRate}%`);
    } catch (error) {
      console.error("❌ Cohort analysis failed:", getErrorMessage(error));
      // Don't fail the test if it's a database connection issue in CI
      if (isDatabaseError(error)) {
        console.warn("⚠️  Database connection issue - skipping test");
        return;
      }
      throw error;
    }
  });

  await t.step("should handle different cohort types", async () => {
    const cohortTypes: Array<'acquisition' | 'behavioral' | 'value'> = ['acquisition', 'behavioral', 'value'];
    
    for (const cohortType of cohortTypes) {
      try {
        const options = { ...mockCohortOptions, cohortType };
        const result = await cohortService.analyzeCohorts(options);
        
        assertExists(result);
        console.log(`✅ ${cohortType} cohort analysis completed`);
      } catch (error) {
        if (isDatabaseError(error)) {
          console.warn(`⚠️  Database connection issue for ${cohortType} - skipping`);
          continue;
        }
        throw error;
      }
    }
  });

  await t.step("should handle different granularities", async () => {
    const granularities: Array<'daily' | 'weekly' | 'monthly'> = ['daily', 'weekly', 'monthly'];
    
    for (const granularity of granularities) {
      try {
        const options = { ...mockCohortOptions, granularity };
        const result = await cohortService.analyzeCohorts(options);
        
        assertExists(result);
        console.log(`✅ ${granularity} granularity analysis completed`);
      } catch (error) {
        if (isDatabaseError(error)) {
          console.warn(`⚠️  Database connection issue for ${granularity} - skipping`);
          continue;
        }
        throw error;
      }
    }
  });

  await t.step("should validate cohort analysis result structure", async () => {
    try {
      const result = await cohortService.analyzeCohorts(mockCohortOptions);

      // Validate segments structure
      if (result.segments.length > 0) {
        const segment = result.segments[0];
        assertExists(segment.cohortMonth);
        assertExists(segment.segmentName);
        assert(typeof segment.customerCount === 'number');
        assert(typeof segment.totalRevenue === 'number');
        assertExists(segment.retentionRates);
        assert(typeof segment.avgOrderValue === 'number');
        assert(typeof segment.churnProbability === 'number');
      }

      // Validate overview structure
      assert(typeof result.overview.totalCohorts === 'number');
      assert(typeof result.overview.avgRetentionRate === 'number');
      assert(typeof result.overview.totalCustomers === 'number');
      assert(typeof result.overview.totalRevenue === 'number');

      // Validate predictive insights structure
      assert(typeof result.predictiveInsights.expectedChurn === 'number');
      assert(typeof result.predictiveInsights.revenueProjection === 'number');
      assert(['improving', 'declining', 'stable'].includes(result.predictiveInsights.retentionTrend));

      console.log("✅ Cohort analysis result structure validation passed");
    } catch (error) {
      if (isDatabaseError(error)) {
        console.warn("⚠️  Database connection issue - skipping validation");
        return;
      }
      throw error;
    }
  });
});

Deno.test("Enhanced Cohort Analysis Performance", async (t) => {
  const cohortService = new CohortAnalysisService();

  await t.step("should meet performance targets", async () => {
    const performanceTests = [
      { name: "12-month analysis", months: 12, target: 500 },
      { name: "6-month analysis", months: 6, target: 300 },
      { name: "3-month analysis", months: 3, target: 200 },
    ];

    for (const test of performanceTests) {
      try {
        const dateFrom = new Date();
        dateFrom.setMonth(dateFrom.getMonth() - test.months);
        
        const options: CohortAnalysisOptions = {
          ...mockCohortOptions,
          dateFrom: dateFrom.toISOString(),
          dateTo: new Date().toISOString(),
        };

        const startTime = performance.now();
        await cohortService.analyzeCohorts(options);
        const executionTime = performance.now() - startTime;

        console.log(`${test.name}: ${executionTime.toFixed(2)}ms (target: <${test.target}ms)`);
        
        // Log performance but don't fail test in CI environment
        if (executionTime > test.target) {
          console.warn(`⚠️  ${test.name} exceeded target: ${executionTime.toFixed(2)}ms > ${test.target}ms`);
        } else {
          console.log(`✅ ${test.name} met performance target`);
        }
      } catch (error) {
        if (isDatabaseError(error)) {
          console.warn(`⚠️  Database connection issue for ${test.name} - skipping`);
          continue;
        }
        throw error;
      }
    }
  });

  await t.step("should handle concurrent requests efficiently", async () => {
    const concurrentRequests = 5;
    const promises: Promise<any>[] = [];

    try {
      for (let i = 0; i < concurrentRequests; i++) {
        const options = {
          ...mockCohortOptions,
          cohortType: ['acquisition', 'behavioral', 'value'][i % 3] as any,
        };
        promises.push(cohortService.analyzeCohorts(options));
      }

      const startTime = performance.now();
      const results = await Promise.all(promises);
      const totalTime = performance.now() - startTime;

      assertEquals(results.length, concurrentRequests);
      console.log(`✅ ${concurrentRequests} concurrent requests completed in ${totalTime.toFixed(2)}ms`);
      console.log(`   Average time per request: ${(totalTime / concurrentRequests).toFixed(2)}ms`);
    } catch (error) {
      if (isDatabaseError(error)) {
        console.warn("⚠️  Database connection issue for concurrent test - skipping");
        return;
      }
      throw error;
    }
  });
});

Deno.test("Enhanced Cohort Analysis Error Handling", async (t) => {
  const cohortService = new CohortAnalysisService();

  await t.step("should handle invalid tenant ID", async () => {
    const invalidOptions = {
      ...mockCohortOptions,
      tenantId: "invalid-uuid",
    };

    try {
      await cohortService.analyzeCohorts(invalidOptions);
      // If we reach here without error, it might be due to database connection issues
      console.warn("⚠️  Expected error for invalid tenant ID but none occurred");
    } catch (error) {
      // Expected error
      assertExists(error);
      console.log("✅ Invalid tenant ID handled correctly");
    }
  });

  await t.step("should handle invalid date range", async () => {
    const invalidOptions = {
      ...mockCohortOptions,
      dateFrom: "invalid-date",
      dateTo: "invalid-date",
    };

    try {
      await cohortService.analyzeCohorts(invalidOptions);
      console.warn("⚠️  Expected error for invalid date range but none occurred");
    } catch (error) {
      // Expected error
      assertExists(error);
      console.log("✅ Invalid date range handled correctly");
    }
  });

  await t.step("should handle empty date range", async () => {
    const futureDate = new Date();
    futureDate.setFullYear(futureDate.getFullYear() + 1);
    
    const emptyRangeOptions = {
      ...mockCohortOptions,
      dateFrom: futureDate.toISOString(),
      dateTo: futureDate.toISOString(),
    };

    try {
      const result = await cohortService.analyzeCohorts(emptyRangeOptions);
      
      // Should return empty results, not error
      assertExists(result);
      assertEquals(result.segments.length, 0);
      assertEquals(result.overview.totalCohorts, 0);
      console.log("✅ Empty date range handled correctly");
    } catch (error) {
      if (isDatabaseError(error)) {
        console.warn("⚠️  Database connection issue for empty range test - skipping");
        return;
      }
      throw error;
    }
  });
});

// Performance benchmark test
Deno.test("Enhanced Cohort Analysis Benchmark", async () => {
  const cohortService = new CohortAnalysisService();
  const iterations = 3;
  const times: number[] = [];

  console.log(`\n🚀 Running cohort analysis benchmark (${iterations} iterations)...`);

  for (let i = 0; i < iterations; i++) {
    try {
      const startTime = performance.now();
      await cohortService.analyzeCohorts(mockCohortOptions);
      const executionTime = performance.now() - startTime;
      times.push(executionTime);
      
      console.log(`   Iteration ${i + 1}: ${executionTime.toFixed(2)}ms`);
    } catch (error) {
      if (isDatabaseError(error)) {
        console.warn(`⚠️  Database connection issue in iteration ${i + 1} - skipping`);
        continue;
      }
      throw error;
    }
  }

  if (times.length > 0) {
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    console.log(`\n📊 Benchmark Results:`);
    console.log(`   Average: ${avgTime.toFixed(2)}ms`);
    console.log(`   Minimum: ${minTime.toFixed(2)}ms`);
    console.log(`   Maximum: ${maxTime.toFixed(2)}ms`);
    console.log(`   Target:  < 500ms`);
    console.log(`   Status:  ${avgTime < 500 ? '✅ OPTIMAL' : '⚠️  NEEDS OPTIMIZATION'}`);
  } else {
    console.warn("⚠️  No successful benchmark iterations due to database connection issues");
  }
});
