-- Dashboard enhancements: alerts, widgets, and configurations

-- Real-time alert rules
CREATE TABLE IF NOT EXISTS alert_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  rule_name VARCHAR(255) NOT NULL,
  rule_type VARCHAR(50) NOT NULL, -- 'threshold', 'anomaly', 'trend'
  metric_name VARCHAR(100) NOT NULL, -- 'conversion_rate', 'revenue', 'clicks', etc.
  condition_operator VARCHAR(20) NOT NULL, -- 'gt', 'lt', 'eq', 'change_pct'
  threshold_value DECIMAL(12,4),
  comparison_period VARCHAR(50), -- 'previous_hour', 'previous_day', 'previous_week'
  notification_channels JSONB, -- ['email', 'webhook', 'dashboard']
  is_active BOOLEAN DEFAULT true,
  created_by UUID,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Alert instances (fired alerts)
CREATE TABLE IF NOT EXISTS alert_instances (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  alert_rule_id UUID REFERENCES alert_rules(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  triggered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  metric_value DECIMAL(12,4),
  threshold_value DECIMAL(12,4),
  severity VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
  status VARCHAR(20) DEFAULT 'active', -- 'active', 'acknowledged', 'resolved'
  message TEXT,
  context_data JSONB,
  acknowledged_by UUID,
  acknowledged_at TIMESTAMP,
  resolved_at TIMESTAMP
);

-- Dashboard widget configurations
CREATE TABLE IF NOT EXISTS dashboard_widgets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  user_id UUID,
  widget_type VARCHAR(50) NOT NULL, -- 'metric_card', 'chart', 'table', 'funnel'
  widget_title VARCHAR(255),
  widget_config JSONB NOT NULL, -- Configuration for the widget
  position_x INTEGER DEFAULT 0,
  position_y INTEGER DEFAULT 0,
  width INTEGER DEFAULT 4,
  height INTEGER DEFAULT 3,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Dashboard layouts (saved configurations)
CREATE TABLE IF NOT EXISTS dashboard_layouts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  user_id UUID,
  layout_name VARCHAR(255) NOT NULL,
  layout_config JSONB NOT NULL, -- Complete dashboard configuration
  is_default BOOLEAN DEFAULT false,
  is_shared BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Comparative analysis cache
CREATE TABLE IF NOT EXISTS comparative_analysis (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  analysis_type VARCHAR(50) NOT NULL, -- 'mom', 'yoy', 'wow', 'custom'
  metric_name VARCHAR(100) NOT NULL,
  current_period_start DATE,
  current_period_end DATE,
  comparison_period_start DATE,
  comparison_period_end DATE,
  current_value DECIMAL(12,4),
  comparison_value DECIMAL(12,4),
  change_value DECIMAL(12,4),
  change_percentage DECIMAL(8,4),
  calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP
);

-- Drill-down session tracking
CREATE TABLE IF NOT EXISTS drill_down_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  user_id UUID,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  initial_metric VARCHAR(100),
  drill_path JSONB, -- Array of drill-down steps
  filters_applied JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours')
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_alert_rules_tenant_active ON alert_rules(tenant_id, is_active);
CREATE INDEX IF NOT EXISTS idx_alert_rules_metric ON alert_rules(metric_name, rule_type);

CREATE INDEX IF NOT EXISTS idx_alert_instances_tenant_status ON alert_instances(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_alert_instances_triggered ON alert_instances(triggered_at DESC);
CREATE INDEX IF NOT EXISTS idx_alert_instances_rule ON alert_instances(alert_rule_id);

CREATE INDEX IF NOT EXISTS idx_dashboard_widgets_tenant_user ON dashboard_widgets(tenant_id, user_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_widgets_active ON dashboard_widgets(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_dashboard_layouts_tenant_user ON dashboard_layouts(tenant_id, user_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_layouts_default ON dashboard_layouts(tenant_id, is_default) WHERE is_default = true;

CREATE INDEX IF NOT EXISTS idx_comparative_analysis_tenant_metric ON comparative_analysis(tenant_id, metric_name);
CREATE INDEX IF NOT EXISTS idx_comparative_analysis_expires ON comparative_analysis(expires_at);

CREATE INDEX IF NOT EXISTS idx_drill_down_sessions_token ON drill_down_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_drill_down_sessions_expires ON drill_down_sessions(expires_at);

-- Triggers for updated_at columns
CREATE TRIGGER update_alert_rules_updated_at BEFORE UPDATE ON alert_rules 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_dashboard_widgets_updated_at BEFORE UPDATE ON dashboard_widgets 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_dashboard_layouts_updated_at BEFORE UPDATE ON dashboard_layouts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Sample alert rules for common scenarios
INSERT INTO alert_rules (tenant_id, rule_name, rule_type, metric_name, condition_operator, threshold_value, notification_channels) VALUES
('00000000-0000-0000-0000-000000000001', 'Low Conversion Rate Alert', 'threshold', 'conversion_rate', 'lt', 2.0, '["dashboard", "email"]'),
('00000000-0000-0000-0000-000000000001', 'High Revenue Spike', 'threshold', 'revenue', 'gt', 10000.0, '["dashboard"]'),
('00000000-0000-0000-0000-000000000001', 'Traffic Drop Alert', 'threshold', 'clicks', 'change_pct', -20.0, '["dashboard", "email"]')
ON CONFLICT DO NOTHING;

-- Sample dashboard widgets
INSERT INTO dashboard_widgets (tenant_id, widget_type, widget_title, widget_config, position_x, position_y, width, height) VALUES
('00000000-0000-0000-0000-000000000001', 'metric_card', 'Total Revenue', '{"metric": "total_revenue", "period": "today", "format": "currency"}', 0, 0, 3, 2),
('00000000-0000-0000-0000-000000000001', 'metric_card', 'Conversion Rate', '{"metric": "conversion_rate", "period": "today", "format": "percentage"}', 3, 0, 3, 2),
('00000000-0000-0000-0000-000000000001', 'chart', 'Revenue Trend', '{"metric": "revenue", "chart_type": "line", "period": "last_30_days"}', 0, 2, 6, 4),
('00000000-0000-0000-0000-000000000001', 'table', 'Top Performing Links', '{"metric": "link_performance", "limit": 10, "sort_by": "revenue"}', 6, 0, 6, 6)
ON CONFLICT DO NOTHING;
