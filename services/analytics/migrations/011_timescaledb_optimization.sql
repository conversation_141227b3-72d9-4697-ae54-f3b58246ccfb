-- TimescaleDB optimization and partitioning setup

-- Enable TimescaleDB extension if not already enabled
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Convert existing tables to hypertables for time-series optimization
-- Note: This should be done carefully in production with proper data migration

-- Convert clicks table to hypertable (partitioned by clicked_at)
SELECT create_hypertable(
  'clicks', 
  'clicked_at',
  chunk_time_interval => INTERVAL '1 day',
  if_not_exists => TRUE
);

-- Convert orders table to hypertable (partitioned by created_at)
SELECT create_hypertable(
  'orders', 
  'created_at',
  chunk_time_interval => INTERVAL '1 day',
  if_not_exists => TRUE
);

-- Convert attributions table to hypertable (partitioned by created_at)
SELECT create_hypertable(
  'attributions', 
  'created_at',
  chunk_time_interval => INTERVAL '1 day',
  if_not_exists => TRUE
);

-- Convert alert_instances table to hypertable (partitioned by triggered_at)
SELECT create_hypertable(
  'alert_instances', 
  'triggered_at',
  chunk_time_interval => INTERVAL '1 day',
  if_not_exists => TRUE
);

-- Create materialized views for common aggregations
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_analytics_summary AS
SELECT 
  l.tenant_id,
  l.id as link_id,
  DATE(c.clicked_at) as date,
  COUNT(DISTINCT c.id) as total_clicks,
  COUNT(DISTINCT c.ip_address) as unique_visitors,
  COUNT(DISTINCT a.id) as total_conversions,
  COALESCE(SUM(o.total_amount), 0) as total_revenue,
  COUNT(DISTINCT CASE WHEN c.country IS NOT NULL THEN c.country END) as countries_count,
  COUNT(DISTINCT CASE WHEN c.device_type IS NOT NULL THEN c.device_type END) as device_types_count
FROM clicks c
JOIN links l ON c.link_id = l.id
LEFT JOIN attributions a ON c.id = a.click_id
LEFT JOIN orders o ON a.order_id = o.id
WHERE c.clicked_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY l.tenant_id, l.id, DATE(c.clicked_at);

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_daily_analytics_summary_unique 
ON daily_analytics_summary (tenant_id, link_id, date);

-- Create hourly analytics summary for real-time dashboards
CREATE MATERIALIZED VIEW IF NOT EXISTS hourly_analytics_summary AS
SELECT 
  l.tenant_id,
  DATE_TRUNC('hour', c.clicked_at) as hour,
  COUNT(DISTINCT c.id) as total_clicks,
  COUNT(DISTINCT c.ip_address) as unique_visitors,
  COUNT(DISTINCT a.id) as total_conversions,
  COALESCE(SUM(o.total_amount), 0) as total_revenue,
  AVG(CASE WHEN a.id IS NOT NULL THEN 1.0 ELSE 0.0 END) * 100 as conversion_rate
FROM clicks c
JOIN links l ON c.link_id = l.id
LEFT JOIN attributions a ON c.id = a.click_id
LEFT JOIN orders o ON a.order_id = o.id
WHERE c.clicked_at >= CURRENT_TIMESTAMP - INTERVAL '7 days'
GROUP BY l.tenant_id, DATE_TRUNC('hour', c.clicked_at);

-- Create unique index on hourly summary
CREATE UNIQUE INDEX IF NOT EXISTS idx_hourly_analytics_summary_unique 
ON hourly_analytics_summary (tenant_id, hour);

-- Create customer lifetime value summary
CREATE MATERIALIZED VIEW IF NOT EXISTS customer_clv_summary AS
SELECT 
  i.tenant_id,
  o.customer_id,
  DATE_TRUNC('month', MIN(o.created_at)) as acquisition_month,
  COUNT(*) as total_orders,
  SUM(o.total_amount) as lifetime_value,
  AVG(o.total_amount) as avg_order_value,
  MAX(o.created_at) - MIN(o.created_at) as customer_lifespan_days,
  EXTRACT(days FROM MAX(o.created_at) - MIN(o.created_at)) / 30.0 as lifespan_months
FROM orders o
JOIN integrations i ON o.integration_id = i.id
WHERE o.customer_id IS NOT NULL
GROUP BY i.tenant_id, o.customer_id;

-- Create unique index on CLV summary
CREATE UNIQUE INDEX IF NOT EXISTS idx_customer_clv_summary_unique 
ON customer_clv_summary (tenant_id, customer_id);

-- Create retention analysis materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS retention_analysis AS
WITH customer_cohorts AS (
  SELECT 
    i.tenant_id,
    o.customer_id,
    DATE_TRUNC('month', MIN(o.created_at)) as cohort_month
  FROM orders o
  JOIN integrations i ON o.integration_id = i.id
  WHERE o.customer_id IS NOT NULL
  GROUP BY i.tenant_id, o.customer_id
),
monthly_activity AS (
  SELECT 
    i.tenant_id,
    o.customer_id,
    DATE_TRUNC('month', o.created_at) as activity_month
  FROM orders o
  JOIN integrations i ON o.integration_id = i.id
  WHERE o.customer_id IS NOT NULL
  GROUP BY i.tenant_id, o.customer_id, DATE_TRUNC('month', o.created_at)
)
SELECT 
  cc.tenant_id,
  cc.cohort_month,
  ma.activity_month,
  EXTRACT(month FROM AGE(ma.activity_month, cc.cohort_month)) as period_number,
  COUNT(DISTINCT cc.customer_id) as customers_in_cohort,
  COUNT(DISTINCT ma.customer_id) as retained_customers
FROM customer_cohorts cc
LEFT JOIN monthly_activity ma ON cc.tenant_id = ma.tenant_id 
  AND cc.customer_id = ma.customer_id
  AND ma.activity_month >= cc.cohort_month
GROUP BY cc.tenant_id, cc.cohort_month, ma.activity_month;

-- Create indexes on retention analysis
CREATE INDEX IF NOT EXISTS idx_retention_analysis_tenant_cohort 
ON retention_analysis (tenant_id, cohort_month);

-- Set up automatic refresh policies for materialized views
-- Refresh daily summary every hour
SELECT add_continuous_aggregate_policy(
  'daily_analytics_summary',
  start_offset => INTERVAL '2 days',
  end_offset => INTERVAL '1 hour',
  schedule_interval => INTERVAL '1 hour',
  if_not_exists => TRUE
);

-- Refresh hourly summary every 15 minutes
SELECT add_continuous_aggregate_policy(
  'hourly_analytics_summary',
  start_offset => INTERVAL '2 hours',
  end_offset => INTERVAL '15 minutes',
  schedule_interval => INTERVAL '15 minutes',
  if_not_exists => TRUE
);

-- Set up data retention policies
-- Keep raw click data for 2 years, then compress
SELECT add_retention_policy(
  'clicks',
  INTERVAL '2 years',
  if_not_exists => TRUE
);

-- Keep raw order data for 5 years
SELECT add_retention_policy(
  'orders',
  INTERVAL '5 years',
  if_not_exists => TRUE
);

-- Keep attribution data for 2 years
SELECT add_retention_policy(
  'attributions',
  INTERVAL '2 years',
  if_not_exists => TRUE
);

-- Keep alert instances for 1 year
SELECT add_retention_policy(
  'alert_instances',
  INTERVAL '1 year',
  if_not_exists => TRUE
);

-- Set up compression policies for better storage efficiency
-- Compress click data older than 7 days
SELECT add_compression_policy(
  'clicks',
  INTERVAL '7 days',
  if_not_exists => TRUE
);

-- Compress order data older than 30 days
SELECT add_compression_policy(
  'orders',
  INTERVAL '30 days',
  if_not_exists => TRUE
);

-- Compress attribution data older than 7 days
SELECT add_compression_policy(
  'attributions',
  INTERVAL '7 days',
  if_not_exists => TRUE
);

-- Create additional performance indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_tenant_hour_device 
ON clicks (tenant_id, DATE_TRUNC('hour', clicked_at), device_type) 
WHERE clicked_at >= CURRENT_DATE - INTERVAL '30 days';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_tenant_month_customer 
ON orders (tenant_id, DATE_TRUNC('month', created_at), customer_id) 
WHERE customer_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attributions_tenant_day 
ON attributions (tenant_id, DATE_TRUNC('day', created_at));

-- Create function to refresh all materialized views
CREATE OR REPLACE FUNCTION refresh_analytics_views()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY daily_analytics_summary;
  REFRESH MATERIALIZED VIEW CONCURRENTLY hourly_analytics_summary;
  REFRESH MATERIALIZED VIEW CONCURRENTLY customer_clv_summary;
  REFRESH MATERIALIZED VIEW CONCURRENTLY retention_analysis;
  
  -- Log the refresh
  INSERT INTO system_logs (log_level, message, created_at)
  VALUES ('INFO', 'Analytics materialized views refreshed', CURRENT_TIMESTAMP);
END;
$$ LANGUAGE plpgsql;

-- Create system logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS system_logs (
  id SERIAL PRIMARY KEY,
  log_level VARCHAR(20) NOT NULL,
  message TEXT NOT NULL,
  context JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index on system logs
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs (log_level);

-- Set up automated cleanup for old system logs
CREATE OR REPLACE FUNCTION cleanup_old_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM system_logs 
  WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
  
  -- Log the cleanup
  INSERT INTO system_logs (log_level, message)
  VALUES ('INFO', 'Old system logs cleaned up');
END;
$$ LANGUAGE plpgsql;

-- Create function to get optimized analytics data
CREATE OR REPLACE FUNCTION get_optimized_analytics(
  p_tenant_id UUID,
  p_date_from DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
  p_date_to DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
  date DATE,
  total_clicks BIGINT,
  unique_visitors BIGINT,
  total_conversions BIGINT,
  total_revenue NUMERIC,
  conversion_rate NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    das.date,
    SUM(das.total_clicks)::BIGINT as total_clicks,
    SUM(das.unique_visitors)::BIGINT as unique_visitors,
    SUM(das.total_conversions)::BIGINT as total_conversions,
    SUM(das.total_revenue) as total_revenue,
    CASE 
      WHEN SUM(das.total_clicks) > 0 
      THEN (SUM(das.total_conversions)::NUMERIC / SUM(das.total_clicks)::NUMERIC) * 100
      ELSE 0 
    END as conversion_rate
  FROM daily_analytics_summary das
  WHERE das.tenant_id = p_tenant_id
    AND das.date >= p_date_from
    AND das.date <= p_date_to
  GROUP BY das.date
  ORDER BY das.date;
END;
$$ LANGUAGE plpgsql;
