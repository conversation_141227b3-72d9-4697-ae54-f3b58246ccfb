-- Scheduled reports and user experience enhancements

-- Scheduled reports table
CREATE TABLE IF NOT EXISTS scheduled_reports (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  user_id UUID,
  report_type VARCHAR(50) NOT NULL, -- 'summary', 'clicks', 'conversions', etc.
  format VARCHAR(10) NOT NULL, -- 'csv', 'pdf'
  schedule_frequency VARCHAR(20) NOT NULL, -- 'daily', 'weekly', 'monthly'
  schedule_time TIME DEFAULT '09:00:00', -- Time of day to send
  schedule_day INTEGER, -- Day of week (1-7) for weekly, day of month (1-31) for monthly
  recipients JSONB, -- Array of email addresses
  options JSONB, -- Report configuration options
  last_sent TIMESTAMP,
  next_scheduled TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Saved dashboard configurations
CREATE TABLE IF NOT EXISTS saved_dashboard_configs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  user_id UUID,
  config_name VARCHAR(255) NOT NULL,
  config_data JSONB NOT NULL, -- Complete dashboard configuration
  is_default BOOLEAN DEFAULT false,
  is_shared BOOLEAN DEFAULT false,
  shared_with JSONB, -- Array of user IDs or 'all'
  tags JSONB, -- Array of tags for categorization
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Data visualization preferences
CREATE TABLE IF NOT EXISTS visualization_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  user_id UUID,
  chart_type VARCHAR(50) NOT NULL, -- 'line', 'bar', 'pie', 'heatmap', etc.
  metric_name VARCHAR(100) NOT NULL,
  preferences JSONB NOT NULL, -- Chart-specific preferences
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Report generation history
CREATE TABLE IF NOT EXISTS report_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  user_id UUID,
  report_type VARCHAR(50) NOT NULL,
  format VARCHAR(10) NOT NULL,
  file_path VARCHAR(500),
  file_size BIGINT,
  generation_time_ms INTEGER,
  parameters JSONB,
  status VARCHAR(20) DEFAULT 'completed', -- 'pending', 'completed', 'failed'
  error_message TEXT,
  download_count INTEGER DEFAULT 0,
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User activity tracking for analytics
CREATE TABLE IF NOT EXISTS user_activity_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  user_id UUID,
  activity_type VARCHAR(50) NOT NULL, -- 'dashboard_view', 'report_export', 'widget_create', etc.
  activity_data JSONB,
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notification preferences
CREATE TABLE IF NOT EXISTS notification_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL,
  user_id UUID NOT NULL,
  notification_type VARCHAR(50) NOT NULL, -- 'alert', 'report', 'system'
  channel VARCHAR(20) NOT NULL, -- 'email', 'dashboard', 'webhook'
  is_enabled BOOLEAN DEFAULT true,
  preferences JSONB, -- Channel-specific preferences
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_scheduled_reports_tenant_active ON scheduled_reports(tenant_id, is_active);
CREATE INDEX IF NOT EXISTS idx_scheduled_reports_next_scheduled ON scheduled_reports(next_scheduled) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_saved_dashboard_configs_tenant_user ON saved_dashboard_configs(tenant_id, user_id);
CREATE INDEX IF NOT EXISTS idx_saved_dashboard_configs_shared ON saved_dashboard_configs(tenant_id, is_shared) WHERE is_shared = true;

CREATE INDEX IF NOT EXISTS idx_visualization_preferences_tenant_user ON visualization_preferences(tenant_id, user_id);
CREATE INDEX IF NOT EXISTS idx_visualization_preferences_metric ON visualization_preferences(metric_name, chart_type);

CREATE INDEX IF NOT EXISTS idx_report_history_tenant_user ON report_history(tenant_id, user_id);
CREATE INDEX IF NOT EXISTS idx_report_history_created_at ON report_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_report_history_expires ON report_history(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_user_activity_log_tenant_user ON user_activity_log(tenant_id, user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_created_at ON user_activity_log(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_activity_type ON user_activity_log(activity_type);

CREATE INDEX IF NOT EXISTS idx_notification_preferences_tenant_user ON notification_preferences(tenant_id, user_id);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_type_channel ON notification_preferences(notification_type, channel);

-- Triggers for updated_at columns
CREATE TRIGGER update_scheduled_reports_updated_at BEFORE UPDATE ON scheduled_reports 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_saved_dashboard_configs_updated_at BEFORE UPDATE ON saved_dashboard_configs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_visualization_preferences_updated_at BEFORE UPDATE ON visualization_preferences 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_preferences_updated_at BEFORE UPDATE ON notification_preferences 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate next scheduled time
CREATE OR REPLACE FUNCTION calculate_next_scheduled_time(
  frequency VARCHAR(20),
  schedule_time TIME,
  schedule_day INTEGER DEFAULT NULL
) RETURNS TIMESTAMP AS $$
DECLARE
  next_time TIMESTAMP;
  current_time TIMESTAMP := CURRENT_TIMESTAMP;
BEGIN
  CASE frequency
    WHEN 'daily' THEN
      next_time := DATE_TRUNC('day', current_time) + schedule_time;
      IF next_time <= current_time THEN
        next_time := next_time + INTERVAL '1 day';
      END IF;
      
    WHEN 'weekly' THEN
      next_time := DATE_TRUNC('week', current_time) + 
                   (COALESCE(schedule_day, 1) - 1) * INTERVAL '1 day' + 
                   schedule_time;
      IF next_time <= current_time THEN
        next_time := next_time + INTERVAL '1 week';
      END IF;
      
    WHEN 'monthly' THEN
      next_time := DATE_TRUNC('month', current_time) + 
                   (COALESCE(schedule_day, 1) - 1) * INTERVAL '1 day' + 
                   schedule_time;
      IF next_time <= current_time THEN
        next_time := (DATE_TRUNC('month', current_time) + INTERVAL '1 month') + 
                     (COALESCE(schedule_day, 1) - 1) * INTERVAL '1 day' + 
                     schedule_time;
      END IF;
      
    ELSE
      RAISE EXCEPTION 'Invalid frequency: %', frequency;
  END CASE;
  
  RETURN next_time;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically calculate next_scheduled when inserting/updating scheduled reports
CREATE OR REPLACE FUNCTION update_next_scheduled_time()
RETURNS TRIGGER AS $$
BEGIN
  NEW.next_scheduled := calculate_next_scheduled_time(
    NEW.schedule_frequency,
    NEW.schedule_time,
    NEW.schedule_day
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_next_scheduled_time
  BEFORE INSERT OR UPDATE ON scheduled_reports
  FOR EACH ROW
  EXECUTE FUNCTION update_next_scheduled_time();

-- Function to clean up expired reports
CREATE OR REPLACE FUNCTION cleanup_expired_reports()
RETURNS void AS $$
BEGIN
  -- Delete expired report files from history
  DELETE FROM report_history 
  WHERE expires_at < CURRENT_TIMESTAMP;
  
  -- Clean up old user activity logs (keep last 90 days)
  DELETE FROM user_activity_log 
  WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
  
  -- Log the cleanup
  INSERT INTO system_logs (log_level, message)
  VALUES ('INFO', 'Expired reports and old activity logs cleaned up');
END;
$$ LANGUAGE plpgsql;

-- Sample data for testing
INSERT INTO scheduled_reports (tenant_id, report_type, format, schedule_frequency, recipients, options) VALUES
('00000000-0000-0000-0000-000000000001', 'summary', 'pdf', 'weekly', '["<EMAIL>"]', '{"includeCharts": true, "template": "executive"}'),
('00000000-0000-0000-0000-000000000001', 'clicks', 'csv', 'daily', '["<EMAIL>"]', '{"includeDetails": true}')
ON CONFLICT DO NOTHING;

INSERT INTO saved_dashboard_configs (tenant_id, config_name, config_data, is_default) VALUES
('00000000-0000-0000-0000-000000000001', 'Executive Dashboard', '{"layout": "executive", "widgets": ["revenue", "conversion_rate", "top_links"]}', true),
('00000000-0000-0000-0000-000000000001', 'Marketing Dashboard', '{"layout": "marketing", "widgets": ["clicks", "traffic_sources", "campaign_performance"]}', false)
ON CONFLICT DO NOTHING;

INSERT INTO visualization_preferences (tenant_id, chart_type, metric_name, preferences, is_default) VALUES
('00000000-0000-0000-0000-000000000001', 'line', 'revenue', '{"color": "#2563eb", "showDataPoints": true, "smoothing": true}', true),
('00000000-0000-0000-0000-000000000001', 'bar', 'clicks', '{"color": "#059669", "showValues": true, "orientation": "vertical"}', true),
('00000000-0000-0000-0000-000000000001', 'pie', 'traffic_sources', '{"showLabels": true, "showPercentages": true, "donutMode": false}', true)
ON CONFLICT DO NOTHING;
