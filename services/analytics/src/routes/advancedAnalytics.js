const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const { recordHttpRequest } = require('../metrics');

// Advanced analytics service will be loaded after database connection
let AdvancedAnalyticsService;
let advancedAnalyticsService;

// Middleware for metrics recording
router.use((req, res, next) => {
  const originalSend = res.send;
  res.send = function(data) {
    const duration = (Date.now() - req.startTime) / 1000;
    recordHttpRequest(req.method, req.route?.path || req.path, res.statusCode, duration);
    return originalSend.call(this, data);
  };
  next();
});

// Lazy load analytics service
function ensureAdvancedAnalyticsService() {
  if (!advancedAnalyticsService) {
    AdvancedAnalyticsService = require('../services/advancedAnalyticsService');
    advancedAnalyticsService = new AdvancedAnalyticsService();
  }
  return advancedAnalyticsService;
}

/**
 * POST /advanced-analytics/cohort-analysis
 * Generate cohort analysis for customer retention tracking
 */
router.post('/cohort-analysis', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;
    const {
      cohortType = 'acquisition',
      period = 'monthly',
      startDate,
      endDate,
      includeRevenue = true
    } = req.body;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    logger.info('Generating cohort analysis', {
      tenantId: tenant_id,
      cohortType,
      period,
      startDate,
      endDate
    });

    const cohortData = await ensureAdvancedAnalyticsService().generateCohortAnalysis(tenant_id, {
      cohortType,
      period,
      startDate,
      endDate,
      includeRevenue
    });

    res.json({
      success: true,
      data: cohortData,
      metadata: {
        cohortType,
        period,
        dateRange: { startDate, endDate },
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * POST /advanced-analytics/clv-analysis
 * Calculate Customer Lifetime Value with segmentation
 */
router.post('/clv-analysis', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;
    const {
      segmentBy = 'acquisition_month',
      includeProjected = true,
      timeHorizon = 12
    } = req.body;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    logger.info('Calculating CLV analysis', {
      tenantId: tenant_id,
      segmentBy,
      includeProjected,
      timeHorizon
    });

    const clvData = await ensureAdvancedAnalyticsService().calculateCustomerLifetimeValue(tenant_id, {
      segmentBy,
      includeProjected,
      timeHorizon
    });

    res.json({
      success: true,
      data: clvData,
      metadata: {
        segmentBy,
        includeProjected,
        timeHorizon,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * POST /advanced-analytics/funnel-analysis
 * Generate funnel analysis for conversion tracking
 */
router.post('/funnel-analysis', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;
    const {
      funnelSteps = ['click', 'visit', 'add_to_cart', 'purchase'],
      dateFrom,
      dateTo,
      segmentBy = null
    } = req.body;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    logger.info('Generating funnel analysis', {
      tenantId: tenant_id,
      funnelSteps,
      dateFrom,
      dateTo,
      segmentBy
    });

    const funnelData = await ensureAdvancedAnalyticsService().generateFunnelAnalysis(tenant_id, {
      funnelSteps,
      dateFrom,
      dateTo,
      segmentBy
    });

    res.json({
      success: true,
      data: funnelData,
      metadata: {
        funnelSteps,
        dateRange: { dateFrom, dateTo },
        segmentBy,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * GET /advanced-analytics/predictive-insights
 * Get predictive analytics and forecasting insights
 */
router.get('/predictive-insights', async (req, res, next) => {
  try {
    const { tenant_id, forecast_period = 30, confidence_level = 0.95 } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    logger.info('Generating predictive insights', {
      tenantId: tenant_id,
      forecastPeriod: forecast_period,
      confidenceLevel: confidence_level
    });

    // For now, return a simplified predictive model
    // In production, this would integrate with ML models
    const insights = await generatePredictiveInsights(tenant_id, {
      forecastPeriod: parseInt(forecast_period),
      confidenceLevel: parseFloat(confidence_level)
    });

    res.json({
      success: true,
      data: insights,
      metadata: {
        forecastPeriod: parseInt(forecast_period),
        confidenceLevel: parseFloat(confidence_level),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * GET /advanced-analytics/retention-curves
 * Get retention curve data for visualization
 */
router.get('/retention-curves', async (req, res, next) => {
  try {
    const { tenant_id, curve_type = 'user_retention', months_back = 12 } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const retentionQuery = `
        SELECT 
          cohort_month,
          period_number,
          retained_users,
          users_count,
          retention_rate,
          revenue
        FROM retention_curves
        WHERE tenant_id = $1
          AND curve_type = $2
          AND cohort_month >= NOW() - INTERVAL '${parseInt(months_back)} months'
        ORDER BY cohort_month, period_number
      `;

      const result = await client.query(retentionQuery, [tenant_id, curve_type]);

      res.json({
        success: true,
        data: {
          curves: result.rows,
          curveType: curve_type,
          monthsBack: parseInt(months_back)
        }
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

/**
 * GET /advanced-analytics/clv-segments
 * Get CLV segments for customer categorization
 */
router.get('/clv-segments', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const segmentsQuery = `
        SELECT 
          segment_name,
          min_clv,
          max_clv,
          customer_count,
          avg_clv,
          segment_characteristics
        FROM clv_segments
        WHERE tenant_id = $1
        ORDER BY avg_clv DESC
      `;

      const result = await client.query(segmentsQuery, [tenant_id]);

      res.json({
        success: true,
        data: {
          segments: result.rows,
          totalSegments: result.rows.length,
          totalCustomers: result.rows.reduce((sum, row) => sum + row.customer_count, 0)
        }
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

// Helper function for predictive insights
async function generatePredictiveInsights(tenantId, options) {
  const { pool } = require('../database');
  const client = await pool.connect();

  try {
    // Simplified predictive model based on historical trends
    const trendsQuery = `
      WITH monthly_metrics AS (
        SELECT 
          DATE_TRUNC('month', o.created_at) as month,
          COUNT(*) as orders,
          SUM(o.total_amount) as revenue,
          COUNT(DISTINCT o.customer_id) as unique_customers
        FROM orders o
        JOIN integrations i ON o.integration_id = i.id
        WHERE i.tenant_id = $1
          AND o.created_at >= NOW() - INTERVAL '12 months'
        GROUP BY DATE_TRUNC('month', o.created_at)
        ORDER BY month
      ),
      growth_rates AS (
        SELECT 
          month,
          orders,
          revenue,
          unique_customers,
          LAG(revenue) OVER (ORDER BY month) as prev_revenue,
          LAG(orders) OVER (ORDER BY month) as prev_orders
        FROM monthly_metrics
      )
      SELECT 
        AVG(CASE WHEN prev_revenue > 0 THEN (revenue - prev_revenue) / prev_revenue ELSE 0 END) as avg_revenue_growth_rate,
        AVG(CASE WHEN prev_orders > 0 THEN (orders - prev_orders) / prev_orders ELSE 0 END) as avg_order_growth_rate,
        AVG(revenue) as avg_monthly_revenue,
        AVG(orders) as avg_monthly_orders
      FROM growth_rates
      WHERE prev_revenue IS NOT NULL
    `;

    const result = await client.query(trendsQuery, [tenantId]);
    const trends = result.rows[0];

    // Simple linear projection
    const currentRevenue = parseFloat(trends.avg_monthly_revenue) || 0;
    const growthRate = parseFloat(trends.avg_revenue_growth_rate) || 0;
    
    const projectedRevenue = currentRevenue * (1 + growthRate) * options.forecastPeriod / 30;
    const projectedOrders = parseFloat(trends.avg_monthly_orders) * (1 + parseFloat(trends.avg_order_growth_rate)) * options.forecastPeriod / 30;

    return {
      forecast: {
        projected_revenue: Math.round(projectedRevenue * 100) / 100,
        projected_orders: Math.round(projectedOrders),
        confidence_level: options.confidenceLevel,
        forecast_period_days: options.forecastPeriod
      },
      trends: {
        revenue_growth_rate: Math.round(growthRate * 10000) / 100, // Convert to percentage
        order_growth_rate: Math.round(parseFloat(trends.avg_order_growth_rate) * 10000) / 100,
        avg_monthly_revenue: currentRevenue,
        avg_monthly_orders: parseFloat(trends.avg_monthly_orders)
      },
      recommendations: generateRecommendations(trends, growthRate)
    };

  } finally {
    client.release();
  }
}

function generateRecommendations(trends, growthRate) {
  const recommendations = [];

  if (growthRate < 0) {
    recommendations.push({
      type: 'warning',
      title: 'Declining Revenue Trend',
      description: 'Revenue growth is negative. Consider reviewing marketing strategies and customer retention programs.',
      priority: 'high'
    });
  } else if (growthRate < 0.05) {
    recommendations.push({
      type: 'info',
      title: 'Slow Growth',
      description: 'Revenue growth is below 5%. Consider implementing growth initiatives.',
      priority: 'medium'
    });
  }

  if (parseFloat(trends.avg_monthly_revenue) > 0) {
    recommendations.push({
      type: 'success',
      title: 'Optimize High-Value Customers',
      description: 'Focus on retaining and expanding high CLV customer segments.',
      priority: 'medium'
    });
  }

  return recommendations;
}

module.exports = router;
