const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const { recordHttpRequest } = require('../metrics');

// Services will be loaded after database connection
let DashboardWidgetsService;
let AlertsService;
let dashboardWidgetsService;
let alertsService;

// Middleware for metrics recording
router.use((req, res, next) => {
  const originalSend = res.send;
  res.send = function(data) {
    const duration = (Date.now() - req.startTime) / 1000;
    recordHttpRequest(req.method, req.route?.path || req.path, res.statusCode, duration);
    return originalSend.call(this, data);
  };
  next();
});

// Lazy load services
function ensureServices() {
  if (!dashboardWidgetsService) {
    DashboardWidgetsService = require('../services/dashboardWidgetsService');
    dashboardWidgetsService = new DashboardWidgetsService();
  }
  if (!alertsService) {
    AlertsService = require('../services/alertsService');
    alertsService = new AlertsService();
  }
  return { dashboardWidgetsService, alertsService };
}

/**
 * GET /dashboard-enhancements/layout
 * Get dashboard layout and widgets for a user
 */
router.get('/layout', async (req, res, next) => {
  try {
    const { tenant_id, user_id, layout_id } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    const { dashboardWidgetsService } = ensureServices();
    const layout = await dashboardWidgetsService.getDashboardLayout(tenant_id, user_id, layout_id);

    res.json({
      success: true,
      data: layout
    });

  } catch (error) {
    next(error);
  }
});

/**
 * POST /dashboard-enhancements/widget
 * Create a new dashboard widget
 */
router.post('/widget', async (req, res, next) => {
  try {
    const { tenant_id, user_id } = req.query;
    const {
      widget_type,
      widget_title,
      widget_config,
      position_x = 0,
      position_y = 0,
      width = 4,
      height = 3
    } = req.body;

    if (!tenant_id || !widget_type || !widget_config) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id, widget_type, and widget_config are required'
      });
    }

    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const insertQuery = `
        INSERT INTO dashboard_widgets (
          tenant_id, user_id, widget_type, widget_title, widget_config,
          position_x, position_y, width, height
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `;

      const result = await client.query(insertQuery, [
        tenant_id, user_id, widget_type, widget_title,
        JSON.stringify(widget_config),
        position_x, position_y, width, height
      ]);

      res.json({
        success: true,
        data: result.rows[0]
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

/**
 * PUT /dashboard-enhancements/widget/:widgetId
 * Update a dashboard widget
 */
router.put('/widget/:widgetId', async (req, res, next) => {
  try {
    const { widgetId } = req.params;
    const { tenant_id } = req.query;
    const updateData = req.body;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const updateFields = [];
      const values = [];
      let paramIndex = 1;

      // Build dynamic update query
      Object.keys(updateData).forEach(key => {
        if (['widget_title', 'widget_config', 'position_x', 'position_y', 'width', 'height'].includes(key)) {
          updateFields.push(`${key} = $${paramIndex}`);
          values.push(key === 'widget_config' ? JSON.stringify(updateData[key]) : updateData[key]);
          paramIndex++;
        }
      });

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No valid fields to update'
        });
      }

      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(widgetId, tenant_id);

      const updateQuery = `
        UPDATE dashboard_widgets
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex} AND tenant_id = $${paramIndex + 1}
        RETURNING *
      `;

      const result = await client.query(updateQuery, values);

      if (result.rows.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Widget not found'
        });
      }

      res.json({
        success: true,
        data: result.rows[0]
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /dashboard-enhancements/widget/:widgetId
 * Delete a dashboard widget
 */
router.delete('/widget/:widgetId', async (req, res, next) => {
  try {
    const { widgetId } = req.params;
    const { tenant_id } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const deleteQuery = `
        DELETE FROM dashboard_widgets
        WHERE id = $1 AND tenant_id = $2
        RETURNING id
      `;

      const result = await client.query(deleteQuery, [widgetId, tenant_id]);

      if (result.rows.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Widget not found'
        });
      }

      res.json({
        success: true,
        message: 'Widget deleted successfully'
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

/**
 * GET /dashboard-enhancements/alerts
 * Get active alerts for a tenant
 */
router.get('/alerts', async (req, res, next) => {
  try {
    const { tenant_id, limit = 50 } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    const { alertsService } = ensureServices();
    const alerts = await alertsService.getActiveAlerts(tenant_id, parseInt(limit));

    res.json({
      success: true,
      data: alerts
    });

  } catch (error) {
    next(error);
  }
});

/**
 * POST /dashboard-enhancements/alert-rule
 * Create a new alert rule
 */
router.post('/alert-rule', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;
    const {
      rule_name,
      rule_type,
      metric_name,
      condition_operator,
      threshold_value,
      comparison_period = 'last_hour',
      notification_channels = ['dashboard']
    } = req.body;

    if (!tenant_id || !rule_name || !metric_name || !condition_operator || threshold_value === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const insertQuery = `
        INSERT INTO alert_rules (
          tenant_id, rule_name, rule_type, metric_name, condition_operator,
          threshold_value, comparison_period, notification_channels
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;

      const result = await client.query(insertQuery, [
        tenant_id, rule_name, rule_type, metric_name, condition_operator,
        threshold_value, comparison_period, JSON.stringify(notification_channels)
      ]);

      res.json({
        success: true,
        data: result.rows[0]
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

/**
 * PUT /dashboard-enhancements/alert/:alertId/acknowledge
 * Acknowledge an alert
 */
router.put('/alert/:alertId/acknowledge', async (req, res, next) => {
  try {
    const { alertId } = req.params;
    const { user_id } = req.body;

    if (!user_id) {
      return res.status(400).json({
        success: false,
        error: 'user_id is required'
      });
    }

    const { alertsService } = ensureServices();
    const result = await alertsService.acknowledgeAlert(alertId, user_id);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Alert not found'
      });
    }

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    next(error);
  }
});

/**
 * GET /dashboard-enhancements/comparative-analysis
 * Get comparative period analysis
 */
router.get('/comparative-analysis', async (req, res, next) => {
  try {
    const { 
      tenant_id, 
      metric_name, 
      analysis_type = 'mom', // month-over-month
      current_period_start,
      current_period_end 
    } = req.query;

    if (!tenant_id || !metric_name) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id and metric_name are required'
      });
    }

    const analysis = await generateComparativeAnalysis(
      tenant_id, 
      metric_name, 
      analysis_type,
      current_period_start,
      current_period_end
    );

    res.json({
      success: true,
      data: analysis
    });

  } catch (error) {
    next(error);
  }
});

/**
 * POST /dashboard-enhancements/drill-down
 * Start a drill-down analysis session
 */
router.post('/drill-down', async (req, res, next) => {
  try {
    const { tenant_id, user_id } = req.query;
    const { initial_metric, filters = {} } = req.body;

    if (!tenant_id || !initial_metric) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id and initial_metric are required'
      });
    }

    const sessionToken = require('crypto').randomBytes(32).toString('hex');

    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const insertQuery = `
        INSERT INTO drill_down_sessions (
          tenant_id, user_id, session_token, initial_metric, filters_applied
        ) VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `;

      const result = await client.query(insertQuery, [
        tenant_id, user_id, sessionToken, initial_metric, JSON.stringify(filters)
      ]);

      res.json({
        success: true,
        data: {
          session: result.rows[0],
          drillDownData: await getDrillDownData(tenant_id, initial_metric, filters)
        }
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

// Helper function for comparative analysis
async function generateComparativeAnalysis(tenantId, metricName, analysisType, currentStart, currentEnd) {
  const { pool } = require('../database');
  const client = await pool.connect();

  try {
    // Calculate comparison periods based on analysis type
    let comparisonStart, comparisonEnd;
    const currentStartDate = new Date(currentStart || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));
    const currentEndDate = new Date(currentEnd || new Date());

    switch (analysisType) {
      case 'mom': // Month over month
        comparisonStart = new Date(currentStartDate);
        comparisonStart.setMonth(comparisonStart.getMonth() - 1);
        comparisonEnd = new Date(currentEndDate);
        comparisonEnd.setMonth(comparisonEnd.getMonth() - 1);
        break;
      case 'yoy': // Year over year
        comparisonStart = new Date(currentStartDate);
        comparisonStart.setFullYear(comparisonStart.getFullYear() - 1);
        comparisonEnd = new Date(currentEndDate);
        comparisonEnd.setFullYear(comparisonEnd.getFullYear() - 1);
        break;
      case 'wow': // Week over week
        comparisonStart = new Date(currentStartDate.getTime() - 7 * 24 * 60 * 60 * 1000);
        comparisonEnd = new Date(currentEndDate.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      default:
        throw new Error(`Unknown analysis type: ${analysisType}`);
    }

    // Get metric values for both periods
    const currentValue = await getMetricValueForPeriod(client, tenantId, metricName, currentStartDate, currentEndDate);
    const comparisonValue = await getMetricValueForPeriod(client, tenantId, metricName, comparisonStart, comparisonEnd);

    const changeValue = currentValue - comparisonValue;
    const changePercentage = comparisonValue > 0 ? (changeValue / comparisonValue) * 100 : 0;

    return {
      metric_name: metricName,
      analysis_type: analysisType,
      current_period: {
        start: currentStartDate.toISOString(),
        end: currentEndDate.toISOString(),
        value: currentValue
      },
      comparison_period: {
        start: comparisonStart.toISOString(),
        end: comparisonEnd.toISOString(),
        value: comparisonValue
      },
      change: {
        absolute: changeValue,
        percentage: changePercentage,
        direction: changeValue > 0 ? 'up' : changeValue < 0 ? 'down' : 'stable'
      }
    };

  } finally {
    client.release();
  }
}

// Helper function to get metric value for a period
async function getMetricValueForPeriod(client, tenantId, metricName, startDate, endDate) {
  let query;

  switch (metricName) {
    case 'revenue':
      query = `
        SELECT COALESCE(SUM(o.total_amount), 0) as value
        FROM orders o
        JOIN integrations i ON o.integration_id = i.id
        WHERE i.tenant_id = $1 AND o.created_at >= $2 AND o.created_at <= $3
      `;
      break;
    case 'clicks':
      query = `
        SELECT COUNT(*) as value
        FROM clicks c
        JOIN links l ON c.link_id = l.id
        WHERE l.tenant_id = $1 AND c.clicked_at >= $2 AND c.clicked_at <= $3
      `;
      break;
    case 'conversions':
      query = `
        SELECT COUNT(*) as value
        FROM attributions a
        JOIN links l ON a.link_id = l.id
        WHERE l.tenant_id = $1 AND a.created_at >= $2 AND a.created_at <= $3
      `;
      break;
    default:
      throw new Error(`Unknown metric: ${metricName}`);
  }

  const result = await client.query(query, [tenantId, startDate, endDate]);
  return parseFloat(result.rows[0].value) || 0;
}

// Helper function for drill-down data
async function getDrillDownData(tenantId, metric, filters) {
  // Simplified drill-down implementation
  // In production, this would provide detailed breakdowns
  return {
    metric,
    filters,
    breakdown: {
      by_country: [],
      by_device: [],
      by_time: []
    }
  };
}

module.exports = router;
