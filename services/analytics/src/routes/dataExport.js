const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const { recordHttpRequest } = require('../metrics');
const path = require('path');

// Data export service will be loaded after database connection
let DataExportService;
let dataExportService;

// Middleware for metrics recording
router.use((req, res, next) => {
  const originalSend = res.send;
  res.send = function(data) {
    const duration = (Date.now() - req.startTime) / 1000;
    recordHttpRequest(req.method, req.route?.path || req.path, res.statusCode, duration);
    return originalSend.call(this, data);
  };
  next();
});

// Lazy load export service
function ensureDataExportService() {
  if (!dataExportService) {
    DataExportService = require('../services/dataExportService');
    dataExportService = new DataExportService();
  }
  return dataExportService;
}

/**
 * POST /data-export/csv
 * Export analytics data to CSV format
 */
router.post('/csv', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;
    const {
      reportType = 'summary',
      dateFrom,
      dateTo,
      includeDetails = false,
      filters = {}
    } = req.body;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    logger.info('CSV export requested', {
      tenantId: tenant_id,
      reportType,
      dateFrom,
      dateTo
    });

    const exportResult = await ensureDataExportService().exportToCSV(tenant_id, {
      reportType,
      dateFrom,
      dateTo,
      includeDetails,
      filters
    });

    res.json({
      success: true,
      data: exportResult,
      message: 'CSV export completed successfully'
    });

  } catch (error) {
    next(error);
  }
});

/**
 * POST /data-export/pdf
 * Export analytics data to PDF report
 */
router.post('/pdf', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;
    const {
      reportType = 'dashboard',
      dateFrom,
      dateTo,
      includeCharts = true,
      template = 'standard'
    } = req.body;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    logger.info('PDF export requested', {
      tenantId: tenant_id,
      reportType,
      dateFrom,
      dateTo
    });

    const exportResult = await ensureDataExportService().exportToPDF(tenant_id, {
      reportType,
      dateFrom,
      dateTo,
      includeCharts,
      template
    });

    res.json({
      success: true,
      data: exportResult,
      message: 'PDF export completed successfully'
    });

  } catch (error) {
    next(error);
  }
});

/**
 * GET /data-export/download/:filename
 * Download exported file
 */
router.get('/download/:filename', async (req, res, next) => {
  try {
    const { filename } = req.params;

    // Validate filename to prevent directory traversal
    if (!filename || filename.includes('..') || filename.includes('/')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid filename'
      });
    }

    const filepath = await ensureDataExportService().getExportFile(filename);
    
    // Determine content type based on file extension
    const ext = path.extname(filename).toLowerCase();
    let contentType = 'application/octet-stream';
    let disposition = 'attachment';

    switch (ext) {
      case '.csv':
        contentType = 'text/csv';
        break;
      case '.pdf':
        contentType = 'application/pdf';
        break;
      case '.html':
        contentType = 'text/html';
        disposition = 'inline';
        break;
    }

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `${disposition}; filename="${filename}"`);
    res.sendFile(filepath);

  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: 'Export file not found or expired'
      });
    }
    next(error);
  }
});

/**
 * GET /data-export/templates
 * Get available export templates
 */
router.get('/templates', async (req, res, next) => {
  try {
    const templates = {
      csv: [
        {
          id: 'summary',
          name: 'Summary Report',
          description: 'Daily summary of clicks, conversions, and revenue',
          fields: ['date', 'total_clicks', 'unique_visitors', 'total_conversions', 'total_revenue', 'conversion_rate', 'avg_order_value']
        },
        {
          id: 'clicks',
          name: 'Clicks Detail',
          description: 'Detailed click-by-click data',
          fields: ['clicked_at', 'link_title', 'short_code', 'ip_address', 'country', 'device_type', 'browser', 'referrer', 'converted']
        },
        {
          id: 'conversions',
          name: 'Conversions Detail',
          description: 'Detailed conversion data with revenue',
          fields: ['conversion_date', 'link_title', 'short_code', 'clicked_at', 'country', 'device_type', 'revenue', 'currency', 'integration_platform']
        },
        {
          id: 'revenue',
          name: 'Revenue Analysis',
          description: 'Revenue breakdown by date and platform',
          fields: ['date', 'platform', 'order_count', 'total_revenue', 'avg_order_value', 'min_order_value', 'max_order_value']
        },
        {
          id: 'links',
          name: 'Link Performance',
          description: 'Performance metrics for all links',
          fields: ['title', 'short_code', 'destination_url', 'created_at', 'is_active', 'total_clicks', 'total_conversions', 'total_revenue']
        }
      ],
      pdf: [
        {
          id: 'dashboard',
          name: 'Dashboard Report',
          description: 'Comprehensive dashboard-style report with charts and tables',
          sections: ['summary_metrics', 'trend_charts', 'top_links', 'recent_activity']
        },
        {
          id: 'executive',
          name: 'Executive Summary',
          description: 'High-level summary for executives',
          sections: ['key_metrics', 'performance_highlights', 'recommendations']
        },
        {
          id: 'detailed',
          name: 'Detailed Analysis',
          description: 'In-depth analysis with all available data',
          sections: ['summary_metrics', 'trend_analysis', 'link_performance', 'conversion_funnel', 'geographic_breakdown']
        }
      ]
    };

    res.json({
      success: true,
      data: templates
    });

  } catch (error) {
    next(error);
  }
});

/**
 * POST /data-export/schedule
 * Schedule automated report generation
 */
router.post('/schedule', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;
    const {
      reportType,
      format, // 'csv' or 'pdf'
      schedule, // 'daily', 'weekly', 'monthly'
      recipients = [], // email addresses
      options = {}
    } = req.body;

    if (!tenant_id || !reportType || !format || !schedule) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id, reportType, format, and schedule are required'
      });
    }

    // Store scheduled report configuration
    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const insertQuery = `
        INSERT INTO scheduled_reports (
          tenant_id, report_type, format, schedule_frequency, 
          recipients, options, is_active, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, true, CURRENT_TIMESTAMP)
        RETURNING *
      `;

      const result = await client.query(insertQuery, [
        tenant_id,
        reportType,
        format,
        schedule,
        JSON.stringify(recipients),
        JSON.stringify(options)
      ]);

      res.json({
        success: true,
        data: result.rows[0],
        message: 'Report scheduled successfully'
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

/**
 * GET /data-export/scheduled
 * Get scheduled reports for a tenant
 */
router.get('/scheduled', async (req, res, next) => {
  try {
    const { tenant_id } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const query = `
        SELECT * FROM scheduled_reports
        WHERE tenant_id = $1
        ORDER BY created_at DESC
      `;

      const result = await client.query(query, [tenant_id]);

      res.json({
        success: true,
        data: result.rows
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /data-export/scheduled/:reportId
 * Cancel a scheduled report
 */
router.delete('/scheduled/:reportId', async (req, res, next) => {
  try {
    const { reportId } = req.params;
    const { tenant_id } = req.query;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        error: 'tenant_id is required'
      });
    }

    const { pool } = require('../database');
    const client = await pool.connect();

    try {
      const deleteQuery = `
        UPDATE scheduled_reports
        SET is_active = false, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND tenant_id = $2
        RETURNING *
      `;

      const result = await client.query(deleteQuery, [reportId, tenant_id]);

      if (result.rows.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Scheduled report not found'
        });
      }

      res.json({
        success: true,
        message: 'Scheduled report cancelled successfully'
      });

    } finally {
      client.release();
    }

  } catch (error) {
    next(error);
  }
});

module.exports = router;
