const AdvancedAnalyticsProcessor = require('../advancedAnalyticsProcessor');

describe('AdvancedAnalyticsProcessor', () => {
  let processor;
  let mockPool;
  let mockClient;

  beforeEach(() => {
    mockClient = {
      query: jest.fn(),
      release: jest.fn()
    };
    
    mockPool = {
      connect: jest.fn().mockResolvedValue(mockClient)
    };

    processor = new AdvancedAnalyticsProcessor();
    processor.pool = mockPool;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('generateRFMSegments', () => {
    const mockRFMData = {
      rows: [
        {
          customer_email: '<EMAIL>',
          recency: 5,
          frequency: 10,
          monetary: 1500.00,
          r_score: 5,
          f_score: 4,
          m_score: 5,
          rfm_segment: 'Champions'
        },
        {
          customer_email: '<EMAIL>',
          recency: 30,
          frequency: 3,
          monetary: 300.00,
          r_score: 2,
          f_score: 2,
          m_score: 2,
          rfm_segment: 'At Risk'
        },
        {
          customer_email: '<EMAIL>',
          recency: 15,
          frequency: 6,
          monetary: 800.00,
          r_score: 3,
          f_score: 3,
          m_score: 4,
          rfm_segment: 'Potential Loyalists'
        }
      ]
    };

    it('should generate RFM segments with correct scoring', async () => {
      mockClient.query.mockResolvedValueOnce(mockRFMData);

      const result = await processor.generateRFMSegments('tenant-1', '2024-01-01', '2024-03-01');

      expect(result).toHaveProperty('segments');
      expect(result).toHaveProperty('summary');
      expect(result.segments).toHaveLength(3);
      
      // Verify Champions segment
      const champion = result.segments.find(s => s.rfm_segment === 'Champions');
      expect(champion).toBeDefined();
      expect(champion.r_score).toBe(5);
      expect(champion.f_score).toBe(4);
      expect(champion.m_score).toBe(5);

      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('NTILE(5)'),
        ['tenant-1']
      );
    });

    it('should calculate RFM segment distribution', async () => {
      mockClient.query.mockResolvedValueOnce(mockRFMData);

      const result = await processor.generateRFMSegments('tenant-1');

      expect(result.summary).toHaveProperty('total_customers', 3);
      expect(result.summary).toHaveProperty('segment_distribution');
      expect(result.summary.segment_distribution).toHaveProperty('Champions', 1);
      expect(result.summary.segment_distribution).toHaveProperty('At Risk', 1);
      expect(result.summary.segment_distribution).toHaveProperty('Potential Loyalists', 1);
    });

    it('should handle edge case with single customer', async () => {
      const singleCustomerData = {
        rows: [{
          customer_email: '<EMAIL>',
          recency: 10,
          frequency: 5,
          monetary: 500.00,
          r_score: 3,
          f_score: 3,
          m_score: 3,
          rfm_segment: 'Potential Loyalists'
        }]
      };

      mockClient.query.mockResolvedValueOnce(singleCustomerData);

      const result = await processor.generateRFMSegments('tenant-1');

      expect(result.segments).toHaveLength(1);
      expect(result.summary.total_customers).toBe(1);
    });

    it('should handle empty customer data', async () => {
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      const result = await processor.generateRFMSegments('tenant-1');

      expect(result.segments).toHaveLength(0);
      expect(result.summary.total_customers).toBe(0);
      expect(result.summary.segment_distribution).toEqual({});
    });
  });

  describe('segmentByValue', () => {
    const mockValueSegmentData = {
      rows: [
        { segment: 'High Value', customer_count: 25, avg_value: 1200.00, min_value: 800.00, max_value: 2500.00 },
        { segment: 'Medium Value', customer_count: 50, avg_value: 400.00, min_value: 200.00, max_value: 799.99 },
        { segment: 'Low Value', customer_count: 25, avg_value: 100.00, min_value: 50.00, max_value: 199.99 }
      ]
    };

    it('should segment customers by monetary value', async () => {
      mockClient.query.mockResolvedValueOnce(mockValueSegmentData);

      const result = await processor.segmentByValue('tenant-1', '2024-01-01', '2024-03-01');

      expect(result).toHaveLength(3);
      expect(result[0]).toHaveProperty('segment', 'High Value');
      expect(result[0]).toHaveProperty('customer_count', 25);
      expect(result[0]).toHaveProperty('avg_value', 1200.00);

      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('CASE WHEN total_value'),
        ['tenant-1', '2024-01-01', '2024-03-01']
      );
    });

    it('should handle value segmentation with percentiles', async () => {
      mockClient.query.mockResolvedValueOnce(mockValueSegmentData);

      const result = await processor.segmentByValue('tenant-1');

      // Verify segments are ordered by value descending
      expect(result[0].avg_value).toBeGreaterThan(result[1].avg_value);
      expect(result[1].avg_value).toBeGreaterThan(result[2].avg_value);
    });
  });

  describe('segmentByFrequency', () => {
    const mockFrequencySegmentData = {
      rows: [
        { segment: 'Frequent Buyers', customer_count: 30, avg_frequency: 8.5, min_frequency: 6, max_frequency: 15 },
        { segment: 'Occasional Buyers', customer_count: 45, avg_frequency: 3.2, min_frequency: 2, max_frequency: 5 },
        { segment: 'One-time Buyers', customer_count: 25, avg_frequency: 1.0, min_frequency: 1, max_frequency: 1 }
      ]
    };

    it('should segment customers by purchase frequency', async () => {
      mockClient.query.mockResolvedValueOnce(mockFrequencySegmentData);

      const result = await processor.segmentByFrequency('tenant-1', '2024-01-01', '2024-03-01');

      expect(result).toHaveLength(3);
      expect(result[0]).toHaveProperty('segment', 'Frequent Buyers');
      expect(result[0]).toHaveProperty('avg_frequency', 8.5);

      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('COUNT(*)'),
        ['tenant-1', '2024-01-01', '2024-03-01']
      );
    });

    it('should identify one-time buyers correctly', async () => {
      mockClient.query.mockResolvedValueOnce(mockFrequencySegmentData);

      const result = await processor.segmentByFrequency('tenant-1');

      const oneTimeBuyers = result.find(s => s.segment === 'One-time Buyers');
      expect(oneTimeBuyers).toBeDefined();
      expect(oneTimeBuyers.avg_frequency).toBe(1.0);
      expect(oneTimeBuyers.min_frequency).toBe(1);
      expect(oneTimeBuyers.max_frequency).toBe(1);
    });
  });

  describe('segmentByRecency', () => {
    const mockRecencySegmentData = {
      rows: [
        { segment: 'Recent Customers', customer_count: 40, avg_recency: 5.2, min_recency: 0, max_recency: 14 },
        { segment: 'Lapsed Customers', customer_count: 35, avg_recency: 45.8, min_recency: 15, max_recency: 89 },
        { segment: 'Lost Customers', customer_count: 25, avg_recency: 150.3, min_recency: 90, max_recency: 365 }
      ]
    };

    it('should segment customers by recency of last purchase', async () => {
      mockClient.query.mockResolvedValueOnce(mockRecencySegmentData);

      const result = await processor.segmentByRecency('tenant-1', '2024-01-01', '2024-03-01');

      expect(result).toHaveLength(3);
      expect(result[0]).toHaveProperty('segment', 'Recent Customers');
      expect(result[0]).toHaveProperty('avg_recency', 5.2);

      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('EXTRACT(DAYS FROM NOW()'),
        ['tenant-1', '2024-01-01', '2024-03-01']
      );
    });

    it('should identify lost customers with high recency', async () => {
      mockClient.query.mockResolvedValueOnce(mockRecencySegmentData);

      const result = await processor.segmentByRecency('tenant-1');

      const lostCustomers = result.find(s => s.segment === 'Lost Customers');
      expect(lostCustomers).toBeDefined();
      expect(lostCustomers.avg_recency).toBeGreaterThan(90);
    });
  });

  describe('processSegmentationAnalysis', () => {
    it('should process comprehensive segmentation analysis', async () => {
      // Mock all segmentation methods
      processor.segmentByValue = jest.fn().mockResolvedValue([{ segment: 'High Value', customer_count: 25 }]);
      processor.segmentByFrequency = jest.fn().mockResolvedValue([{ segment: 'Frequent', customer_count: 30 }]);
      processor.segmentByRecency = jest.fn().mockResolvedValue([{ segment: 'Recent', customer_count: 40 }]);
      processor.generateRFMSegments = jest.fn().mockResolvedValue({
        segments: [{ rfm_segment: 'Champions', customer_count: 20 }],
        summary: { total_customers: 100 }
      });

      const result = await processor.processSegmentationAnalysis('tenant-1', '2024-01-01', '2024-03-01');

      expect(result).toHaveProperty('byValue');
      expect(result).toHaveProperty('byFrequency');
      expect(result).toHaveProperty('byRecency');
      expect(result).toHaveProperty('rfmSegments');

      expect(processor.segmentByValue).toHaveBeenCalledWith('tenant-1', '2024-01-01', '2024-03-01');
      expect(processor.segmentByFrequency).toHaveBeenCalledWith('tenant-1', '2024-01-01', '2024-03-01');
      expect(processor.segmentByRecency).toHaveBeenCalledWith('tenant-1', '2024-01-01', '2024-03-01');
      expect(processor.generateRFMSegments).toHaveBeenCalledWith('tenant-1', '2024-01-01', '2024-03-01');
    });

    it('should handle errors in individual segmentation methods', async () => {
      processor.segmentByValue = jest.fn().mockRejectedValue(new Error('Value segmentation failed'));
      processor.segmentByFrequency = jest.fn().mockResolvedValue([]);
      processor.segmentByRecency = jest.fn().mockResolvedValue([]);
      processor.generateRFMSegments = jest.fn().mockResolvedValue({ segments: [], summary: {} });

      await expect(processor.processSegmentationAnalysis('tenant-1'))
        .rejects.toThrow('Value segmentation failed');
    });
  });

  describe('error handling and validation', () => {
    it('should handle database connection errors', async () => {
      mockClient.query.mockRejectedValue(new Error('Database connection failed'));

      await expect(processor.generateRFMSegments('tenant-1'))
        .rejects.toThrow('Database connection failed');
      
      expect(mockClient.release).toHaveBeenCalled();
    });

    it('should validate tenant ID parameter', async () => {
      await expect(processor.generateRFMSegments(null))
        .rejects.toThrow();
      
      await expect(processor.generateRFMSegments(''))
        .rejects.toThrow();
    });

    it('should validate date range parameters', async () => {
      await expect(processor.generateRFMSegments('tenant-1', '2024-03-01', '2024-01-01'))
        .rejects.toThrow('End date must be after start date');
    });

    it('should ensure tenant isolation in all queries', async () => {
      mockClient.query.mockResolvedValue({ rows: [] });

      await processor.generateRFMSegments('tenant-1');
      await processor.segmentByValue('tenant-1');
      await processor.segmentByFrequency('tenant-1');
      await processor.segmentByRecency('tenant-1');

      // Verify all queries include tenant_id filter
      expect(mockClient.query).toHaveBeenCalledTimes(4);
      mockClient.query.mock.calls.forEach(call => {
        expect(call[0]).toContain('tenant_id = $1');
        expect(call[1]).toContain('tenant-1');
      });
    });
  });
});
