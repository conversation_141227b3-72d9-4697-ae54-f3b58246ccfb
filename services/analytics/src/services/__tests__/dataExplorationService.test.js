const DataExplorationService = require('../dataExplorationService');

describe('DataExplorationService', () => {
  let service;
  let mockPool;
  let mockClient;

  beforeEach(() => {
    mockClient = {
      query: jest.fn(),
      release: jest.fn()
    };
    
    mockPool = {
      connect: jest.fn().mockResolvedValue(mockClient)
    };

    service = new DataExplorationService();
    service.pool = mockPool;
    service.logger = {
      error: jest.fn(),
      info: jest.fn(),
      debug: jest.fn()
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('analyzeFunnel', () => {
    const mockFunnelSteps = [
      { name: 'Page View', event_type: 'page_view' },
      { name: 'Add to Cart', event_type: 'add_to_cart' },
      { name: 'Checkout', event_type: 'checkout' },
      { name: 'Purchase', event_type: 'purchase' }
    ];

    it('should analyze funnel with correct conversion rates', async () => {
      // Mock getFunnelStepUsers to return decreasing user counts
      service.getFunnelStepUsers = jest.fn()
        .mockResolvedValueOnce(['user1', 'user2', 'user3', 'user4', 'user5']) // 5 users for step 1
        .mockResolvedValueOnce(['user1', 'user2', 'user3', 'user4']) // 4 users for step 2
        .mockResolvedValueOnce(['user1', 'user2']) // 2 users for step 3
        .mockResolvedValueOnce(['user1']); // 1 user for step 4

      const result = await service.analyzeFunnel(mockFunnelSteps, {
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      }, 'tenant-1');

      expect(result).toHaveProperty('funnel_data');
      expect(result).toHaveProperty('overall_conversion_rate');
      expect(result).toHaveProperty('total_users_entered', 5);
      expect(result).toHaveProperty('total_users_completed', 1);

      expect(result.funnel_data).toHaveLength(4);
      
      // Check conversion rates
      expect(result.funnel_data[0]).toHaveProperty('conversion_rate', '100');
      expect(result.funnel_data[1]).toHaveProperty('conversion_rate', '80.00');
      expect(result.funnel_data[2]).toHaveProperty('conversion_rate', '50.00');
      expect(result.funnel_data[3]).toHaveProperty('conversion_rate', '50.00');

      // Check drop-off rates
      expect(result.funnel_data[1]).toHaveProperty('drop_off_rate', '20.00');
      expect(result.funnel_data[2]).toHaveProperty('drop_off_rate', '50.00');
      expect(result.funnel_data[3]).toHaveProperty('drop_off_rate', '50.00');

      expect(result.overall_conversion_rate).toBe(20.00); // 1/5 * 100
    });

    it('should handle empty funnel steps', async () => {
      await expect(service.analyzeFunnel([], {}, 'tenant-1'))
        .rejects.toThrow('Funnel analysis requires at least 2 steps');
    });

    it('should handle single step funnel', async () => {
      const singleStep = [{ name: 'Page View', event_type: 'page_view' }];

      await expect(service.analyzeFunnel(singleStep, {}, 'tenant-1'))
        .rejects.toThrow('Funnel analysis requires at least 2 steps');
    });

    it('should handle zero users in first step', async () => {
      service.getFunnelStepUsers = jest.fn()
        .mockResolvedValue([]); // No users for any step

      const result = await service.analyzeFunnel(mockFunnelSteps, {}, 'tenant-1');

      expect(result.total_users_entered).toBe(0);
      expect(result.total_users_completed).toBe(0);
      expect(result.overall_conversion_rate).toBe(0);
    });

    it('should calculate drop-off correctly for perfect retention', async () => {
      service.getFunnelStepUsers = jest.fn()
        .mockResolvedValue(['user1', 'user2', 'user3']); // Same users for all steps

      const result = await service.analyzeFunnel(mockFunnelSteps, {}, 'tenant-1');

      result.funnel_data.forEach((step, index) => {
        if (index > 0) {
          expect(step.drop_off_rate).toBe('0.00');
          expect(step.conversion_rate).toBe('100.00');
        }
      });
    });
  });

  describe('getFunnelStepUsers', () => {
    const mockStep = { name: 'Add to Cart', event_type: 'add_to_cart' };
    const dateRange = { startDate: '2024-01-01', endDate: '2024-01-31' };

    it('should get users for a specific funnel step', async () => {
      const mockUserData = {
        rows: [
          { user_id: 'user1' },
          { user_id: 'user2' },
          { user_id: 'user3' }
        ]
      };

      mockClient.query.mockResolvedValueOnce(mockUserData);

      const result = await service.getFunnelStepUsers(mockStep, dateRange, 'tenant-1');

      expect(result).toEqual(['user1', 'user2', 'user3']);
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('event_type'),
        expect.arrayContaining(['add_to_cart', 'tenant-1', '2024-01-01', '2024-01-31'])
      );
    });

    it('should handle empty user results', async () => {
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      const result = await service.getFunnelStepUsers(mockStep, dateRange, 'tenant-1');

      expect(result).toEqual([]);
    });

    it('should handle database errors', async () => {
      mockClient.query.mockRejectedValueOnce(new Error('Database error'));

      await expect(service.getFunnelStepUsers(mockStep, dateRange, 'tenant-1'))
        .rejects.toThrow('Database error');
    });
  });

  describe('performCohortAnalysis', () => {
    const mockCohortData = {
      rows: [
        {
          cohort_period: '2024-01-01',
          period_number: 0,
          users: 100,
          retention_rate: 100.00,
          revenue: 10000
        },
        {
          cohort_period: '2024-01-01',
          period_number: 1,
          users: 85,
          retention_rate: 85.00,
          revenue: 8500
        },
        {
          cohort_period: '2024-02-01',
          period_number: 0,
          users: 120,
          retention_rate: 100.00,
          revenue: 12000
        }
      ]
    };

    it('should perform cohort analysis with retention metrics', async () => {
      mockClient.query.mockResolvedValueOnce(mockCohortData);

      const result = await service.performCohortAnalysis({
        cohort_type: 'acquisition',
        metric: 'retention',
        period: 'month',
        tenantId: 'tenant-1'
      });

      expect(result).toHaveProperty('cohorts');
      expect(result).toHaveProperty('summary');
      expect(result.cohorts).toHaveLength(3);
      
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('cohort_period'),
        expect.arrayContaining(['tenant-1'])
      );
    });

    it('should handle revenue-based cohort analysis', async () => {
      mockClient.query.mockResolvedValueOnce(mockCohortData);

      const result = await service.performCohortAnalysis({
        cohort_type: 'acquisition',
        metric: 'revenue',
        period: 'week',
        tenantId: 'tenant-1'
      });

      expect(result).toHaveProperty('cohorts');
      expect(result.cohorts[0]).toHaveProperty('revenue');
    });

    it('should validate cohort analysis parameters', async () => {
      await expect(service.performCohortAnalysis({
        cohort_type: 'invalid_type',
        tenantId: 'tenant-1'
      })).rejects.toThrow();

      await expect(service.performCohortAnalysis({
        metric: 'invalid_metric',
        tenantId: 'tenant-1'
      })).rejects.toThrow();
    });

    it('should handle empty cohort data', async () => {
      mockClient.query.mockResolvedValueOnce({ rows: [] });

      const result = await service.performCohortAnalysis({
        tenantId: 'tenant-1'
      });

      expect(result.cohorts).toHaveLength(0);
      expect(result.summary).toHaveProperty('total_cohorts', 0);
    });
  });

  describe('advanced funnel analysis', () => {
    it('should identify biggest drop-off points', async () => {
      service.getFunnelStepUsers = jest.fn()
        .mockResolvedValueOnce(Array.from({ length: 1000 }, (_, i) => `user${i}`)) // 1000 users
        .mockResolvedValueOnce(Array.from({ length: 800 }, (_, i) => `user${i}`))  // 800 users (20% drop)
        .mockResolvedValueOnce(Array.from({ length: 200 }, (_, i) => `user${i}`))  // 200 users (75% drop)
        .mockResolvedValueOnce(Array.from({ length: 150 }, (_, i) => `user${i}`)); // 150 users (25% drop)

      const steps = [
        { name: 'Landing', event_type: 'page_view' },
        { name: 'Product View', event_type: 'product_view' },
        { name: 'Add to Cart', event_type: 'add_to_cart' },
        { name: 'Purchase', event_type: 'purchase' }
      ];

      const result = await service.analyzeFunnel(steps, {}, 'tenant-1');

      // The biggest drop should be from Product View to Add to Cart (75% drop)
      expect(result.funnel_data[2]).toHaveProperty('drop_off_rate', '75.00');
      
      // Verify the drop-off rates are calculated correctly
      expect(result.funnel_data[1]).toHaveProperty('drop_off_rate', '20.00');
      expect(result.funnel_data[3]).toHaveProperty('drop_off_rate', '25.00');
    });

    it('should handle complex user journey patterns', async () => {
      // Simulate users who skip steps or have non-linear journeys
      service.getFunnelStepUsers = jest.fn()
        .mockResolvedValueOnce(['user1', 'user2', 'user3', 'user4', 'user5'])
        .mockResolvedValueOnce(['user1', 'user3', 'user5']) // Some users skip step 2
        .mockResolvedValueOnce(['user1', 'user2', 'user3']) // Different users in step 3
        .mockResolvedValueOnce(['user1']); // Only one user completes

      const steps = [
        { name: 'Step 1', event_type: 'step1' },
        { name: 'Step 2', event_type: 'step2' },
        { name: 'Step 3', event_type: 'step3' },
        { name: 'Step 4', event_type: 'step4' }
      ];

      const result = await service.analyzeFunnel(steps, {}, 'tenant-1');

      expect(result.funnel_data).toHaveLength(4);
      expect(result.overall_conversion_rate).toBe(20.00); // 1/5 * 100
    });
  });

  describe('performance and edge cases', () => {
    it('should handle large funnels efficiently', async () => {
      const largeSteps = Array.from({ length: 20 }, (_, i) => ({
        name: `Step ${i + 1}`,
        event_type: `step_${i + 1}`
      }));

      service.getFunnelStepUsers = jest.fn()
        .mockImplementation(() => Promise.resolve(['user1', 'user2', 'user3']));

      const startTime = Date.now();
      const result = await service.analyzeFunnel(largeSteps, {}, 'tenant-1');
      const endTime = Date.now();

      expect(result.funnel_data).toHaveLength(20);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should ensure tenant isolation', async () => {
      service.getFunnelStepUsers = jest.fn().mockResolvedValue(['user1']);

      const steps = [
        { name: 'Step 1', event_type: 'step1' },
        { name: 'Step 2', event_type: 'step2' }
      ];

      await service.analyzeFunnel(steps, {}, 'tenant-1');

      // Verify all calls to getFunnelStepUsers include the correct tenant
      expect(service.getFunnelStepUsers).toHaveBeenCalledTimes(2);
      service.getFunnelStepUsers.mock.calls.forEach(call => {
        expect(call[2]).toBe('tenant-1'); // Third parameter should be tenantId
      });
    });

    it('should handle malformed step data', async () => {
      const malformedSteps = [
        { name: 'Valid Step', event_type: 'valid' },
        { name: null, event_type: 'invalid' }, // Missing name
        { name: 'Another Step' }, // Missing event_type
        { event_type: 'no_name' } // Missing name
      ];

      service.getFunnelStepUsers = jest.fn().mockResolvedValue([]);

      await expect(service.analyzeFunnel(malformedSteps, {}, 'tenant-1'))
        .rejects.toThrow();
    });
  });
});
