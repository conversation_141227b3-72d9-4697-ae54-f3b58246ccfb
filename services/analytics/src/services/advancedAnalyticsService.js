const logger = require('../utils/logger');
const { recordAnalyticsProcessing } = require('../metrics');

class AdvancedAnalyticsService {
  constructor() {
    this.cacheTTL = {
      short: 300,    // 5 minutes
      medium: 1800,  // 30 minutes
      long: 3600,    // 1 hour
      daily: 86400   // 24 hours
    };
  }

  get pool() {
    const { pool } = require('../database');
    return pool;
  }

  async getFromCache(key) {
    try {
      const redis = require('../redis');
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      logger.warn('Cache get failed', { key, error: error.message });
      return null;
    }
  }

  async setCache(key, data, ttl = this.cacheTTL.medium) {
    try {
      const redis = require('../redis');
      await redis.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      logger.warn('Cache set failed', { key, error: error.message });
    }
  }

  /**
   * Generate cohort analysis for customer retention
   */
  async generateCohortAnalysis(tenantId, options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        cohortType = 'acquisition',
        period = 'monthly',
        startDate,
        endDate,
        includeRevenue = true
      } = options;

      const cacheKey = `cohort_analysis:${tenantId}:${JSON.stringify(options)}`;
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('cohort_analysis', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        // Build cohort analysis query
        const cohortQuery = `
          WITH customer_cohorts AS (
            SELECT 
              customer_id,
              DATE_TRUNC('${period}', MIN(o.created_at)) as cohort_month,
              MIN(o.created_at) as first_purchase_date
            FROM orders o
            JOIN integrations i ON o.integration_id = i.id
            WHERE i.tenant_id = $1
              ${startDate ? 'AND o.created_at >= $2' : ''}
              ${endDate ? `AND o.created_at <= $${startDate ? '3' : '2'}` : ''}
            GROUP BY customer_id
          ),
          period_table AS (
            SELECT 
              cc.cohort_month,
              DATE_TRUNC('${period}', o.created_at) as period_month,
              EXTRACT(${period === 'monthly' ? 'month' : 'week'} FROM AGE(o.created_at, cc.first_purchase_date)) as period_number,
              COUNT(DISTINCT o.customer_id) as customers,
              ${includeRevenue ? 'SUM(o.total_amount) as revenue' : '0 as revenue'}
            FROM customer_cohorts cc
            JOIN orders o ON cc.customer_id = o.customer_id
            JOIN integrations i ON o.integration_id = i.id
            WHERE i.tenant_id = $1
            GROUP BY cc.cohort_month, period_month, period_number
          ),
          cohort_sizes AS (
            SELECT 
              cohort_month,
              COUNT(DISTINCT customer_id) as cohort_size
            FROM customer_cohorts
            GROUP BY cohort_month
          )
          SELECT 
            pt.cohort_month,
            pt.period_number,
            pt.customers,
            pt.revenue,
            cs.cohort_size,
            ROUND((pt.customers::decimal / cs.cohort_size::decimal) * 100, 2) as retention_rate
          FROM period_table pt
          JOIN cohort_sizes cs ON pt.cohort_month = cs.cohort_month
          ORDER BY pt.cohort_month, pt.period_number
        `;

        const params = [tenantId];
        if (startDate) params.push(startDate);
        if (endDate) params.push(endDate);

        const result = await client.query(cohortQuery, params);

        // Transform data into cohort table format
        const cohortData = this.transformCohortData(result.rows);

        await this.setCache(cacheKey, cohortData, this.cacheTTL.long);
        recordAnalyticsProcessing('cohort_analysis', 'success', (Date.now() - startTime) / 1000);
        
        return cohortData;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('cohort_analysis', 'error');
      logger.error('Failed to generate cohort analysis', error);
      throw error;
    }
  }

  /**
   * Calculate Customer Lifetime Value (CLV)
   */
  async calculateCustomerLifetimeValue(tenantId, options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        segmentBy = 'acquisition_month',
        includeProjected = true,
        timeHorizon = 12 // months
      } = options;

      const cacheKey = `clv_analysis:${tenantId}:${JSON.stringify(options)}`;
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('clv_analysis', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        // Calculate historical CLV
        const clvQuery = `
          WITH customer_metrics AS (
            SELECT 
              o.customer_id,
              DATE_TRUNC('month', MIN(o.created_at)) as acquisition_month,
              COUNT(*) as total_orders,
              SUM(o.total_amount) as total_revenue,
              AVG(o.total_amount) as avg_order_value,
              MAX(o.created_at) - MIN(o.created_at) as customer_lifespan,
              EXTRACT(days FROM MAX(o.created_at) - MIN(o.created_at)) / 30.0 as lifespan_months
            FROM orders o
            JOIN integrations i ON o.integration_id = i.id
            WHERE i.tenant_id = $1
              AND o.customer_id IS NOT NULL
            GROUP BY o.customer_id
          ),
          clv_segments AS (
            SELECT 
              acquisition_month,
              COUNT(*) as customer_count,
              AVG(total_revenue) as avg_clv,
              PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY total_revenue) as median_clv,
              AVG(avg_order_value) as avg_order_value,
              AVG(total_orders) as avg_orders_per_customer,
              AVG(lifespan_months) as avg_lifespan_months
            FROM customer_metrics
            WHERE lifespan_months > 0
            GROUP BY acquisition_month
          )
          SELECT 
            acquisition_month,
            customer_count,
            ROUND(avg_clv::numeric, 2) as avg_clv,
            ROUND(median_clv::numeric, 2) as median_clv,
            ROUND(avg_order_value::numeric, 2) as avg_order_value,
            ROUND(avg_orders_per_customer::numeric, 2) as avg_orders_per_customer,
            ROUND(avg_lifespan_months::numeric, 2) as avg_lifespan_months,
            CASE 
              WHEN avg_lifespan_months > 0 
              THEN ROUND((avg_clv / avg_lifespan_months)::numeric, 2)
              ELSE 0 
            END as monthly_value
          FROM clv_segments
          ORDER BY acquisition_month DESC
        `;

        const result = await client.query(clvQuery, [tenantId]);

        // Calculate projected CLV if requested
        let projectedCLV = null;
        if (includeProjected) {
          projectedCLV = await this.calculateProjectedCLV(client, tenantId, timeHorizon);
        }

        const clvData = {
          historical: result.rows,
          projected: projectedCLV,
          summary: this.calculateCLVSummary(result.rows),
          segments: await this.generateCLVSegments(client, tenantId)
        };

        await this.setCache(cacheKey, clvData, this.cacheTTL.long);
        recordAnalyticsProcessing('clv_analysis', 'success', (Date.now() - startTime) / 1000);
        
        return clvData;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('clv_analysis', 'error');
      logger.error('Failed to calculate CLV', error);
      throw error;
    }
  }

  /**
   * Generate funnel analysis for conversion tracking
   */
  async generateFunnelAnalysis(tenantId, options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        funnelSteps = ['click', 'visit', 'add_to_cart', 'purchase'],
        dateFrom,
        dateTo,
        segmentBy = null
      } = options;

      const cacheKey = `funnel_analysis:${tenantId}:${JSON.stringify(options)}`;
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('funnel_analysis', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        // Build funnel analysis query
        const funnelQuery = `
          WITH funnel_data AS (
            SELECT 
              c.tracking_id,
              c.clicked_at,
              c.country,
              c.device_type,
              l.short_code,
              CASE WHEN a.id IS NOT NULL THEN 1 ELSE 0 END as converted,
              o.total_amount
            FROM clicks c
            JOIN links l ON c.link_id = l.id
            LEFT JOIN attributions a ON c.id = a.click_id
            LEFT JOIN orders o ON a.order_id = o.id
            WHERE l.tenant_id = $1
              ${dateFrom ? 'AND c.clicked_at >= $2' : ''}
              ${dateTo ? `AND c.clicked_at <= $${dateFrom ? '3' : '2'}` : ''}
          )
          SELECT 
            COUNT(*) as total_clicks,
            COUNT(CASE WHEN converted = 1 THEN 1 END) as total_conversions,
            SUM(total_amount) as total_revenue,
            ROUND(
              (COUNT(CASE WHEN converted = 1 THEN 1 END)::decimal / COUNT(*)::decimal) * 100, 
              2
            ) as conversion_rate,
            ${segmentBy ? `${segmentBy},` : ''}
            COUNT(DISTINCT tracking_id) as unique_sessions
          FROM funnel_data
          ${segmentBy ? `GROUP BY ${segmentBy}` : ''}
          ORDER BY ${segmentBy ? `${segmentBy}` : 'total_clicks DESC'}
        `;

        const params = [tenantId];
        if (dateFrom) params.push(dateFrom);
        if (dateTo) params.push(dateTo);

        const result = await client.query(funnelQuery, params);

        const funnelData = {
          steps: this.buildFunnelSteps(result.rows, funnelSteps),
          summary: this.calculateFunnelSummary(result.rows),
          segments: segmentBy ? result.rows : null
        };

        await this.setCache(cacheKey, funnelData, this.cacheTTL.medium);
        recordAnalyticsProcessing('funnel_analysis', 'success', (Date.now() - startTime) / 1000);
        
        return funnelData;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('funnel_analysis', 'error');
      logger.error('Failed to generate funnel analysis', error);
      throw error;
    }
  }

  // Helper methods
  transformCohortData(rows) {
    const cohortTable = {};
    const cohortSizes = {};

    rows.forEach(row => {
      const cohortMonth = row.cohort_month;
      const periodNumber = parseInt(row.period_number);
      
      if (!cohortTable[cohortMonth]) {
        cohortTable[cohortMonth] = {};
        cohortSizes[cohortMonth] = row.cohort_size;
      }
      
      cohortTable[cohortMonth][periodNumber] = {
        customers: parseInt(row.customers),
        retention_rate: parseFloat(row.retention_rate),
        revenue: parseFloat(row.revenue) || 0
      };
    });

    return {
      cohortTable,
      cohortSizes,
      periods: Math.max(...rows.map(r => parseInt(r.period_number))) + 1
    };
  }

  async calculateProjectedCLV(client, tenantId, timeHorizon) {
    // Simplified projected CLV calculation
    // In production, this would use more sophisticated ML models
    const projectionQuery = `
      WITH recent_trends AS (
        SELECT 
          AVG(total_amount) as avg_order_value,
          COUNT(*) / COUNT(DISTINCT customer_id) as avg_orders_per_customer_per_month
        FROM orders o
        JOIN integrations i ON o.integration_id = i.id
        WHERE i.tenant_id = $1
          AND o.created_at >= NOW() - INTERVAL '3 months'
      )
      SELECT 
        avg_order_value * avg_orders_per_customer_per_month * $2 as projected_clv_12_months
      FROM recent_trends
    `;

    const result = await client.query(projectionQuery, [tenantId, timeHorizon]);
    return result.rows[0];
  }

  calculateCLVSummary(clvData) {
    if (!clvData.length) return null;

    const totalCustomers = clvData.reduce((sum, row) => sum + parseInt(row.customer_count), 0);
    const weightedAvgCLV = clvData.reduce((sum, row) => 
      sum + (parseFloat(row.avg_clv) * parseInt(row.customer_count)), 0) / totalCustomers;

    return {
      total_customers: totalCustomers,
      overall_avg_clv: Math.round(weightedAvgCLV * 100) / 100,
      highest_clv_month: clvData.reduce((max, row) => 
        parseFloat(row.avg_clv) > parseFloat(max.avg_clv) ? row : max, clvData[0])
    };
  }

  async generateCLVSegments(client, tenantId) {
    const segmentQuery = `
      WITH customer_clv AS (
        SELECT 
          customer_id,
          SUM(total_amount) as clv
        FROM orders o
        JOIN integrations i ON o.integration_id = i.id
        WHERE i.tenant_id = $1
        GROUP BY customer_id
      ),
      clv_percentiles AS (
        SELECT 
          PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY clv) as p25,
          PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY clv) as p50,
          PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY clv) as p75,
          PERCENTILE_CONT(0.90) WITHIN GROUP (ORDER BY clv) as p90
        FROM customer_clv
      )
      SELECT 
        CASE 
          WHEN clv >= p90 THEN 'Champions'
          WHEN clv >= p75 THEN 'Loyal Customers'
          WHEN clv >= p50 THEN 'Potential Loyalists'
          WHEN clv >= p25 THEN 'New Customers'
          ELSE 'At Risk'
        END as segment,
        COUNT(*) as customer_count,
        ROUND(AVG(clv)::numeric, 2) as avg_clv,
        ROUND(MIN(clv)::numeric, 2) as min_clv,
        ROUND(MAX(clv)::numeric, 2) as max_clv
      FROM customer_clv, clv_percentiles
      GROUP BY segment
      ORDER BY avg_clv DESC
    `;

    const result = await client.query(segmentQuery, [tenantId]);
    return result.rows;
  }

  buildFunnelSteps(data, stepNames) {
    if (!data.length) return [];

    const totalClicks = parseInt(data[0].total_clicks);
    const totalConversions = parseInt(data[0].total_conversions);

    return [
      {
        step: 'Clicks',
        count: totalClicks,
        conversion_rate: 100,
        drop_off: 0
      },
      {
        step: 'Conversions',
        count: totalConversions,
        conversion_rate: totalClicks > 0 ? (totalConversions / totalClicks * 100) : 0,
        drop_off: totalClicks - totalConversions
      }
    ];
  }

  calculateFunnelSummary(data) {
    if (!data.length) return null;

    const row = data[0];
    return {
      total_sessions: parseInt(row.unique_sessions),
      total_conversions: parseInt(row.total_conversions),
      total_revenue: parseFloat(row.total_revenue) || 0,
      overall_conversion_rate: parseFloat(row.conversion_rate),
      revenue_per_session: row.unique_sessions > 0 ? 
        (parseFloat(row.total_revenue) || 0) / parseInt(row.unique_sessions) : 0
    };
  }
}

module.exports = AdvancedAnalyticsService;
