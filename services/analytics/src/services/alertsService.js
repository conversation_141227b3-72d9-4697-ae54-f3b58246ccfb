const logger = require('../utils/logger');
const { recordAnalyticsProcessing } = require('../metrics');

class AlertsService {
  constructor() {
    this.alertCheckInterval = 60000; // Check every minute
    this.isRunning = false;
    this.alertCheckers = new Map();
  }

  get pool() {
    const { pool } = require('../database');
    return pool;
  }

  /**
   * Start the alert monitoring system
   */
  async startAlertMonitoring() {
    if (this.isRunning) {
      logger.warn('Alert monitoring is already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting alert monitoring system');

    // Start the main alert checking loop
    this.alertCheckLoop();
  }

  /**
   * Stop the alert monitoring system
   */
  stopAlertMonitoring() {
    this.isRunning = false;
    logger.info('Stopping alert monitoring system');
  }

  /**
   * Main alert checking loop
   */
  async alertCheckLoop() {
    while (this.isRunning) {
      try {
        await this.checkAllAlerts();
        await new Promise(resolve => setTimeout(resolve, this.alertCheckInterval));
      } catch (error) {
        logger.error('Error in alert check loop', error);
        await new Promise(resolve => setTimeout(resolve, this.alertCheckInterval));
      }
    }
  }

  /**
   * Check all active alert rules
   */
  async checkAllAlerts() {
    const startTime = Date.now();
    
    try {
      const client = await this.pool.connect();
      
      try {
        // Get all active alert rules
        const alertRulesQuery = `
          SELECT 
            id,
            tenant_id,
            rule_name,
            rule_type,
            metric_name,
            condition_operator,
            threshold_value,
            comparison_period,
            notification_channels
          FROM alert_rules
          WHERE is_active = true
        `;

        const result = await client.query(alertRulesQuery);
        const alertRules = result.rows;

        logger.debug(`Checking ${alertRules.length} alert rules`);

        // Check each alert rule
        for (const rule of alertRules) {
          await this.checkAlertRule(rule);
        }

        recordAnalyticsProcessing('alert_check', 'success', (Date.now() - startTime) / 1000);

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('alert_check', 'error');
      logger.error('Failed to check alerts', error);
    }
  }

  /**
   * Check a specific alert rule
   */
  async checkAlertRule(rule) {
    try {
      const metricValue = await this.getMetricValue(rule.tenant_id, rule.metric_name, rule.comparison_period);
      const shouldTrigger = this.evaluateAlertCondition(rule, metricValue);

      if (shouldTrigger) {
        await this.triggerAlert(rule, metricValue);
      }

    } catch (error) {
      logger.error(`Failed to check alert rule ${rule.id}`, error);
    }
  }

  /**
   * Get current metric value for alert evaluation
   */
  async getMetricValue(tenantId, metricName, comparisonPeriod) {
    const client = await this.pool.connect();
    
    try {
      let query;
      let timeFilter;

      // Determine time filter based on comparison period
      switch (comparisonPeriod) {
        case 'last_hour':
          timeFilter = "AND created_at >= NOW() - INTERVAL '1 hour'";
          break;
        case 'last_day':
          timeFilter = "AND created_at >= NOW() - INTERVAL '1 day'";
          break;
        case 'last_week':
          timeFilter = "AND created_at >= NOW() - INTERVAL '1 week'";
          break;
        default:
          timeFilter = "AND created_at >= NOW() - INTERVAL '1 hour'";
      }

      // Build query based on metric name
      switch (metricName) {
        case 'conversion_rate':
          query = `
            SELECT 
              CASE 
                WHEN COUNT(DISTINCT c.id) > 0 
                THEN (COUNT(DISTINCT a.id)::decimal / COUNT(DISTINCT c.id)::decimal) * 100
                ELSE 0 
              END as metric_value
            FROM clicks c
            JOIN links l ON c.link_id = l.id
            LEFT JOIN attributions a ON c.id = a.click_id
            WHERE l.tenant_id = $1 ${timeFilter}
          `;
          break;

        case 'revenue':
          query = `
            SELECT COALESCE(SUM(o.total_amount), 0) as metric_value
            FROM orders o
            JOIN integrations i ON o.integration_id = i.id
            WHERE i.tenant_id = $1 ${timeFilter}
          `;
          break;

        case 'clicks':
          query = `
            SELECT COUNT(*) as metric_value
            FROM clicks c
            JOIN links l ON c.link_id = l.id
            WHERE l.tenant_id = $1 ${timeFilter}
          `;
          break;

        case 'active_sessions':
          query = `
            SELECT COUNT(DISTINCT c.ip_address) as metric_value
            FROM clicks c
            JOIN links l ON c.link_id = l.id
            WHERE l.tenant_id = $1 ${timeFilter}
          `;
          break;

        default:
          throw new Error(`Unknown metric: ${metricName}`);
      }

      const result = await client.query(query, [tenantId]);
      return parseFloat(result.rows[0].metric_value) || 0;

    } finally {
      client.release();
    }
  }

  /**
   * Evaluate if alert condition is met
   */
  evaluateAlertCondition(rule, currentValue) {
    const threshold = parseFloat(rule.threshold_value);

    switch (rule.condition_operator) {
      case 'gt':
        return currentValue > threshold;
      case 'lt':
        return currentValue < threshold;
      case 'eq':
        return Math.abs(currentValue - threshold) < 0.01;
      case 'gte':
        return currentValue >= threshold;
      case 'lte':
        return currentValue <= threshold;
      default:
        return false;
    }
  }

  /**
   * Trigger an alert
   */
  async triggerAlert(rule, metricValue) {
    const client = await this.pool.connect();
    
    try {
      // Check if alert was recently triggered to avoid spam
      const recentAlertQuery = `
        SELECT id FROM alert_instances
        WHERE alert_rule_id = $1
          AND triggered_at >= NOW() - INTERVAL '1 hour'
          AND status = 'active'
        LIMIT 1
      `;

      const recentAlert = await client.query(recentAlertQuery, [rule.id]);
      
      if (recentAlert.rows.length > 0) {
        logger.debug(`Alert ${rule.id} recently triggered, skipping`);
        return;
      }

      // Create alert instance
      const severity = this.calculateSeverity(rule, metricValue);
      const message = this.generateAlertMessage(rule, metricValue);

      const insertAlertQuery = `
        INSERT INTO alert_instances (
          alert_rule_id,
          tenant_id,
          metric_value,
          threshold_value,
          severity,
          message,
          context_data
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id
      `;

      const contextData = {
        rule_name: rule.rule_name,
        metric_name: rule.metric_name,
        condition: rule.condition_operator,
        comparison_period: rule.comparison_period
      };

      const alertResult = await client.query(insertAlertQuery, [
        rule.id,
        rule.tenant_id,
        metricValue,
        rule.threshold_value,
        severity,
        message,
        JSON.stringify(contextData)
      ]);

      const alertId = alertResult.rows[0].id;

      logger.info(`Alert triggered: ${rule.rule_name}`, {
        alertId,
        tenantId: rule.tenant_id,
        metricValue,
        threshold: rule.threshold_value,
        severity
      });

      // Send notifications
      await this.sendAlertNotifications(rule, alertId, message, severity);

    } finally {
      client.release();
    }
  }

  /**
   * Calculate alert severity based on how much the metric exceeds threshold
   */
  calculateSeverity(rule, metricValue) {
    const threshold = parseFloat(rule.threshold_value);
    const difference = Math.abs(metricValue - threshold);
    const percentageDiff = threshold > 0 ? (difference / threshold) * 100 : 0;

    if (percentageDiff > 50) return 'critical';
    if (percentageDiff > 25) return 'high';
    if (percentageDiff > 10) return 'medium';
    return 'low';
  }

  /**
   * Generate alert message
   */
  generateAlertMessage(rule, metricValue) {
    const threshold = parseFloat(rule.threshold_value);
    const operator = rule.condition_operator;
    
    let operatorText;
    switch (operator) {
      case 'gt': operatorText = 'exceeded'; break;
      case 'lt': operatorText = 'dropped below'; break;
      case 'gte': operatorText = 'reached or exceeded'; break;
      case 'lte': operatorText = 'reached or dropped below'; break;
      default: operatorText = 'triggered';
    }

    return `${rule.rule_name}: ${rule.metric_name} has ${operatorText} the threshold of ${threshold}. Current value: ${metricValue.toFixed(2)}`;
  }

  /**
   * Send alert notifications through configured channels
   */
  async sendAlertNotifications(rule, alertId, message, severity) {
    const channels = rule.notification_channels || [];

    for (const channel of channels) {
      try {
        switch (channel) {
          case 'email':
            await this.sendEmailNotification(rule, message, severity);
            break;
          case 'webhook':
            await this.sendWebhookNotification(rule, alertId, message, severity);
            break;
          case 'dashboard':
            // Dashboard notifications are handled by real-time updates
            await this.sendDashboardNotification(rule, alertId, message, severity);
            break;
          default:
            logger.warn(`Unknown notification channel: ${channel}`);
        }
      } catch (error) {
        logger.error(`Failed to send ${channel} notification for alert ${alertId}`, error);
      }
    }
  }

  /**
   * Send email notification (placeholder - integrate with email service)
   */
  async sendEmailNotification(rule, message, severity) {
    logger.info(`Email notification: ${message}`, { severity, ruleName: rule.rule_name });
    // TODO: Integrate with email service (SendGrid, AWS SES, etc.)
  }

  /**
   * Send webhook notification
   */
  async sendWebhookNotification(rule, alertId, message, severity) {
    logger.info(`Webhook notification: ${message}`, { alertId, severity });
    // TODO: Implement webhook delivery
  }

  /**
   * Send dashboard notification via WebSocket
   */
  async sendDashboardNotification(rule, alertId, message, severity) {
    try {
      // For now, just log the notification
      // TODO: Integrate with WebSocket server when available
      logger.info('Dashboard notification sent', {
        tenantId: rule.tenant_id,
        alertId,
        message,
        severity
      });

    } catch (error) {
      logger.error('Failed to send dashboard notification', error);
    }
  }

  /**
   * Get active alerts for a tenant
   */
  async getActiveAlerts(tenantId, limit = 50) {
    const client = await this.pool.connect();
    
    try {
      const query = `
        SELECT 
          ai.id,
          ai.triggered_at,
          ai.severity,
          ai.message,
          ai.status,
          ar.rule_name,
          ar.metric_name
        FROM alert_instances ai
        JOIN alert_rules ar ON ai.alert_rule_id = ar.id
        WHERE ai.tenant_id = $1
          AND ai.status IN ('active', 'acknowledged')
        ORDER BY ai.triggered_at DESC
        LIMIT $2
      `;

      const result = await client.query(query, [tenantId, limit]);
      return result.rows;

    } finally {
      client.release();
    }
  }

  /**
   * Acknowledge an alert
   */
  async acknowledgeAlert(alertId, userId) {
    const client = await this.pool.connect();
    
    try {
      const query = `
        UPDATE alert_instances
        SET status = 'acknowledged',
            acknowledged_by = $2,
            acknowledged_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING *
      `;

      const result = await client.query(query, [alertId, userId]);
      return result.rows[0];

    } finally {
      client.release();
    }
  }
}

module.exports = AlertsService;
