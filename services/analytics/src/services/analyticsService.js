const logger = require('../utils/logger');
const redis = require('../redis');
const { recordAnalyticsProcessing } = require('../metrics');

class AnalyticsService {
  constructor() {
    this.cacheTTL = {
      short: parseInt(process.env.CACHE_TTL_SHORT) || 300,    // 5 minutes
      medium: parseInt(process.env.CACHE_TTL_MEDIUM) || 1800, // 30 minutes
      long: parseInt(process.env.CACHE_TTL_LONG) || 3600,     // 1 hour
    };
  }

  /**
   * Get database pool (lazy loaded)
   */
  get pool() {
    const { pool } = require('../database');
    return pool;
  }

  async getSummary(options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        dateFrom,
        dateTo,
        platform,
        linkId,
        integrationId,
        tenantId,
        useOptimized = true
      } = options;

      // Build cache key
      const cacheKey = `analytics:summary:${JSON.stringify(options)}`;
      
      // Try cache first
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('summary', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      // Use optimized queries for date ranges that can benefit from materialized views
      if (useOptimized && tenantId && this.canUseOptimizedQuery(dateFrom, dateTo)) {
        return await this.getOptimizedSummary(tenantId, dateFrom, dateTo, options);
      }

      const client = await this.pool.connect();

      try {
        // Build WHERE conditions
        const conditions = [];
        const params = [];
        let paramIndex = 1;

        if (dateFrom) {
          conditions.push(`c.clicked_at >= $${paramIndex}`);
          params.push(dateFrom);
          paramIndex++;
        }

        if (dateTo) {
          conditions.push(`c.clicked_at <= $${paramIndex}`);
          params.push(dateTo);
          paramIndex++;
        }

        if (platform) {
          conditions.push(`i.platform = $${paramIndex}`);
          params.push(platform);
          paramIndex++;
        }

        if (linkId) {
          conditions.push(`l.id = $${paramIndex}`);
          params.push(linkId);
          paramIndex++;
        }

        if (integrationId) {
          conditions.push(`i.id = $${paramIndex}`);
          params.push(integrationId);
          paramIndex++;
        }

        if (tenantId) {
          conditions.push(`l.tenant_id = $${paramIndex}`);
          params.push(tenantId);
          paramIndex++;
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        // Get summary metrics
        const summaryQuery = `
          SELECT 
            COUNT(DISTINCT c.id) as total_clicks,
            COUNT(DISTINCT a.id) as total_conversions,
            COUNT(DISTINCT l.id) as total_links,
            COALESCE(SUM(o.total_amount), 0) as total_revenue,
            COALESCE(AVG(o.total_amount), 0) as avg_order_value,
            CASE 
              WHEN COUNT(DISTINCT c.id) > 0 
              THEN (COUNT(DISTINCT a.id)::float / COUNT(DISTINCT c.id)::float * 100)
              ELSE 0 
            END as conversion_rate
          FROM clicks c
          LEFT JOIN links l ON c.link_id = l.id
          LEFT JOIN attributions a ON c.id = a.click_id
          LEFT JOIN orders o ON a.order_id = o.id
          LEFT JOIN integrations i ON o.integration_id = i.id
          ${whereClause}
        `;

        const summaryResult = await client.query(summaryQuery, params);
        const summary = summaryResult.rows[0];

        // Get platform breakdown
        const platformQuery = `
          SELECT 
            COALESCE(i.platform, 'unknown') as platform,
            COUNT(DISTINCT c.id) as clicks,
            COUNT(DISTINCT a.id) as conversions,
            COALESCE(SUM(o.total_amount), 0) as revenue
          FROM clicks c
          LEFT JOIN links l ON c.link_id = l.id
          LEFT JOIN attributions a ON c.id = a.click_id
          LEFT JOIN orders o ON a.order_id = o.id
          LEFT JOIN integrations i ON o.integration_id = i.id
          ${whereClause}
          GROUP BY i.platform
          ORDER BY revenue DESC
        `;

        const platformResult = await client.query(platformQuery, params);

        // Get daily trend for the last 30 days
        const trendQuery = `
          SELECT 
            DATE(c.clicked_at) as date,
            COUNT(DISTINCT c.id) as clicks,
            COUNT(DISTINCT a.id) as conversions,
            COALESCE(SUM(o.total_amount), 0) as revenue
          FROM clicks c
          LEFT JOIN attributions a ON c.id = a.click_id
          LEFT JOIN orders o ON a.order_id = o.id
          WHERE c.clicked_at >= NOW() - INTERVAL '30 days'
          GROUP BY DATE(c.clicked_at)
          ORDER BY date DESC
          LIMIT 30
        `;

        const trendResult = await client.query(trendQuery);

        const result = {
          summary: {
            total_clicks: parseInt(summary.total_clicks),
            total_conversions: parseInt(summary.total_conversions),
            total_links: parseInt(summary.total_links),
            total_revenue: parseFloat(summary.total_revenue),
            avg_order_value: parseFloat(summary.avg_order_value),
            conversion_rate: parseFloat(summary.conversion_rate)
          },
          platforms: platformResult.rows.map(row => ({
            platform: row.platform,
            clicks: parseInt(row.clicks),
            conversions: parseInt(row.conversions),
            revenue: parseFloat(row.revenue),
            conversion_rate: row.clicks > 0 ? (row.conversions / row.clicks * 100) : 0
          })),
          trend: trendResult.rows.map(row => ({
            date: row.date,
            clicks: parseInt(row.clicks),
            conversions: parseInt(row.conversions),
            revenue: parseFloat(row.revenue)
          }))
        };

        // Cache the result
        await this.setCache(cacheKey, result, this.cacheTTL.short);

        recordAnalyticsProcessing('summary', 'success', (Date.now() - startTime) / 1000);
        return result;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('summary', 'error');
      logger.error('Failed to get analytics summary', error);
      throw error;
    }
  }

  async getLinkAnalytics(linkId, options = {}) {
    const startTime = Date.now();
    
    try {
      const { dateFrom, dateTo, granularity = 'day' } = options;

      const cacheKey = `analytics:link:${linkId}:${JSON.stringify(options)}`;
      
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('link_analytics', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        // Get link details
        const linkQuery = `
          SELECT id, original_url, short_code, title, created_at
          FROM links 
          WHERE id = $1
        `;
        
        const linkResult = await client.query(linkQuery, [linkId]);
        
        if (linkResult.rows.length === 0) {
          throw new Error('Link not found');
        }

        const link = linkResult.rows[0];

        // Build time grouping based on granularity
        let timeGroup;
        switch (granularity) {
          case 'hour':
            timeGroup = "DATE_TRUNC('hour', c.clicked_at)";
            break;
          case 'day':
            timeGroup = "DATE_TRUNC('day', c.clicked_at)";
            break;
          case 'week':
            timeGroup = "DATE_TRUNC('week', c.clicked_at)";
            break;
          case 'month':
            timeGroup = "DATE_TRUNC('month', c.clicked_at)";
            break;
          default:
            timeGroup = "DATE_TRUNC('day', c.clicked_at)";
        }

        // Build WHERE conditions
        const conditions = [`c.link_id = $1`];
        const params = [linkId];
        let paramIndex = 2;

        if (dateFrom) {
          conditions.push(`c.clicked_at >= $${paramIndex}`);
          params.push(dateFrom);
          paramIndex++;
        }

        if (dateTo) {
          conditions.push(`c.clicked_at <= $${paramIndex}`);
          params.push(dateTo);
          paramIndex++;
        }

        const whereClause = `WHERE ${conditions.join(' AND ')}`;

        // Get time series data
        const timeSeriesQuery = `
          SELECT 
            ${timeGroup} as period,
            COUNT(DISTINCT c.id) as clicks,
            COUNT(DISTINCT a.id) as conversions,
            COALESCE(SUM(o.total_amount), 0) as revenue,
            COUNT(DISTINCT c.ip_address) as unique_visitors
          FROM clicks c
          LEFT JOIN attributions a ON c.id = a.click_id
          LEFT JOIN orders o ON a.order_id = o.id
          ${whereClause}
          GROUP BY ${timeGroup}
          ORDER BY period DESC
        `;

        const timeSeriesResult = await client.query(timeSeriesQuery, params);

        // Get geographic breakdown
        const geoQuery = `
          SELECT 
            COALESCE(c.country, 'Unknown') as country,
            COUNT(DISTINCT c.id) as clicks,
            COUNT(DISTINCT a.id) as conversions
          FROM clicks c
          LEFT JOIN attributions a ON c.id = a.click_id
          ${whereClause}
          GROUP BY c.country
          ORDER BY clicks DESC
          LIMIT 10
        `;

        const geoResult = await client.query(geoQuery, params);

        // Get device breakdown
        const deviceQuery = `
          SELECT 
            COALESCE(c.device_type, 'Unknown') as device_type,
            COUNT(DISTINCT c.id) as clicks,
            COUNT(DISTINCT a.id) as conversions
          FROM clicks c
          LEFT JOIN attributions a ON c.id = a.click_id
          ${whereClause}
          GROUP BY c.device_type
          ORDER BY clicks DESC
        `;

        const deviceResult = await client.query(deviceQuery, params);

        // Get referrer breakdown
        const referrerQuery = `
          SELECT 
            CASE 
              WHEN c.referrer IS NULL OR c.referrer = '' THEN 'Direct'
              WHEN c.referrer LIKE '%google%' THEN 'Google'
              WHEN c.referrer LIKE '%facebook%' THEN 'Facebook'
              WHEN c.referrer LIKE '%twitter%' THEN 'Twitter'
              WHEN c.referrer LIKE '%instagram%' THEN 'Instagram'
              ELSE 'Other'
            END as referrer_category,
            COUNT(DISTINCT c.id) as clicks,
            COUNT(DISTINCT a.id) as conversions
          FROM clicks c
          LEFT JOIN attributions a ON c.id = a.click_id
          ${whereClause}
          GROUP BY referrer_category
          ORDER BY clicks DESC
        `;

        const referrerResult = await client.query(referrerQuery, params);

        const result = {
          link: {
            id: link.id,
            original_url: link.original_url,
            short_code: link.short_code,
            title: link.title,
            created_at: link.created_at
          },
          time_series: timeSeriesResult.rows.map(row => ({
            period: row.period,
            clicks: parseInt(row.clicks),
            conversions: parseInt(row.conversions),
            revenue: parseFloat(row.revenue),
            unique_visitors: parseInt(row.unique_visitors),
            conversion_rate: row.clicks > 0 ? (row.conversions / row.clicks * 100) : 0
          })),
          geographic: geoResult.rows.map(row => ({
            country: row.country,
            clicks: parseInt(row.clicks),
            conversions: parseInt(row.conversions),
            conversion_rate: row.clicks > 0 ? (row.conversions / row.clicks * 100) : 0
          })),
          devices: deviceResult.rows.map(row => ({
            device_type: row.device_type,
            clicks: parseInt(row.clicks),
            conversions: parseInt(row.conversions),
            conversion_rate: row.clicks > 0 ? (row.conversions / row.clicks * 100) : 0
          })),
          referrers: referrerResult.rows.map(row => ({
            referrer_category: row.referrer_category,
            clicks: parseInt(row.clicks),
            conversions: parseInt(row.conversions),
            conversion_rate: row.clicks > 0 ? (row.conversions / row.clicks * 100) : 0
          }))
        };

        // Cache the result
        await this.setCache(cacheKey, result, this.cacheTTL.medium);

        recordAnalyticsProcessing('link_analytics', 'success', (Date.now() - startTime) / 1000);
        return result;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('link_analytics', 'error');
      logger.error('Failed to get link analytics', { linkId, error: error.message });
      throw error;
    }
  }

  async getAttribution(trackingId) {
    const startTime = Date.now();
    
    try {
      const cacheKey = `analytics:attribution:${trackingId}`;
      
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('attribution', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        const query = `
          SELECT 
            a.id,
            a.tracking_id,
            a.attribution_method,
            a.time_to_purchase,
            a.created_at,
            c.id as click_id,
            c.ip_address,
            c.user_agent,
            c.country,
            c.device_type,
            c.referrer,
            c.clicked_at as click_created_at,
            l.id as link_id,
            l.original_url,
            l.short_code,
            l.title as link_title,
            o.id as order_id,
            i.platform,
            o.platform_order_id,
            o.customer_email,
            o.total_amount,
            o.currency,
            o.status as order_status,
            o.created_at as order_created_at
          FROM attributions a
          JOIN clicks c ON a.click_id = c.id
          JOIN links l ON a.link_id = l.id
          JOIN orders o ON a.order_id = o.id
          WHERE a.tracking_id = $1
        `;

        const result = await client.query(query, [trackingId]);
        
        if (result.rows.length === 0) {
          return null;
        }

        const attribution = result.rows[0];
        
        const formattedResult = {
          id: attribution.id,
          tracking_id: attribution.tracking_id,
          attribution_method: attribution.attribution_method,
          time_to_purchase: attribution.time_to_purchase,
          created_at: attribution.created_at,
          click: {
            id: attribution.click_id,
            ip_address: attribution.ip_address,
            user_agent: attribution.user_agent,
            country: attribution.country,
            device_type: attribution.device_type,
            referrer: attribution.referrer,
            created_at: attribution.click_created_at
          },
          link: {
            id: attribution.link_id,
            original_url: attribution.original_url,
            short_code: attribution.short_code,
            title: attribution.link_title
          },
          order: {
            id: attribution.order_id,
            platform: attribution.platform,
            platform_order_id: attribution.platform_order_id,
            customer_email: attribution.customer_email,
            total_amount: parseFloat(attribution.total_amount),
            currency: attribution.currency,
            status: attribution.order_status,
            created_at: attribution.order_created_at
          }
        };

        // Cache the result
        await this.setCache(cacheKey, formattedResult, this.cacheTTL.long);

        recordAnalyticsProcessing('attribution', 'success', (Date.now() - startTime) / 1000);
        return formattedResult;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('attribution', 'error');
      logger.error('Failed to get attribution', { trackingId, error: error.message });
      throw error;
    }
  }

  // Helper methods for caching
  async getFromCache(key) {
    try {
      const data = await redis.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.warn('Cache get failed', { key, error: error.message });
      return null;
    }
  }

  async setCache(key, data, ttl) {
    try {
      await redis.setEx(key, ttl, JSON.stringify(data));
    } catch (error) {
      logger.warn('Cache set failed', { key, error: error.message });
    }
  }

  async getClickAnalytics(options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        linkId,
        dateFrom,
        dateTo,
        country,
        deviceType,
        limit = 100,
        offset = 0
      } = options;

      const cacheKey = `analytics:clicks:${JSON.stringify(options)}`;
      
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('click_analytics', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        const conditions = [];
        const params = [];
        let paramIndex = 1;

        if (linkId) {
          conditions.push(`c.link_id = $${paramIndex}`);
          params.push(linkId);
          paramIndex++;
        }

        if (dateFrom) {
          conditions.push(`c.clicked_at >= $${paramIndex}`);
          params.push(dateFrom);
          paramIndex++;
        }

        if (dateTo) {
          conditions.push(`c.clicked_at <= $${paramIndex}`);
          params.push(dateTo);
          paramIndex++;
        }

        if (country) {
          conditions.push(`c.country = $${paramIndex}`);
          params.push(country);
          paramIndex++;
        }

        if (deviceType) {
          conditions.push(`c.device_type = $${paramIndex}`);
          params.push(deviceType);
          paramIndex++;
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        // Get total count
        const countQuery = `
          SELECT COUNT(*) as total
          FROM clicks c
          LEFT JOIN links l ON c.link_id = l.id
          ${whereClause}
        `;

        const countResult = await client.query(countQuery, params);
        const total = parseInt(countResult.rows[0].total);

        // Get clicks with pagination
        const clicksQuery = `
          SELECT 
            c.id,
            c.link_id,
            c.ip_address,
            c.user_agent,
            c.referrer,
            c.country,
            c.city,
            c.device_type,
            c.browser,
            c.os,
            c.clicked_at,
            c.tracking_id,
            l.short_code,
            l.title as link_title,
            CASE WHEN a.id IS NOT NULL THEN true ELSE false END as converted
          FROM clicks c
          LEFT JOIN links l ON c.link_id = l.id
          LEFT JOIN attributions a ON c.id = a.click_id
          ${whereClause}
          ORDER BY c.clicked_at DESC
          LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
        `;

        params.push(limit, offset);
        const clicksResult = await client.query(clicksQuery, params);

        const result = {
          clicks: clicksResult.rows.map(row => ({
            id: row.id,
            link_id: row.link_id,
            ip_address: row.ip_address,
            user_agent: row.user_agent,
            referrer: row.referrer,
            country: row.country,
            city: row.city,
            device_type: row.device_type,
            browser: row.browser,
            os: row.os,
            clicked_at: row.clicked_at,
            tracking_id: row.tracking_id,
            link: {
              short_code: row.short_code,
              title: row.link_title
            },
            converted: row.converted
          })),
          pagination: {
            total,
            limit,
            offset,
            page: Math.floor(offset / limit) + 1
          }
        };

        await this.setCache(cacheKey, result, this.cacheTTL.short);

        recordAnalyticsProcessing('click_analytics', 'success', (Date.now() - startTime) / 1000);
        return result;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('click_analytics', 'error');
      logger.error('Failed to get click analytics', error);
      throw error;
    }
  }

  async getConversionAnalytics(options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        linkId,
        platform,
        dateFrom,
        dateTo,
        status,
        limit = 100,
        offset = 0
      } = options;

      const cacheKey = `analytics:conversions:${JSON.stringify(options)}`;
      
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('conversion_analytics', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        const conditions = [];
        const params = [];
        let paramIndex = 1;

        if (linkId) {
          conditions.push(`a.link_id = $${paramIndex}`);
          params.push(linkId);
          paramIndex++;
        }

        if (platform) {
          conditions.push(`i.platform = $${paramIndex}`);
          params.push(platform);
          paramIndex++;
        }

        if (dateFrom) {
          conditions.push(`a.created_at >= $${paramIndex}`);
          params.push(dateFrom);
          paramIndex++;
        }

        if (dateTo) {
          conditions.push(`a.created_at <= $${paramIndex}`);
          params.push(dateTo);
          paramIndex++;
        }

        if (status) {
          conditions.push(`o.status = $${paramIndex}`);
          params.push(status);
          paramIndex++;
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        // Get total count
        const countQuery = `
          SELECT COUNT(*) as total
          FROM attributions a
          LEFT JOIN orders o ON a.order_id = o.id
          LEFT JOIN integrations i ON o.integration_id = i.id
          ${whereClause}
        `;

        const countResult = await client.query(countQuery, params);
        const total = parseInt(countResult.rows[0].total);

        // Get conversions with pagination
        const conversionsQuery = `
          SELECT 
            a.id,
            a.tracking_id,
            a.attribution_method,
            a.time_to_purchase,
            a.created_at,
            o.id as order_id,
            o.platform_order_id,
            o.customer_email,
            o.total_amount,
            o.currency,
            o.status,
            o.created_at as order_created_at,
            l.short_code,
            l.title as link_title,
            i.platform,
            i.store_name
          FROM attributions a
          LEFT JOIN orders o ON a.order_id = o.id
          LEFT JOIN links l ON a.link_id = l.id
          LEFT JOIN integrations i ON o.integration_id = i.id
          ${whereClause}
          ORDER BY a.created_at DESC
          LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
        `;

        params.push(limit, offset);
        const conversionsResult = await client.query(conversionsQuery, params);

        const result = {
          conversions: conversionsResult.rows.map(row => ({
            id: row.id,
            tracking_id: row.tracking_id,
            attribution_method: row.attribution_method,
            time_to_purchase: row.time_to_purchase,
            created_at: row.created_at,
            order: {
              id: row.order_id,
              platform_order_id: row.platform_order_id,
              customer_email: row.customer_email,
              total_amount: parseFloat(row.total_amount),
              currency: row.currency,
              status: row.status,
              created_at: row.order_created_at
            },
            link: {
              short_code: row.short_code,
              title: row.link_title
            },
            integration: {
              platform: row.platform,
              store_name: row.store_name
            }
          })),
          pagination: {
            total,
            limit,
            offset,
            page: Math.floor(offset / limit) + 1
          }
        };

        await this.setCache(cacheKey, result, this.cacheTTL.short);

        recordAnalyticsProcessing('conversion_analytics', 'success', (Date.now() - startTime) / 1000);
        return result;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('conversion_analytics', 'error');
      logger.error('Failed to get conversion analytics', error);
      throw error;
    }
  }

  async getTopPerformers(options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        metric = 'clicks',
        dateFrom,
        dateTo,
        limit = 10
      } = options;

      const cacheKey = `analytics:top-performers:${JSON.stringify(options)}`;
      
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('top_performers', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        const conditions = [];
        const params = [];
        let paramIndex = 1;

        if (dateFrom) {
          conditions.push(`c.clicked_at >= $${paramIndex}`);
          params.push(dateFrom);
          paramIndex++;
        }

        if (dateTo) {
          conditions.push(`c.clicked_at <= $${paramIndex}`);
          params.push(dateTo);
          paramIndex++;
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        let orderBy;
        switch (metric) {
          case 'clicks':
            orderBy = 'total_clicks DESC';
            break;
          case 'conversions':
            orderBy = 'total_conversions DESC';
            break;
          case 'revenue':
            orderBy = 'total_revenue DESC';
            break;
          case 'conversion_rate':
            orderBy = 'conversion_rate DESC';
            break;
          default:
            orderBy = 'total_clicks DESC';
        }

        const query = `
          SELECT 
            l.id,
            l.short_code,
            l.title,
            l.target_url,
            COUNT(DISTINCT c.id) as total_clicks,
            COUNT(DISTINCT a.id) as total_conversions,
            COALESCE(SUM(o.total_amount), 0) as total_revenue,
            CASE 
              WHEN COUNT(DISTINCT c.id) > 0 
              THEN (COUNT(DISTINCT a.id)::float / COUNT(DISTINCT c.id)::float * 100)
              ELSE 0 
            END as conversion_rate
          FROM links l
          LEFT JOIN clicks c ON l.id = c.link_id
          LEFT JOIN attributions a ON c.id = a.click_id
          LEFT JOIN orders o ON a.order_id = o.id
          ${whereClause}
          GROUP BY l.id, l.short_code, l.title, l.target_url
          HAVING COUNT(DISTINCT c.id) > 0
          ORDER BY ${orderBy}
          LIMIT $${paramIndex}
        `;

        params.push(limit);
        const result = await client.query(query, params);

        const topPerformers = result.rows.map(row => ({
          id: row.id,
          short_code: row.short_code,
          title: row.title,
          target_url: row.target_url,
          metrics: {
            total_clicks: parseInt(row.total_clicks),
            total_conversions: parseInt(row.total_conversions),
            total_revenue: parseFloat(row.total_revenue),
            conversion_rate: parseFloat(row.conversion_rate)
          }
        }));

        await this.setCache(cacheKey, topPerformers, this.cacheTTL.medium);

        recordAnalyticsProcessing('top_performers', 'success', (Date.now() - startTime) / 1000);
        return topPerformers;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('top_performers', 'error');
      logger.error('Failed to get top performers', error);
      throw error;
    }
  }

  async getGeographicAnalytics(options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        linkId,
        dateFrom,
        dateTo,
        level = 'country'
      } = options;

      const cacheKey = `analytics:geographic:${JSON.stringify(options)}`;
      
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('geographic_analytics', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        const conditions = [];
        const params = [];
        let paramIndex = 1;

        if (linkId) {
          conditions.push(`c.link_id = $${paramIndex}`);
          params.push(linkId);
          paramIndex++;
        }

        if (dateFrom) {
          conditions.push(`c.clicked_at >= $${paramIndex}`);
          params.push(dateFrom);
          paramIndex++;
        }

        if (dateTo) {
          conditions.push(`c.clicked_at <= $${paramIndex}`);
          params.push(dateTo);
          paramIndex++;
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        const groupByField = level === 'city' ? 'c.city' : 'c.country';
        const selectField = level === 'city' ? 'c.city as location, c.country' : 'c.country as location';

        const query = `
          SELECT 
            ${selectField},
            COUNT(DISTINCT c.id) as clicks,
            COUNT(DISTINCT a.id) as conversions,
            COALESCE(SUM(o.total_amount), 0) as revenue,
            COUNT(DISTINCT c.ip_address) as unique_visitors
          FROM clicks c
          LEFT JOIN attributions a ON c.id = a.click_id
          LEFT JOIN orders o ON a.order_id = o.id
          ${whereClause}
          GROUP BY ${groupByField}${level === 'city' ? ', c.country' : ''}
          ORDER BY clicks DESC
          LIMIT 50
        `;

        const result = await client.query(query, params);

        const geographic = result.rows.map(row => ({
          location: row.location || 'Unknown',
          country: level === 'city' ? row.country : row.location,
          clicks: parseInt(row.clicks),
          conversions: parseInt(row.conversions),
          revenue: parseFloat(row.revenue),
          unique_visitors: parseInt(row.unique_visitors),
          conversion_rate: row.clicks > 0 ? (row.conversions / row.clicks * 100) : 0
        }));

        await this.setCache(cacheKey, geographic, this.cacheTTL.medium);

        recordAnalyticsProcessing('geographic_analytics', 'success', (Date.now() - startTime) / 1000);
        return geographic;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('geographic_analytics', 'error');
      logger.error('Failed to get geographic analytics', error);
      throw error;
    }
  }

  async getTimeSeries(options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        metric = 'clicks',
        granularity = 'day',
        dateFrom,
        dateTo,
        linkId,
        platform
      } = options;

      const cacheKey = `analytics:time-series:${JSON.stringify(options)}`;
      
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('time_series', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        // Build time grouping based on granularity
        let timeGroup;
        switch (granularity) {
          case 'hour':
            timeGroup = "DATE_TRUNC('hour', c.clicked_at)";
            break;
          case 'day':
            timeGroup = "DATE_TRUNC('day', c.clicked_at)";
            break;
          case 'week':
            timeGroup = "DATE_TRUNC('week', c.clicked_at)";
            break;
          case 'month':
            timeGroup = "DATE_TRUNC('month', c.clicked_at)";
            break;
          default:
            timeGroup = "DATE_TRUNC('day', c.clicked_at)";
        }

        const conditions = [];
        const params = [];
        let paramIndex = 1;

        if (dateFrom) {
          conditions.push(`c.clicked_at >= $${paramIndex}`);
          params.push(dateFrom);
          paramIndex++;
        }

        if (dateTo) {
          conditions.push(`c.clicked_at <= $${paramIndex}`);
          params.push(dateTo);
          paramIndex++;
        }

        if (linkId) {
          conditions.push(`c.link_id = $${paramIndex}`);
          params.push(linkId);
          paramIndex++;
        }

        if (platform) {
          conditions.push(`i.platform = $${paramIndex}`);
          params.push(platform);
          paramIndex++;
        }

        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

        const query = `
          SELECT 
            ${timeGroup} as period,
            COUNT(DISTINCT c.id) as clicks,
            COUNT(DISTINCT a.id) as conversions,
            COALESCE(SUM(o.total_amount), 0) as revenue,
            COUNT(DISTINCT c.ip_address) as unique_visitors
          FROM clicks c
          LEFT JOIN attributions a ON c.id = a.click_id
          LEFT JOIN orders o ON a.order_id = o.id
          LEFT JOIN integrations i ON o.integration_id = i.id
          ${whereClause}
          GROUP BY ${timeGroup}
          ORDER BY period ASC
        `;

        const result = await client.query(query, params);

        const timeSeries = result.rows.map(row => ({
          period: row.period,
          clicks: parseInt(row.clicks),
          conversions: parseInt(row.conversions),
          revenue: parseFloat(row.revenue),
          unique_visitors: parseInt(row.unique_visitors),
          conversion_rate: row.clicks > 0 ? (row.conversions / row.clicks * 100) : 0
        }));

        await this.setCache(cacheKey, timeSeries, this.cacheTTL.medium);

        recordAnalyticsProcessing('time_series', 'success', (Date.now() - startTime) / 1000);
        return timeSeries;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('time_series', 'error');
      logger.error('Failed to get time series', error);
      throw error;
    }
  }

  /**
   * Get real-time metrics for live dashboard
   */
  async getLiveMetrics(tenantId) {
    const startTime = Date.now();

    try {
      const cacheKey = `live_metrics:${tenantId}`;

      // Try cache first (short TTL for live data)
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('live_metrics', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();

      try {
        const now = new Date();
        const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        // Get metrics for the last minute
        const lastMinuteQuery = `
          SELECT
            COUNT(DISTINCT c.id) as clicks_last_minute,
            COUNT(DISTINCT a.id) as conversions_last_minute,
            COALESCE(SUM(o.total_amount), 0) as revenue_last_minute
          FROM clicks c
          LEFT JOIN links l ON c.link_id = l.id
          LEFT JOIN attributions a ON c.id = a.click_id
          LEFT JOIN orders o ON a.order_id = o.id
          WHERE l.tenant_id = $1
            AND c.clicked_at >= $2
        `;

        // Get active sessions (unique IPs in last hour)
        const activeSessionsQuery = `
          SELECT COUNT(DISTINCT c.ip_address) as active_sessions
          FROM clicks c
          LEFT JOIN links l ON c.link_id = l.id
          WHERE l.tenant_id = $1
            AND c.clicked_at >= $2
        `;

        // Get recent clicks for activity feed
        const recentClicksQuery = `
          SELECT
            c.id,
            c.clicked_at as timestamp,
            l.id as link_id,
            l.short_code,
            c.country,
            c.device_type,
            CASE WHEN a.id IS NOT NULL THEN true ELSE false END as converted
          FROM clicks c
          LEFT JOIN links l ON c.link_id = l.id
          LEFT JOIN attributions a ON c.id = a.click_id
          WHERE l.tenant_id = $1
            AND c.clicked_at >= $2
          ORDER BY c.clicked_at DESC
          LIMIT 5
        `;

        // Get trend data (compare last hour vs previous hour)
        const trendQuery = `
          WITH current_hour AS (
            SELECT
              COUNT(DISTINCT c.id) as clicks,
              COUNT(DISTINCT a.id) as conversions,
              COALESCE(SUM(o.total_amount), 0) as revenue
            FROM clicks c
            LEFT JOIN links l ON c.link_id = l.id
            LEFT JOIN attributions a ON c.id = a.click_id
            LEFT JOIN orders o ON a.order_id = o.id
            WHERE l.tenant_id = $1
              AND c.clicked_at >= $2
          ),
          previous_hour AS (
            SELECT
              COUNT(DISTINCT c.id) as clicks,
              COUNT(DISTINCT a.id) as conversions,
              COALESCE(SUM(o.total_amount), 0) as revenue
            FROM clicks c
            LEFT JOIN links l ON c.link_id = l.id
            LEFT JOIN attributions a ON c.id = a.click_id
            LEFT JOIN orders o ON a.order_id = o.id
            WHERE l.tenant_id = $1
              AND c.clicked_at >= $3
              AND c.clicked_at < $2
          )
          SELECT
            current_hour.clicks as current_clicks,
            previous_hour.clicks as previous_clicks,
            current_hour.conversions as current_conversions,
            previous_hour.conversions as previous_conversions,
            current_hour.revenue as current_revenue,
            previous_hour.revenue as previous_revenue
          FROM current_hour, previous_hour
        `;

        // Execute all queries in parallel
        const [lastMinuteResult, activeSessionsResult, recentClicksResult, trendResult] = await Promise.all([
          client.query(lastMinuteQuery, [tenantId, oneMinuteAgo]),
          client.query(activeSessionsQuery, [tenantId, oneHourAgo]),
          client.query(recentClicksQuery, [tenantId, oneHourAgo]),
          client.query(trendQuery, [tenantId, oneHourAgo, new Date(oneHourAgo.getTime() - 60 * 60 * 1000)])
        ]);

        const lastMinute = lastMinuteResult.rows[0];
        const activeSessions = activeSessionsResult.rows[0];
        const recentClicks = recentClicksResult.rows;
        const trend = trendResult.rows[0] || {};

        // Calculate trends
        const calculateTrend = (current, previous) => {
          if (previous === 0) return current > 0 ? 'up' : 'stable';
          const change = ((current - previous) / previous) * 100;
          if (change > 5) return 'up';
          if (change < -5) return 'down';
          return 'stable';
        };

        const liveMetrics = {
          metrics: {
            active_sessions: parseInt(activeSessions.active_sessions) || 0,
            clicks_last_minute: parseInt(lastMinute.clicks_last_minute) || 0,
            conversions_last_minute: parseInt(lastMinute.conversions_last_minute) || 0,
            revenue_last_minute: parseFloat(lastMinute.revenue_last_minute) || 0
          },
          trends: {
            clicks_trend: calculateTrend(trend.current_clicks || 0, trend.previous_clicks || 0),
            conversions_trend: calculateTrend(trend.current_conversions || 0, trend.previous_conversions || 0),
            revenue_trend: calculateTrend(trend.current_revenue || 0, trend.previous_revenue || 0)
          },
          recentClicks: recentClicks.map(click => ({
            timestamp: click.timestamp,
            link_id: click.link_id,
            short_code: click.short_code,
            country: click.country || 'Unknown',
            device: click.device_type || 'Unknown',
            converted: click.converted
          })),
          lastUpdate: now.toISOString()
        };

        // Cache for 30 seconds (live data should be fresh)
        await this.setCache(cacheKey, liveMetrics, 30);

        recordAnalyticsProcessing('live_metrics', 'success', (Date.now() - startTime) / 1000);
        return liveMetrics;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('live_metrics', 'error');
      logger.error('Failed to get live metrics', error);
      throw error;
    }
  }

  /**
   * Check if we can use optimized queries (materialized views)
   */
  canUseOptimizedQuery(dateFrom, dateTo) {
    if (!dateFrom || !dateTo) return false;

    const fromDate = new Date(dateFrom);
    const toDate = new Date(dateTo);
    const daysDiff = (toDate - fromDate) / (1000 * 60 * 60 * 24);

    // Use optimized queries for date ranges >= 1 day and <= 90 days
    return daysDiff >= 1 && daysDiff <= 90;
  }

  /**
   * Get optimized summary using materialized views
   */
  async getOptimizedSummary(tenantId, dateFrom, dateTo, options = {}) {
    const startTime = Date.now();

    try {
      const client = await this.pool.connect();

      try {
        // Use the optimized function from the database
        const summaryQuery = `
          SELECT * FROM get_optimized_analytics($1, $2, $3)
        `;

        const summaryResult = await client.query(summaryQuery, [
          tenantId,
          dateFrom,
          dateTo
        ]);

        // Get platform breakdown from daily summary
        const platformQuery = `
          SELECT
            'aggregated' as platform,
            SUM(das.total_clicks) as clicks,
            SUM(das.total_conversions) as conversions,
            SUM(das.total_revenue) as revenue
          FROM daily_analytics_summary das
          WHERE das.tenant_id = $1
            AND das.date >= $2
            AND das.date <= $3
          GROUP BY 'aggregated'
        `;

        const platformResult = await client.query(platformQuery, [
          tenantId,
          dateFrom,
          dateTo
        ]);

        // Calculate summary metrics
        const totalClicks = summaryResult.rows.reduce((sum, row) => sum + parseInt(row.total_clicks), 0);
        const totalConversions = summaryResult.rows.reduce((sum, row) => sum + parseInt(row.total_conversions), 0);
        const totalRevenue = summaryResult.rows.reduce((sum, row) => sum + parseFloat(row.total_revenue), 0);

        const summary = {
          total_clicks: totalClicks,
          total_conversions: totalConversions,
          total_links: await this.getTotalLinks(tenantId),
          total_revenue: totalRevenue,
          avg_order_value: totalConversions > 0 ? totalRevenue / totalConversions : 0,
          conversion_rate: totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0
        };

        const result = {
          summary,
          trend: summaryResult.rows.map(row => ({
            date: row.date,
            clicks: parseInt(row.total_clicks),
            conversions: parseInt(row.total_conversions),
            revenue: parseFloat(row.total_revenue),
            conversion_rate: parseFloat(row.conversion_rate)
          })),
          platforms: platformResult.rows.map(row => ({
            platform: row.platform,
            clicks: parseInt(row.clicks),
            conversions: parseInt(row.conversions),
            revenue: parseFloat(row.revenue)
          }))
        };

        recordAnalyticsProcessing('optimized_summary', 'success', (Date.now() - startTime) / 1000);
        return result;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('optimized_summary', 'error');
      logger.error('Failed to get optimized summary', error);
      // Fallback to regular summary
      return await this.getSummary({ ...options, useOptimized: false });
    }
  }

  /**
   * Get total links count for tenant
   */
  async getTotalLinks(tenantId) {
    const client = await this.pool.connect();

    try {
      const query = `
        SELECT COUNT(*) as total
        FROM links
        WHERE tenant_id = $1 AND is_active = true
      `;

      const result = await client.query(query, [tenantId]);
      return parseInt(result.rows[0].total) || 0;

    } finally {
      client.release();
    }
  }
}

module.exports = AnalyticsService;