const logger = require('../utils/logger');
const { recordAnalyticsProcessing } = require('../metrics');

class DashboardWidgetsService {
  constructor() {
    this.cacheTTL = {
      widget_data: 300,    // 5 minutes
      layout_config: 1800, // 30 minutes
    };
  }

  get pool() {
    const { pool } = require('../database');
    return pool;
  }

  async getFromCache(key) {
    try {
      const redis = require('../redis');
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      logger.warn('Cache get failed', { key, error: error.message });
      return null;
    }
  }

  async setCache(key, data, ttl = this.cacheTTL.widget_data) {
    try {
      const redis = require('../redis');
      await redis.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      logger.warn('Cache set failed', { key, error: error.message });
    }
  }

  /**
   * Get dashboard layout for a user/tenant
   */
  async getDashboardLayout(tenantId, userId = null, layoutId = null) {
    const startTime = Date.now();
    
    try {
      const cacheKey = `dashboard_layout:${tenantId}:${userId || 'default'}:${layoutId || 'default'}`;
      const cached = await this.getFromCache(cacheKey);
      if (cached) {
        recordAnalyticsProcessing('dashboard_layout', 'cache_hit', (Date.now() - startTime) / 1000);
        return cached;
      }

      const client = await this.pool.connect();
      
      try {
        let layoutQuery;
        let params;

        if (layoutId) {
          // Get specific layout
          layoutQuery = `
            SELECT * FROM dashboard_layouts
            WHERE id = $1 AND tenant_id = $2
          `;
          params = [layoutId, tenantId];
        } else {
          // Get default layout for user or tenant
          layoutQuery = `
            SELECT * FROM dashboard_layouts
            WHERE tenant_id = $1 
              AND (user_id = $2 OR user_id IS NULL)
              AND is_default = true
            ORDER BY user_id NULLS LAST
            LIMIT 1
          `;
          params = [tenantId, userId];
        }

        const layoutResult = await client.query(layoutQuery, params);
        
        if (layoutResult.rows.length === 0) {
          // Create default layout if none exists
          return await this.createDefaultLayout(tenantId, userId);
        }

        const layout = layoutResult.rows[0];

        // Get widgets for this layout
        const widgetsQuery = `
          SELECT * FROM dashboard_widgets
          WHERE tenant_id = $1 
            AND (user_id = $2 OR user_id IS NULL)
            AND is_active = true
          ORDER BY position_y, position_x
        `;

        const widgetsResult = await client.query(widgetsQuery, [tenantId, userId]);

        const dashboardData = {
          layout: layout,
          widgets: widgetsResult.rows,
          widgetData: await this.getWidgetData(tenantId, widgetsResult.rows)
        };

        await this.setCache(cacheKey, dashboardData, this.cacheTTL.layout_config);
        recordAnalyticsProcessing('dashboard_layout', 'success', (Date.now() - startTime) / 1000);
        
        return dashboardData;

      } finally {
        client.release();
      }

    } catch (error) {
      recordAnalyticsProcessing('dashboard_layout', 'error');
      logger.error('Failed to get dashboard layout', error);
      throw error;
    }
  }

  /**
   * Get data for all widgets
   */
  async getWidgetData(tenantId, widgets) {
    const widgetData = {};

    for (const widget of widgets) {
      try {
        const data = await this.getIndividualWidgetData(tenantId, widget);
        widgetData[widget.id] = data;
      } catch (error) {
        logger.error(`Failed to get data for widget ${widget.id}`, error);
        widgetData[widget.id] = { error: 'Failed to load widget data' };
      }
    }

    return widgetData;
  }

  /**
   * Get data for a specific widget
   */
  async getIndividualWidgetData(tenantId, widget) {
    const config = widget.widget_config;
    const client = await this.pool.connect();

    try {
      switch (widget.widget_type) {
        case 'metric_card':
          return await this.getMetricCardData(client, tenantId, config);
        
        case 'chart':
          return await this.getChartData(client, tenantId, config);
        
        case 'table':
          return await this.getTableData(client, tenantId, config);
        
        case 'funnel':
          return await this.getFunnelData(client, tenantId, config);
        
        default:
          throw new Error(`Unknown widget type: ${widget.widget_type}`);
      }

    } finally {
      client.release();
    }
  }

  /**
   * Get metric card data
   */
  async getMetricCardData(client, tenantId, config) {
    const { metric, period = 'today', format = 'number' } = config;
    
    let timeFilter;
    switch (period) {
      case 'today':
        timeFilter = "AND DATE(created_at) = CURRENT_DATE";
        break;
      case 'yesterday':
        timeFilter = "AND DATE(created_at) = CURRENT_DATE - INTERVAL '1 day'";
        break;
      case 'last_7_days':
        timeFilter = "AND created_at >= CURRENT_DATE - INTERVAL '7 days'";
        break;
      case 'last_30_days':
        timeFilter = "AND created_at >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      default:
        timeFilter = "AND DATE(created_at) = CURRENT_DATE";
    }

    let query;
    switch (metric) {
      case 'total_revenue':
        query = `
          SELECT COALESCE(SUM(o.total_amount), 0) as value
          FROM orders o
          JOIN integrations i ON o.integration_id = i.id
          WHERE i.tenant_id = $1 ${timeFilter}
        `;
        break;

      case 'total_clicks':
        query = `
          SELECT COUNT(*) as value
          FROM clicks c
          JOIN links l ON c.link_id = l.id
          WHERE l.tenant_id = $1 ${timeFilter}
        `;
        break;

      case 'conversion_rate':
        query = `
          SELECT 
            CASE 
              WHEN COUNT(DISTINCT c.id) > 0 
              THEN (COUNT(DISTINCT a.id)::decimal / COUNT(DISTINCT c.id)::decimal) * 100
              ELSE 0 
            END as value
          FROM clicks c
          JOIN links l ON c.link_id = l.id
          LEFT JOIN attributions a ON c.id = a.click_id
          WHERE l.tenant_id = $1 ${timeFilter}
        `;
        break;

      case 'active_links':
        query = `
          SELECT COUNT(*) as value
          FROM links
          WHERE tenant_id = $1 AND is_active = true
        `;
        break;

      default:
        throw new Error(`Unknown metric: ${metric}`);
    }

    const result = await client.query(query, [tenantId]);
    const value = parseFloat(result.rows[0].value) || 0;

    return {
      value,
      formatted: this.formatValue(value, format),
      metric,
      period
    };
  }

  /**
   * Get chart data
   */
  async getChartData(client, tenantId, config) {
    const { metric, chart_type = 'line', period = 'last_30_days' } = config;
    
    let dateGrouping;
    let dateFilter;
    
    switch (period) {
      case 'last_7_days':
        dateGrouping = "DATE(created_at)";
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '7 days'";
        break;
      case 'last_30_days':
        dateGrouping = "DATE(created_at)";
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      case 'last_12_months':
        dateGrouping = "DATE_TRUNC('month', created_at)";
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '12 months'";
        break;
      default:
        dateGrouping = "DATE(created_at)";
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '30 days'";
    }

    let query;
    switch (metric) {
      case 'revenue':
        query = `
          SELECT 
            ${dateGrouping} as date,
            COALESCE(SUM(o.total_amount), 0) as value
          FROM orders o
          JOIN integrations i ON o.integration_id = i.id
          WHERE i.tenant_id = $1 ${dateFilter}
          GROUP BY ${dateGrouping}
          ORDER BY date
        `;
        break;

      case 'clicks':
        query = `
          SELECT 
            ${dateGrouping} as date,
            COUNT(*) as value
          FROM clicks c
          JOIN links l ON c.link_id = l.id
          WHERE l.tenant_id = $1 ${dateFilter}
          GROUP BY ${dateGrouping}
          ORDER BY date
        `;
        break;

      case 'conversions':
        query = `
          SELECT 
            ${dateGrouping} as date,
            COUNT(DISTINCT a.id) as value
          FROM attributions a
          JOIN links l ON a.link_id = l.id
          WHERE l.tenant_id = $1 ${dateFilter}
          GROUP BY ${dateGrouping}
          ORDER BY date
        `;
        break;

      default:
        throw new Error(`Unknown chart metric: ${metric}`);
    }

    const result = await client.query(query, [tenantId]);
    
    return {
      chartType: chart_type,
      data: result.rows.map(row => ({
        date: row.date,
        value: parseFloat(row.value) || 0
      })),
      metric,
      period
    };
  }

  /**
   * Get table data
   */
  async getTableData(client, tenantId, config) {
    const { metric, limit = 10, sort_by = 'value' } = config;

    let query;
    switch (metric) {
      case 'link_performance':
        query = `
          SELECT 
            l.title,
            l.short_code,
            COUNT(DISTINCT c.id) as clicks,
            COUNT(DISTINCT a.id) as conversions,
            COALESCE(SUM(o.total_amount), 0) as revenue
          FROM links l
          LEFT JOIN clicks c ON l.id = c.link_id
          LEFT JOIN attributions a ON c.id = a.click_id
          LEFT JOIN orders o ON a.order_id = o.id
          WHERE l.tenant_id = $1
          GROUP BY l.id, l.title, l.short_code
          ORDER BY ${sort_by === 'revenue' ? 'revenue' : 'clicks'} DESC
          LIMIT $2
        `;
        break;

      case 'top_countries':
        query = `
          SELECT 
            c.country,
            COUNT(*) as clicks,
            COUNT(DISTINCT a.id) as conversions
          FROM clicks c
          JOIN links l ON c.link_id = l.id
          LEFT JOIN attributions a ON c.id = a.click_id
          WHERE l.tenant_id = $1 AND c.country IS NOT NULL
          GROUP BY c.country
          ORDER BY clicks DESC
          LIMIT $2
        `;
        break;

      default:
        throw new Error(`Unknown table metric: ${metric}`);
    }

    const result = await client.query(query, [tenantId, limit]);
    
    return {
      columns: this.getTableColumns(metric),
      rows: result.rows,
      metric,
      totalRows: result.rows.length
    };
  }

  /**
   * Get funnel data
   */
  async getFunnelData(client, tenantId, config) {
    const { steps = ['clicks', 'conversions'], period = 'last_30_days' } = config;
    
    let dateFilter;
    switch (period) {
      case 'last_7_days':
        dateFilter = "AND c.clicked_at >= CURRENT_DATE - INTERVAL '7 days'";
        break;
      case 'last_30_days':
        dateFilter = "AND c.clicked_at >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      default:
        dateFilter = "AND c.clicked_at >= CURRENT_DATE - INTERVAL '30 days'";
    }

    const query = `
      SELECT 
        COUNT(DISTINCT c.id) as total_clicks,
        COUNT(DISTINCT a.id) as total_conversions
      FROM clicks c
      JOIN links l ON c.link_id = l.id
      LEFT JOIN attributions a ON c.id = a.click_id
      WHERE l.tenant_id = $1 ${dateFilter}
    `;

    const result = await client.query(query, [tenantId]);
    const data = result.rows[0];

    const totalClicks = parseInt(data.total_clicks) || 0;
    const totalConversions = parseInt(data.total_conversions) || 0;

    return {
      steps: [
        {
          name: 'Clicks',
          value: totalClicks,
          percentage: 100
        },
        {
          name: 'Conversions',
          value: totalConversions,
          percentage: totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0
        }
      ],
      conversionRate: totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0
    };
  }

  /**
   * Create default dashboard layout
   */
  async createDefaultLayout(tenantId, userId) {
    const client = await this.pool.connect();
    
    try {
      // Create default layout
      const layoutQuery = `
        INSERT INTO dashboard_layouts (tenant_id, user_id, layout_name, layout_config, is_default)
        VALUES ($1, $2, 'Default Dashboard', '{"grid": {"cols": 12, "rows": 10}}', true)
        RETURNING *
      `;

      const layoutResult = await client.query(layoutQuery, [tenantId, userId, 'Default Dashboard']);
      const layout = layoutResult.rows[0];

      // Create default widgets
      const defaultWidgets = [
        {
          widget_type: 'metric_card',
          widget_title: 'Total Revenue',
          widget_config: { metric: 'total_revenue', period: 'today', format: 'currency' },
          position_x: 0, position_y: 0, width: 3, height: 2
        },
        {
          widget_type: 'metric_card',
          widget_title: 'Total Clicks',
          widget_config: { metric: 'total_clicks', period: 'today', format: 'number' },
          position_x: 3, position_y: 0, width: 3, height: 2
        },
        {
          widget_type: 'chart',
          widget_title: 'Revenue Trend',
          widget_config: { metric: 'revenue', chart_type: 'line', period: 'last_30_days' },
          position_x: 0, position_y: 2, width: 6, height: 4
        }
      ];

      const widgets = [];
      for (const widgetConfig of defaultWidgets) {
        const widgetQuery = `
          INSERT INTO dashboard_widgets (
            tenant_id, user_id, widget_type, widget_title, widget_config,
            position_x, position_y, width, height
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          RETURNING *
        `;

        const widgetResult = await client.query(widgetQuery, [
          tenantId, userId, widgetConfig.widget_type, widgetConfig.widget_title,
          JSON.stringify(widgetConfig.widget_config),
          widgetConfig.position_x, widgetConfig.position_y,
          widgetConfig.width, widgetConfig.height
        ]);

        widgets.push(widgetResult.rows[0]);
      }

      return {
        layout,
        widgets,
        widgetData: await this.getWidgetData(tenantId, widgets)
      };

    } finally {
      client.release();
    }
  }

  /**
   * Format value based on format type
   */
  formatValue(value, format) {
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(value);
      
      case 'percentage':
        return `${value.toFixed(2)}%`;
      
      case 'number':
        return new Intl.NumberFormat('en-US').format(Math.round(value));
      
      default:
        return value.toString();
    }
  }

  /**
   * Get table columns based on metric type
   */
  getTableColumns(metric) {
    switch (metric) {
      case 'link_performance':
        return [
          { key: 'title', label: 'Link Title' },
          { key: 'short_code', label: 'Short Code' },
          { key: 'clicks', label: 'Clicks' },
          { key: 'conversions', label: 'Conversions' },
          { key: 'revenue', label: 'Revenue' }
        ];
      
      case 'top_countries':
        return [
          { key: 'country', label: 'Country' },
          { key: 'clicks', label: 'Clicks' },
          { key: 'conversions', label: 'Conversions' }
        ];
      
      default:
        return [];
    }
  }
}

module.exports = DashboardWidgetsService;
