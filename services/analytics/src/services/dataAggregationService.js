const logger = require('../utils/logger');
const { recordAnalyticsProcessing } = require('../metrics');

class DataAggregationService {
  constructor() {
    this.isRunning = false;
    this.aggregationInterval = 15 * 60 * 1000; // 15 minutes
    this.jobs = new Map();
  }

  get pool() {
    const { pool } = require('../database');
    return pool;
  }

  /**
   * Start the data aggregation service
   */
  async startAggregationService() {
    if (this.isRunning) {
      logger.warn('Data aggregation service is already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting data aggregation service');

    // Register aggregation jobs
    this.registerAggregationJobs();

    // Start the main aggregation loop
    this.aggregationLoop();
  }

  /**
   * Stop the data aggregation service
   */
  stopAggregationService() {
    this.isRunning = false;
    logger.info('Stopping data aggregation service');
  }

  /**
   * Register all aggregation jobs
   */
  registerAggregationJobs() {
    // Hourly analytics aggregation (runs every 15 minutes)
    this.jobs.set('hourly_analytics', {
      name: 'Hourly Analytics Aggregation',
      interval: 15 * 60 * 1000, // 15 minutes
      lastRun: null,
      handler: this.aggregateHourlyAnalytics.bind(this)
    });

    // Daily analytics aggregation (runs every hour)
    this.jobs.set('daily_analytics', {
      name: 'Daily Analytics Aggregation',
      interval: 60 * 60 * 1000, // 1 hour
      lastRun: null,
      handler: this.aggregateDailyAnalytics.bind(this)
    });

    // CLV calculation (runs every 6 hours)
    this.jobs.set('clv_calculation', {
      name: 'Customer Lifetime Value Calculation',
      interval: 6 * 60 * 60 * 1000, // 6 hours
      lastRun: null,
      handler: this.calculateCustomerCLV.bind(this)
    });

    // Retention analysis (runs daily)
    this.jobs.set('retention_analysis', {
      name: 'Retention Analysis',
      interval: 24 * 60 * 60 * 1000, // 24 hours
      lastRun: null,
      handler: this.calculateRetentionMetrics.bind(this)
    });

    // Data cleanup (runs daily)
    this.jobs.set('data_cleanup', {
      name: 'Data Cleanup',
      interval: 24 * 60 * 60 * 1000, // 24 hours
      lastRun: null,
      handler: this.performDataCleanup.bind(this)
    });
  }

  /**
   * Main aggregation loop
   */
  async aggregationLoop() {
    while (this.isRunning) {
      try {
        await this.runScheduledJobs();
        await new Promise(resolve => setTimeout(resolve, 60000)); // Check every minute
      } catch (error) {
        logger.error('Error in aggregation loop', error);
        await new Promise(resolve => setTimeout(resolve, 60000));
      }
    }
  }

  /**
   * Run scheduled jobs that are due
   */
  async runScheduledJobs() {
    const now = Date.now();

    for (const [jobId, job] of this.jobs) {
      try {
        const shouldRun = !job.lastRun || (now - job.lastRun) >= job.interval;
        
        if (shouldRun) {
          logger.info(`Running aggregation job: ${job.name}`);
          const startTime = Date.now();
          
          await job.handler();
          
          job.lastRun = now;
          const duration = (Date.now() - startTime) / 1000;
          
          logger.info(`Completed aggregation job: ${job.name}`, { duration });
          recordAnalyticsProcessing(`aggregation_${jobId}`, 'success', duration);
        }
      } catch (error) {
        logger.error(`Failed to run aggregation job: ${job.name}`, error);
        recordAnalyticsProcessing(`aggregation_${jobId}`, 'error');
      }
    }
  }

  /**
   * Aggregate hourly analytics data
   */
  async aggregateHourlyAnalytics() {
    const client = await this.pool.connect();
    
    try {
      // Refresh hourly analytics materialized view
      await client.query('REFRESH MATERIALIZED VIEW CONCURRENTLY hourly_analytics_summary');
      
      // Update analytics_daily table with latest hourly data
      const updateQuery = `
        INSERT INTO analytics_daily (
          tenant_id, link_id, date, clicks, unique_visitors, conversions, revenue, created_at
        )
        SELECT 
          has.tenant_id,
          NULL as link_id, -- Aggregate across all links
          DATE(has.hour) as date,
          SUM(has.total_clicks) as clicks,
          SUM(has.unique_visitors) as unique_visitors,
          SUM(has.total_conversions) as conversions,
          SUM(has.total_revenue) as revenue,
          CURRENT_TIMESTAMP
        FROM hourly_analytics_summary has
        WHERE has.hour >= CURRENT_DATE - INTERVAL '2 days'
        GROUP BY has.tenant_id, DATE(has.hour)
        ON CONFLICT (tenant_id, COALESCE(link_id, '00000000-0000-0000-0000-000000000000'::uuid), date)
        DO UPDATE SET
          clicks = EXCLUDED.clicks,
          unique_visitors = EXCLUDED.unique_visitors,
          conversions = EXCLUDED.conversions,
          revenue = EXCLUDED.revenue,
          updated_at = CURRENT_TIMESTAMP
      `;

      await client.query(updateQuery);
      
      logger.debug('Hourly analytics aggregation completed');

    } finally {
      client.release();
    }
  }

  /**
   * Aggregate daily analytics data
   */
  async aggregateDailyAnalytics() {
    const client = await this.pool.connect();
    
    try {
      // Refresh daily analytics materialized view
      await client.query('REFRESH MATERIALIZED VIEW CONCURRENTLY daily_analytics_summary');
      
      // Calculate and store daily metrics for faster dashboard loading
      const metricsQuery = `
        INSERT INTO comparative_analysis (
          tenant_id, analysis_type, metric_name, 
          current_period_start, current_period_end,
          comparison_period_start, comparison_period_end,
          current_value, comparison_value, change_value, change_percentage,
          expires_at
        )
        WITH daily_metrics AS (
          SELECT 
            tenant_id,
            SUM(total_clicks) as today_clicks,
            SUM(total_conversions) as today_conversions,
            SUM(total_revenue) as today_revenue
          FROM daily_analytics_summary
          WHERE date = CURRENT_DATE
          GROUP BY tenant_id
        ),
        yesterday_metrics AS (
          SELECT 
            tenant_id,
            SUM(total_clicks) as yesterday_clicks,
            SUM(total_conversions) as yesterday_conversions,
            SUM(total_revenue) as yesterday_revenue
          FROM daily_analytics_summary
          WHERE date = CURRENT_DATE - INTERVAL '1 day'
          GROUP BY tenant_id
        )
        SELECT 
          dm.tenant_id,
          'daily' as analysis_type,
          'clicks' as metric_name,
          CURRENT_DATE,
          CURRENT_DATE,
          CURRENT_DATE - INTERVAL '1 day',
          CURRENT_DATE - INTERVAL '1 day',
          dm.today_clicks,
          COALESCE(ym.yesterday_clicks, 0),
          dm.today_clicks - COALESCE(ym.yesterday_clicks, 0),
          CASE 
            WHEN COALESCE(ym.yesterday_clicks, 0) > 0 
            THEN ((dm.today_clicks - COALESCE(ym.yesterday_clicks, 0))::decimal / ym.yesterday_clicks::decimal) * 100
            ELSE 0 
          END,
          CURRENT_TIMESTAMP + INTERVAL '25 hours'
        FROM daily_metrics dm
        LEFT JOIN yesterday_metrics ym ON dm.tenant_id = ym.tenant_id
        WHERE dm.today_clicks > 0
        ON CONFLICT (tenant_id, analysis_type, metric_name, current_period_start, current_period_end)
        DO UPDATE SET
          current_value = EXCLUDED.current_value,
          comparison_value = EXCLUDED.comparison_value,
          change_value = EXCLUDED.change_value,
          change_percentage = EXCLUDED.change_percentage,
          calculated_at = CURRENT_TIMESTAMP,
          expires_at = EXCLUDED.expires_at
      `;

      await client.query(metricsQuery);
      
      logger.debug('Daily analytics aggregation completed');

    } finally {
      client.release();
    }
  }

  /**
   * Calculate customer lifetime value
   */
  async calculateCustomerCLV() {
    const client = await this.pool.connect();
    
    try {
      // Refresh CLV materialized view
      await client.query('REFRESH MATERIALIZED VIEW CONCURRENTLY customer_clv_summary');
      
      // Calculate CLV segments for each tenant
      const segmentQuery = `
        INSERT INTO clv_segments (
          tenant_id, segment_name, min_clv, max_clv, customer_count, avg_clv, segment_characteristics
        )
        WITH clv_percentiles AS (
          SELECT 
            tenant_id,
            PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY lifetime_value) as p25,
            PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY lifetime_value) as p50,
            PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY lifetime_value) as p75,
            PERCENTILE_CONT(0.90) WITHIN GROUP (ORDER BY lifetime_value) as p90,
            MAX(lifetime_value) as max_clv
          FROM customer_clv_summary
          GROUP BY tenant_id
        ),
        segments AS (
          SELECT 
            ccs.tenant_id,
            CASE 
              WHEN ccs.lifetime_value >= cp.p90 THEN 'Champions'
              WHEN ccs.lifetime_value >= cp.p75 THEN 'Loyal Customers'
              WHEN ccs.lifetime_value >= cp.p50 THEN 'Potential Loyalists'
              WHEN ccs.lifetime_value >= cp.p25 THEN 'New Customers'
              ELSE 'At Risk'
            END as segment_name,
            ccs.lifetime_value
          FROM customer_clv_summary ccs
          JOIN clv_percentiles cp ON ccs.tenant_id = cp.tenant_id
        )
        SELECT 
          tenant_id,
          segment_name,
          MIN(lifetime_value) as min_clv,
          MAX(lifetime_value) as max_clv,
          COUNT(*) as customer_count,
          AVG(lifetime_value) as avg_clv,
          jsonb_build_object(
            'avg_lifespan_months', AVG(CASE WHEN segment_name = 'Champions' THEN 12 ELSE 6 END),
            'recommended_actions', CASE 
              WHEN segment_name = 'Champions' THEN '["retention_programs", "upsell_opportunities"]'
              WHEN segment_name = 'At Risk' THEN '["win_back_campaigns", "engagement_boost"]'
              ELSE '["nurture_campaigns", "value_optimization"]'
            END
          ) as segment_characteristics
        FROM segments
        GROUP BY tenant_id, segment_name
        ON CONFLICT (tenant_id, segment_name)
        DO UPDATE SET
          min_clv = EXCLUDED.min_clv,
          max_clv = EXCLUDED.max_clv,
          customer_count = EXCLUDED.customer_count,
          avg_clv = EXCLUDED.avg_clv,
          segment_characteristics = EXCLUDED.segment_characteristics,
          updated_at = CURRENT_TIMESTAMP
      `;

      await client.query(segmentQuery);
      
      logger.debug('CLV calculation completed');

    } finally {
      client.release();
    }
  }

  /**
   * Calculate retention metrics
   */
  async calculateRetentionMetrics() {
    const client = await this.pool.connect();
    
    try {
      // Refresh retention analysis materialized view
      await client.query('REFRESH MATERIALIZED VIEW CONCURRENTLY retention_analysis');
      
      // Calculate retention curves for visualization
      const retentionCurveQuery = `
        INSERT INTO retention_curves (
          tenant_id, cohort_month, period_number, retained_users, users_count, retention_rate, revenue, curve_type
        )
        SELECT 
          tenant_id,
          cohort_month,
          period_number,
          retained_customers,
          customers_in_cohort,
          CASE 
            WHEN customers_in_cohort > 0 
            THEN (retained_customers::decimal / customers_in_cohort::decimal) * 100
            ELSE 0 
          END as retention_rate,
          0 as revenue, -- Will be calculated separately
          'user_retention' as curve_type
        FROM retention_analysis
        WHERE cohort_month >= CURRENT_DATE - INTERVAL '12 months'
          AND period_number <= 12
        ON CONFLICT (tenant_id, cohort_month, period_number, curve_type)
        DO UPDATE SET
          retained_users = EXCLUDED.retained_users,
          users_count = EXCLUDED.users_count,
          retention_rate = EXCLUDED.retention_rate,
          updated_at = CURRENT_TIMESTAMP
      `;

      await client.query(retentionCurveQuery);
      
      logger.debug('Retention metrics calculation completed');

    } finally {
      client.release();
    }
  }

  /**
   * Perform data cleanup operations
   */
  async performDataCleanup() {
    const client = await this.pool.connect();
    
    try {
      // Clean up expired comparative analysis data
      await client.query(`
        DELETE FROM comparative_analysis 
        WHERE expires_at < CURRENT_TIMESTAMP
      `);

      // Clean up old drill-down sessions
      await client.query(`
        DELETE FROM drill_down_sessions 
        WHERE expires_at < CURRENT_TIMESTAMP
      `);

      // Clean up resolved alerts older than 30 days
      await client.query(`
        DELETE FROM alert_instances 
        WHERE status = 'resolved' 
          AND resolved_at < CURRENT_TIMESTAMP - INTERVAL '30 days'
      `);

      // Clean up old system logs
      await client.query('SELECT cleanup_old_logs()');

      // Update statistics for query planner
      await client.query('ANALYZE');

      logger.debug('Data cleanup completed');

    } finally {
      client.release();
    }
  }

  /**
   * Get aggregation job status
   */
  getJobStatus() {
    const status = {};
    
    for (const [jobId, job] of this.jobs) {
      status[jobId] = {
        name: job.name,
        lastRun: job.lastRun ? new Date(job.lastRun).toISOString() : null,
        nextRun: job.lastRun ? new Date(job.lastRun + job.interval).toISOString() : 'pending',
        interval: job.interval / 1000 // Convert to seconds
      };
    }

    return {
      isRunning: this.isRunning,
      jobs: status
    };
  }

  /**
   * Force run a specific job
   */
  async forceRunJob(jobId) {
    const job = this.jobs.get(jobId);
    
    if (!job) {
      throw new Error(`Job not found: ${jobId}`);
    }

    logger.info(`Force running aggregation job: ${job.name}`);
    const startTime = Date.now();
    
    await job.handler();
    
    job.lastRun = Date.now();
    const duration = (Date.now() - startTime) / 1000;
    
    logger.info(`Force completed aggregation job: ${job.name}`, { duration });
    recordAnalyticsProcessing(`aggregation_${jobId}`, 'success', duration);
    
    return { success: true, duration };
  }
}

module.exports = DataAggregationService;
