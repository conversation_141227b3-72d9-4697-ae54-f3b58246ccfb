const logger = require('../utils/logger');
const { recordAnalyticsProcessing } = require('../metrics');
const fs = require('fs').promises;
const path = require('path');

class DataExportService {
  constructor() {
    this.exportDir = process.env.EXPORT_DIR || '/tmp/exports';
    this.maxExportSize = 100000; // Max 100k rows
    this.exportTTL = 24 * 60 * 60 * 1000; // 24 hours
  }

  get pool() {
    const { pool } = require('../database');
    return pool;
  }

  /**
   * Export analytics data to CSV
   */
  async exportToCSV(tenantId, options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        reportType = 'summary',
        dateFrom,
        dateTo,
        includeDetails = false,
        filters = {}
      } = options;

      logger.info('Starting CSV export', { tenantId, reportType, dateFrom, dateTo });

      // Ensure export directory exists
      await this.ensureExportDirectory();

      // Get data based on report type
      const data = await this.getExportData(tenantId, reportType, {
        dateFrom,
        dateTo,
        includeDetails,
        filters
      });

      // Generate CSV content
      const csvContent = this.generateCSV(data, reportType);

      // Save to file
      const filename = `${reportType}_${tenantId}_${Date.now()}.csv`;
      const filepath = path.join(this.exportDir, filename);
      
      await fs.writeFile(filepath, csvContent, 'utf8');

      // Schedule cleanup
      setTimeout(() => this.cleanupFile(filepath), this.exportTTL);

      recordAnalyticsProcessing('csv_export', 'success', (Date.now() - startTime) / 1000);

      return {
        filename,
        filepath,
        size: csvContent.length,
        rowCount: data.length,
        downloadUrl: `/api/analytics/export/download/${filename}`
      };

    } catch (error) {
      recordAnalyticsProcessing('csv_export', 'error');
      logger.error('Failed to export CSV', error);
      throw error;
    }
  }

  /**
   * Export analytics data to PDF report
   */
  async exportToPDF(tenantId, options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        reportType = 'dashboard',
        dateFrom,
        dateTo,
        includeCharts = true,
        template = 'standard'
      } = options;

      logger.info('Starting PDF export', { tenantId, reportType, dateFrom, dateTo });

      // Ensure export directory exists
      await this.ensureExportDirectory();

      // Get data for PDF
      const data = await this.getPDFData(tenantId, reportType, {
        dateFrom,
        dateTo,
        includeCharts
      });

      // Generate PDF content
      const pdfBuffer = await this.generatePDF(data, template, {
        tenantId,
        reportType,
        dateFrom,
        dateTo
      });

      // Save to file
      const filename = `${reportType}_report_${tenantId}_${Date.now()}.pdf`;
      const filepath = path.join(this.exportDir, filename);
      
      await fs.writeFile(filepath, pdfBuffer);

      // Schedule cleanup
      setTimeout(() => this.cleanupFile(filepath), this.exportTTL);

      recordAnalyticsProcessing('pdf_export', 'success', (Date.now() - startTime) / 1000);

      return {
        filename,
        filepath,
        size: pdfBuffer.length,
        downloadUrl: `/api/analytics/export/download/${filename}`
      };

    } catch (error) {
      recordAnalyticsProcessing('pdf_export', 'error');
      logger.error('Failed to export PDF', error);
      throw error;
    }
  }

  /**
   * Get export data based on report type
   */
  async getExportData(tenantId, reportType, options) {
    const client = await this.pool.connect();
    
    try {
      let query;
      let params = [tenantId];

      switch (reportType) {
        case 'summary':
          query = this.getSummaryExportQuery(options);
          break;
        case 'clicks':
          query = this.getClicksExportQuery(options);
          break;
        case 'conversions':
          query = this.getConversionsExportQuery(options);
          break;
        case 'revenue':
          query = this.getRevenueExportQuery(options);
          break;
        case 'links':
          query = this.getLinksExportQuery(options);
          break;
        default:
          throw new Error(`Unknown report type: ${reportType}`);
      }

      // Add date filters if provided
      if (options.dateFrom) {
        params.push(options.dateFrom);
      }
      if (options.dateTo) {
        params.push(options.dateTo);
      }

      const result = await client.query(query, params);
      
      if (result.rows.length > this.maxExportSize) {
        throw new Error(`Export too large: ${result.rows.length} rows (max: ${this.maxExportSize})`);
      }

      return result.rows;

    } finally {
      client.release();
    }
  }

  /**
   * Get summary export query
   */
  getSummaryExportQuery(options) {
    const dateFilter = options.dateFrom && options.dateTo 
      ? 'AND das.date >= $2 AND das.date <= $3'
      : '';

    return `
      SELECT 
        das.date,
        SUM(das.total_clicks) as total_clicks,
        SUM(das.unique_visitors) as unique_visitors,
        SUM(das.total_conversions) as total_conversions,
        SUM(das.total_revenue) as total_revenue,
        CASE 
          WHEN SUM(das.total_clicks) > 0 
          THEN (SUM(das.total_conversions)::decimal / SUM(das.total_clicks)::decimal) * 100
          ELSE 0 
        END as conversion_rate,
        CASE 
          WHEN SUM(das.total_conversions) > 0 
          THEN SUM(das.total_revenue) / SUM(das.total_conversions)
          ELSE 0 
        END as avg_order_value
      FROM daily_analytics_summary das
      WHERE das.tenant_id = $1 ${dateFilter}
      GROUP BY das.date
      ORDER BY das.date DESC
    `;
  }

  /**
   * Get clicks export query
   */
  getClicksExportQuery(options) {
    const dateFilter = options.dateFrom && options.dateTo 
      ? 'AND c.clicked_at >= $2 AND c.clicked_at <= $3'
      : '';

    return `
      SELECT 
        c.clicked_at,
        l.title as link_title,
        l.short_code,
        c.ip_address,
        c.country,
        c.device_type,
        c.browser,
        c.referrer,
        CASE WHEN a.id IS NOT NULL THEN 'Yes' ELSE 'No' END as converted
      FROM clicks c
      JOIN links l ON c.link_id = l.id
      LEFT JOIN attributions a ON c.id = a.click_id
      WHERE l.tenant_id = $1 ${dateFilter}
      ORDER BY c.clicked_at DESC
      LIMIT ${this.maxExportSize}
    `;
  }

  /**
   * Get conversions export query
   */
  getConversionsExportQuery(options) {
    const dateFilter = options.dateFrom && options.dateTo 
      ? 'AND a.created_at >= $2 AND a.created_at <= $3'
      : '';

    return `
      SELECT 
        a.created_at as conversion_date,
        l.title as link_title,
        l.short_code,
        c.clicked_at,
        c.country,
        c.device_type,
        o.total_amount as revenue,
        o.currency,
        i.platform as integration_platform
      FROM attributions a
      JOIN clicks c ON a.click_id = c.id
      JOIN links l ON a.link_id = l.id
      JOIN orders o ON a.order_id = o.id
      JOIN integrations i ON o.integration_id = i.id
      WHERE l.tenant_id = $1 ${dateFilter}
      ORDER BY a.created_at DESC
      LIMIT ${this.maxExportSize}
    `;
  }

  /**
   * Get revenue export query
   */
  getRevenueExportQuery(options) {
    const dateFilter = options.dateFrom && options.dateTo 
      ? 'AND o.created_at >= $2 AND o.created_at <= $3'
      : '';

    return `
      SELECT 
        DATE(o.created_at) as date,
        i.platform,
        COUNT(*) as order_count,
        SUM(o.total_amount) as total_revenue,
        AVG(o.total_amount) as avg_order_value,
        MIN(o.total_amount) as min_order_value,
        MAX(o.total_amount) as max_order_value
      FROM orders o
      JOIN integrations i ON o.integration_id = i.id
      WHERE i.tenant_id = $1 ${dateFilter}
      GROUP BY DATE(o.created_at), i.platform
      ORDER BY date DESC, total_revenue DESC
    `;
  }

  /**
   * Get links export query
   */
  getLinksExportQuery(options) {
    return `
      SELECT 
        l.title,
        l.short_code,
        l.destination_url,
        l.created_at,
        l.is_active,
        COUNT(DISTINCT c.id) as total_clicks,
        COUNT(DISTINCT a.id) as total_conversions,
        COALESCE(SUM(o.total_amount), 0) as total_revenue
      FROM links l
      LEFT JOIN clicks c ON l.id = c.link_id
      LEFT JOIN attributions a ON c.id = a.click_id
      LEFT JOIN orders o ON a.order_id = o.id
      WHERE l.tenant_id = $1
      GROUP BY l.id, l.title, l.short_code, l.destination_url, l.created_at, l.is_active
      ORDER BY total_clicks DESC
    `;
  }

  /**
   * Generate CSV content from data
   */
  generateCSV(data, reportType) {
    if (!data.length) {
      return 'No data available for the selected criteria\n';
    }

    // Get headers from first row
    const headers = Object.keys(data[0]);
    
    // Create CSV content
    let csv = headers.join(',') + '\n';
    
    for (const row of data) {
      const values = headers.map(header => {
        let value = row[header];
        
        // Handle null/undefined values
        if (value === null || value === undefined) {
          value = '';
        }
        
        // Handle dates
        if (value instanceof Date) {
          value = value.toISOString();
        }
        
        // Escape commas and quotes in strings
        if (typeof value === 'string') {
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            value = '"' + value.replace(/"/g, '""') + '"';
          }
        }
        
        return value;
      });
      
      csv += values.join(',') + '\n';
    }
    
    return csv;
  }

  /**
   * Get data for PDF generation
   */
  async getPDFData(tenantId, reportType, options) {
    // Get summary data
    const AnalyticsService = require('./analyticsService');
    const analyticsService = new AnalyticsService();
    
    const summary = await analyticsService.getSummary({
      tenantId,
      dateFrom: options.dateFrom,
      dateTo: options.dateTo
    });

    // Get additional data based on report type
    let additionalData = {};
    
    if (reportType === 'dashboard') {
      // Get top performing links
      const topLinks = await this.getExportData(tenantId, 'links', { limit: 10 });
      additionalData.topLinks = topLinks.slice(0, 10);
      
      // Get recent activity
      const recentActivity = await this.getExportData(tenantId, 'clicks', { 
        ...options, 
        limit: 20 
      });
      additionalData.recentActivity = recentActivity.slice(0, 20);
    }

    return {
      summary,
      ...additionalData,
      metadata: {
        tenantId,
        reportType,
        dateFrom: options.dateFrom,
        dateTo: options.dateTo,
        generatedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Generate PDF from data (simplified implementation)
   */
  async generatePDF(data, template, metadata) {
    // This is a simplified implementation
    // In production, you would use a library like puppeteer, jsPDF, or PDFKit
    
    const htmlContent = this.generateHTMLReport(data, template, metadata);
    
    // For now, return HTML as buffer (in production, convert to PDF)
    return Buffer.from(htmlContent, 'utf8');
  }

  /**
   * Generate HTML report content
   */
  generateHTMLReport(data, template, metadata) {
    const { summary, topLinks = [], recentActivity = [] } = data;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Analytics Report - ${metadata.reportType}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
          .summary { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 30px; }
          .metric { background: #f5f5f5; padding: 15px; border-radius: 5px; text-align: center; }
          .metric-value { font-size: 24px; font-weight: bold; color: #333; }
          .metric-label { font-size: 14px; color: #666; margin-top: 5px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Analytics Report</h1>
          <p>Report Type: ${metadata.reportType}</p>
          <p>Period: ${metadata.dateFrom} to ${metadata.dateTo}</p>
          <p>Generated: ${new Date(metadata.generatedAt).toLocaleString()}</p>
        </div>
        
        <div class="summary">
          <div class="metric">
            <div class="metric-value">${summary.summary?.total_clicks || 0}</div>
            <div class="metric-label">Total Clicks</div>
          </div>
          <div class="metric">
            <div class="metric-value">${summary.summary?.total_conversions || 0}</div>
            <div class="metric-label">Total Conversions</div>
          </div>
          <div class="metric">
            <div class="metric-value">$${(summary.summary?.total_revenue || 0).toFixed(2)}</div>
            <div class="metric-label">Total Revenue</div>
          </div>
        </div>
        
        ${topLinks.length > 0 ? `
          <h2>Top Performing Links</h2>
          <table>
            <tr>
              <th>Link Title</th>
              <th>Short Code</th>
              <th>Clicks</th>
              <th>Conversions</th>
              <th>Revenue</th>
            </tr>
            ${topLinks.map(link => `
              <tr>
                <td>${link.title || 'Untitled'}</td>
                <td>${link.short_code}</td>
                <td>${link.total_clicks}</td>
                <td>${link.total_conversions}</td>
                <td>$${parseFloat(link.total_revenue || 0).toFixed(2)}</td>
              </tr>
            `).join('')}
          </table>
        ` : ''}
        
        <div style="margin-top: 40px; font-size: 12px; color: #666;">
          <p>This report was generated automatically by the Analytics Platform.</p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Ensure export directory exists
   */
  async ensureExportDirectory() {
    try {
      await fs.access(this.exportDir);
    } catch (error) {
      await fs.mkdir(this.exportDir, { recursive: true });
    }
  }

  /**
   * Clean up export file
   */
  async cleanupFile(filepath) {
    try {
      await fs.unlink(filepath);
      logger.debug('Cleaned up export file', { filepath });
    } catch (error) {
      logger.warn('Failed to cleanup export file', { filepath, error: error.message });
    }
  }

  /**
   * Get export file for download
   */
  async getExportFile(filename) {
    const filepath = path.join(this.exportDir, filename);
    
    try {
      await fs.access(filepath);
      return filepath;
    } catch (error) {
      throw new Error('Export file not found or expired');
    }
  }
}

module.exports = DataExportService;
