# Billing Service Environment Configuration
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=3003
NODE_ENV=development
LOG_LEVEL=info

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/ecommerce_analytics
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001
ANALYTICS_SERVICE_URL=http://localhost:3002

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9003

# Billing Configuration
DEFAULT_CURRENCY=usd
TRIAL_PERIOD_DAYS=14
GRACE_PERIOD_DAYS=3

# Queue Configuration
QUEUE_REDIS_URL=redis://localhost:6379
QUEUE_CONCURRENCY=5
