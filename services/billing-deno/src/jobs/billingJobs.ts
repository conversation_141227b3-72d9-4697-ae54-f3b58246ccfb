import { config } from "../config/config.ts";
import { logger, logJobEvent } from "../utils/logger.ts";
import { billingQuery, billingTransaction } from "../utils/database.ts";
import { getSubscriptionService, getInvoiceService, getUsageTrackingService } from "../services/index.ts";
import type { Subscription } from "../types/billing.ts";

// Database row types
interface SubscriptionRow {
  id: string;
  tenant_id: string;
  status: string;
  trial_end?: string;
  current_period_end?: string;
}

interface InvoiceRow {
  id: string;
  tenant_id: string;
  due_date: string;
  status: string;
}

export class BillingJobs {
  private intervals: number[] = [];
  private isRunning = false;

  constructor() {
    logger.info("Billing jobs initialized");
  }

  /**
   * Start all background jobs
   */
  start(): void {
    if (this.isRunning) {
      logger.warn("Billing jobs already running");
      return;
    }

    this.isRunning = true;
    logger.info("Starting billing background jobs");

    // Trial expiring notifications (daily)
    this.intervals.push(
      setInterval(() => {
        this.processTrialExpiringNotifications().catch(error => {
          logger.error("Trial expiring notifications job failed", error as Error);
        });
      }, 24 * 60 * 60 * 1000) // 24 hours
    );

    // Subscription renewal reminders (daily)
    this.intervals.push(
      setInterval(() => {
        this.processSubscriptionRenewalReminders().catch(error => {
          logger.error("Subscription renewal reminders job failed", error as Error);
        });
      }, 24 * 60 * 60 * 1000) // 24 hours
    );

    // Overdue invoices processing (every 6 hours)
    this.intervals.push(
      setInterval(() => {
        this.processOverdueInvoices().catch(error => {
          logger.error("Overdue invoices job failed", error as Error);
        });
      }, 6 * 60 * 60 * 1000) // 6 hours
    );

    // Usage aggregation (hourly)
    this.intervals.push(
      setInterval(() => {
        this.processUsageAggregation().catch(error => {
          logger.error("Usage aggregation job failed", error as Error);
        });
      }, 60 * 60 * 1000) // 1 hour
    );

    // Cleanup old usage records (daily)
    this.intervals.push(
      setInterval(() => {
        this.cleanupOldUsageRecords().catch(error => {
          logger.error("Usage cleanup job failed", error as Error);
        });
      }, 24 * 60 * 60 * 1000) // 24 hours
    );

    // Subscription metrics calculation (every 4 hours)
    this.intervals.push(
      setInterval(() => {
        this.calculateSubscriptionMetrics().catch(error => {
          logger.error("Subscription metrics job failed", error as Error);
        });
      }, 4 * 60 * 60 * 1000) // 4 hours
    );

    logger.info("Billing background jobs started", {
      jobCount: this.intervals.length,
    });
  }

  /**
   * Stop all background jobs
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    logger.info("Stopping billing background jobs");

    // Clear all intervals
    for (const interval of this.intervals) {
      clearInterval(interval);
    }
    this.intervals = [];
    this.isRunning = false;

    logger.info("Billing background jobs stopped");
  }

  /**
   * Process trial expiring notifications
   */
  private async processTrialExpiringNotifications(): Promise<void> {
    const jobId = crypto.randomUUID();
    logJobEvent("job_started", "trial_expiring_notifications", jobId);

    try {
      // Find subscriptions with trials expiring in 3 days
      const expiringTrials = await billingQuery(`
        SELECT * FROM subscriptions
        WHERE status = 'trialing'
        AND trial_end <= NOW() + INTERVAL '3 days'
        AND trial_end > NOW()
      `, [], "") as SubscriptionRow[];

      const subscriptionService = getSubscriptionService();

      for (const subscription of expiringTrials) {
        try {
          await subscriptionService.sendTrialExpiringNotification(subscription as unknown as Subscription);
          logger.info("Trial expiring notification sent", {
            subscriptionId: subscription.id,
            tenantId: subscription.tenant_id,
          });
        } catch (error) {
          logger.error("Failed to send trial expiring notification", {
            error: (error as Error).message,
            subscriptionId: subscription.id,
          });
        }
      }

      logJobEvent("job_completed", "trial_expiring_notifications", jobId, {
        processedCount: expiringTrials.length,
      });
    } catch (error) {
      logJobEvent("job_failed", "trial_expiring_notifications", jobId, {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Process subscription renewal reminders
   */
  private async processSubscriptionRenewalReminders(): Promise<void> {
    const jobId = crypto.randomUUID();
    logJobEvent("job_started", "subscription_renewal_reminders", jobId);

    try {
      // Find subscriptions renewing in 7 days
      const renewingSubscriptions = await billingQuery(`
        SELECT * FROM subscriptions
        WHERE status = 'active'
        AND current_period_end <= NOW() + INTERVAL '7 days'
        AND current_period_end > NOW()
      `, [], "") as SubscriptionRow[];

      for (const subscription of renewingSubscriptions) {
        try {
          // Send renewal reminder notification
          logger.info("Subscription renewal reminder sent", {
            subscriptionId: subscription.id,
            tenantId: subscription.tenant_id,
          });
        } catch (error) {
          logger.error("Failed to send renewal reminder", {
            error: (error as Error).message,
            subscriptionId: subscription.id,
          });
        }
      }

      logJobEvent("job_completed", "subscription_renewal_reminders", jobId, {
        processedCount: renewingSubscriptions.length,
      });
    } catch (error) {
      logJobEvent("job_failed", "subscription_renewal_reminders", jobId, {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Process overdue invoices
   */
  private async processOverdueInvoices(): Promise<void> {
    const jobId = crypto.randomUUID();
    logJobEvent("job_started", "overdue_invoices", jobId);

    try {
      // Find overdue invoices
      const overdueInvoices = await billingQuery(`
        SELECT * FROM invoices
        WHERE status = 'open'
        AND due_date < NOW() - INTERVAL '1 day'
      `, [], "") as InvoiceRow[];

      for (const invoice of overdueInvoices) {
        try {
          // Process overdue invoice (send notifications, apply late fees, etc.)
          logger.info("Overdue invoice processed", {
            invoiceId: invoice.id,
            tenantId: invoice.tenant_id,
            daysPastDue: Math.floor((Date.now() - new Date(invoice.due_date).getTime()) / (1000 * 60 * 60 * 24)),
          });
        } catch (error) {
          logger.error("Failed to process overdue invoice", {
            error: (error as Error).message,
            invoiceId: invoice.id,
          });
        }
      }

      logJobEvent("job_completed", "overdue_invoices", jobId, {
        processedCount: overdueInvoices.length,
      });
    } catch (error) {
      logJobEvent("job_failed", "overdue_invoices", jobId, {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Process usage aggregation
   */
  private async processUsageAggregation(): Promise<void> {
    const jobId = crypto.randomUUID();
    logJobEvent("job_started", "usage_aggregation", jobId);

    try {
      // Aggregate usage data for billing
      const usageService = getUsageTrackingService();
      
      // Get all active subscriptions
      const activeSubscriptions = await billingQuery(`
        SELECT DISTINCT tenant_id FROM subscriptions 
        WHERE status = 'active'
      `, [], "");

      let processedCount = 0;

      for (const subscription of activeSubscriptions) {
        try {
          const sub = subscription as { tenant_id: string };
          const tenantId = sub.tenant_id;
          
          // Get usage metrics for the tenant
          const metrics = await usageService.getUsageMetrics(tenantId);
          
          // Store aggregated metrics (in a real implementation)
          logger.debug("Usage metrics aggregated", {
            tenantId,
            totalUsage: metrics.totalUsage,
            currentPeriodUsage: metrics.currentPeriodUsage,
          });

          processedCount++;
        } catch (error) {
          const sub = subscription as { tenant_id: string };
          logger.error("Failed to aggregate usage for tenant", {
            error: (error as Error).message,
            tenantId: sub.tenant_id,
          });
        }
      }

      logJobEvent("job_completed", "usage_aggregation", jobId, {
        processedCount,
      });
    } catch (error) {
      logJobEvent("job_failed", "usage_aggregation", jobId, {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Cleanup old usage records
   */
  private async cleanupOldUsageRecords(): Promise<void> {
    const jobId = crypto.randomUUID();
    logJobEvent("job_started", "usage_cleanup", jobId);

    try {
      const retentionDate = new Date();
      retentionDate.setDate(retentionDate.getDate() - config.usage.retentionDays);

      const result = await billingTransaction(async (client) => {
        const deleteResult = await client.queryObject(`
          DELETE FROM usage_records 
          WHERE created_at < $1
        `, [retentionDate]);

        return deleteResult.rowCount || 0;
      }, "");

      logJobEvent("job_completed", "usage_cleanup", jobId, {
        deletedCount: result,
        retentionDays: config.usage.retentionDays,
      });

      logger.info("Old usage records cleaned up", {
        deletedCount: result,
        retentionDays: config.usage.retentionDays,
      });
    } catch (error) {
      logJobEvent("job_failed", "usage_cleanup", jobId, {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Calculate subscription metrics
   */
  private async calculateSubscriptionMetrics(): Promise<void> {
    const jobId = crypto.randomUUID();
    logJobEvent("job_started", "subscription_metrics", jobId);

    try {
      const subscriptionService = getSubscriptionService();
      const metrics = await subscriptionService.getSubscriptionMetrics();

      // Store metrics in cache or database for dashboard
      logger.info("Subscription metrics calculated", {
        totalSubscriptions: metrics.totalSubscriptions,
        activeSubscriptions: metrics.activeSubscriptions,
        totalMonthlyRevenue: metrics.totalMonthlyRevenue,
      });

      logJobEvent("job_completed", "subscription_metrics", jobId, {
        metrics,
      });
    } catch (error) {
      logJobEvent("job_failed", "subscription_metrics", jobId, {
        error: (error as Error).message,
      });
      throw error;
    }
  }
}
