import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { BillingJobs } from "./billingJobs.ts";

// Job instances
let billingJobs: BillingJobs | null = null;

/**
 * Initialize all background jobs
 */
export async function initializeJobs(): Promise<void> {
  try {
    logger.info("Initializing background jobs...");

    // Initialize billing jobs
    billingJobs = new BillingJobs();
    await billingJobs.start();

    logger.info("Background jobs initialized successfully");
  } catch (error) {
    logger.error("Failed to initialize background jobs", error as Error);
    throw error;
  }
}

/**
 * Close all jobs and cleanup resources
 */
export async function closeJobs(): Promise<void> {
  try {
    logger.info("Closing background jobs...");

    if (billingJobs) {
      await billingJobs.stop();
      billingJobs = null;
      logger.info("Billing jobs closed");
    }

    logger.info("All background jobs closed successfully");
  } catch (error) {
    logger.error("Error closing background jobs", error as Error);
    throw error;
  }
}

/**
 * Get billing jobs instance
 */
export function getBillingJobs(): BillingJobs {
  if (!billingJobs) {
    throw new Error("Billing jobs not initialized. Call initializeJobs() first.");
  }
  return billingJobs;
}

/**
 * Health check for all jobs
 */
export async function jobsHealthCheck(): Promise<{
  billingJobs: boolean;
}> {
  return {
    billingJobs: billingJobs !== null,
  };
}

// Export job classes for direct instantiation if needed
export { BillingJobs };
