import { Application } from "@oak/oak";
import { config } from "./config/config.ts";
import { initializeLogger, logger } from "./utils/logger.ts";
import { initializeDatabase, closeDatabase } from "./utils/database.ts";
import { initializeRedis, closeRedis } from "./utils/redis.ts";
import { setupMiddleware } from "./middleware/index.ts";
import { setupRoutes } from "./routes/index.ts";
import { initializeServices, closeServices } from "./services/index.ts";
import { initializeJobs, closeJobs } from "./jobs/index.ts";

// Initialize logger first
initializeLogger();

// Create Oak application
const app = new Application();

// Global error handler
app.addEventListener("error", (evt) => {
  logger.error("Unhandled application error", {
    error: evt.error?.message,
    stack: evt.error?.stack,
  });
});

// Setup middleware
setupMiddleware(app);

// Setup routes
setupRoutes(app);

// Graceful shutdown handler
async function gracefulShutdown(signal: string): Promise<void> {
  logger.info(`Received ${signal}, starting graceful shutdown...`);
  
  try {
    // Close background jobs
    await closeJobs();
    
    // Close services
    await closeServices();
    
    // Close database connections
    await closeDatabase();
    
    // Close Redis connections
    closeRedis();
    
    logger.info("Graceful shutdown completed");
    Deno.exit(0);
  } catch (error) {
    logger.error("Error during graceful shutdown", error as Error);
    Deno.exit(1);
  }
}

// Setup signal handlers
Deno.addSignalListener("SIGINT", () => gracefulShutdown("SIGINT"));
Deno.addSignalListener("SIGTERM", () => gracefulShutdown("SIGTERM"));

// Start server
async function startServer(): Promise<void> {
  try {
    // Initialize database
    await initializeDatabase();
    logger.info("Database initialized successfully");
    
    // Initialize Redis
    await initializeRedis();
    logger.info("Redis initialized successfully");
    
    // Initialize services
    await initializeServices();
    logger.info("Services initialized successfully");
    
    // Initialize background jobs
    await initializeJobs();
    logger.info("Background jobs initialized successfully");
    
    // Start HTTP server
    logger.info(`Starting Billing service on ${config.host}:${config.port}`, {
      environment: config.nodeEnv,
      port: config.port,
      host: config.host,
      serviceName: config.serviceName,
      version: config.version,
    });
    
    await app.listen({ 
      hostname: config.host, 
      port: config.port 
    });
  } catch (error) {
    logger.error("Failed to start Billing service", error as Error);
    Deno.exit(1);
  }
}

// Handle uncaught exceptions
globalThis.addEventListener("unhandledrejection", (event) => {
  logger.error("Unhandled promise rejection", {
    reason: event.reason?.message || event.reason,
    stack: event.reason?.stack,
  });
  event.preventDefault();
});

globalThis.addEventListener("error", (event) => {
  logger.error("Uncaught exception", {
    error: event.error?.message || event.message,
    stack: event.error?.stack,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
  });
});

// Start the server
if (import.meta.main) {
  startServer();
}
