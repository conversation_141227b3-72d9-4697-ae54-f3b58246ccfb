import { Context, Middleware } from "@oak/oak";
import { verify } from "djwt";
import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { errors } from "./errorHandler.ts";

export interface User {
  id: string;
  email: string;
  tenantId: string;
  role: string;
  permissions: string[];
}

export interface JWTPayload {
  sub: string;
  email: string;
  tenant_id: string;
  role: string;
  permissions: string[];
  iat: number;
  exp: number;
  iss: string;
}

/**
 * Authentication middleware
 */
export const authMiddleware: Middleware = async (ctx: Context, next) => {
  try {
    const authHeader = ctx.request.headers.get("authorization");
    
    if (!authHeader) {
      throw errors.unauthorized("Missing authorization header");
    }

    const [scheme, token] = authHeader.split(" ");
    
    if (scheme !== "Bearer" || !token) {
      throw errors.unauthorized("Invalid authorization header format");
    }

    // Verify JWT token
    const key = await crypto.subtle.importKey(
      "raw",
      new TextEncoder().encode(config.jwt.secret),
      { name: "HM<PERSON>", hash: "SHA-256" },
      false,
      ["verify"]
    );

    const payload = await verify(token, key) as unknown as JWTPayload;

    // Validate token issuer
    if (payload.iss !== config.jwt.issuer) {
      throw errors.unauthorized("Invalid token issuer");
    }

    // Create user object
    const user: User = {
      id: payload.sub,
      email: payload.email,
      tenantId: payload.tenant_id,
      role: payload.role,
      permissions: payload.permissions || [],
    };

    // Store user in context state
    ctx.state.user = user;
    ctx.state.tenantId = user.tenantId;

    logger.debug("User authenticated", {
      userId: user.id,
      tenantId: user.tenantId,
      role: user.role,
    });

    await next();
  } catch (error) {
    if (error instanceof Error && error.message.includes("JWT")) {
      logger.warn("JWT verification failed", {
        error: error.message,
        ip: ctx.request.headers.get("x-forwarded-for") || 
            ctx.request.headers.get("x-real-ip") || 
            "unknown",
      });
      throw errors.unauthorized("Invalid or expired token");
    }
    throw error;
  }
};

/**
 * API key authentication middleware (for service-to-service communication)
 */
export const apiKeyMiddleware: Middleware = async (ctx: Context, next) => {
  try {
    const apiKey = ctx.request.headers.get("x-api-key");
    
    if (!apiKey) {
      throw errors.unauthorized("Missing API key");
    }

    // In a real implementation, you would validate the API key against a database
    // For now, we'll use a simple environment variable check
    const validApiKey = Deno.env.get("BILLING_API_KEY");
    
    if (!validApiKey || apiKey !== validApiKey) {
      throw errors.unauthorized("Invalid API key");
    }

    // For API key auth, we might not have a specific user but we can set service info
    ctx.state.apiAuth = true;
    ctx.state.service = "internal";

    logger.debug("API key authenticated", {
      service: "internal",
    });

    await next();
  } catch (error) {
    logger.warn("API key authentication failed", {
      error: (error as Error).message,
      ip: ctx.request.headers.get("x-forwarded-for") || 
          ctx.request.headers.get("x-real-ip") || 
          "unknown",
    });
    throw error;
  }
};

/**
 * Permission-based authorization middleware
 */
export function requirePermission(permission: string): Middleware {
  return async (ctx: Context, next) => {
    const user = ctx.state.user as User;
    
    if (!user) {
      throw errors.unauthorized("Authentication required");
    }

    if (!user.permissions.includes(permission) && user.role !== "admin") {
      logger.warn("Permission denied", {
        userId: user.id,
        tenantId: user.tenantId,
        requiredPermission: permission,
        userPermissions: user.permissions,
        userRole: user.role,
      });
      throw errors.forbidden(`Permission required: ${permission}`);
    }

    logger.debug("Permission granted", {
      userId: user.id,
      tenantId: user.tenantId,
      permission,
    });

    await next();
  };
}

/**
 * Role-based authorization middleware
 */
export function requireRole(role: string): Middleware {
  return async (ctx: Context, next) => {
    const user = ctx.state.user as User;
    
    if (!user) {
      throw errors.unauthorized("Authentication required");
    }

    if (user.role !== role && user.role !== "admin") {
      logger.warn("Role access denied", {
        userId: user.id,
        tenantId: user.tenantId,
        requiredRole: role,
        userRole: user.role,
      });
      throw errors.forbidden(`Role required: ${role}`);
    }

    logger.debug("Role access granted", {
      userId: user.id,
      tenantId: user.tenantId,
      role,
    });

    await next();
  };
}

/**
 * Billing-specific permission checks
 */
export const billingPermissions = {
  // Subscription permissions
  createSubscription: requirePermission("billing:subscription:create"),
  readSubscription: requirePermission("billing:subscription:read"),
  updateSubscription: requirePermission("billing:subscription:update"),
  cancelSubscription: requirePermission("billing:subscription:cancel"),

  // Invoice permissions
  createInvoice: requirePermission("billing:invoice:create"),
  readInvoice: requirePermission("billing:invoice:read"),
  updateInvoice: requirePermission("billing:invoice:update"),
  deleteInvoice: requirePermission("billing:invoice:delete"),

  // Payment permissions
  processPayment: requirePermission("billing:payment:process"),
  readPayment: requirePermission("billing:payment:read"),
  refundPayment: requirePermission("billing:payment:refund"),

  // Usage permissions
  reportUsage: requirePermission("billing:usage:report"),
  readUsage: requirePermission("billing:usage:read"),

  // Plan permissions
  readPlan: requirePermission("billing:plan:read"),
  managePlan: requirePermission("billing:plan:manage"),

  // Admin permissions
  adminAccess: requireRole("admin"),
  billingAdmin: requirePermission("billing:admin"),
};

/**
 * Optional authentication middleware (for public endpoints that can benefit from auth)
 */
export const optionalAuthMiddleware: Middleware = async (ctx: Context, next) => {
  try {
    await authMiddleware(ctx, next);
  } catch (error) {
    // If authentication fails, continue without user context
    logger.debug("Optional authentication failed, continuing without auth", {
      error: (error as Error).message,
    });
    await next();
  }
};

/**
 * Tenant ownership validation middleware
 */
export const validateTenantOwnership: Middleware = async (ctx: Context, next) => {
  const user = ctx.state.user as User;
  const requestTenantId = (ctx as Context & { params: Record<string, string> }).params?.tenantId || ctx.state.tenantId;
  
  if (!user) {
    throw errors.unauthorized("Authentication required");
  }

  if (user.tenantId !== requestTenantId && user.role !== "admin") {
    logger.warn("Tenant access denied", {
      userId: user.id,
      userTenantId: user.tenantId,
      requestedTenantId: requestTenantId,
    });
    throw errors.forbidden("Access denied to tenant resources");
  }

  await next();
};
