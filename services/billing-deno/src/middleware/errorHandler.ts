import { Context, Middleware } from "@oak/oak";
import { logger } from "../utils/logger.ts";

export interface ApiError extends Error {
  status?: number;
  code?: string;
  details?: unknown;
}

export interface StripeError extends ApiError {
  stripeCode?: string;
  declineCode?: string;
  param?: string;
}

/**
 * Global error handling middleware
 */
export const errorHandler: Middleware = async (ctx: Context, next) => {
  try {
    await next();
  } catch (error) {
    const apiError = error as ApiError;
    
    // Default error response
    let status = 500;
    let code = "INTERNAL_SERVER_ERROR";
    let message = "An unexpected error occurred";
    let details: unknown = undefined;

    // Handle different error types
    if (apiError.status) {
      status = apiError.status;
    }

    if (apiError.code) {
      code = apiError.code;
    }

    if (apiError.message) {
      message = apiError.message;
    }

    if (apiError.details) {
      details = apiError.details;
    }

    // Handle specific error types
    const errorName = (error as { name?: string }).name;
    if (errorName === "ValidationError") {
      status = 400;
      code = "VALIDATION_ERROR";
      message = "Request validation failed";
      details = apiError.details || apiError.message;
    } else if (errorName === "UnauthorizedError" || message.includes("Unauthorized")) {
      status = 401;
      code = "UNAUTHORIZED";
      message = "Authentication required";
    } else if (errorName === "ForbiddenError" || message.includes("Forbidden")) {
      status = 403;
      code = "FORBIDDEN";
      message = "Access denied";
    } else if (errorName === "NotFoundError" || message.includes("Not found")) {
      status = 404;
      code = "NOT_FOUND";
      message = "Resource not found";
    } else if (errorName === "ConflictError" || message.includes("Conflict")) {
      status = 409;
      code = "CONFLICT";
      message = "Resource conflict";
    } else if (errorName === "RateLimitError" || message.includes("Rate limit")) {
      status = 429;
      code = "RATE_LIMIT_EXCEEDED";
      message = "Rate limit exceeded";
    }

    // Billing-specific errors
    if (message.includes("stripe") || message.includes("Stripe")) {
      status = 402;
      code = "PAYMENT_ERROR";
      message = "Payment processing failed";
    } else if (message.includes("subscription") || message.includes("Subscription")) {
      status = 422;
      code = "SUBSCRIPTION_ERROR";
      message = "Subscription operation failed";
    } else if (message.includes("invoice") || message.includes("Invoice")) {
      status = 422;
      code = "INVOICE_ERROR";
      message = "Invoice operation failed";
    } else if (message.includes("billing") || message.includes("Billing")) {
      status = 422;
      code = "BILLING_ERROR";
      message = "Billing operation failed";
    } else if (message.includes("usage") || message.includes("Usage")) {
      status = 422;
      code = "USAGE_ERROR";
      message = "Usage tracking failed";
    }

    // Stripe-specific error handling
    const stripeError = error as StripeError;
    if (stripeError.stripeCode) {
      switch (stripeError.stripeCode) {
        case "card_declined":
          status = 402;
          code = "CARD_DECLINED";
          message = "Your card was declined";
          break;
        case "insufficient_funds":
          status = 402;
          code = "INSUFFICIENT_FUNDS";
          message = "Insufficient funds";
          break;
        case "expired_card":
          status = 402;
          code = "EXPIRED_CARD";
          message = "Your card has expired";
          break;
        case "incorrect_cvc":
          status = 402;
          code = "INCORRECT_CVC";
          message = "Your card's security code is incorrect";
          break;
        case "processing_error":
          status = 402;
          code = "PROCESSING_ERROR";
          message = "An error occurred while processing your card";
          break;
        case "rate_limit":
          status = 429;
          code = "STRIPE_RATE_LIMIT";
          message = "Too many requests to Stripe";
          break;
        default:
          status = 402;
          code = "STRIPE_ERROR";
          message = `Stripe error: ${stripeError.stripeCode}`;
      }
    }

    // Database-specific errors
    if (message.includes("duplicate key") || message.includes("unique constraint")) {
      status = 409;
      code = "DUPLICATE_RESOURCE";
      message = "Resource already exists";
    } else if (message.includes("foreign key") || message.includes("constraint")) {
      status = 400;
      code = "CONSTRAINT_VIOLATION";
      message = "Data constraint violation";
    } else if (message.includes("connection") || message.includes("timeout")) {
      status = 503;
      code = "SERVICE_UNAVAILABLE";
      message = "Service temporarily unavailable";
    }

    // Log error with appropriate level
    const logContext = {
      method: ctx.request.method,
      url: ctx.request.url.pathname,
      status,
      code,
      userAgent: ctx.request.headers.get("user-agent"),
      ip: ctx.request.headers.get("x-forwarded-for") || 
          ctx.request.headers.get("x-real-ip") || 
          "unknown",
      tenantId: ctx.request.headers.get("x-tenant-id") || ctx.state.tenantId,
      userId: ctx.state.user?.id,
      stripeCode: stripeError.stripeCode,
      declineCode: stripeError.declineCode,
    };

    if (status >= 500) {
      logger.error("Server error", {
        ...logContext,
        error: apiError.message,
        stack: apiError.stack,
      });
    } else if (status >= 400) {
      logger.warn("Client error", {
        ...logContext,
        error: apiError.message,
      });
    }

    // Set response
    ctx.response.status = status;
    ctx.response.body = {
      success: false,
      error: {
        code,
        message,
        ...(details && typeof details === 'object' && details !== null ? { details } : {}),
        ...(stripeError.stripeCode && { stripeCode: stripeError.stripeCode }),
        ...(stripeError.declineCode && { declineCode: stripeError.declineCode }),
        ...(stripeError.param && { param: stripeError.param }),
        ...(Deno.env.get("NODE_ENV") === "development" && status >= 500 && {
          stack: apiError.stack,
        }),
      },
      timestamp: new Date().toISOString(),
    };

    // Set appropriate headers
    ctx.response.headers.set("Content-Type", "application/json");
    
    // Add retry-after header for rate limiting
    if (status === 429) {
      ctx.response.headers.set("Retry-After", "60");
    }
  }
};

/**
 * Create a custom API error
 */
export function createApiError(
  message: string,
  status: number = 500,
  code?: string,
  details?: unknown,
): ApiError {
  const error = new Error(message) as ApiError;
  error.status = status;
  error.code = code;
  error.details = details;
  return error;
}

/**
 * Create a Stripe-specific error
 */
export function createStripeError(
  message: string,
  status: number = 402,
  code?: string,
  stripeCode?: string,
  declineCode?: string,
  param?: string,
): StripeError {
  const error = createApiError(message, status, code) as StripeError;
  error.stripeCode = stripeCode;
  error.declineCode = declineCode;
  error.param = param;
  return error;
}

/**
 * Common error creators
 */
export const errors = {
  badRequest: (message: string, details?: unknown) =>
    createApiError(message, 400, "BAD_REQUEST", details),

  unauthorized: (message: string = "Authentication required") =>
    createApiError(message, 401, "UNAUTHORIZED"),

  forbidden: (message: string = "Access denied") =>
    createApiError(message, 403, "FORBIDDEN"),

  notFound: (message: string = "Resource not found") =>
    createApiError(message, 404, "NOT_FOUND"),

  conflict: (message: string, details?: unknown) =>
    createApiError(message, 409, "CONFLICT", details),

  unprocessableEntity: (message: string, details?: unknown) =>
    createApiError(message, 422, "UNPROCESSABLE_ENTITY", details),

  tooManyRequests: (message: string = "Rate limit exceeded") =>
    createApiError(message, 429, "RATE_LIMIT_EXCEEDED"),

  internalServerError: (message: string = "Internal server error") =>
    createApiError(message, 500, "INTERNAL_SERVER_ERROR"),

  serviceUnavailable: (message: string = "Service unavailable") =>
    createApiError(message, 503, "SERVICE_UNAVAILABLE"),

  validation: (message: string, details?: unknown) =>
    createApiError(message, 400, "VALIDATION_ERROR", details),

  // Billing-specific errors
  paymentRequired: (message: string, details?: unknown) =>
    createApiError(message, 402, "PAYMENT_REQUIRED", details),

  subscriptionError: (message: string, details?: unknown) =>
    createApiError(message, 422, "SUBSCRIPTION_ERROR", details),

  invoiceError: (message: string, details?: unknown) =>
    createApiError(message, 422, "INVOICE_ERROR", details),

  billingError: (message: string, details?: unknown) =>
    createApiError(message, 422, "BILLING_ERROR", details),

  usageError: (message: string, details?: unknown) =>
    createApiError(message, 422, "USAGE_ERROR", details),

  // Stripe-specific errors
  stripeError: (message: string, stripeCode?: string, declineCode?: string, param?: string) =>
    createStripeError(message, 402, "STRIPE_ERROR", stripeCode, declineCode, param),

  cardDeclined: (message: string = "Your card was declined", declineCode?: string) =>
    createStripeError(message, 402, "CARD_DECLINED", "card_declined", declineCode),

  insufficientFunds: (message: string = "Insufficient funds") =>
    createStripeError(message, 402, "INSUFFICIENT_FUNDS", "insufficient_funds"),

  expiredCard: (message: string = "Your card has expired") =>
    createStripeError(message, 402, "EXPIRED_CARD", "expired_card"),

  incorrectCvc: (message: string = "Your card's security code is incorrect") =>
    createStripeError(message, 402, "INCORRECT_CVC", "incorrect_cvc"),
};
