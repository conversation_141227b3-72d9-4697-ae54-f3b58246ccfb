import { Context, Middleware } from "@oak/oak";
import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { errors } from "./errorHandler.ts";
import { getRedisClient } from "../utils/redis.ts";

interface RateLimitOptions {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (ctx: Context) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  onLimitReached?: (ctx: Context) => void;
}

interface RateLimitInfo {
  totalHits: number;
  totalHitsInWindow: number;
  resetTime: Date;
  remainingRequests: number;
}

/**
 * Redis-based rate limiter
 */
class RedisRateLimiter {
  private options: RateLimitOptions;

  constructor(options: RateLimitOptions) {
    this.options = {
      windowMs: options.windowMs || 15 * 60 * 1000, // 15 minutes default
      maxRequests: options.maxRequests || 1000, // 1000 requests default
      keyGenerator: options.keyGenerator || ((ctx) => this.defaultKeyGenerator(ctx)),
      skipSuccessfulRequests: options.skipSuccessfulRequests || false,
      skipFailedRequests: options.skipFailedRequests || false,
      onLimitReached: options.onLimitReached,
    };
  }

  private defaultKeyGenerator(ctx: Context): string {
    const ip = ctx.request.headers.get("x-forwarded-for") || 
               ctx.request.headers.get("x-real-ip") || 
               "unknown";
    const tenantId = ctx.state.tenantId;
    const userId = ctx.state.user?.id;

    if (tenantId) {
      return `rate_limit:tenant:${tenantId}`;
    } else if (userId) {
      return `rate_limit:user:${userId}`;
    } else {
      return `rate_limit:ip:${ip}`;
    }
  }

  async checkLimit(ctx: Context): Promise<RateLimitInfo> {
    const redis = getRedisClient();
    const key = this.options.keyGenerator!(ctx);
    const now = Date.now();
    const windowStart = now - this.options.windowMs;

    try {
      // Use Redis sorted set to track requests in time window
      const pipeline = redis.pipeline();
      
      // Remove old entries outside the window
      pipeline.zremrangebyscore(key, 0, windowStart);
      
      // Add current request
      pipeline.zadd(key, now, `${now}-${Math.random()}`);
      
      // Count requests in current window
      pipeline.zcard(key);
      
      // Set expiration
      pipeline.expire(key, Math.ceil(this.options.windowMs / 1000));
      
      const results = await pipeline.exec();
      const totalHitsInWindow = results[2] as number;

      const resetTime = new Date(now + this.options.windowMs);
      const remainingRequests = Math.max(0, this.options.maxRequests - totalHitsInWindow);

      return {
        totalHits: totalHitsInWindow,
        totalHitsInWindow,
        resetTime,
        remainingRequests,
      };
    } catch (error) {
      logger.error("Rate limit check failed", {
        key,
        error: (error as Error).message,
      });
      
      // If Redis fails, allow the request but log the error
      return {
        totalHits: 0,
        totalHitsInWindow: 0,
        resetTime: new Date(now + this.options.windowMs),
        remainingRequests: this.options.maxRequests,
      };
    }
  }

  middleware(): Middleware {
    return async (ctx: Context, next) => {
      const limitInfo = await this.checkLimit(ctx);

      // Set rate limit headers
      ctx.response.headers.set("X-RateLimit-Limit", this.options.maxRequests.toString());
      ctx.response.headers.set("X-RateLimit-Remaining", limitInfo.remainingRequests.toString());
      ctx.response.headers.set("X-RateLimit-Reset", Math.ceil(limitInfo.resetTime.getTime() / 1000).toString());

      if (limitInfo.totalHitsInWindow > this.options.maxRequests) {
        // Rate limit exceeded
        const retryAfter = Math.ceil((limitInfo.resetTime.getTime() - Date.now()) / 1000);
        ctx.response.headers.set("Retry-After", retryAfter.toString());

        logger.warn("Rate limit exceeded", {
          key: this.options.keyGenerator!(ctx),
          totalHits: limitInfo.totalHitsInWindow,
          maxRequests: this.options.maxRequests,
          resetTime: limitInfo.resetTime,
          ip: ctx.request.headers.get("x-forwarded-for") || 
              ctx.request.headers.get("x-real-ip") || 
              "unknown",
          tenantId: ctx.state.tenantId,
          userId: ctx.state.user?.id,
        });

        if (this.options.onLimitReached) {
          this.options.onLimitReached(ctx);
        }

        throw errors.tooManyRequests("Rate limit exceeded");
      }

      await next();

      // Optionally remove the request from count if it was successful/failed
      if ((this.options.skipSuccessfulRequests && ctx.response.status < 400) ||
          (this.options.skipFailedRequests && ctx.response.status >= 400)) {
        try {
          const redis = getRedisClient();
          const key = this.options.keyGenerator!(ctx);
          await redis.zremrangebyrank(key, -1, -1); // Remove the last added entry
        } catch (error) {
          logger.warn("Failed to remove request from rate limit count", {
            error: (error as Error).message,
          });
        }
      }
    };
  }
}

/**
 * Basic rate limiter using global configuration
 */
export const rateLimiter = new RedisRateLimiter({
  windowMs: config.rateLimit.windowMs,
  maxRequests: config.rateLimit.maxRequests,
}).middleware();

/**
 * Strict rate limiter for sensitive billing operations
 */
export const strictRateLimiter = new RedisRateLimiter({
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 50, // 50 requests per 5 minutes
  keyGenerator: (ctx) => {
    const tenantId = ctx.state.tenantId;
    const userId = ctx.state.user?.id;
    return `strict_rate_limit:${tenantId || userId || "anonymous"}`;
  },
}).middleware();

/**
 * Webhook rate limiter
 */
export const webhookRateLimiter = new RedisRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  maxRequests: 100, // 100 webhooks per minute
  keyGenerator: (ctx) => {
    const source = ctx.request.headers.get("user-agent") || "unknown";
    return `webhook_rate_limit:${source}`;
  },
}).middleware();

/**
 * Per-tenant rate limiter
 */
export function createTenantRateLimiter(maxRequests: number, windowMs: number = 15 * 60 * 1000): Middleware {
  return new RedisRateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (ctx) => {
      const tenantId = ctx.state.tenantId;
      if (!tenantId) {
        throw errors.badRequest("Tenant ID required for tenant rate limiting");
      }
      return `tenant_rate_limit:${tenantId}`;
    },
  }).middleware();
}

/**
 * Per-user rate limiter
 */
export function createUserRateLimiter(maxRequests: number, windowMs: number = 15 * 60 * 1000): Middleware {
  return new RedisRateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (ctx) => {
      const userId = ctx.state.user?.id;
      if (!userId) {
        throw errors.unauthorized("Authentication required for user rate limiting");
      }
      return `user_rate_limit:${userId}`;
    },
  }).middleware();
}

/**
 * IP-based rate limiter
 */
export function createIpRateLimiter(maxRequests: number, windowMs: number = 15 * 60 * 1000): Middleware {
  return new RedisRateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (ctx) => {
      const ip = ctx.request.headers.get("x-forwarded-for") || 
                 ctx.request.headers.get("x-real-ip") || 
                 "unknown";
      return `ip_rate_limit:${ip}`;
    },
  }).middleware();
}

/**
 * Subscription-based rate limiter (based on plan limits)
 */
export const subscriptionRateLimiter: Middleware = async (ctx, next) => {
  const tenantId = ctx.state.tenantId;
  
  if (!tenantId) {
    await next();
    return;
  }

  try {
    // In a real implementation, you would:
    // 1. Fetch the tenant's subscription plan
    // 2. Get the rate limits for that plan
    // 3. Apply the appropriate rate limiting

    // For now, we'll use a basic tenant-based rate limiter
    const basicTenantLimiter = createTenantRateLimiter(1000, 15 * 60 * 1000);
    await basicTenantLimiter(ctx, next);
  } catch (error) {
    logger.error("Subscription rate limiter error", {
      tenantId,
      error: (error as Error).message,
    });
    throw error;
  }
};

/**
 * Billing operation specific rate limiters
 */
export const billingRateLimiters = {
  // Subscription operations - more restrictive
  subscription: createTenantRateLimiter(100, 15 * 60 * 1000), // 100 per 15 min
  
  // Payment operations - very restrictive
  payment: createTenantRateLimiter(20, 5 * 60 * 1000), // 20 per 5 min
  
  // Invoice operations - moderate
  invoice: createTenantRateLimiter(200, 15 * 60 * 1000), // 200 per 15 min
  
  // Usage reporting - less restrictive
  usage: createTenantRateLimiter(1000, 15 * 60 * 1000), // 1000 per 15 min
  
  // Plan queries - least restrictive
  plan: createTenantRateLimiter(500, 15 * 60 * 1000), // 500 per 15 min
};
