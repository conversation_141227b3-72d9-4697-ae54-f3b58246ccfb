import { Context, Middleware } from "@oak/oak";
import { logger } from "../utils/logger.ts";
import { errors } from "./errorHandler.ts";
import { User } from "./auth.ts";

/**
 * Tenant isolation middleware
 */
export const tenantMiddleware: Middleware = async (ctx: Context, next) => {
  try {
    // Get tenant ID from various sources
    let tenantId: string | null = null;
    
    // 1. From authenticated user (most common)
    const user = ctx.state.user as User;
    if (user && user.tenantId) {
      tenantId = user.tenantId;
    }
    
    // 2. From X-Tenant-ID header (for service-to-service calls)
    if (!tenantId) {
      tenantId = ctx.request.headers.get("x-tenant-id");
    }
    
    // 3. From URL parameters
    if (!tenantId && (ctx as Context & { params: Record<string, string> }).params?.tenantId) {
      tenantId = (ctx as Context & { params: Record<string, string> }).params.tenantId;
    }
    
    // 4. From query parameters
    if (!tenantId) {
      const url = new URL(ctx.request.url);
      tenantId = url.searchParams.get("tenantId");
    }

    if (!tenantId) {
      throw errors.badRequest("Tenant ID is required");
    }

    // Validate tenant ID format (UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(tenantId)) {
      throw errors.badRequest("Invalid tenant ID format");
    }

    // Validate tenant access for authenticated users
    if (user && user.tenantId !== tenantId && user.role !== "admin") {
      logger.warn("Tenant access violation attempt", {
        userId: user.id,
        userTenantId: user.tenantId,
        requestedTenantId: tenantId,
        ip: ctx.request.headers.get("x-forwarded-for") || 
            ctx.request.headers.get("x-real-ip") || 
            "unknown",
      });
      throw errors.forbidden("Access denied to tenant resources");
    }

    // Store tenant ID in context state
    ctx.state.tenantId = tenantId;

    logger.debug("Tenant context established", {
      tenantId,
      userId: user?.id,
      userRole: user?.role,
    });

    await next();
  } catch (error) {
    logger.error("Tenant middleware error", {
      error: (error as Error).message,
      url: ctx.request.url.pathname,
      method: ctx.request.method,
    });
    throw error;
  }
};

/**
 * Validate tenant exists and is active
 */
export const validateTenantStatus: Middleware = async (ctx: Context, next) => {
  const tenantId = ctx.state.tenantId;
  
  if (!tenantId) {
    throw errors.badRequest("Tenant ID is required");
  }

  try {
    // In a real implementation, you would check the tenant status in the database
    // For now, we'll assume all tenants are active
    // This is where you would add tenant validation logic:
    // - Check if tenant exists
    // - Check if tenant is active
    // - Check if tenant subscription is valid
    // - Check if tenant is not suspended

    logger.debug("Tenant status validated", {
      tenantId,
      status: "active",
    });

    await next();
  } catch (error) {
    logger.error("Tenant validation failed", {
      tenantId,
      error: (error as Error).message,
    });
    throw errors.forbidden("Tenant access denied");
  }
};

/**
 * Extract tenant ID from Stripe webhook events
 */
export const extractTenantFromStripeWebhook: Middleware = async (ctx: Context, next) => {
  try {
    const body = ctx.state.webhookBody;
    
    if (!body || !body.data || !body.data.object) {
      throw errors.badRequest("Invalid webhook payload");
    }

    const stripeObject = body.data.object;
    let tenantId: string | null = null;

    // Extract tenant ID from metadata
    if (stripeObject.metadata && stripeObject.metadata.tenant_id) {
      tenantId = stripeObject.metadata.tenant_id;
    }
    
    // For subscription events, check the subscription metadata
    if (!tenantId && stripeObject.subscription) {
      const subscription = stripeObject.subscription;
      if (subscription.metadata && subscription.metadata.tenant_id) {
        tenantId = subscription.metadata.tenant_id;
      }
    }
    
    // For invoice events, check the subscription metadata
    if (!tenantId && stripeObject.lines && stripeObject.lines.data) {
      for (const line of stripeObject.lines.data) {
        if (line.subscription_item && line.subscription_item.subscription) {
          // In a real implementation, you would fetch the subscription from Stripe
          // and extract the tenant ID from its metadata
        }
      }
    }

    if (!tenantId) {
      logger.warn("No tenant ID found in Stripe webhook", {
        eventType: body.type,
        objectType: stripeObject.object,
        objectId: stripeObject.id,
      });
      throw errors.badRequest("Tenant ID not found in webhook metadata");
    }

    ctx.state.tenantId = tenantId;

    logger.debug("Tenant extracted from Stripe webhook", {
      tenantId,
      eventType: body.type,
      objectId: stripeObject.id,
    });

    await next();
  } catch (error) {
    logger.error("Failed to extract tenant from Stripe webhook", {
      error: (error as Error).message,
    });
    throw error;
  }
};

/**
 * Tenant-specific rate limiting
 */
export const tenantRateLimit: Middleware = async (ctx: Context, next) => {
  const tenantId = ctx.state.tenantId;
  
  if (!tenantId) {
    await next();
    return;
  }

  try {
    // In a real implementation, you would implement Redis-based rate limiting
    // per tenant to prevent abuse and ensure fair usage
    
    // Example rate limiting logic:
    // - Check current request count for tenant
    // - Apply tenant-specific limits based on subscription plan
    // - Block requests if limit exceeded
    // - Set appropriate headers

    logger.debug("Tenant rate limit check passed", {
      tenantId,
    });

    await next();
  } catch (error) {
    logger.warn("Tenant rate limit exceeded", {
      tenantId,
      error: (error as Error).message,
    });
    throw errors.tooManyRequests("Tenant rate limit exceeded");
  }
};

/**
 * Tenant context cleanup middleware
 */
export const cleanupTenantContext: Middleware = async (ctx: Context, next) => {
  try {
    await next();
  } finally {
    // Clean up any tenant-specific resources or connections
    // This is useful for ensuring proper cleanup after request processing
    
    const tenantId = ctx.state.tenantId;
    if (tenantId) {
      logger.debug("Cleaning up tenant context", {
        tenantId,
      });
      
      // Example cleanup operations:
      // - Close tenant-specific database connections
      // - Clear tenant-specific cache entries
      // - Log tenant activity metrics
    }
  }
};

/**
 * Tenant feature flag middleware
 */
export function requireTenantFeature(feature: string): Middleware {
  return async (ctx: Context, next) => {
    const tenantId = ctx.state.tenantId;
    
    if (!tenantId) {
      throw errors.badRequest("Tenant ID is required");
    }

    try {
      // In a real implementation, you would check if the tenant
      // has access to the specific feature based on their subscription plan
      // or feature flags
      
      logger.debug("Tenant feature access granted", {
        tenantId,
        feature,
      });

      await next();
    } catch (error) {
      logger.warn("Tenant feature access denied", {
        tenantId,
        feature,
        error: (error as Error).message,
      });
      throw errors.forbidden(`Feature not available: ${feature}`);
    }
  };
}

/**
 * Tenant subscription validation middleware
 */
export const validateTenantSubscription: Middleware = async (ctx: Context, next) => {
  const tenantId = ctx.state.tenantId;
  
  if (!tenantId) {
    throw errors.badRequest("Tenant ID is required");
  }

  try {
    // In a real implementation, you would:
    // 1. Check if tenant has an active subscription
    // 2. Validate subscription status
    // 3. Check if subscription allows the requested operation
    // 4. Verify usage limits haven't been exceeded

    logger.debug("Tenant subscription validated", {
      tenantId,
      subscriptionStatus: "active",
    });

    await next();
  } catch (error) {
    logger.warn("Tenant subscription validation failed", {
      tenantId,
      error: (error as Error).message,
    });
    throw errors.forbidden("Invalid or inactive subscription");
  }
};
