import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { getStripeService } from "../services/index.ts";
import { errors } from "../middleware/errorHandler.ts";
import { billingPermissions } from "../middleware/auth.ts";
import { ApiResponse, parseRequestBody } from "./index.ts";

export const billingRoutes = new Router();

// Zod schemas
const CreatePortalSessionSchema = z.object({
  returnUrl: z.string().url("Valid return URL is required"),
});

const CreateCheckoutSessionSchema = z.object({
  mode: z.enum(["payment", "setup", "subscription"]).default("subscription"),
  lineItems: z.array(z.object({
    price: z.string().min(1, "Price ID is required"),
    quantity: z.number().int().min(1).default(1),
  })).min(1, "At least one line item is required"),
  successUrl: z.string().url("Valid success URL is required"),
  cancelUrl: z.string().url("Valid cancel URL is required"),
  allowPromotionCodes: z.boolean().default(true),
  metadata: z.record(z.string()).optional(),
});

/**
 * Get customer billing information
 * GET /api/billing/customer
 */
billingRoutes.get("/customer",
  billingPermissions.readSubscription,
  async (ctx) => {
    try {
      // In a real implementation, you would:
      // 1. Get customer ID from tenant
      // 2. Fetch customer from Stripe
      // 3. Return customer billing information
      
      ctx.response.body = ApiResponse.success({
        message: "Customer billing information endpoint",
        tenantId: ctx.state.tenantId,
      });
    } catch (error) {
      throw error;
    }
  }
);

/**
 * Create billing portal session
 * POST /api/billing/portal
 */
billingRoutes.post("/portal",
  billingPermissions.readSubscription,
  async (ctx) => {
    try {
      const body = await parseRequestBody(ctx);
      const validatedData = CreatePortalSessionSchema.parse(body);

      // In a real implementation, you would:
      // 1. Get customer ID from tenant
      // 2. Create billing portal session
      // 3. Return session URL
      
      ctx.response.body = ApiResponse.success({
        url: "https://billing.stripe.com/session/...",
        returnUrl: validatedData.returnUrl,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        ctx.response.status = 400;
        ctx.response.body = ApiResponse.error(
          "Validation failed",
          "VALIDATION_ERROR",
          400,
          error.errors
        );
        return;
      }
      throw error;
    }
  }
);

/**
 * Create checkout session
 * POST /api/billing/checkout
 */
billingRoutes.post("/checkout",
  billingPermissions.createSubscription,
  async (ctx) => {
    try {
      const body = await parseRequestBody(ctx);
      const validatedData = CreateCheckoutSessionSchema.parse(body);

      const stripeService = getStripeService();
      const session = await stripeService.createCheckoutSession({
        ...validatedData,
        metadata: {
          tenant_id: ctx.state.tenantId,
          ...validatedData.metadata,
        },
      });

      ctx.response.status = 201;
      ctx.response.body = ApiResponse.success({
        sessionId: session.id,
        url: session.url,
      }, 201);
    } catch (error) {
      if (error instanceof z.ZodError) {
        ctx.response.status = 400;
        ctx.response.body = ApiResponse.error(
          "Validation failed",
          "VALIDATION_ERROR",
          400,
          error.errors
        );
        return;
      }
      throw error;
    }
  }
);
