import { Router } from "@oak/oak";
import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { healthCheck as dbHealthCheck, getConnectionStats } from "../utils/database.ts";
import { healthCheck as redisHealthCheck } from "../utils/redis.ts";
import { servicesHealthCheck, getServiceStats } from "../services/index.ts";
import { ApiResponse } from "./index.ts";

export const healthRoutes = new Router();

/**
 * Basic health check endpoint
 * GET /health
 */
healthRoutes.get("/", async (ctx) => {
  const startTime = Date.now();
  
  try {
    // Basic service health
    const health = {
      status: "healthy",
      service: "billing-service",
      version: config.version,
      environment: config.nodeEnv,
      timestamp: new Date().toISOString(),
      uptime: process.uptime ? process.uptime() : 0,
      responseTime: 0,
    };

    health.responseTime = Date.now() - startTime;

    ctx.response.status = 200;
    ctx.response.body = ApiResponse.success(health);

    logger.debug("Health check completed", {
      responseTime: health.responseTime,
      status: health.status,
    });
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    logger.error("Health check failed", {
      error: (error as Error).message,
      responseTime,
    });

    ctx.response.status = 503;
    ctx.response.body = ApiResponse.error(
      "Service unhealthy",
      "SERVICE_UNHEALTHY",
      503,
      {
        responseTime,
        error: (error as Error).message,
      }
    );
  }
});

/**
 * Detailed health check endpoint
 * GET /health/detailed
 */
healthRoutes.get("/detailed", async (ctx) => {
  const startTime = Date.now();
  
  try {
    // Check all dependencies
    const [
      databaseHealth,
      redisHealth,
      servicesHealth,
    ] = await Promise.allSettled([
      dbHealthCheck(),
      redisHealthCheck(),
      servicesHealthCheck(),
    ]);

    const connectionStats = getConnectionStats();
    const serviceStats = getServiceStats();

    const health = {
      status: "healthy",
      service: "billing-service",
      version: config.version,
      environment: config.nodeEnv,
      timestamp: new Date().toISOString(),
      uptime: process.uptime ? process.uptime() : 0,
      responseTime: 0,
      dependencies: {
        database: {
          status: databaseHealth.status === "fulfilled" && databaseHealth.value ? "healthy" : "unhealthy",
          responseTime: 0,
          connections: connectionStats,
          error: databaseHealth.status === "rejected" ? databaseHealth.reason?.message : undefined,
        },
        redis: {
          status: redisHealth.status === "fulfilled" && redisHealth.value ? "healthy" : "unhealthy",
          responseTime: 0,
          error: redisHealth.status === "rejected" ? redisHealth.reason?.message : undefined,
        },
        services: {
          status: servicesHealth.status === "fulfilled" ? "healthy" : "unhealthy",
          details: servicesHealth.status === "fulfilled" ? servicesHealth.value : undefined,
          stats: serviceStats,
          error: servicesHealth.status === "rejected" ? servicesHealth.reason?.message : undefined,
        },
      },
      system: {
        memory: {
          used: Deno.memoryUsage ? Deno.memoryUsage().rss : 0,
          heap: Deno.memoryUsage ? Deno.memoryUsage().heapUsed : 0,
          external: Deno.memoryUsage ? Deno.memoryUsage().external : 0,
        },
        platform: Deno.build.os,
        arch: Deno.build.arch,
        version: Deno.version.deno,
      },
    };

    // Determine overall health status
    const isHealthy = 
      health.dependencies.database.status === "healthy" &&
      health.dependencies.redis.status === "healthy" &&
      health.dependencies.services.status === "healthy";

    health.status = isHealthy ? "healthy" : "degraded";
    health.responseTime = Date.now() - startTime;

    const statusCode = isHealthy ? 200 : 503;
    ctx.response.status = statusCode;
    ctx.response.body = ApiResponse.success(health, statusCode);

    logger.debug("Detailed health check completed", {
      responseTime: health.responseTime,
      status: health.status,
      databaseHealth: health.dependencies.database.status,
      redisHealth: health.dependencies.redis.status,
      servicesHealth: health.dependencies.services.status,
    });
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    logger.error("Detailed health check failed", {
      error: (error as Error).message,
      responseTime,
    });

    ctx.response.status = 503;
    ctx.response.body = ApiResponse.error(
      "Service unhealthy",
      "SERVICE_UNHEALTHY",
      503,
      {
        responseTime,
        error: (error as Error).message,
      }
    );
  }
});

/**
 * Readiness check endpoint (for Kubernetes)
 * GET /health/ready
 */
healthRoutes.get("/ready", async (ctx) => {
  try {
    // Check if service is ready to accept traffic
    const [databaseReady, redisReady, servicesReady] = await Promise.allSettled([
      dbHealthCheck(),
      redisHealthCheck(),
      servicesHealthCheck(),
    ]);

    const isReady = 
      databaseReady.status === "fulfilled" && databaseReady.value &&
      redisReady.status === "fulfilled" && redisReady.value &&
      servicesReady.status === "fulfilled";

    if (isReady) {
      ctx.response.status = 200;
      ctx.response.body = ApiResponse.success({
        status: "ready",
        timestamp: new Date().toISOString(),
      });
    } else {
      ctx.response.status = 503;
      ctx.response.body = ApiResponse.error(
        "Service not ready",
        "SERVICE_NOT_READY",
        503,
        {
          database: databaseReady.status === "fulfilled" ? databaseReady.value : false,
          redis: redisReady.status === "fulfilled" ? redisReady.value : false,
          services: servicesReady.status === "fulfilled",
        }
      );
    }
  } catch (error) {
    logger.error("Readiness check failed", {
      error: (error as Error).message,
    });

    ctx.response.status = 503;
    ctx.response.body = ApiResponse.error(
      "Service not ready",
      "SERVICE_NOT_READY",
      503,
      { error: (error as Error).message }
    );
  }
});

/**
 * Liveness check endpoint (for Kubernetes)
 * GET /health/live
 */
healthRoutes.get("/live", (ctx) => {
  // Simple liveness check - if we can respond, we're alive
  ctx.response.status = 200;
  ctx.response.body = ApiResponse.success({
    status: "alive",
    timestamp: new Date().toISOString(),
    uptime: process.uptime ? process.uptime() : 0,
  });
});

/**
 * Metrics endpoint for monitoring
 * GET /health/metrics
 */
healthRoutes.get("/metrics", async (ctx) => {
  try {
    const connectionStats = getConnectionStats();
    const serviceStats = getServiceStats();

    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime ? process.uptime() : 0,
      memory: {
        used: Deno.memoryUsage ? Deno.memoryUsage().rss : 0,
        heap: Deno.memoryUsage ? Deno.memoryUsage().heapUsed : 0,
        external: Deno.memoryUsage ? Deno.memoryUsage().external : 0,
      },
      database: connectionStats,
      services: serviceStats,
      config: {
        environment: config.nodeEnv,
        version: config.version,
        port: config.port,
        logLevel: config.logging.level,
      },
    };

    ctx.response.status = 200;
    ctx.response.body = ApiResponse.success(metrics);
  } catch (error) {
    logger.error("Metrics endpoint failed", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Failed to retrieve metrics",
      "METRICS_ERROR",
      500,
      { error: (error as Error).message }
    );
  }
});
