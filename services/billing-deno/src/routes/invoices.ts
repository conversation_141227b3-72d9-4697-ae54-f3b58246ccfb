import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { getInvoiceService } from "../services/index.ts";
import { errors } from "../middleware/errorHandler.ts";
import { billingPermissions } from "../middleware/auth.ts";
import { ApiResponse, parseRequestBody, parseQueryParams } from "./index.ts";
import { getRequiredParam } from "../types/router.ts";

export const invoiceRoutes = new Router();

// Zod schemas
const CreateInvoiceSchema = z.object({
  customerId: z.string().min(1, "Customer ID is required"),
  subscriptionId: z.string().optional(),
  collectionMethod: z.enum(["charge_automatically", "send_invoice"]).default("charge_automatically"),
  daysUntilDue: z.number().int().min(1).max(90).optional(),
  autoAdvance: z.boolean().default(true),
  metadata: z.record(z.string()).optional(),
});

/**
 * Create invoice
 * POST /api/invoices
 */
invoiceRoutes.post("/",
  billingPermissions.createInvoice,
  async (ctx) => {
    try {
      const body = await parseRequestBody(ctx);
      const validatedData = CreateInvoiceSchema.parse(body);

      const invoiceService = getInvoiceService();
      const invoice = await invoiceService.createInvoice({
        ...validatedData,
        tenantId: ctx.state.tenantId,
      });

      ctx.response.status = 201;
      ctx.response.body = ApiResponse.success({ invoice }, 201);
    } catch (error) {
      if (error instanceof z.ZodError) {
        ctx.response.status = 400;
        ctx.response.body = ApiResponse.error(
          "Validation failed",
          "VALIDATION_ERROR",
          400,
          error.errors
        );
        return;
      }
      throw error;
    }
  }
);

/**
 * Get invoice
 * GET /api/invoices/:invoiceId
 */
invoiceRoutes.get("/:invoiceId",
  billingPermissions.readInvoice,
  async (ctx) => {
    try {
      const invoiceId = getRequiredParam(ctx, "invoiceId");

      const invoiceService = getInvoiceService();
      const invoice = await invoiceService.getInvoice(invoiceId, ctx.state.tenantId);

      ctx.response.body = ApiResponse.success({ invoice });
    } catch (error) {
      throw error;
    }
  }
);

/**
 * List invoices
 * GET /api/invoices
 */
invoiceRoutes.get("/",
  billingPermissions.readInvoice,
  async (ctx) => {
    try {
      const queryParams = parseQueryParams(ctx.request.url);
      
      const filters = {
        tenantId: ctx.state.tenantId,
        subscriptionId: queryParams.getString("subscriptionId"),
        status: queryParams.getString("status") as "draft" | "open" | "paid" | "uncollectible" | "void" | undefined,
        limit: queryParams.getNumber("limit", 50),
        offset: queryParams.getNumber("offset", 0),
      };

      const invoiceService = getInvoiceService();
      const result = await invoiceService.listInvoices(filters);

      ctx.response.body = ApiResponse.paginated(
        result.invoices,
        result.total,
        result.limit,
        result.offset
      );
    } catch (error) {
      throw error;
    }
  }
);

/**
 * Finalize invoice
 * POST /api/invoices/:invoiceId/finalize
 */
invoiceRoutes.post("/:invoiceId/finalize",
  billingPermissions.updateInvoice,
  async (ctx) => {
    try {
      const invoiceId = getRequiredParam(ctx, "invoiceId");

      const invoiceService = getInvoiceService();
      const invoice = await invoiceService.finalizeInvoice(invoiceId, ctx.state.tenantId);

      ctx.response.body = ApiResponse.success({ invoice });
    } catch (error) {
      throw error;
    }
  }
);

/**
 * Pay invoice
 * POST /api/invoices/:invoiceId/pay
 */
invoiceRoutes.post("/:invoiceId/pay",
  billingPermissions.processPayment,
  async (ctx) => {
    try {
      const invoiceId = getRequiredParam(ctx, "invoiceId");

      const body = await parseRequestBody(ctx) as { paymentMethod?: string };
      const paymentMethod = body?.paymentMethod;

      const invoiceService = getInvoiceService();
      const invoice = await invoiceService.payInvoice(invoiceId, ctx.state.tenantId, paymentMethod);

      ctx.response.body = ApiResponse.success({ invoice });
    } catch (error) {
      throw error;
    }
  }
);
