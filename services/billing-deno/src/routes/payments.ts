import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { getPaymentService } from "../services/index.ts";
import { errors } from "../middleware/errorHandler.ts";
import { billingPermissions } from "../middleware/auth.ts";
import { ApiResponse, parseRequestBody, parseQueryParams } from "./index.ts";
import { getRequiredParam } from "../types/router.ts";

export const paymentRoutes = new Router();

// Zod schemas
const CreatePaymentIntentSchema = z.object({
  amount: z.number().int().min(50, "Amount must be at least 50 cents"),
  currency: z.string().length(3, "Currency must be 3 characters").default("USD"),
  customerId: z.string().optional(),
  paymentMethodId: z.string().optional(),
  subscriptionId: z.string().optional(),
  invoiceId: z.string().optional(),
  confirmationMethod: z.enum(["automatic", "manual"]).default("automatic"),
  confirm: z.boolean().default(false),
  metadata: z.record(z.string()).optional(),
});

/**
 * Create payment intent
 * POST /api/payments
 */
paymentRoutes.post("/",
  billingPermissions.processPayment,
  async (ctx) => {
    try {
      const body = await parseRequestBody(ctx);
      const validatedData = CreatePaymentIntentSchema.parse(body);

      const paymentService = getPaymentService();
      const result = await paymentService.createPaymentIntent({
        ...validatedData,
        tenantId: ctx.state.tenantId,
      });

      ctx.response.status = 201;
      ctx.response.body = ApiResponse.success({
        payment: result.payment,
        clientSecret: result.clientSecret,
      }, 201);
    } catch (error) {
      if (error instanceof z.ZodError) {
        ctx.response.status = 400;
        ctx.response.body = ApiResponse.error(
          "Validation failed",
          "VALIDATION_ERROR",
          400,
          error.errors
        );
        return;
      }
      throw error;
    }
  }
);

/**
 * Get payment
 * GET /api/payments/:paymentId
 */
paymentRoutes.get("/:paymentId",
  billingPermissions.readPayment,
  async (ctx) => {
    try {
      const paymentId = getRequiredParam(ctx, "paymentId");

      const paymentService = getPaymentService();
      const payment = await paymentService.getPayment(paymentId, ctx.state.tenantId);

      ctx.response.body = ApiResponse.success({ payment });
    } catch (error) {
      throw error;
    }
  }
);

/**
 * List payments
 * GET /api/payments
 */
paymentRoutes.get("/",
  billingPermissions.readPayment,
  async (ctx) => {
    try {
      const queryParams = parseQueryParams(ctx.request.url);
      
      const filters = {
        tenantId: ctx.state.tenantId,
        subscriptionId: queryParams.getString("subscriptionId"),
        invoiceId: queryParams.getString("invoiceId"),
        status: queryParams.getString("status") as "requires_payment_method" | "requires_confirmation" | "requires_action" | "processing" | "requires_capture" | "canceled" | "succeeded" | undefined,
        limit: queryParams.getNumber("limit", 50),
        offset: queryParams.getNumber("offset", 0),
      };

      const paymentService = getPaymentService();
      const result = await paymentService.listPayments(filters);

      ctx.response.body = ApiResponse.paginated(
        result.payments,
        result.total,
        result.limit,
        result.offset
      );
    } catch (error) {
      throw error;
    }
  }
);
