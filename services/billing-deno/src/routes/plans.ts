import { Router } from "@oak/oak";
import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { billingPermissions } from "../middleware/auth.ts";
import { ApiResponse } from "./index.ts";
import { getRequiredParam } from "../types/router.ts";

export const planRoutes = new Router();

/**
 * Get all available plans
 * GET /api/plans
 */
planRoutes.get("/",
  billingPermissions.readPlan,
  (ctx) => {
    try {
      const plans = Object.values(config.plans).map(plan => ({
        id: plan.id,
        name: plan.name,
        stripePriceId: plan.stripePriceId,
        monthlyPrice: plan.monthlyPrice,
        yearlyPrice: plan.yearlyPrice,
        features: plan.features,
      }));

      ctx.response.body = ApiResponse.success({ plans });
    } catch (error) {
      logger.error("Failed to get plans via API", {
        error: (error as Error).message,
      });
      throw error;
    }
  }
);

/**
 * Get specific plan details
 * GET /api/plans/:planId
 */
planRoutes.get("/:planId",
  billingPermissions.readPlan,
  (ctx) => {
    try {
      const planId = getRequiredParam(ctx, "planId");
      const plan = config.plans[planId as keyof typeof config.plans];

      if (!plan) {
        ctx.response.status = 404;
        ctx.response.body = ApiResponse.error(
          "Plan not found",
          "PLAN_NOT_FOUND",
          404
        );
        return;
      }

      ctx.response.body = ApiResponse.success({
        plan: {
          id: plan.id,
          name: plan.name,
          stripePriceId: plan.stripePriceId,
          monthlyPrice: plan.monthlyPrice,
          yearlyPrice: plan.yearlyPrice,
          features: plan.features,
        }
      });
    } catch (error) {
      logger.error("Failed to get plan via API", {
        error: (error as Error).message,
        planId: getRequiredParam(ctx, "planId"),
      });
      throw error;
    }
  }
);
