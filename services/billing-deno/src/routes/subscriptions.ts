import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { getSubscriptionService } from "../services/index.ts";
import { errors } from "../middleware/errorHandler.ts";
import { billingPermissions } from "../middleware/auth.ts";
import { ApiResponse, parseRequestBody, parseQueryParams } from "./index.ts";
import { getRequiredParam } from "../types/router.ts";
import type { CreateSubscriptionRequest, UpdateSubscriptionRequest } from "../types/billing.ts";

export const subscriptionRoutes = new Router();

// Zod schemas for validation
const CreateSubscriptionSchema = z.object({
  tenantId: z.string().uuid("Valid tenant ID is required"),
  planId: z.enum(["basic", "pro", "enterprise"], {
    errorMap: () => ({ message: "Valid plan ID is required" }),
  }),
  email: z.string().email("Valid email is required"),
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  company: z.string().max(100).optional(),
  paymentMethodId: z.string().optional(),
  couponId: z.string().optional(),
  trialDays: z.number().int().min(0).max(30).optional(),
});

const UpdateSubscriptionSchema = z.object({
  planId: z.enum(["basic", "pro", "enterprise"]).optional(),
  cancelAtPeriodEnd: z.boolean().optional(),
  metadata: z.record(z.string()).optional(),
});

const CancelSubscriptionSchema = z.object({
  immediate: z.boolean().default(false),
  reason: z.string().max(500).optional(),
  invoiceNow: z.boolean().default(false),
  prorate: z.boolean().default(true),
});

const ListSubscriptionsSchema = z.object({
  tenantId: z.string().uuid().optional(),
  status: z.enum(["incomplete", "incomplete_expired", "trialing", "active", "past_due", "canceled", "unpaid"]).optional(),
  planId: z.enum(["basic", "pro", "enterprise"]).optional(),
  limit: z.number().int().min(1).max(100).default(50),
  offset: z.number().int().min(0).default(0),
});

/**
 * Create a new subscription
 * POST /api/subscriptions
 */
subscriptionRoutes.post("/",
  billingPermissions.createSubscription,
  async (ctx) => {
    try {
      const body = await parseRequestBody<CreateSubscriptionRequest>(ctx);
      const validatedData = CreateSubscriptionSchema.parse(body);

      const subscriptionService = getSubscriptionService();
      const result = await subscriptionService.createSubscription({
        ...validatedData,
        userId: ctx.state.user?.id,
      });

      logger.info("Subscription created via API", {
        subscriptionId: result.subscription.id,
        tenantId: validatedData.tenantId,
        planId: validatedData.planId,
        userId: ctx.state.user?.id,
      });

      ctx.response.status = 201;
      ctx.response.body = ApiResponse.success({
        subscription: result.subscription,
        clientSecret: result.clientSecret,
      }, 201);
    } catch (error) {
      if (error instanceof z.ZodError) {
        ctx.response.status = 400;
        ctx.response.body = ApiResponse.error(
          "Validation failed",
          "VALIDATION_ERROR",
          400,
          error.errors
        );
        return;
      }

      logger.error("Failed to create subscription via API", {
        error: (error as Error).message,
        tenantId: ctx.state.tenantId,
        userId: ctx.state.user?.id,
      });

      throw error;
    }
  }
);

/**
 * Get subscription details
 * GET /api/subscriptions/:subscriptionId
 */
subscriptionRoutes.get("/:subscriptionId",
  billingPermissions.readSubscription,
  async (ctx) => {
    try {
      const subscriptionId = getRequiredParam(ctx, "subscriptionId");

      const subscriptionService = getSubscriptionService();
      const subscription = await subscriptionService.getSubscription(
        subscriptionId,
        ctx.state.tenantId
      );

      ctx.response.body = ApiResponse.success({ subscription });
    } catch (error) {
      logger.error("Failed to get subscription via API", {
        error: (error as Error).message,
        subscriptionId: getRequiredParam(ctx, "subscriptionId"),
        tenantId: ctx.state.tenantId,
      });

      throw error;
    }
  }
);

/**
 * List subscriptions
 * GET /api/subscriptions
 */
subscriptionRoutes.get("/",
  billingPermissions.readSubscription,
  async (ctx) => {
    try {
      const queryParams = parseQueryParams(ctx.request.url);
      
      const filters = ListSubscriptionsSchema.parse({
        tenantId: queryParams.getString("tenantId") || ctx.state.tenantId,
        status: queryParams.getString("status"),
        planId: queryParams.getString("planId"),
        limit: queryParams.getNumber("limit", 50),
        offset: queryParams.getNumber("offset", 0),
      });

      const subscriptionService = getSubscriptionService();
      const result = await subscriptionService.listSubscriptions(filters);

      ctx.response.body = ApiResponse.paginated(
        result.subscriptions,
        result.total,
        result.limit,
        result.offset
      );
    } catch (error) {
      if (error instanceof z.ZodError) {
        ctx.response.status = 400;
        ctx.response.body = ApiResponse.error(
          "Invalid query parameters",
          "VALIDATION_ERROR",
          400,
          error.errors
        );
        return;
      }

      logger.error("Failed to list subscriptions via API", {
        error: (error as Error).message,
        tenantId: ctx.state.tenantId,
      });

      throw error;
    }
  }
);

/**
 * Update subscription
 * PUT /api/subscriptions/:subscriptionId
 */
subscriptionRoutes.put("/:subscriptionId",
  billingPermissions.updateSubscription,
  async (ctx) => {
    try {
      const subscriptionId = getRequiredParam(ctx, "subscriptionId");

      const body = await parseRequestBody<UpdateSubscriptionRequest>(ctx);
      const validatedData = UpdateSubscriptionSchema.parse(body);

      const subscriptionService = getSubscriptionService();
      const subscription = await subscriptionService.updateSubscription(
        subscriptionId,
        validatedData,
        ctx.state.tenantId
      );

      logger.info("Subscription updated via API", {
        subscriptionId,
        changes: Object.keys(validatedData),
        tenantId: ctx.state.tenantId,
        userId: ctx.state.user?.id,
      });

      ctx.response.body = ApiResponse.success({ subscription });
    } catch (error) {
      if (error instanceof z.ZodError) {
        ctx.response.status = 400;
        ctx.response.body = ApiResponse.error(
          "Validation failed",
          "VALIDATION_ERROR",
          400,
          error.errors
        );
        return;
      }

      logger.error("Failed to update subscription via API", {
        error: (error as Error).message,
        subscriptionId: getRequiredParam(ctx, "subscriptionId"),
        tenantId: ctx.state.tenantId,
      });

      throw error;
    }
  }
);

/**
 * Cancel subscription
 * POST /api/subscriptions/:subscriptionId/cancel
 */
subscriptionRoutes.post("/:subscriptionId/cancel",
  billingPermissions.cancelSubscription,
  async (ctx) => {
    try {
      const subscriptionId = getRequiredParam(ctx, "subscriptionId");

      const body = await parseRequestBody(ctx);
      const validatedData = CancelSubscriptionSchema.parse(body);

      const subscriptionService = getSubscriptionService();
      const subscription = await subscriptionService.cancelSubscription(
        subscriptionId,
        ctx.state.tenantId,
        validatedData
      );

      logger.info("Subscription cancelled via API", {
        subscriptionId,
        immediate: validatedData.immediate,
        reason: validatedData.reason,
        tenantId: ctx.state.tenantId,
        userId: ctx.state.user?.id,
      });

      ctx.response.body = ApiResponse.success({ subscription });
    } catch (error) {
      if (error instanceof z.ZodError) {
        ctx.response.status = 400;
        ctx.response.body = ApiResponse.error(
          "Validation failed",
          "VALIDATION_ERROR",
          400,
          error.errors
        );
        return;
      }

      logger.error("Failed to cancel subscription via API", {
        error: (error as Error).message,
        subscriptionId: getRequiredParam(ctx, "subscriptionId"),
        tenantId: ctx.state.tenantId,
      });

      throw error;
    }
  }
);

/**
 * Get subscription metrics
 * GET /api/subscriptions/metrics
 */
subscriptionRoutes.get("/metrics",
  billingPermissions.readSubscription,
  async (ctx) => {
    try {
      const subscriptionService = getSubscriptionService();
      const metrics = await subscriptionService.getSubscriptionMetrics(ctx.state.tenantId);

      ctx.response.body = ApiResponse.success({ metrics });
    } catch (error) {
      logger.error("Failed to get subscription metrics via API", {
        error: (error as Error).message,
        tenantId: ctx.state.tenantId,
      });

      throw error;
    }
  }
);
