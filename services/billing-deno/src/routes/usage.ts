import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { getUsageTrackingService } from "../services/index.ts";
import { errors } from "../middleware/errorHandler.ts";
import { billingPermissions } from "../middleware/auth.ts";
import { ApiResponse, parseRequestBody, parseQueryParams } from "./index.ts";

export const usageRoutes = new Router();

// Zod schemas
const ReportUsageSchema = z.object({
  subscriptionId: z.string().min(1, "Subscription ID is required"),
  metricName: z.string().min(1, "Metric name is required"),
  quantity: z.number().int().min(0, "Quantity must be non-negative"),
  timestamp: z.string().datetime().optional(),
  action: z.enum(["increment", "set"]).default("increment"),
  metadata: z.record(z.string()).optional(),
});

/**
 * Report usage
 * POST /api/usage/report
 */
usageRoutes.post("/report",
  billingPermissions.reportUsage,
  async (ctx) => {
    try {
      const body = await parseRequestBody(ctx);
      const validatedData = ReportUsageSchema.parse(body);

      const usageService = getUsageTrackingService();
      await usageService.reportUsage({
        ...validatedData,
        tenantId: ctx.state.tenantId,
        timestamp: validatedData.timestamp ? new Date(validatedData.timestamp) : undefined,
      });

      ctx.response.status = 201;
      ctx.response.body = ApiResponse.success({
        message: "Usage reported successfully",
        metricName: validatedData.metricName,
        quantity: validatedData.quantity,
      }, 201);
    } catch (error) {
      if (error instanceof z.ZodError) {
        ctx.response.status = 400;
        ctx.response.body = ApiResponse.error(
          "Validation failed",
          "VALIDATION_ERROR",
          400,
          error.errors
        );
        return;
      }
      throw error;
    }
  }
);

/**
 * Get usage records
 * GET /api/usage/records
 */
usageRoutes.get("/records",
  billingPermissions.readUsage,
  async (ctx) => {
    try {
      const queryParams = parseQueryParams(ctx.request.url);
      
      const filters = {
        tenantId: ctx.state.tenantId,
        subscriptionId: queryParams.getString("subscriptionId"),
        metricName: queryParams.getString("metricName"),
        startDate: queryParams.getString("startDate") ? new Date(queryParams.getString("startDate")!) : undefined,
        endDate: queryParams.getString("endDate") ? new Date(queryParams.getString("endDate")!) : undefined,
        limit: queryParams.getNumber("limit", 100),
        offset: queryParams.getNumber("offset", 0),
      };

      const usageService = getUsageTrackingService();
      const result = await usageService.getUsageRecords(filters);

      ctx.response.body = ApiResponse.paginated(
        result.records,
        result.total,
        result.limit,
        result.offset
      );
    } catch (error) {
      throw error;
    }
  }
);

/**
 * Get usage metrics
 * GET /api/usage/metrics
 */
usageRoutes.get("/metrics",
  billingPermissions.readUsage,
  async (ctx) => {
    try {
      const queryParams = parseQueryParams(ctx.request.url);
      const metricName = queryParams.getString("metricName");
      
      let period;
      const startDate = queryParams.getString("startDate");
      const endDate = queryParams.getString("endDate");
      
      if (startDate && endDate) {
        period = {
          start: new Date(startDate),
          end: new Date(endDate),
        };
      }

      const usageService = getUsageTrackingService();
      const metrics = await usageService.getUsageMetrics(
        ctx.state.tenantId,
        metricName,
        period
      );

      ctx.response.body = ApiResponse.success({ metrics });
    } catch (error) {
      throw error;
    }
  }
);
