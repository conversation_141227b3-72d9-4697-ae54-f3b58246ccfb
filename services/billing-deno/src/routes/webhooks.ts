import { Router } from "@oak/oak";
import { logger, logStripeEvent } from "../utils/logger.ts";
import { getStripeService, getSubscriptionService, getInvoiceService, getPaymentService } from "../services/index.ts";
import { errors } from "../middleware/errorHandler.ts";
import { ApiResponse } from "./index.ts";
import type Stripe from "stripe";

export const webhookRoutes = new Router();

/**
 * Stripe webhook endpoint
 * POST /webhooks/stripe
 */
webhookRoutes.post("/stripe", async (ctx) => {
  try {
    const signature = ctx.state.stripeSignature;
    const rawBody = ctx.state.rawBody;

    if (!signature || !rawBody) {
      throw errors.badRequest("Missing webhook signature or body");
    }

    // Verify webhook signature and construct event
    const stripeService = getStripeService();
    const event = stripeService.constructWebhookEvent(rawBody, signature);

    logStripeEvent("webhook_received", event.id, {
      type: event.type,
      livemode: event.livemode,
    });

    // Process the event based on type
    await processStripeEvent(event);

    logger.info("Stripe webhook processed successfully", {
      eventId: event.id,
      eventType: event.type,
    });

    ctx.response.status = 200;
    ctx.response.body = ApiResponse.success({
      received: true,
      eventId: event.id,
      eventType: event.type,
    });
  } catch (error) {
    logger.error("Failed to process Stripe webhook", {
      error: (error as Error).message,
      signature: ctx.state.stripeSignature,
    });

    // Return 400 for signature verification failures
    if ((error as Error).message.includes("signature")) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Webhook signature verification failed",
        "WEBHOOK_SIGNATURE_INVALID",
        400
      );
      return;
    }

    throw error;
  }
});

/**
 * Webhook health check
 * GET /webhooks/health
 */
webhookRoutes.get("/health", (ctx) => {
  ctx.response.body = ApiResponse.success({
    service: "webhook-handler",
    status: "healthy",
    timestamp: new Date().toISOString(),
  });
});

/**
 * Process Stripe webhook events
 */
async function processStripeEvent(event: Stripe.Event): Promise<void> {
  try {
    switch (event.type) {
      // Subscription events
      case "customer.subscription.created":
      case "customer.subscription.updated":
      case "customer.subscription.deleted":
      case "customer.subscription.trial_will_end":
        const subscriptionService = getSubscriptionService();
        await subscriptionService.handleWebhookEvent(event);
        break;

      // Invoice events
      case "invoice.created":
      case "invoice.finalized":
      case "invoice.paid":
      case "invoice.payment_failed":
        const invoiceService = getInvoiceService();
        await invoiceService.handleWebhookEvent(event);
        break;

      // Payment events
      case "payment_intent.succeeded":
      case "payment_intent.payment_failed":
        const paymentService = getPaymentService();
        await paymentService.handleWebhookEvent(event);
        break;

      // Customer events
      case "customer.created":
      case "customer.updated":
      case "customer.deleted":
        await handleCustomerEvent(event);
        break;

      // Payment method events
      case "payment_method.attached":
      case "payment_method.detached":
        await handlePaymentMethodEvent(event);
        break;

      // Checkout events
      case "checkout.session.completed":
      case "checkout.session.expired":
        await handleCheckoutEvent(event);
        break;

      default:
        logger.info("Unhandled Stripe webhook event type", {
          type: event.type,
          eventId: event.id,
        });
    }
  } catch (error) {
    logger.error("Failed to process Stripe webhook event", {
      error: (error as Error).message,
      eventType: event.type,
      eventId: event.id,
    });
    throw error;
  }
}

/**
 * Handle customer events
 */
async function handleCustomerEvent(event: Stripe.Event): Promise<void> {
  const customer = event.data.object as Stripe.Customer;
  
  logger.info("Processing customer event", {
    eventType: event.type,
    customerId: customer.id,
    email: customer.email,
  });

  // In a real implementation, you would:
  // 1. Update customer information in database
  // 2. Sync with tenant records
  // 3. Send notifications if needed
}

/**
 * Handle payment method events
 */
async function handlePaymentMethodEvent(event: Stripe.Event): Promise<void> {
  const paymentMethod = event.data.object as Stripe.PaymentMethod;
  
  logger.info("Processing payment method event", {
    eventType: event.type,
    paymentMethodId: paymentMethod.id,
    customerId: paymentMethod.customer,
    type: paymentMethod.type,
  });

  // In a real implementation, you would:
  // 1. Update payment method records
  // 2. Notify customer of changes
  // 3. Update default payment methods if needed
}

/**
 * Handle checkout session events
 */
async function handleCheckoutEvent(event: Stripe.Event): Promise<void> {
  const session = event.data.object as Stripe.Checkout.Session;
  
  logger.info("Processing checkout event", {
    eventType: event.type,
    sessionId: session.id,
    customerId: session.customer,
    mode: session.mode,
    status: session.status,
  });

  // In a real implementation, you would:
  // 1. Update subscription or payment records
  // 2. Send confirmation emails
  // 3. Provision access to services
  // 4. Handle failed checkouts
}
