import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { StripeService } from "./stripeService.ts";
import { SubscriptionService } from "./subscriptionService.ts";
import { InvoiceService } from "./invoiceService.ts";
import { UsageTrackingService } from "./usageTrackingService.ts";
import { NotificationService } from "./notificationService.ts";
import { PaymentService } from "./paymentService.ts";

// Service instances
let stripeService: StripeService | null = null;
let subscriptionService: SubscriptionService | null = null;
let invoiceService: InvoiceService | null = null;
let usageTrackingService: UsageTrackingService | null = null;
let notificationService: NotificationService | null = null;
let paymentService: PaymentService | null = null;

/**
 * Initialize all billing services
 */
export async function initializeServices(): Promise<void> {
  try {
    logger.info("Initializing billing services...");

    // Initialize Stripe service first
    stripeService = new StripeService(config.stripe.secretKey);
    logger.info("Stripe service initialized");

    // Initialize other services that depend on Stripe
    subscriptionService = new SubscriptionService(stripeService);
    logger.info("Subscription service initialized");

    invoiceService = new InvoiceService(stripeService);
    logger.info("Invoice service initialized");

    paymentService = new PaymentService(stripeService);
    logger.info("Payment service initialized");

    usageTrackingService = new UsageTrackingService();
    logger.info("Usage tracking service initialized");

    notificationService = new NotificationService();
    logger.info("Notification service initialized");

    logger.info("All billing services initialized successfully");
  } catch (error) {
    logger.error("Failed to initialize billing services", error as Error);
    throw error;
  }
}

/**
 * Close all services and cleanup resources
 */
export async function closeServices(): Promise<void> {
  try {
    logger.info("Closing billing services...");

    // Close services in reverse order
    if (notificationService) {
      await notificationService.close();
      notificationService = null;
      logger.info("Notification service closed");
    }

    if (usageTrackingService) {
      await usageTrackingService.close();
      usageTrackingService = null;
      logger.info("Usage tracking service closed");
    }

    if (paymentService) {
      await paymentService.close();
      paymentService = null;
      logger.info("Payment service closed");
    }

    if (invoiceService) {
      await invoiceService.close();
      invoiceService = null;
      logger.info("Invoice service closed");
    }

    if (subscriptionService) {
      await subscriptionService.close();
      subscriptionService = null;
      logger.info("Subscription service closed");
    }

    if (stripeService) {
      // Stripe service doesn't need explicit cleanup
      stripeService = null;
      logger.info("Stripe service closed");
    }

    logger.info("All billing services closed successfully");
  } catch (error) {
    logger.error("Error closing billing services", error as Error);
    throw error;
  }
}

/**
 * Get service instances (with null checks)
 */
export function getStripeService(): StripeService {
  if (!stripeService) {
    throw new Error("Stripe service not initialized. Call initializeServices() first.");
  }
  return stripeService;
}

export function getSubscriptionService(): SubscriptionService {
  if (!subscriptionService) {
    throw new Error("Subscription service not initialized. Call initializeServices() first.");
  }
  return subscriptionService;
}

export function getInvoiceService(): InvoiceService {
  if (!invoiceService) {
    throw new Error("Invoice service not initialized. Call initializeServices() first.");
  }
  return invoiceService;
}

export function getPaymentService(): PaymentService {
  if (!paymentService) {
    throw new Error("Payment service not initialized. Call initializeServices() first.");
  }
  return paymentService;
}

export function getUsageTrackingService(): UsageTrackingService {
  if (!usageTrackingService) {
    throw new Error("Usage tracking service not initialized. Call initializeServices() first.");
  }
  return usageTrackingService;
}

export function getNotificationService(): NotificationService {
  if (!notificationService) {
    throw new Error("Notification service not initialized. Call initializeServices() first.");
  }
  return notificationService;
}

/**
 * Health check for all services
 */
export async function servicesHealthCheck(): Promise<{
  stripe: boolean;
  subscription: boolean;
  invoice: boolean;
  payment: boolean;
  usageTracking: boolean;
  notification: boolean;
}> {
  const health = {
    stripe: false,
    subscription: false,
    invoice: false,
    payment: false,
    usageTracking: false,
    notification: false,
  };

  try {
    // Check if services are initialized
    health.stripe = stripeService !== null;
    health.subscription = subscriptionService !== null;
    health.invoice = invoiceService !== null;
    health.payment = paymentService !== null;
    health.usageTracking = usageTrackingService !== null;
    health.notification = notificationService !== null;

    // Additional health checks could be added here
    // For example, testing Stripe API connectivity
    if (stripeService) {
      try {
        // Test Stripe connectivity by retrieving account info
        // This is a lightweight operation that verifies API key validity
        await stripeService.getPrice("price_test"); // This will fail but confirms API connectivity
      } catch (error) {
        // Expected to fail for test price, but if it's an auth error, mark as unhealthy
        const errorMessage = (error as Error).message;
        if (errorMessage.includes("Invalid API key") || errorMessage.includes("authentication")) {
          health.stripe = false;
        }
      }
    }

    logger.debug("Services health check completed", health);
    return health;
  } catch (error) {
    logger.error("Services health check failed", error as Error);
    return health;
  }
}

/**
 * Get service statistics
 */
export function getServiceStats(): {
  initialized: boolean;
  services: {
    stripe: boolean;
    subscription: boolean;
    invoice: boolean;
    payment: boolean;
    usageTracking: boolean;
    notification: boolean;
  };
  initializationTime?: Date;
} {
  return {
    initialized: stripeService !== null,
    services: {
      stripe: stripeService !== null,
      subscription: subscriptionService !== null,
      invoice: invoiceService !== null,
      payment: paymentService !== null,
      usageTracking: usageTrackingService !== null,
      notification: notificationService !== null,
    },
  };
}

// Export service classes for direct instantiation if needed
export {
  StripeService,
  SubscriptionService,
  InvoiceService,
  PaymentService,
  UsageTrackingService,
  NotificationService,
};
