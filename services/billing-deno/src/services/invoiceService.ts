import { config } from "../config/config.ts";
import { logger, logInvoiceEvent } from "../utils/logger.ts";
import { billingTransaction, billingQuery, billingQueryOne } from "../utils/database.ts";
import { StripeService } from "./stripeService.ts";
import { NotificationService } from "./notificationService.ts";
import { errors } from "../middleware/errorHandler.ts";
import type { Invoice, InvoiceStatus, CreateInvoiceRequest } from "../types/billing.ts";
import type Stripe from "stripe";

export interface InvoiceFilters {
  tenantId?: string;
  subscriptionId?: string;
  status?: InvoiceStatus;
  limit?: number;
  offset?: number;
}

export class InvoiceService {
  private stripe: StripeService;
  private notifications: NotificationService;

  constructor(stripeService: StripeService) {
    this.stripe = stripeService;
    this.notifications = new NotificationService();
  }

  /**
   * Create a new invoice
   */
  async createInvoice(invoiceData: CreateInvoiceRequest): Promise<Invoice> {
    return await billingTransaction(async (client) => {
      // Create invoice in Stripe
      const stripeInvoice = await this.stripe.createInvoice({
        customerId: invoiceData.customerId,
        subscriptionId: invoiceData.subscriptionId,
        metadata: {
          tenant_id: invoiceData.tenantId,
          ...invoiceData.metadata,
        },
        collectionMethod: invoiceData.collectionMethod,
        daysUntilDue: invoiceData.daysUntilDue,
        autoAdvance: invoiceData.autoAdvance,
      });

      // Create invoice record in database
      const invoiceResult = await client.queryObject(`
        INSERT INTO invoices (
          id, tenant_id, subscription_id, stripe_invoice_id,
          number, status, amount_due, amount_paid, currency,
          due_date, hosted_invoice_url, invoice_pdf, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())
        RETURNING *
      `, [
        stripeInvoice.id,
        invoiceData.tenantId,
        invoiceData.subscriptionId,
        stripeInvoice.id,
        stripeInvoice.number,
        stripeInvoice.status,
        stripeInvoice.amount_due,
        stripeInvoice.amount_paid,
        stripeInvoice.currency,
        stripeInvoice.due_date ? new Date(stripeInvoice.due_date * 1000) : null,
        stripeInvoice.hosted_invoice_url,
        stripeInvoice.invoice_pdf,
      ]);

      const invoice = invoiceResult.rows[0] as Invoice;

      logInvoiceEvent(
        "invoice_created",
        invoice.id,
        invoiceData.tenantId,
        {
          subscriptionId: invoiceData.subscriptionId,
          amountDue: invoice.amountDue,
          status: invoice.status,
        }
      );

      // Send notification
      await this.notifications.sendInvoiceCreated(invoice);

      return invoice;
    }, invoiceData.tenantId);
  }

  /**
   * Get invoice details
   */
  async getInvoice(invoiceId: string, tenantId: string): Promise<Invoice> {
    const result = await billingQueryOne(
      "SELECT * FROM invoices WHERE id = $1 AND tenant_id = $2",
      [invoiceId, tenantId],
      tenantId
    );

    if (!result) {
      throw errors.notFound("Invoice not found");
    }

    return result as Invoice;
  }

  /**
   * List invoices with filters
   */
  async listInvoices(filters: InvoiceFilters = {}): Promise<{
    invoices: Invoice[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const queryParams: unknown[] = [];
    const whereConditions: string[] = [];
    let paramCount = 0;

    // Build WHERE clause
    if (filters.tenantId) {
      paramCount++;
      whereConditions.push(`tenant_id = $${paramCount}`);
      queryParams.push(filters.tenantId);
    }

    if (filters.subscriptionId) {
      paramCount++;
      whereConditions.push(`subscription_id = $${paramCount}`);
      queryParams.push(filters.subscriptionId);
    }

    if (filters.status) {
      paramCount++;
      whereConditions.push(`status = $${paramCount}`);
      queryParams.push(filters.status);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";

    const limit = filters.limit || 50;
    const offset = filters.offset || 0;

    const result = await billingQuery(`
      SELECT * FROM invoices
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `, [...queryParams, limit, offset], filters.tenantId || "");

    // Get total count
    const countResult = await billingQueryOne(`
      SELECT COUNT(*) as total FROM invoices
      ${whereClause}
    `, queryParams, filters.tenantId || "");

    return {
      invoices: result as Invoice[],
      total: parseInt((countResult as Record<string, unknown>).total as string),
      limit,
      offset,
    };
  }

  /**
   * Finalize an invoice
   */
  async finalizeInvoice(invoiceId: string, tenantId: string): Promise<Invoice> {
    return await billingTransaction(async (client) => {
      // Finalize invoice in Stripe
      const stripeInvoice = await this.stripe.finalizeInvoice(invoiceId);

      // Update invoice in database
      await client.queryObject(`
        UPDATE invoices SET
          status = $1,
          amount_due = $2,
          hosted_invoice_url = $3,
          invoice_pdf = $4,
          updated_at = NOW()
        WHERE id = $5 AND tenant_id = $6
      `, [
        stripeInvoice.status,
        stripeInvoice.amount_due,
        stripeInvoice.hosted_invoice_url,
        stripeInvoice.invoice_pdf,
        invoiceId,
        tenantId,
      ]);

      const updatedResult = await client.queryObject(
        "SELECT * FROM invoices WHERE id = $1 AND tenant_id = $2",
        [invoiceId, tenantId]
      );

      const invoice = updatedResult.rows[0] as Invoice;

      logInvoiceEvent(
        "invoice_finalized",
        invoiceId,
        tenantId,
        {
          amountDue: invoice.amountDue,
          status: invoice.status,
        }
      );

      return invoice;
    }, tenantId);
  }

  /**
   * Pay an invoice
   */
  async payInvoice(invoiceId: string, tenantId: string, paymentMethod?: string): Promise<Invoice> {
    return await billingTransaction(async (client) => {
      // Pay invoice in Stripe
      const stripeInvoice = await this.stripe.payInvoice(invoiceId, paymentMethod);

      // Update invoice in database
      await client.queryObject(`
        UPDATE invoices SET
          status = $1,
          amount_paid = $2,
          paid_at = $3,
          updated_at = NOW()
        WHERE id = $4 AND tenant_id = $5
      `, [
        stripeInvoice.status,
        stripeInvoice.amount_paid,
        stripeInvoice.status_transitions?.paid_at ? 
          new Date(stripeInvoice.status_transitions.paid_at * 1000) : null,
        invoiceId,
        tenantId,
      ]);

      const updatedResult = await client.queryObject(
        "SELECT * FROM invoices WHERE id = $1 AND tenant_id = $2",
        [invoiceId, tenantId]
      );

      const invoice = updatedResult.rows[0] as Invoice;

      logInvoiceEvent(
        "invoice_paid",
        invoiceId,
        tenantId,
        {
          amountPaid: invoice.amountPaid,
          status: invoice.status,
        }
      );

      return invoice;
    }, tenantId);
  }

  /**
   * Handle Stripe webhook events
   */
  async handleWebhookEvent(event: Stripe.Event): Promise<void> {
    try {
      switch (event.type) {
        case "invoice.created":
          await this.handleInvoiceCreated(event.data.object as Stripe.Invoice);
          break;
        case "invoice.finalized":
          await this.handleInvoiceFinalized(event.data.object as Stripe.Invoice);
          break;
        case "invoice.paid":
          await this.handleInvoicePaid(event.data.object as Stripe.Invoice);
          break;
        case "invoice.payment_failed":
          await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
          break;
        default:
          logger.info("Unhandled invoice webhook event type", { type: event.type });
      }
    } catch (error) {
      logger.error("Failed to handle invoice webhook event", {
        error: (error as Error).message,
        eventType: event.type,
        eventId: event.id,
      });
      throw error;
    }
  }

  private async handleInvoiceCreated(stripeInvoice: Stripe.Invoice): Promise<void> {
    // Check if invoice already exists
    const existingResult = await billingQueryOne(
      "SELECT id FROM invoices WHERE id = $1",
      [stripeInvoice.id],
      ""
    );

    if (existingResult) {
      logger.info("Invoice already exists, skipping creation", {
        invoiceId: stripeInvoice.id,
      });
      return;
    }

    logger.info("Handling invoice created webhook", {
      invoiceId: stripeInvoice.id,
    });
  }

  private async handleInvoiceFinalized(stripeInvoice: Stripe.Invoice): Promise<void> {
    await billingTransaction(async (client) => {
      await client.queryObject(`
        UPDATE invoices SET
          status = $1,
          amount_due = $2,
          hosted_invoice_url = $3,
          invoice_pdf = $4,
          updated_at = NOW()
        WHERE id = $5
      `, [
        stripeInvoice.status,
        stripeInvoice.amount_due,
        stripeInvoice.hosted_invoice_url,
        stripeInvoice.invoice_pdf,
        stripeInvoice.id,
      ]);

      logger.info("Invoice finalized via webhook", {
        invoiceId: stripeInvoice.id,
        amountDue: stripeInvoice.amount_due,
      });
    }, "");
  }

  private async handleInvoicePaid(stripeInvoice: Stripe.Invoice): Promise<void> {
    await billingTransaction(async (client) => {
      await client.queryObject(`
        UPDATE invoices SET
          status = $1,
          amount_paid = $2,
          paid_at = $3,
          updated_at = NOW()
        WHERE id = $4
      `, [
        stripeInvoice.status,
        stripeInvoice.amount_paid,
        stripeInvoice.status_transitions?.paid_at ? 
          new Date(stripeInvoice.status_transitions.paid_at * 1000) : null,
        stripeInvoice.id,
      ]);

      logger.info("Invoice paid via webhook", {
        invoiceId: stripeInvoice.id,
        amountPaid: stripeInvoice.amount_paid,
      });
    }, "");
  }

  private async handleInvoicePaymentFailed(stripeInvoice: Stripe.Invoice): Promise<void> {
    await billingTransaction(async (client) => {
      await client.queryObject(`
        UPDATE invoices SET
          status = $1,
          updated_at = NOW()
        WHERE id = $2
      `, [
        stripeInvoice.status,
        stripeInvoice.id,
      ]);

      logger.warn("Invoice payment failed via webhook", {
        invoiceId: stripeInvoice.id,
        amountDue: stripeInvoice.amount_due,
      });
    }, "");
  }

  /**
   * Close service and cleanup resources
   */
  async close(): Promise<void> {
    await this.notifications.close();
    logger.info("Invoice service closed");
  }
}
