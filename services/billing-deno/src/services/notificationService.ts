import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { notificationClient } from "../utils/httpClient.ts";
import type { 
  Subscription, 
  Invoice, 
  Payment,
  NotificationData,
  EmailNotification,
  SlackNotification,
  WebhookNotification 
} from "../types/billing.ts";

export class NotificationService {
  constructor() {
    logger.info("Notification service initialized");
  }

  /**
   * Send subscription created notification
   */
  async sendSubscriptionCreated(subscription: Subscription): Promise<void> {
    try {
      const notificationData: NotificationData = {
        type: "subscription_created",
        tenantId: subscription.tenantId,
        data: {
          subscriptionId: subscription.id,
          planId: subscription.planId,
          status: subscription.status,
          currentPeriodStart: subscription.currentPeriodStart,
          currentPeriodEnd: subscription.currentPeriodEnd,
        },
        channels: ["email", "webhook"],
      };

      await this.sendNotification(notificationData);

      logger.info("Subscription created notification sent", {
        subscriptionId: subscription.id,
        tenantId: subscription.tenantId,
      });
    } catch (error) {
      logger.error("Failed to send subscription created notification", {
        error: (error as Error).message,
        subscriptionId: subscription.id,
      });
    }
  }

  /**
   * Send subscription updated notification
   */
  async sendSubscriptionUpdated(
    subscription: Subscription,
    oldPlanId: string,
    newPlanId: string
  ): Promise<void> {
    try {
      const notificationData: NotificationData = {
        type: "subscription_updated",
        tenantId: subscription.tenantId,
        data: {
          subscriptionId: subscription.id,
          oldPlanId,
          newPlanId,
          status: subscription.status,
        },
        channels: ["email", "webhook"],
      };

      await this.sendNotification(notificationData);

      logger.info("Subscription updated notification sent", {
        subscriptionId: subscription.id,
        tenantId: subscription.tenantId,
        oldPlanId,
        newPlanId,
      });
    } catch (error) {
      logger.error("Failed to send subscription updated notification", {
        error: (error as Error).message,
        subscriptionId: subscription.id,
      });
    }
  }

  /**
   * Send subscription cancelled notification
   */
  async sendSubscriptionCancelled(
    subscription: Subscription,
    reason?: string
  ): Promise<void> {
    try {
      const notificationData: NotificationData = {
        type: "subscription_cancelled",
        tenantId: subscription.tenantId,
        data: {
          subscriptionId: subscription.id,
          planId: subscription.planId,
          canceledAt: subscription.canceledAt,
          reason,
        },
        channels: ["email", "webhook", "slack"],
      };

      await this.sendNotification(notificationData);

      logger.info("Subscription cancelled notification sent", {
        subscriptionId: subscription.id,
        tenantId: subscription.tenantId,
        reason,
      });
    } catch (error) {
      logger.error("Failed to send subscription cancelled notification", {
        error: (error as Error).message,
        subscriptionId: subscription.id,
      });
    }
  }

  /**
   * Send trial expiring notification
   */
  async sendTrialExpiringNotification(subscription: Subscription): Promise<void> {
    try {
      const notificationData: NotificationData = {
        type: "trial_expiring",
        tenantId: subscription.tenantId,
        data: {
          subscriptionId: subscription.id,
          planId: subscription.planId,
          trialEnd: subscription.trialEnd,
          daysRemaining: subscription.trialEnd ? 
            Math.ceil((subscription.trialEnd.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0,
        },
        channels: ["email"],
      };

      await this.sendNotification(notificationData);

      logger.info("Trial expiring notification sent", {
        subscriptionId: subscription.id,
        tenantId: subscription.tenantId,
        trialEnd: subscription.trialEnd,
      });
    } catch (error) {
      logger.error("Failed to send trial expiring notification", {
        error: (error as Error).message,
        subscriptionId: subscription.id,
      });
    }
  }

  /**
   * Send invoice created notification
   */
  async sendInvoiceCreated(invoice: Invoice): Promise<void> {
    try {
      const notificationData: NotificationData = {
        type: "invoice_created",
        tenantId: invoice.tenantId,
        data: {
          invoiceId: invoice.id,
          subscriptionId: invoice.subscriptionId,
          amountDue: invoice.amountDue,
          currency: invoice.currency,
          dueDate: invoice.dueDate,
          hostedInvoiceUrl: invoice.hostedInvoiceUrl,
        },
        channels: ["email", "webhook"],
      };

      await this.sendNotification(notificationData);

      logger.info("Invoice created notification sent", {
        invoiceId: invoice.id,
        tenantId: invoice.tenantId,
      });
    } catch (error) {
      logger.error("Failed to send invoice created notification", {
        error: (error as Error).message,
        invoiceId: invoice.id,
      });
    }
  }

  /**
   * Send payment failed notification
   */
  async sendPaymentFailed(payment: Payment): Promise<void> {
    try {
      const notificationData: NotificationData = {
        type: "payment_failed",
        tenantId: payment.tenantId,
        data: {
          paymentId: payment.id,
          subscriptionId: payment.subscriptionId,
          invoiceId: payment.invoiceId,
          amount: payment.amount,
          currency: payment.currency,
          failureCode: payment.failureCode,
          failureMessage: payment.failureMessage,
        },
        channels: ["email", "webhook", "slack"],
      };

      await this.sendNotification(notificationData);

      logger.info("Payment failed notification sent", {
        paymentId: payment.id,
        tenantId: payment.tenantId,
      });
    } catch (error) {
      logger.error("Failed to send payment failed notification", {
        error: (error as Error).message,
        paymentId: payment.id,
      });
    }
  }

  /**
   * Send generic notification
   */
  private async sendNotification(notificationData: NotificationData): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const channel of notificationData.channels) {
      switch (channel) {
        case "email":
          promises.push(this.sendEmailNotification(notificationData));
          break;
        case "slack":
          promises.push(this.sendSlackNotification(notificationData));
          break;
        case "webhook":
          promises.push(this.sendWebhookNotification(notificationData));
          break;
      }
    }

    await Promise.allSettled(promises);
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(notificationData: NotificationData): Promise<void> {
    if (!config.email.sendgrid.apiKey) {
      logger.warn("SendGrid API key not configured, skipping email notification");
      return;
    }

    try {
      // In a real implementation, you would:
      // 1. Get tenant email from database
      // 2. Select appropriate email template
      // 3. Send email via SendGrid API
      
      logger.info("Email notification would be sent", {
        type: notificationData.type,
        tenantId: notificationData.tenantId,
      });
    } catch (error) {
      logger.error("Failed to send email notification", {
        error: (error as Error).message,
        type: notificationData.type,
      });
    }
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(notificationData: NotificationData): Promise<void> {
    if (!config.notifications.slack.enabled || !config.notifications.slack.webhookUrl) {
      logger.debug("Slack notifications not configured, skipping");
      return;
    }

    try {
      const slackMessage = this.formatSlackMessage(notificationData);
      
      await notificationClient.post(config.notifications.slack.webhookUrl, slackMessage);

      logger.info("Slack notification sent", {
        type: notificationData.type,
        tenantId: notificationData.tenantId,
      });
    } catch (error) {
      logger.error("Failed to send Slack notification", {
        error: (error as Error).message,
        type: notificationData.type,
      });
    }
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(notificationData: NotificationData): Promise<void> {
    if (!config.notifications.webhook.enabled || !config.notifications.webhook.url) {
      logger.debug("Webhook notifications not configured, skipping");
      return;
    }

    try {
      const webhookPayload = {
        type: notificationData.type,
        tenantId: notificationData.tenantId,
        data: notificationData.data,
        timestamp: new Date().toISOString(),
      };

      const headers: Record<string, string> = {
        "Content-Type": "application/json",
      };

      if (config.notifications.webhook.secret) {
        // Add webhook signature for verification
        const signature = await this.generateWebhookSignature(
          JSON.stringify(webhookPayload),
          config.notifications.webhook.secret
        );
        headers["X-Webhook-Signature"] = signature;
      }

      await notificationClient.post(config.notifications.webhook.url, webhookPayload, {
        headers,
        timeout: config.notifications.webhook.timeout,
      });

      logger.info("Webhook notification sent", {
        type: notificationData.type,
        tenantId: notificationData.tenantId,
      });
    } catch (error) {
      logger.error("Failed to send webhook notification", {
        error: (error as Error).message,
        type: notificationData.type,
      });
    }
  }

  /**
   * Format Slack message
   */
  private formatSlackMessage(notificationData: NotificationData): SlackNotification {
    const { type, tenantId, data } = notificationData;

    let color = "good";
    let title = "";
    let text = "";

    switch (type) {
      case "subscription_created":
        color = "good";
        title = "New Subscription Created";
        text = `Tenant ${tenantId} created a new ${data.planId} subscription`;
        break;
      case "subscription_cancelled":
        color = "warning";
        title = "Subscription Cancelled";
        text = `Tenant ${tenantId} cancelled their ${data.planId} subscription`;
        break;
      case "payment_failed":
        color = "danger";
        title = "Payment Failed";
        text = `Payment failed for tenant ${tenantId}: ${data.failureMessage}`;
        break;
      default:
        title = `Billing Event: ${type}`;
        text = `Event for tenant ${tenantId}`;
    }

    return {
      channel: config.notifications.slack.channel,
      text: title,
      attachments: [
        {
          color,
          title,
          text,
          fields: [
            {
              title: "Tenant ID",
              value: tenantId,
              short: true,
            },
            {
              title: "Event Type",
              value: type,
              short: true,
            },
          ],
        },
      ],
    };
  }

  /**
   * Generate webhook signature
   */
  private async generateWebhookSignature(payload: string, secret: string): Promise<string> {
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey(
      "raw",
      encoder.encode(secret),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );

    const signature = await crypto.subtle.sign("HMAC", key, encoder.encode(payload));
    const hashArray = Array.from(new Uint8Array(signature));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, "0")).join("");
    
    return `sha256=${hashHex}`;
  }

  /**
   * Close service and cleanup resources
   */
  async close(): Promise<void> {
    logger.info("Notification service closed");
  }
}
