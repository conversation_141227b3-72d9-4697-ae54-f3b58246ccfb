import { config } from "../config/config.ts";
import { logger, logPaymentEvent } from "../utils/logger.ts";
import { billingTransaction, billingQuery, billingQueryOne } from "../utils/database.ts";
import { StripeService } from "./stripeService.ts";
import { NotificationService } from "./notificationService.ts";
import { errors } from "../middleware/errorHandler.ts";
import type { Payment, PaymentStatus, CreatePaymentIntentRequest } from "../types/billing.ts";
import type Stripe from "stripe";

export interface PaymentFilters {
  tenantId?: string;
  subscriptionId?: string;
  invoiceId?: string;
  status?: PaymentStatus;
  limit?: number;
  offset?: number;
}

export class PaymentService {
  private stripe: StripeService;
  private notifications: NotificationService;

  constructor(stripeService: StripeService) {
    this.stripe = stripeService;
    this.notifications = new NotificationService();
  }

  /**
   * Create a payment intent
   */
  async createPaymentIntent(paymentData: CreatePaymentIntentRequest): Promise<{
    payment: Payment;
    clientSecret: string;
  }> {
    return await billingTransaction(async (client) => {
      // Create payment intent in Stripe
      const stripePaymentIntent = await this.stripe.createPaymentIntent({
        amount: paymentData.amount,
        currency: paymentData.currency,
        customer: paymentData.customerId,
        payment_method: paymentData.paymentMethodId,
        confirmation_method: paymentData.confirmationMethod || "automatic",
        confirm: paymentData.confirm || false,
        metadata: {
          tenant_id: paymentData.tenantId,
          ...paymentData.metadata,
        },
      });

      // Create payment record in database
      const paymentResult = await client.queryObject(`
        INSERT INTO payments (
          id, tenant_id, subscription_id, invoice_id,
          stripe_payment_intent_id, amount, currency, status,
          payment_method, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW())
        RETURNING *
      `, [
        stripePaymentIntent.id,
        paymentData.tenantId,
        paymentData.subscriptionId,
        paymentData.invoiceId,
        stripePaymentIntent.id,
        paymentData.amount,
        paymentData.currency,
        stripePaymentIntent.status,
        stripePaymentIntent.payment_method || "unknown",
      ]);

      const payment = paymentResult.rows[0] as Payment;

      logPaymentEvent(
        "payment_intent_created",
        payment.id,
        paymentData.tenantId,
        {
          amount: payment.amount,
          currency: payment.currency,
          status: payment.status,
        }
      );

      return {
        payment,
        clientSecret: stripePaymentIntent.client_secret!,
      };
    }, paymentData.tenantId);
  }

  /**
   * Get payment details
   */
  async getPayment(paymentId: string, tenantId: string): Promise<Payment> {
    const result = await billingQueryOne(
      "SELECT * FROM payments WHERE id = $1 AND tenant_id = $2",
      [paymentId, tenantId],
      tenantId
    );

    if (!result) {
      throw errors.notFound("Payment not found");
    }

    return result as Payment;
  }

  /**
   * List payments with filters
   */
  async listPayments(filters: PaymentFilters = {}): Promise<{
    payments: Payment[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const queryParams: unknown[] = [];
    const whereConditions: string[] = [];
    let paramCount = 0;

    // Build WHERE clause
    if (filters.tenantId) {
      paramCount++;
      whereConditions.push(`tenant_id = $${paramCount}`);
      queryParams.push(filters.tenantId);
    }

    if (filters.subscriptionId) {
      paramCount++;
      whereConditions.push(`subscription_id = $${paramCount}`);
      queryParams.push(filters.subscriptionId);
    }

    if (filters.invoiceId) {
      paramCount++;
      whereConditions.push(`invoice_id = $${paramCount}`);
      queryParams.push(filters.invoiceId);
    }

    if (filters.status) {
      paramCount++;
      whereConditions.push(`status = $${paramCount}`);
      queryParams.push(filters.status);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";

    const limit = filters.limit || 50;
    const offset = filters.offset || 0;

    const result = await billingQuery(`
      SELECT * FROM payments
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `, [...queryParams, limit, offset], filters.tenantId || "");

    // Get total count
    const countResult = await billingQueryOne(`
      SELECT COUNT(*) as total FROM payments
      ${whereClause}
    `, queryParams, filters.tenantId || "");

    return {
      payments: result as Payment[],
      total: parseInt((countResult as Record<string, unknown>).total as string),
      limit,
      offset,
    };
  }

  /**
   * Handle Stripe webhook events
   */
  async handleWebhookEvent(event: Stripe.Event): Promise<void> {
    try {
      switch (event.type) {
        case "payment_intent.succeeded":
          await this.handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;
        case "payment_intent.payment_failed":
          await this.handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
          break;
        default:
          logger.info("Unhandled payment webhook event type", { type: event.type });
      }
    } catch (error) {
      logger.error("Failed to handle payment webhook event", {
        error: (error as Error).message,
        eventType: event.type,
        eventId: event.id,
      });
      throw error;
    }
  }

  private async handlePaymentSucceeded(stripePaymentIntent: Stripe.PaymentIntent): Promise<void> {
    await billingTransaction(async (client) => {
      await client.queryObject(`
        UPDATE payments SET
          status = $1,
          updated_at = NOW()
        WHERE stripe_payment_intent_id = $2
      `, [
        stripePaymentIntent.status,
        stripePaymentIntent.id,
      ]);

      logger.info("Payment succeeded via webhook", {
        paymentIntentId: stripePaymentIntent.id,
        amount: stripePaymentIntent.amount,
      });
    }, "");
  }

  private async handlePaymentFailed(stripePaymentIntent: Stripe.PaymentIntent): Promise<void> {
    await billingTransaction(async (client) => {
      await client.queryObject(`
        UPDATE payments SET
          status = $1,
          failure_code = $2,
          failure_message = $3,
          updated_at = NOW()
        WHERE stripe_payment_intent_id = $4
      `, [
        stripePaymentIntent.status,
        stripePaymentIntent.last_payment_error?.code,
        stripePaymentIntent.last_payment_error?.message,
        stripePaymentIntent.id,
      ]);

      // Get payment for notification
      const paymentResult = await client.queryObject(
        "SELECT * FROM payments WHERE stripe_payment_intent_id = $1",
        [stripePaymentIntent.id]
      );

      if (paymentResult.rows.length > 0) {
        const payment = paymentResult.rows[0] as Payment;
        await this.notifications.sendPaymentFailed(payment);
      }

      logger.warn("Payment failed via webhook", {
        paymentIntentId: stripePaymentIntent.id,
        amount: stripePaymentIntent.amount,
        failureCode: stripePaymentIntent.last_payment_error?.code,
        failureMessage: stripePaymentIntent.last_payment_error?.message,
      });
    }, "");
  }

  /**
   * Close service and cleanup resources
   */
  async close(): Promise<void> {
    await this.notifications.close();
    logger.info("Payment service closed");
  }
}
