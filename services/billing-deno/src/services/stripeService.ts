import Stripe from "stripe";
import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { createStripeError } from "../middleware/errorHandler.ts";

// Type alias for Stripe errors
type StripeError = Stripe.StripeRawError;

export interface CustomerData {
  email: string;
  name?: string;
  phone?: string;
  tenantId: string;
  userId?: string;
  company?: string;
  taxExempt?: boolean;
  preferredLocales?: string[];
}

export interface SubscriptionData {
  customerId: string;
  items: Array<{
    price: string;
    quantity?: number;
  }>;
  tenantId: string;
  planId: string;
  trialDays?: number;
  couponId?: string;
}

export interface InvoiceData {
  customerId: string;
  subscriptionId?: string;
  metadata?: Record<string, string>;
  collectionMethod?: "charge_automatically" | "send_invoice";
  daysUntilDue?: number;
  autoAdvance?: boolean;
}

export interface CheckoutSessionData {
  mode?: "payment" | "setup" | "subscription";
  customerId?: string;
  lineItems: Array<{
    price: string;
    quantity: number;
  }>;
  successUrl: string;
  cancelUrl: string;
  metadata?: Record<string, string>;
  subscriptionData?: Record<string, unknown>;
  allowPromotionCodes?: boolean;
}

export class StripeService {
  private stripe: Stripe;

  constructor(secretKey: string) {
    this.stripe = new Stripe(secretKey, {
      apiVersion: config.stripe.apiVersion as Stripe.LatestApiVersion,
      maxNetworkRetries: config.stripe.maxNetworkRetries,
      timeout: config.stripe.timeout,
      typescript: true,
    });

    logger.info("Stripe service initialized", {
      apiVersion: config.stripe.apiVersion,
      timeout: config.stripe.timeout,
    });
  }

  // Customer Management
  async createCustomer(customerData: CustomerData): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.create({
        email: customerData.email,
        name: customerData.name,
        phone: customerData.phone,
        metadata: {
          tenant_id: customerData.tenantId,
          user_id: customerData.userId || "",
          company: customerData.company || "",
        },
        tax_exempt: customerData.taxExempt ? "exempt" : "none",
        preferred_locales: customerData.preferredLocales || ["en"],
      });

      logger.info("Stripe customer created", {
        customerId: customer.id,
        email: customerData.email,
        tenantId: customerData.tenantId,
      });

      return customer;
    } catch (error) {
      const stripeError = error as Stripe.StripeRawError;
      logger.error("Failed to create Stripe customer", {
        error: stripeError.message,
        code: stripeError.code,
        email: customerData.email,
      });
      throw createStripeError(
        `Failed to create customer: ${stripeError.message}`,
        402,
        "CUSTOMER_CREATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async updateCustomer(customerId: string, updateData: Partial<Stripe.CustomerUpdateParams>): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.update(customerId, updateData);

      logger.info("Stripe customer updated", {
        customerId,
        fields: Object.keys(updateData),
      });

      return customer;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to update Stripe customer", {
        error: stripeError.message,
        code: stripeError.code,
        customerId,
      });
      throw createStripeError(
        `Failed to update customer: ${stripeError.message}`,
        402,
        "CUSTOMER_UPDATE_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async getCustomer(customerId: string): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.retrieve(customerId);
      return customer as Stripe.Customer;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to retrieve Stripe customer", {
        error: stripeError.message,
        code: stripeError.code,
        customerId,
      });
      throw createStripeError(
        `Failed to retrieve customer: ${stripeError.message}`,
        404,
        "CUSTOMER_NOT_FOUND",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async deleteCustomer(customerId: string): Promise<Stripe.DeletedCustomer> {
    try {
      const deleted = await this.stripe.customers.del(customerId);

      logger.info("Stripe customer deleted", {
        customerId,
        deleted: deleted.deleted,
      });

      return deleted;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to delete Stripe customer", {
        error: stripeError.message,
        code: stripeError.code,
        customerId,
      });
      throw createStripeError(
        `Failed to delete customer: ${stripeError.message}`,
        402,
        "CUSTOMER_DELETION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Subscription Management
  async createSubscription(subscriptionData: SubscriptionData): Promise<Stripe.Subscription> {
    try {
      const subscriptionParams: Stripe.SubscriptionCreateParams = {
        customer: subscriptionData.customerId,
        items: subscriptionData.items,
        metadata: {
          tenant_id: subscriptionData.tenantId,
          plan_id: subscriptionData.planId,
        },
        trial_period_days: subscriptionData.trialDays || config.billing.trialDays,
        collection_method: "charge_automatically",
        payment_behavior: "default_incomplete",
        payment_settings: {
          save_default_payment_method: "on_subscription",
        },
        expand: ["latest_invoice.payment_intent"],
      };

      // Add coupon if provided
      if (subscriptionData.couponId) {
        subscriptionParams.coupon = subscriptionData.couponId;
      }

      const subscription = await this.stripe.subscriptions.create(subscriptionParams);

      logger.info("Stripe subscription created", {
        subscriptionId: subscription.id,
        customerId: subscriptionData.customerId,
        planId: subscriptionData.planId,
        status: subscription.status,
      });

      return subscription;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to create Stripe subscription", {
        error: stripeError.message,
        code: stripeError.code,
        customerId: subscriptionData.customerId,
        planId: subscriptionData.planId,
      });
      throw createStripeError(
        `Failed to create subscription: ${stripeError.message}`,
        402,
        "SUBSCRIPTION_CREATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async updateSubscription(subscriptionId: string, updateData: Partial<Stripe.SubscriptionUpdateParams>): Promise<Stripe.Subscription> {
    try {
      const subscription = await this.stripe.subscriptions.update(subscriptionId, updateData);

      logger.info("Stripe subscription updated", {
        subscriptionId,
        fields: Object.keys(updateData),
        status: subscription.status,
      });

      return subscription;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to update Stripe subscription", {
        error: stripeError.message,
        code: stripeError.code,
        subscriptionId,
      });
      throw createStripeError(
        `Failed to update subscription: ${stripeError.message}`,
        402,
        "SUBSCRIPTION_UPDATE_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.retrieve(subscriptionId);
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to retrieve Stripe subscription", {
        error: stripeError.message,
        code: stripeError.code,
        subscriptionId,
      });
      throw createStripeError(
        `Failed to retrieve subscription: ${stripeError.message}`,
        404,
        "SUBSCRIPTION_NOT_FOUND",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async cancelSubscription(subscriptionId: string, cancelOptions: { invoiceNow?: boolean; prorate?: boolean } = {}): Promise<Stripe.Subscription> {
    try {
      const subscription = await this.stripe.subscriptions.cancel(subscriptionId, {
        invoice_now: cancelOptions.invoiceNow || false,
        prorate: cancelOptions.prorate !== false,
      });

      logger.info("Stripe subscription cancelled", {
        subscriptionId,
        canceledAt: subscription.canceled_at,
      });

      return subscription;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to cancel Stripe subscription", {
        error: stripeError.message,
        code: stripeError.code,
        subscriptionId,
      });
      throw createStripeError(
        `Failed to cancel subscription: ${stripeError.message}`,
        402,
        "SUBSCRIPTION_CANCELLATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Payment Method Management
  async attachPaymentMethod(paymentMethodId: string, customerId: string): Promise<Stripe.PaymentMethod> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      logger.info("Payment method attached", {
        paymentMethodId,
        customerId,
        type: paymentMethod.type,
      });

      return paymentMethod;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to attach payment method", {
        error: stripeError.message,
        code: stripeError.code,
        paymentMethodId,
        customerId,
      });
      throw createStripeError(
        `Failed to attach payment method: ${stripeError.message}`,
        402,
        "PAYMENT_METHOD_ATTACH_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async detachPaymentMethod(paymentMethodId: string): Promise<Stripe.PaymentMethod> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.detach(paymentMethodId);

      logger.info("Payment method detached", {
        paymentMethodId,
        type: paymentMethod.type,
      });

      return paymentMethod;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to detach payment method", {
        error: stripeError.message,
        code: stripeError.code,
        paymentMethodId,
      });
      throw createStripeError(
        `Failed to detach payment method: ${stripeError.message}`,
        402,
        "PAYMENT_METHOD_DETACH_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async setDefaultPaymentMethod(customerId: string, paymentMethodId: string): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });

      logger.info("Default payment method set", {
        customerId,
        paymentMethodId,
      });

      return customer;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to set default payment method", {
        error: stripeError.message,
        code: stripeError.code,
        customerId,
        paymentMethodId,
      });
      throw createStripeError(
        `Failed to set default payment method: ${stripeError.message}`,
        402,
        "DEFAULT_PAYMENT_METHOD_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async getPaymentMethods(customerId: string, type: string = "card"): Promise<Stripe.PaymentMethod[]> {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: type as Stripe.PaymentMethodListParams.Type,
      });

      return paymentMethods.data;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to get payment methods", {
        error: stripeError.message,
        code: stripeError.code,
        customerId,
        type,
      });
      throw createStripeError(
        `Failed to get payment methods: ${stripeError.message}`,
        402,
        "PAYMENT_METHODS_RETRIEVAL_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Invoice Management
  async createInvoice(invoiceData: InvoiceData): Promise<Stripe.Invoice> {
    try {
      const invoice = await this.stripe.invoices.create({
        customer: invoiceData.customerId,
        subscription: invoiceData.subscriptionId,
        metadata: invoiceData.metadata || {},
        collection_method: invoiceData.collectionMethod || "charge_automatically",
        days_until_due: invoiceData.daysUntilDue,
        auto_advance: invoiceData.autoAdvance !== false,
      });

      logger.info("Stripe invoice created", {
        invoiceId: invoice.id,
        customerId: invoiceData.customerId,
        amount: invoice.amount_due,
      });

      return invoice;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to create Stripe invoice", {
        error: stripeError.message,
        code: stripeError.code,
        customerId: invoiceData.customerId,
      });
      throw createStripeError(
        `Failed to create invoice: ${stripeError.message}`,
        402,
        "INVOICE_CREATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async finalizeInvoice(invoiceId: string): Promise<Stripe.Invoice> {
    try {
      const invoice = await this.stripe.invoices.finalizeInvoice(invoiceId);

      logger.info("Stripe invoice finalized", {
        invoiceId,
        amount: invoice.amount_due,
        status: invoice.status,
      });

      return invoice;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to finalize Stripe invoice", {
        error: stripeError.message,
        code: stripeError.code,
        invoiceId,
      });
      throw createStripeError(
        `Failed to finalize invoice: ${stripeError.message}`,
        402,
        "INVOICE_FINALIZATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async payInvoice(invoiceId: string, paymentMethod?: string): Promise<Stripe.Invoice> {
    try {
      const invoice = await this.stripe.invoices.pay(invoiceId, {
        payment_method: paymentMethod,
      });

      logger.info("Stripe invoice paid", {
        invoiceId,
        amount: invoice.amount_paid,
        status: invoice.status,
      });

      return invoice;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to pay Stripe invoice", {
        error: stripeError.message,
        code: stripeError.code,
        invoiceId,
      });
      throw createStripeError(
        `Failed to pay invoice: ${stripeError.message}`,
        402,
        "INVOICE_PAYMENT_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async getInvoice(invoiceId: string): Promise<Stripe.Invoice> {
    try {
      return await this.stripe.invoices.retrieve(invoiceId);
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to retrieve Stripe invoice", {
        error: stripeError.message,
        code: stripeError.code,
        invoiceId,
      });
      throw createStripeError(
        `Failed to retrieve invoice: ${stripeError.message}`,
        404,
        "INVOICE_NOT_FOUND",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Usage Records (for metered billing)
  async createUsageRecord(subscriptionItemId: string, quantity: number, timestamp?: number): Promise<Stripe.UsageRecord> {
    try {
      const usageRecord = await this.stripe.subscriptionItems.createUsageRecord(
        subscriptionItemId,
        {
          quantity,
          timestamp: timestamp || Math.floor(Date.now() / 1000),
          action: "increment",
        }
      );

      logger.info("Usage record created", {
        subscriptionItemId,
        quantity,
        timestamp: usageRecord.timestamp,
      });

      return usageRecord;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to create usage record", {
        error: stripeError.message,
        code: stripeError.code,
        subscriptionItemId,
        quantity,
      });
      throw createStripeError(
        `Failed to create usage record: ${stripeError.message}`,
        402,
        "USAGE_RECORD_CREATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async getUsageRecords(subscriptionItemId: string, startingAfter?: string, limit: number = 100): Promise<Stripe.UsageRecordSummary[]> {
    try {
      const params: Stripe.SubscriptionItemListUsageRecordSummariesParams = { limit };
      if (startingAfter) {
        params.starting_after = startingAfter;
      }

      const usageRecords = await this.stripe.subscriptionItems.listUsageRecordSummaries(
        subscriptionItemId,
        params
      );

      return usageRecords.data;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to get usage records", {
        error: stripeError.message,
        code: stripeError.code,
        subscriptionItemId,
      });
      throw createStripeError(
        `Failed to get usage records: ${stripeError.message}`,
        402,
        "USAGE_RECORDS_RETRIEVAL_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Webhook Management
  constructWebhookEvent(payload: string, signature: string, endpointSecret?: string): Stripe.Event {
    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        endpointSecret || config.stripe.webhookSecret
      );

      logger.info("Webhook event constructed", {
        type: event.type,
        id: event.id,
      });

      return event;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to construct webhook event", {
        error: stripeError.message,
        code: stripeError.code,
      });
      throw createStripeError(
        `Webhook signature verification failed: ${stripeError.message}`,
        400,
        "WEBHOOK_SIGNATURE_VERIFICATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Coupon Management
  async createCoupon(couponData: Stripe.CouponCreateParams): Promise<Stripe.Coupon> {
    try {
      const coupon = await this.stripe.coupons.create(couponData);

      logger.info("Stripe coupon created", {
        couponId: coupon.id,
        type: coupon.percent_off ? "percent" : "amount",
        value: coupon.percent_off || coupon.amount_off,
      });

      return coupon;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to create Stripe coupon", {
        error: stripeError.message,
        code: stripeError.code,
      });
      throw createStripeError(
        `Failed to create coupon: ${stripeError.message}`,
        402,
        "COUPON_CREATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async getCoupon(couponId: string): Promise<Stripe.Coupon> {
    try {
      return await this.stripe.coupons.retrieve(couponId);
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to retrieve Stripe coupon", {
        error: stripeError.message,
        code: stripeError.code,
        couponId,
      });
      throw createStripeError(
        `Failed to retrieve coupon: ${stripeError.message}`,
        404,
        "COUPON_NOT_FOUND",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Portal and Billing Management
  async createBillingPortalSession(customerId: string, returnUrl: string): Promise<Stripe.BillingPortal.Session> {
    try {
      const session = await this.stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      });

      logger.info("Billing portal session created", {
        customerId,
        sessionId: session.id,
      });

      return session;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to create billing portal session", {
        error: stripeError.message,
        code: stripeError.code,
        customerId,
      });
      throw createStripeError(
        `Failed to create billing portal session: ${stripeError.message}`,
        402,
        "BILLING_PORTAL_CREATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Checkout Sessions
  async createCheckoutSession(sessionData: CheckoutSessionData): Promise<Stripe.Checkout.Session> {
    try {
      const session = await this.stripe.checkout.sessions.create({
        mode: sessionData.mode || "subscription",
        customer: sessionData.customerId,
        line_items: sessionData.lineItems,
        success_url: sessionData.successUrl,
        cancel_url: sessionData.cancelUrl,
        metadata: sessionData.metadata || {},
        subscription_data: sessionData.subscriptionData,
        allow_promotion_codes: sessionData.allowPromotionCodes !== false,
      });

      logger.info("Checkout session created", {
        sessionId: session.id,
        customerId: sessionData.customerId,
        mode: sessionData.mode,
      });

      return session;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to create checkout session", {
        error: stripeError.message,
        code: stripeError.code,
        customerId: sessionData.customerId,
      });
      throw createStripeError(
        `Failed to create checkout session: ${stripeError.message}`,
        402,
        "CHECKOUT_SESSION_CREATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Price and Product Management
  async getPrice(priceId: string): Promise<Stripe.Price> {
    try {
      return await this.stripe.prices.retrieve(priceId);
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to retrieve Stripe price", {
        error: stripeError.message,
        code: stripeError.code,
        priceId,
      });
      throw createStripeError(
        `Failed to retrieve price: ${stripeError.message}`,
        404,
        "PRICE_NOT_FOUND",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async getProduct(productId: string): Promise<Stripe.Product> {
    try {
      return await this.stripe.products.retrieve(productId);
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to retrieve Stripe product", {
        error: stripeError.message,
        code: stripeError.code,
        productId,
      });
      throw createStripeError(
        `Failed to retrieve product: ${stripeError.message}`,
        404,
        "PRODUCT_NOT_FOUND",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  // Payment Intent Management
  async createPaymentIntent(params: Stripe.PaymentIntentCreateParams): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create(params);

      logger.info("Payment intent created", {
        paymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status,
      });

      return paymentIntent;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to create payment intent", {
        error: stripeError.message,
        code: stripeError.code,
        amount: params.amount,
        currency: params.currency,
      });
      throw createStripeError(
        `Failed to create payment intent: ${stripeError.message}`,
        402,
        "PAYMENT_INTENT_CREATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }

  async confirmPaymentIntent(paymentIntentId: string, params?: Stripe.PaymentIntentConfirmParams): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.confirm(paymentIntentId, params);

      logger.info("Payment intent confirmed", {
        paymentIntentId,
        status: paymentIntent.status,
      });

      return paymentIntent;
    } catch (error) {
      const stripeError = error as StripeError;
      logger.error("Failed to confirm payment intent", {
        error: stripeError.message,
        code: stripeError.code,
        paymentIntentId,
      });
      throw createStripeError(
        `Failed to confirm payment intent: ${stripeError.message}`,
        402,
        "PAYMENT_INTENT_CONFIRMATION_FAILED",
        stripeError.code,
        stripeError.decline_code,
        stripeError.param
      );
    }
  }
}
