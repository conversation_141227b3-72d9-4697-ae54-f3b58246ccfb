import { config } from "../config/config.ts";
import { logger, logSubscriptionEvent } from "../utils/logger.ts";
import { billingTransaction, billingQuery, billingQueryOne } from "../utils/database.ts";
import { StripeService } from "./stripeService.ts";
import { NotificationService } from "./notificationService.ts";
import { errors } from "../middleware/errorHandler.ts";
import type { 
  Subscription, 
  SubscriptionStatus, 
  CreateSubscriptionRequest, 
  UpdateSubscriptionRequest 
} from "../types/billing.ts";
import type Stripe from "stripe";

export interface SubscriptionFilters {
  tenantId?: string;
  status?: SubscriptionStatus;
  planId?: string;
  limit?: number;
  offset?: number;
}

export interface SubscriptionMetrics {
  totalSubscriptions: number;
  activeSubscriptions: number;
  trialSubscriptions: number;
  canceledSubscriptions: number;
  pastDueSubscriptions: number;
  avgRevenuePerUser: number;
  totalMonthlyRevenue: number;
}

export class SubscriptionService {
  private stripe: StripeService;
  private notifications: NotificationService;

  constructor(stripeService: StripeService) {
    this.stripe = stripeService;
    this.notifications = new NotificationService();
  }

  /**
   * Create a new subscription
   */
  async createSubscription(subscriptionData: CreateSubscriptionRequest): Promise<{
    subscription: Subscription;
    stripeSubscription: Stripe.Subscription;
    clientSecret?: string;
  }> {
    return await billingTransaction(async (client) => {
      // Create customer in Stripe if not exists
      let stripeCustomerId = subscriptionData.stripeCustomerId;
      if (!stripeCustomerId) {
        const stripeCustomer = await this.stripe.createCustomer({
          email: subscriptionData.email,
          name: subscriptionData.name,
          tenantId: subscriptionData.tenantId,
          userId: subscriptionData.userId,
          company: subscriptionData.company,
        });
        stripeCustomerId = stripeCustomer.id;

        // Update tenant with Stripe customer ID
        await client.queryObject(
          "UPDATE tenants SET stripe_customer_id = $1 WHERE id = $2",
          [stripeCustomerId, subscriptionData.tenantId]
        );
      }

      // Get plan configuration
      const plan = config.plans[subscriptionData.planId as keyof typeof config.plans];
      if (!plan) {
        throw errors.badRequest(`Invalid plan ID: ${subscriptionData.planId}`);
      }

      // Create subscription in Stripe
      const stripeSubscription = await this.stripe.createSubscription({
        customerId: stripeCustomerId,
        items: [
          {
            price: plan.stripePriceId,
            quantity: 1,
          },
        ],
        tenantId: subscriptionData.tenantId,
        planId: subscriptionData.planId,
        trialDays: subscriptionData.trialDays,
        couponId: subscriptionData.couponId,
      });

      // Create subscription record in database
      const subscriptionResult = await client.queryObject(`
        INSERT INTO subscriptions (
          id, tenant_id, stripe_subscription_id, stripe_customer_id,
          plan_id, status, current_period_start, current_period_end,
          trial_start, trial_end, cancel_at_period_end, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW())
        RETURNING *
      `, [
        stripeSubscription.id,
        subscriptionData.tenantId,
        stripeSubscription.id,
        stripeCustomerId,
        subscriptionData.planId,
        stripeSubscription.status,
        new Date(stripeSubscription.current_period_start * 1000),
        new Date(stripeSubscription.current_period_end * 1000),
        stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : null,
        stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
        stripeSubscription.cancel_at_period_end || false,
      ]);

      // Create subscription items
      for (const item of stripeSubscription.items.data) {
        await client.queryObject(`
          INSERT INTO subscription_items (
            id, subscription_id, stripe_item_id, stripe_price_id,
            quantity, created_at
          ) VALUES ($1, $2, $3, $4, $5, NOW())
        `, [
          item.id,
          stripeSubscription.id,
          item.id,
          item.price.id,
          item.quantity,
        ]);
      }

      // Update tenant subscription status
      await client.queryObject(`
        UPDATE tenants SET 
          subscription_status = $1,
          plan_id = $2,
          updated_at = NOW()
        WHERE id = $3
      `, [
        stripeSubscription.status,
        subscriptionData.planId,
        subscriptionData.tenantId,
      ]);

      const subscription = subscriptionResult.rows[0] as Subscription;

      logSubscriptionEvent(
        "subscription_created",
        subscription.id,
        subscriptionData.tenantId,
        {
          planId: subscriptionData.planId,
          status: subscription.status,
        }
      );

      // Send notification
      await this.notifications.sendSubscriptionCreated(subscription);

      return {
        subscription,
        stripeSubscription,
        clientSecret: ((stripeSubscription.latest_invoice as Stripe.Invoice)?.payment_intent as Stripe.PaymentIntent)?.client_secret || undefined,
      };
    }, subscriptionData.tenantId);
  }

  /**
   * Update an existing subscription
   */
  async updateSubscription(subscriptionId: string, updateData: UpdateSubscriptionRequest, tenantId: string): Promise<Subscription> {
    return await billingTransaction(async (client) => {
      // Get current subscription
      const currentResult = await client.queryObject(
        "SELECT * FROM subscriptions WHERE id = $1 AND tenant_id = $2",
        [subscriptionId, tenantId]
      );

      if (currentResult.rows.length === 0) {
        throw errors.notFound("Subscription not found");
      }

      const currentSubscription = currentResult.rows[0] as Subscription;

      // Update subscription in Stripe
      const stripeUpdateData: Partial<Stripe.SubscriptionUpdateParams> = {};
      
      if (updateData.planId && updateData.planId !== currentSubscription.planId) {
        const newPlan = config.plans[updateData.planId as keyof typeof config.plans];
        if (!newPlan) {
          throw errors.badRequest(`Invalid plan ID: ${updateData.planId}`);
        }

        // Get current subscription items
        const stripeSubscription = await this.stripe.getSubscription(subscriptionId);
        const currentItem = stripeSubscription.items.data[0];

        stripeUpdateData.items = [
          {
            id: currentItem.id,
            price: newPlan.stripePriceId,
            quantity: 1,
          },
        ];

        stripeUpdateData.proration_behavior = config.billing.prorationEnabled ? "create_prorations" : "none";
      }

      if (updateData.cancelAtPeriodEnd !== undefined) {
        stripeUpdateData.cancel_at_period_end = updateData.cancelAtPeriodEnd;
      }

      if (updateData.metadata) {
        stripeUpdateData.metadata = updateData.metadata as Record<string, string>;
      }

      // Update in Stripe if there are changes
      let updatedStripeSubscription = await this.stripe.getSubscription(subscriptionId);
      if (Object.keys(stripeUpdateData).length > 0) {
        updatedStripeSubscription = await this.stripe.updateSubscription(subscriptionId, stripeUpdateData);
      }

      // Prepare database update
      const dbUpdateData: Record<string, unknown> = {
        updated_at: new Date(),
      };

      if (updateData.planId) {
        dbUpdateData.plan_id = updateData.planId;
      }

      if (updateData.cancelAtPeriodEnd !== undefined) {
        dbUpdateData.cancel_at_period_end = updateData.cancelAtPeriodEnd;
      }

      if (updateData.metadata) {
        dbUpdateData.metadata = updateData.metadata;
      }

      // Update subscription status from Stripe
      dbUpdateData.status = updatedStripeSubscription.status;
      dbUpdateData.current_period_start = new Date(updatedStripeSubscription.current_period_start * 1000);
      dbUpdateData.current_period_end = new Date(updatedStripeSubscription.current_period_end * 1000);

      const updateColumns = Object.keys(dbUpdateData).map((key, index) => `${key} = $${index + 2}`).join(", ");
      const updateValues = [subscriptionId, ...Object.values(dbUpdateData)];

      const updatedResult = await client.queryObject(
        `UPDATE subscriptions SET ${updateColumns} WHERE id = $1 RETURNING *`,
        updateValues
      );

      // Update tenant if plan changed
      if (updateData.planId) {
        await client.queryObject(`
          UPDATE tenants SET 
            plan_id = $1,
            subscription_status = $2,
            updated_at = NOW()
          WHERE id = $3
        `, [
          updateData.planId,
          updatedStripeSubscription.status,
          currentSubscription.tenantId,
        ]);
      }

      const updatedSubscription = updatedResult.rows[0] as Subscription;

      logSubscriptionEvent(
        "subscription_updated",
        subscriptionId,
        tenantId,
        {
          changes: Object.keys(updateData),
          newStatus: updatedSubscription.status,
        }
      );

      // Send notification if plan changed
      if (updateData.planId) {
        await this.notifications.sendSubscriptionUpdated(
          updatedSubscription,
          currentSubscription.planId,
          updateData.planId
        );
      }

      return updatedSubscription;
    }, tenantId);
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(
    subscriptionId: string,
    tenantId: string,
    cancelOptions: {
      immediate?: boolean;
      invoiceNow?: boolean;
      prorate?: boolean;
      reason?: string;
    } = {}
  ): Promise<Subscription> {
    return await billingTransaction(async (client) => {
      // Get current subscription
      const subscriptionResult = await client.queryObject(
        "SELECT * FROM subscriptions WHERE id = $1 AND tenant_id = $2",
        [subscriptionId, tenantId]
      );

      if (subscriptionResult.rows.length === 0) {
        throw errors.notFound("Subscription not found");
      }

      const subscription = subscriptionResult.rows[0] as Subscription;

      // Cancel subscription in Stripe
      let cancelledStripeSubscription: Stripe.Subscription;
      if (cancelOptions.immediate) {
        cancelledStripeSubscription = await this.stripe.cancelSubscription(subscriptionId, {
          invoiceNow: cancelOptions.invoiceNow,
          prorate: cancelOptions.prorate,
        });
      } else {
        cancelledStripeSubscription = await this.stripe.updateSubscription(subscriptionId, {
          cancel_at_period_end: true,
        });
      }

      // Update subscription in database
      const updateData: Record<string, unknown> = {
        status: cancelledStripeSubscription.status,
        cancel_at_period_end: cancelledStripeSubscription.cancel_at_period_end,
        canceled_at: cancelledStripeSubscription.canceled_at ?
          new Date(cancelledStripeSubscription.canceled_at * 1000) : null,
        cancellation_reason: cancelOptions.reason || null,
        updated_at: new Date(),
      };

      const updateColumns = Object.keys(updateData).map((key, index) => `${key} = $${index + 2}`).join(", ");
      const updateValues = [subscriptionId, ...Object.values(updateData)];

      const updatedResult = await client.queryObject(
        `UPDATE subscriptions SET ${updateColumns} WHERE id = $1 RETURNING *`,
        updateValues
      );

      // Update tenant subscription status
      await client.queryObject(`
        UPDATE tenants SET
          subscription_status = $1,
          updated_at = NOW()
        WHERE id = $2
      `, [
        cancelledStripeSubscription.status,
        subscription.tenantId,
      ]);

      const updatedSubscription = updatedResult.rows[0] as Subscription;

      logSubscriptionEvent(
        "subscription_cancelled",
        subscriptionId,
        tenantId,
        {
          immediate: cancelOptions.immediate,
          reason: cancelOptions.reason,
          status: updatedSubscription.status,
        }
      );

      // Send notification
      await this.notifications.sendSubscriptionCancelled(
        updatedSubscription,
        cancelOptions.reason
      );

      return updatedSubscription;
    }, tenantId);
  }

  /**
   * Get subscription details
   */
  async getSubscription(subscriptionId: string, tenantId: string): Promise<Subscription & { plan: unknown; items: unknown[] }> {
    const result = await billingQueryOne(
      `SELECT s.*, t.name as tenant_name, t.email as tenant_email
       FROM subscriptions s
       JOIN tenants t ON s.tenant_id = t.id
       WHERE s.id = $1 AND s.tenant_id = $2`,
      [subscriptionId, tenantId],
      tenantId
    );

    if (!result) {
      throw errors.notFound("Subscription not found");
    }

    const subscription = result as Subscription & { tenant_name: string; tenant_email: string; plan?: unknown; items?: unknown[] };

    // Get subscription items
    const itemsResult = await billingQuery(
      "SELECT * FROM subscription_items WHERE subscription_id = $1",
      [subscriptionId],
      tenantId
    );

    subscription.items = itemsResult || [];

    // Get plan details
    subscription.plan = config.plans[subscription.planId as keyof typeof config.plans] || null;

    return subscription as Subscription & { plan: unknown; items: unknown[] };
  }

  /**
   * List subscriptions with filters
   */
  async listSubscriptions(filters: SubscriptionFilters = {}): Promise<{
    subscriptions: (Subscription & { plan: unknown })[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const queryParams: unknown[] = [];
    const whereConditions: string[] = [];
    let paramCount = 0;

    // Build WHERE clause
    if (filters.tenantId) {
      paramCount++;
      whereConditions.push(`s.tenant_id = $${paramCount}`);
      queryParams.push(filters.tenantId);
    }

    if (filters.status) {
      paramCount++;
      whereConditions.push(`s.status = $${paramCount}`);
      queryParams.push(filters.status);
    }

    if (filters.planId) {
      paramCount++;
      whereConditions.push(`s.plan_id = $${paramCount}`);
      queryParams.push(filters.planId);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";

    const limit = filters.limit || 50;
    const offset = filters.offset || 0;

    // Use regular query for list operations (no tenant filtering needed here)
    const result = await billingQuery(`
      SELECT s.*, t.name as tenant_name, t.email as tenant_email
      FROM subscriptions s
      JOIN tenants t ON s.tenant_id = t.id
      ${whereClause}
      ORDER BY s.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `, [...queryParams, limit, offset], filters.tenantId || "");

    // Get total count
    const countResult = await billingQueryOne(`
      SELECT COUNT(*) as total
      FROM subscriptions s
      JOIN tenants t ON s.tenant_id = t.id
      ${whereClause}
    `, queryParams, filters.tenantId || "");

    const subscriptions = result.map((subscription: unknown) => {
      const sub = subscription as Record<string, unknown>;
      return {
        ...sub,
        plan: config.plans[sub.plan_id as keyof typeof config.plans],
      };
    }) as (Subscription & { plan: unknown })[];

    return {
      subscriptions,
      total: parseInt((countResult as Record<string, unknown>).total as string),
      limit,
      offset,
    };
  }

  /**
   * Handle Stripe webhook events
   */
  async handleWebhookEvent(event: Stripe.Event): Promise<void> {
    try {
      switch (event.type) {
        case "customer.subscription.created":
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
          break;
        case "customer.subscription.updated":
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;
        case "customer.subscription.deleted":
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;
        case "customer.subscription.trial_will_end":
          await this.handleTrialWillEnd(event.data.object as Stripe.Subscription);
          break;
        default:
          logger.info("Unhandled webhook event type", { type: event.type });
      }
    } catch (error) {
      logger.error("Failed to handle webhook event", {
        error: (error as Error).message,
        eventType: event.type,
        eventId: event.id,
      });
      throw error;
    }
  }

  private async handleSubscriptionCreated(stripeSubscription: Stripe.Subscription): Promise<void> {
    // This might be called by webhook before our API completes
    // Check if subscription already exists
    const existingResult = await billingQueryOne(
      "SELECT id FROM subscriptions WHERE id = $1",
      [stripeSubscription.id],
      ""
    );

    if (existingResult) {
      logger.info("Subscription already exists, skipping creation", {
        subscriptionId: stripeSubscription.id,
      });
      return;
    }

    logger.info("Handling subscription created webhook", {
      subscriptionId: stripeSubscription.id,
    });
  }

  private async handleSubscriptionUpdated(stripeSubscription: Stripe.Subscription): Promise<void> {
    await billingTransaction(async (client) => {
      // Update subscription in database
      await client.queryObject(`
        UPDATE subscriptions SET
          status = $1,
          current_period_start = $2,
          current_period_end = $3,
          cancel_at_period_end = $4,
          canceled_at = $5,
          updated_at = NOW()
        WHERE id = $6
      `, [
        stripeSubscription.status,
        new Date(stripeSubscription.current_period_start * 1000),
        new Date(stripeSubscription.current_period_end * 1000),
        stripeSubscription.cancel_at_period_end,
        stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : null,
        stripeSubscription.id,
      ]);

      // Update tenant subscription status
      await client.queryObject(`
        UPDATE tenants SET
          subscription_status = $1,
          updated_at = NOW()
        WHERE stripe_customer_id = $2
      `, [
        stripeSubscription.status,
        stripeSubscription.customer,
      ]);

      logger.info("Subscription updated via webhook", {
        subscriptionId: stripeSubscription.id,
        status: stripeSubscription.status,
      });
    }, "");
  }

  private async handleSubscriptionDeleted(stripeSubscription: Stripe.Subscription): Promise<void> {
    await billingTransaction(async (client) => {
      // Update subscription status to canceled
      await client.queryObject(`
        UPDATE subscriptions SET
          status = 'canceled',
          canceled_at = $1,
          updated_at = NOW()
        WHERE id = $2
      `, [
        new Date(stripeSubscription.canceled_at! * 1000),
        stripeSubscription.id,
      ]);

      // Update tenant subscription status
      await client.queryObject(`
        UPDATE tenants SET
          subscription_status = 'canceled',
          updated_at = NOW()
        WHERE stripe_customer_id = $1
      `, [stripeSubscription.customer]);

      logger.info("Subscription deleted via webhook", {
        subscriptionId: stripeSubscription.id,
      });
    }, "");
  }

  private async handleTrialWillEnd(stripeSubscription: Stripe.Subscription): Promise<void> {
    // Send trial ending notification
    const subscription = await billingQueryOne(
      "SELECT * FROM subscriptions WHERE id = $1",
      [stripeSubscription.id],
      ""
    );

    if (subscription) {
      await this.notifications.sendTrialExpiringNotification(subscription as Subscription);

      logger.info("Trial will end notification sent", {
        subscriptionId: stripeSubscription.id,
        trialEnd: stripeSubscription.trial_end,
      });
    }
  }

  /**
   * Get subscription metrics
   */
  async getSubscriptionMetrics(tenantId?: string): Promise<SubscriptionMetrics> {
    const whereClause = tenantId ? "WHERE s.tenant_id = $1" : "";
    const params = tenantId ? [tenantId] : [];

    const result = await billingQueryOne(`
      SELECT
        COUNT(*) as total_subscriptions,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscriptions,
        COUNT(CASE WHEN status = 'trialing' THEN 1 END) as trial_subscriptions,
        COUNT(CASE WHEN status = 'canceled' THEN 1 END) as canceled_subscriptions,
        COUNT(CASE WHEN status = 'past_due' THEN 1 END) as past_due_subscriptions,
        AVG(CASE WHEN status = 'active' THEN
          CASE plan_id
            WHEN 'basic' THEN ${config.plans.basic.monthlyPrice}
            WHEN 'pro' THEN ${config.plans.pro.monthlyPrice}
            WHEN 'enterprise' THEN ${config.plans.enterprise.monthlyPrice}
          END
        END) as avg_revenue_per_user,
        SUM(CASE WHEN status = 'active' THEN
          CASE plan_id
            WHEN 'basic' THEN ${config.plans.basic.monthlyPrice}
            WHEN 'pro' THEN ${config.plans.pro.monthlyPrice}
            WHEN 'enterprise' THEN ${config.plans.enterprise.monthlyPrice}
          END
        END) as total_monthly_revenue
      FROM subscriptions s
      ${whereClause}
    `, params, tenantId || "");

    return {
      totalSubscriptions: parseInt((result as Record<string, unknown>).total_subscriptions as string),
      activeSubscriptions: parseInt((result as Record<string, unknown>).active_subscriptions as string),
      trialSubscriptions: parseInt((result as Record<string, unknown>).trial_subscriptions as string),
      canceledSubscriptions: parseInt((result as Record<string, unknown>).canceled_subscriptions as string),
      pastDueSubscriptions: parseInt((result as Record<string, unknown>).past_due_subscriptions as string),
      avgRevenuePerUser: parseFloat((result as Record<string, unknown>).avg_revenue_per_user as string) || 0,
      totalMonthlyRevenue: parseFloat((result as Record<string, unknown>).total_monthly_revenue as string) || 0,
    };
  }

  /**
   * Send trial expiring notification
   */
  async sendTrialExpiringNotification(subscription: Subscription): Promise<void> {
    await this.notifications.sendTrialExpiringNotification(subscription);
  }

  /**
   * Close service and cleanup resources
   */
  async close(): Promise<void> {
    // Close notification service
    await this.notifications.close();
    logger.info("Subscription service closed");
  }
}
