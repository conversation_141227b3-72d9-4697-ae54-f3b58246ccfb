import { config } from "../config/config.ts";
import { logger, logUsageEvent } from "../utils/logger.ts";
import { billingTransaction, billingQuery, billingQueryOne } from "../utils/database.ts";
import { set, get, incr } from "../utils/redis.ts";
import { errors } from "../middleware/errorHandler.ts";
import type { UsageRecord, UsageReportRequest } from "../types/billing.ts";

export interface UsageFilters {
  tenantId?: string;
  subscriptionId?: string;
  metricName?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export interface UsageMetrics {
  totalUsage: number;
  currentPeriodUsage: number;
  averageDailyUsage: number;
  peakUsage: number;
  usageByMetric: Record<string, number>;
}

export class UsageTrackingService {
  private batchQueue: Map<string, UsageRecord[]> = new Map();
  private flushTimer: number | null = null;

  constructor() {
    this.startBatchProcessor();
    logger.info("Usage tracking service initialized");
  }

  /**
   * Report usage for a subscription
   */
  async reportUsage(usageData: UsageReportRequest): Promise<void> {
    try {
      const usageRecord: UsageRecord = {
        id: crypto.randomUUID(),
        tenantId: usageData.tenantId,
        subscriptionId: usageData.subscriptionId,
        metricName: usageData.metricName,
        quantity: usageData.quantity,
        timestamp: usageData.timestamp || new Date(),
        metadata: usageData.metadata,
        createdAt: new Date(),
      };

      // Add to batch queue for processing
      const queueKey = `${usageData.tenantId}:${usageData.subscriptionId}:${usageData.metricName}`;
      if (!this.batchQueue.has(queueKey)) {
        this.batchQueue.set(queueKey, []);
      }
      this.batchQueue.get(queueKey)!.push(usageRecord);

      // Update real-time cache
      await this.updateUsageCache(usageData.tenantId, usageData.metricName, usageData.quantity);

      logUsageEvent(
        "usage_reported",
        usageData.tenantId,
        usageData.metricName,
        usageData.quantity,
        {
          subscriptionId: usageData.subscriptionId,
          action: usageData.action,
        }
      );

      // Check if batch is full and needs immediate processing
      if (this.batchQueue.get(queueKey)!.length >= config.usage.batchSize) {
        await this.processBatch(queueKey);
      }
    } catch (error) {
      logger.error("Failed to report usage", {
        error: (error as Error).message,
        tenantId: usageData.tenantId,
        metricName: usageData.metricName,
        quantity: usageData.quantity,
      });
      throw error;
    }
  }

  /**
   * Get usage records with filters
   */
  async getUsageRecords(filters: UsageFilters = {}): Promise<{
    records: UsageRecord[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const queryParams: unknown[] = [];
    const whereConditions: string[] = [];
    let paramCount = 0;

    // Build WHERE clause
    if (filters.tenantId) {
      paramCount++;
      whereConditions.push(`tenant_id = $${paramCount}`);
      queryParams.push(filters.tenantId);
    }

    if (filters.subscriptionId) {
      paramCount++;
      whereConditions.push(`subscription_id = $${paramCount}`);
      queryParams.push(filters.subscriptionId);
    }

    if (filters.metricName) {
      paramCount++;
      whereConditions.push(`metric_name = $${paramCount}`);
      queryParams.push(filters.metricName);
    }

    if (filters.startDate) {
      paramCount++;
      whereConditions.push(`timestamp >= $${paramCount}`);
      queryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      paramCount++;
      whereConditions.push(`timestamp <= $${paramCount}`);
      queryParams.push(filters.endDate);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";

    const limit = filters.limit || 100;
    const offset = filters.offset || 0;

    const result = await billingQuery(`
      SELECT * FROM usage_records
      ${whereClause}
      ORDER BY timestamp DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `, [...queryParams, limit, offset], filters.tenantId || "");

    // Get total count
    const countResult = await billingQueryOne(`
      SELECT COUNT(*) as total FROM usage_records
      ${whereClause}
    `, queryParams, filters.tenantId || "");

    return {
      records: result as UsageRecord[],
      total: parseInt((countResult as Record<string, unknown>).total as string),
      limit,
      offset,
    };
  }

  /**
   * Get usage metrics for a tenant
   */
  async getUsageMetrics(tenantId: string, metricName?: string, period?: { start: Date; end: Date }): Promise<UsageMetrics> {
    const queryParams: unknown[] = [tenantId];
    const whereConditions = ["tenant_id = $1"];
    let paramCount = 1;

    if (metricName) {
      paramCount++;
      whereConditions.push(`metric_name = $${paramCount}`);
      queryParams.push(metricName);
    }

    if (period) {
      paramCount++;
      whereConditions.push(`timestamp >= $${paramCount}`);
      queryParams.push(period.start);

      paramCount++;
      whereConditions.push(`timestamp <= $${paramCount}`);
      queryParams.push(period.end);
    }

    const whereClause = `WHERE ${whereConditions.join(" AND ")}`;

    // Get aggregated metrics
    const metricsResult = await billingQueryOne(`
      SELECT 
        SUM(quantity) as total_usage,
        AVG(quantity) as average_usage,
        MAX(quantity) as peak_usage,
        COUNT(*) as record_count
      FROM usage_records
      ${whereClause}
    `, queryParams, tenantId);

    // Get usage by metric
    const metricBreakdownResult = await billingQuery(`
      SELECT 
        metric_name,
        SUM(quantity) as total_quantity
      FROM usage_records
      ${whereClause}
      GROUP BY metric_name
    `, queryParams, tenantId);

    const usageByMetric: Record<string, number> = {};
    for (const row of metricBreakdownResult) {
      const record = row as { metric_name: string; total_quantity: string };
      usageByMetric[record.metric_name] = parseInt(record.total_quantity);
    }

    // Calculate current period usage (this month)
    const currentPeriodStart = new Date();
    currentPeriodStart.setDate(1);
    currentPeriodStart.setHours(0, 0, 0, 0);

    const currentPeriodResult = await billingQueryOne(`
      SELECT SUM(quantity) as current_period_usage
      FROM usage_records
      WHERE tenant_id = $1 AND timestamp >= $2
      ${metricName ? "AND metric_name = $3" : ""}
    `, metricName ? [tenantId, currentPeriodStart, metricName] : [tenantId, currentPeriodStart], tenantId);

    const metrics = metricsResult as Record<string, unknown>;
    const currentPeriod = currentPeriodResult as Record<string, unknown>;

    return {
      totalUsage: parseInt(metrics.total_usage as string) || 0,
      currentPeriodUsage: parseInt(currentPeriod.current_period_usage as string) || 0,
      averageDailyUsage: parseFloat(metrics.average_usage as string) || 0,
      peakUsage: parseInt(metrics.peak_usage as string) || 0,
      usageByMetric,
    };
  }

  /**
   * Get current usage from cache
   */
  async getCurrentUsage(tenantId: string, metricName: string): Promise<number> {
    try {
      const cacheKey = `usage:current:${metricName}`;
      const cachedUsage = await get(cacheKey, tenantId);
      return cachedUsage ? parseInt(cachedUsage) : 0;
    } catch (error) {
      logger.error("Failed to get current usage from cache", {
        error: (error as Error).message,
        tenantId,
        metricName,
      });
      return 0;
    }
  }

  /**
   * Update usage cache
   */
  private async updateUsageCache(tenantId: string, metricName: string, quantity: number): Promise<void> {
    try {
      const cacheKey = `usage:current:${metricName}`;
      await incr(cacheKey, tenantId);
      
      // Set expiration for cache cleanup
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      const secondsUntilMidnight = Math.floor((tomorrow.getTime() - Date.now()) / 1000);
      
      await set(cacheKey, quantity.toString(), secondsUntilMidnight, tenantId);
    } catch (error) {
      logger.error("Failed to update usage cache", {
        error: (error as Error).message,
        tenantId,
        metricName,
      });
    }
  }

  /**
   * Start batch processor
   */
  private startBatchProcessor(): void {
    this.flushTimer = setInterval(() => {
      this.flushAllBatches();
    }, config.usage.flushInterval);
  }

  /**
   * Process a specific batch
   */
  private async processBatch(queueKey: string): Promise<void> {
    const batch = this.batchQueue.get(queueKey);
    if (!batch || batch.length === 0) {
      return;
    }

    try {
      await billingTransaction(async (client) => {
        for (const record of batch) {
          await client.queryObject(`
            INSERT INTO usage_records (
              id, tenant_id, subscription_id, metric_name,
              quantity, timestamp, metadata, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          `, [
            record.id,
            record.tenantId,
            record.subscriptionId,
            record.metricName,
            record.quantity,
            record.timestamp,
            record.metadata ? JSON.stringify(record.metadata) : null,
            record.createdAt,
          ]);
        }

        logger.info("Usage batch processed", {
          queueKey,
          recordCount: batch.length,
        });
      }, batch[0].tenantId);

      // Clear the batch
      this.batchQueue.delete(queueKey);
    } catch (error) {
      logger.error("Failed to process usage batch", {
        error: (error as Error).message,
        queueKey,
        recordCount: batch.length,
      });
    }
  }

  /**
   * Flush all batches
   */
  private async flushAllBatches(): Promise<void> {
    const promises: Promise<void>[] = [];
    
    for (const queueKey of this.batchQueue.keys()) {
      promises.push(this.processBatch(queueKey));
    }

    await Promise.allSettled(promises);
  }

  /**
   * Close service and cleanup resources
   */
  async close(): Promise<void> {
    // Clear the flush timer
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }

    // Flush any remaining batches
    await this.flushAllBatches();

    logger.info("Usage tracking service closed");
  }
}
