// Billing service types

export interface Subscription {
  id: string;
  tenantId: string;
  stripeSubscriptionId: string;
  stripeCustomerId: string;
  planId: string;
  status: SubscriptionStatus;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  trialStart?: Date;
  trialEnd?: Date;
  canceledAt?: Date;
  cancelAtPeriodEnd: boolean;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

export type SubscriptionStatus = 
  | "incomplete"
  | "incomplete_expired"
  | "trialing"
  | "active"
  | "past_due"
  | "canceled"
  | "unpaid";

export interface Invoice {
  id: string;
  tenantId: string;
  subscriptionId: string;
  stripeInvoiceId: string;
  number: string;
  status: InvoiceStatus;
  amountDue: number;
  amountPaid: number;
  currency: string;
  dueDate?: Date;
  paidAt?: Date;
  hostedInvoiceUrl?: string;
  invoicePdf?: string;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

export type InvoiceStatus = 
  | "draft"
  | "open"
  | "paid"
  | "uncollectible"
  | "void";

export interface Payment {
  id: string;
  tenantId: string;
  subscriptionId?: string;
  invoiceId?: string;
  stripePaymentIntentId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string;
  failureCode?: string;
  failureMessage?: string;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

export type PaymentStatus = 
  | "requires_payment_method"
  | "requires_confirmation"
  | "requires_action"
  | "processing"
  | "requires_capture"
  | "canceled"
  | "succeeded";

export interface UsageRecord {
  id: string;
  tenantId: string;
  subscriptionId: string;
  metricName: string;
  quantity: number;
  timestamp: Date;
  metadata?: Record<string, unknown>;
  createdAt: Date;
}

export interface Plan {
  id: string;
  name: string;
  description?: string;
  stripePriceId: string;
  stripeProductId?: string;
  monthlyPrice: number;
  yearlyPrice?: number;
  currency: string;
  features: PlanFeatures;
  limits: PlanLimits;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface PlanFeatures {
  apiRequests: number;
  dataRetentionDays: number;
  teamMembers: number;
  integrations: number;
  customReports: boolean;
  advancedAnalytics: boolean;
  prioritySupport: boolean;
  customIntegrations?: boolean;
  dedicatedSupport?: boolean;
}

export interface PlanLimits {
  maxApiRequests: number;
  maxDataRetentionDays: number;
  maxTeamMembers: number;
  maxIntegrations: number;
}

export interface Customer {
  id: string;
  tenantId: string;
  stripeCustomerId: string;
  email: string;
  name?: string;
  phone?: string;
  company?: string;
  address?: CustomerAddress;
  taxExempt: boolean;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CustomerAddress {
  line1?: string;
  line2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

export interface WebhookEvent {
  id: string;
  type: string;
  data: Record<string, unknown>;
  processed: boolean;
  processedAt?: Date;
  error?: string;
  retryCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// Request/Response types
export interface CreateSubscriptionRequest {
  tenantId: string;
  planId: string;
  email: string;
  name?: string;
  company?: string;
  paymentMethodId?: string;
  couponId?: string;
  trialDays?: number;
  userId?: string;
  stripeCustomerId?: string;
}

export interface CreateSubscriptionResponse {
  subscription: Subscription;
  clientSecret?: string;
}

export interface UpdateSubscriptionRequest {
  planId?: string;
  cancelAtPeriodEnd?: boolean;
  metadata?: Record<string, unknown>;
}

export interface CreateInvoiceRequest {
  tenantId: string;
  customerId: string;
  subscriptionId?: string;
  dueDate?: Date;
  collectionMethod?: "charge_automatically" | "send_invoice";
  daysUntilDue?: number;
  autoAdvance?: boolean;
  metadata?: Record<string, string>;
}

export interface CreatePaymentIntentRequest {
  tenantId: string;
  amount: number;
  currency: string;
  customerId?: string;
  paymentMethodId?: string;
  subscriptionId?: string;
  invoiceId?: string;
  confirmationMethod?: "automatic" | "manual";
  confirm?: boolean;
  metadata?: Record<string, string>;
}

export interface UsageReportRequest {
  tenantId: string;
  subscriptionId: string;
  metricName: string;
  quantity: number;
  timestamp?: Date;
  action?: "increment" | "set";
  metadata?: Record<string, unknown>;
}

// Error types
export interface BillingError extends Error {
  code: string;
  statusCode: number;
  details?: Record<string, unknown>;
}

export interface StripeError extends BillingError {
  stripeCode?: string;
  declineCode?: string;
  param?: string;
}

// Job types
export interface BillingJob {
  id: string;
  type: string;
  data: Record<string, unknown>;
  priority: number;
  attempts: number;
  maxAttempts: number;
  delay?: number;
  createdAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  failedAt?: Date;
  error?: string;
}

export type BillingJobType = 
  | "subscription_created"
  | "subscription_updated"
  | "subscription_canceled"
  | "invoice_created"
  | "invoice_paid"
  | "invoice_payment_failed"
  | "usage_aggregation"
  | "dunning_process"
  | "trial_expiring_notification"
  | "subscription_renewal_reminder";

// Notification types
export interface NotificationData {
  type: string;
  tenantId: string;
  data: Record<string, unknown>;
  channels: NotificationChannel[];
}

export type NotificationChannel = "email" | "slack" | "webhook";

export interface EmailNotification {
  to: string;
  subject: string;
  templateId: string;
  templateData: Record<string, unknown>;
}

export interface SlackNotification {
  channel: string;
  text: string;
  attachments?: SlackAttachment[];
}

export interface SlackAttachment {
  color: string;
  title: string;
  text: string;
  fields: SlackField[];
}

export interface SlackField {
  title: string;
  value: string;
  short: boolean;
}

export interface WebhookNotification {
  url: string;
  payload: Record<string, unknown>;
  headers?: Record<string, string>;
}
