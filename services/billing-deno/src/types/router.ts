import { Context } from "@oak/oak";

// Extended context type for router with params
export interface RouterContext extends Context {
  params: Record<string, string>;
}

// Type guard to check if context has params
export function hasParams(ctx: Context): ctx is RouterContext {
  return 'params' in ctx && typeof (ctx as { params?: unknown }).params === 'object';
}

// Helper to safely get params from context
export function getParam(ctx: Context, key: string): string | undefined {
  if (hasParams(ctx)) {
    return ctx.params[key];
  }
  return undefined;
}

// Helper to get required param from context
export function getRequiredParam(ctx: Context, key: string): string {
  const value = getParam(ctx, key);
  if (!value) {
    throw new Error(`Required parameter '${key}' is missing`);
  }
  return value;
}
