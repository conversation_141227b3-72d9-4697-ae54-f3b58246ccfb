import { logger } from "./logger.ts";

export interface HttpClientOptions {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
  retries?: number;
  retryDelay?: number;
}

export interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: unknown;
  params?: Record<string, string>;
  timeout?: number;
}

export interface HttpResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

export class HttpClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;
  private retries: number;
  private retryDelay: number;

  constructor(options: HttpClientOptions = {}) {
    this.baseURL = options.baseURL || "";
    this.timeout = options.timeout || 30000;
    this.defaultHeaders = options.headers || {};
    this.retries = options.retries || 3;
    this.retryDelay = options.retryDelay || 1000;
  }

  async request<T = unknown>(url: string, options: RequestOptions = {}): Promise<HttpResponse<T>> {
    const fullUrl = this.buildUrl(url, options.params);
    const requestOptions = this.buildRequestOptions(options);

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.retries; attempt++) {
      const startTime = Date.now();
      try {
        logger.debug("HTTP request started", {
          method: options.method || "GET",
          url: fullUrl,
          attempt: attempt + 1,
          maxAttempts: this.retries + 1,
        });

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        const response = await fetch(fullUrl, {
          ...requestOptions,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        const duration = Date.now() - startTime;
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentType = response.headers.get("content-type");
        let data: T;
        
        if (contentType?.includes("application/json")) {
          data = await response.json() as T;
        } else {
          data = await response.text() as T;
        }

        logger.debug("HTTP request completed", {
          method: options.method || "GET",
          url: fullUrl,
          status: response.status,
          duration,
          attempt: attempt + 1,
        });

        return {
          data,
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
        };
      } catch (error) {
        lastError = error as Error;
        const duration = Date.now() - startTime;
        
        logger.warn("HTTP request failed", {
          method: options.method || "GET",
          url: fullUrl,
          attempt: attempt + 1,
          maxAttempts: this.retries + 1,
          duration,
          error: lastError.message,
        });

        // Don't retry on the last attempt
        if (attempt < this.retries) {
          await this.delay(this.retryDelay * Math.pow(2, attempt));
        }
      }
    }

    logger.error("HTTP request failed after all retries", {
      method: options.method || "GET",
      url: fullUrl,
      attempts: this.retries + 1,
      error: lastError?.message,
    });

    throw lastError || new Error("Request failed after all retries");
  }

  async get<T = unknown>(url: string, options: Omit<RequestOptions, "method"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "GET" });
  }

  async post<T = unknown>(url: string, body?: unknown, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "POST", body });
  }

  async put<T = unknown>(url: string, body?: unknown, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "PUT", body });
  }

  async patch<T = unknown>(url: string, body?: unknown, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "PATCH", body });
  }

  async delete<T = unknown>(url: string, options: Omit<RequestOptions, "method"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: "DELETE" });
  }

  private buildUrl(url: string, params?: Record<string, string>): string {
    const fullUrl = this.baseURL ? `${this.baseURL}${url}` : url;
    
    if (!params || Object.keys(params).length === 0) {
      return fullUrl;
    }

    const urlObj = new URL(fullUrl);
    Object.entries(params).forEach(([key, value]) => {
      urlObj.searchParams.set(key, value);
    });

    return urlObj.toString();
  }

  private buildRequestOptions(options: RequestOptions): RequestInit {
    const headers = new Headers();
    
    // Add default headers
    Object.entries(this.defaultHeaders).forEach(([key, value]) => {
      headers.set(key, value);
    });
    
    // Add request-specific headers
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        headers.set(key, value);
      });
    }

    const requestOptions: RequestInit = {
      method: options.method || "GET",
      headers,
    };

    // Add body if present
    if (options.body !== undefined) {
      if (typeof options.body === "string") {
        requestOptions.body = options.body;
      } else {
        requestOptions.body = JSON.stringify(options.body);
        headers.set("Content-Type", "application/json");
      }
    }

    return requestOptions;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Create default HTTP client instances for external services
export const analyticsClient = new HttpClient({
  baseURL: Deno.env.get("ANALYTICS_SERVICE_URL") || "http://analytics-service:3002",
  timeout: parseInt(Deno.env.get("ANALYTICS_SERVICE_TIMEOUT") || "30000"),
  headers: {
    "User-Agent": "billing-service/1.0.0",
    "Content-Type": "application/json",
  },
});

export const dashboardClient = new HttpClient({
  baseURL: Deno.env.get("DASHBOARD_SERVICE_URL") || "http://dashboard-service:3001",
  timeout: parseInt(Deno.env.get("DASHBOARD_SERVICE_TIMEOUT") || "30000"),
  headers: {
    "User-Agent": "billing-service/1.0.0",
    "Content-Type": "application/json",
  },
});

export const adminClient = new HttpClient({
  baseURL: Deno.env.get("ADMIN_SERVICE_URL") || "http://admin-service:3005",
  timeout: parseInt(Deno.env.get("ADMIN_SERVICE_TIMEOUT") || "30000"),
  headers: {
    "User-Agent": "billing-service/1.0.0",
    "Content-Type": "application/json",
  },
});

// Notification HTTP client for webhooks
export const notificationClient = new HttpClient({
  timeout: parseInt(Deno.env.get("NOTIFICATION_WEBHOOK_TIMEOUT") || "10000"),
  retries: parseInt(Deno.env.get("NOTIFICATION_WEBHOOK_RETRIES") || "3"),
  headers: {
    "User-Agent": "billing-service/1.0.0",
    "Content-Type": "application/json",
  },
});
