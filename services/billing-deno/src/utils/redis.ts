import { connect, Redis } from "redis";
import { config } from "../config/config.ts";
import { logger } from "./logger.ts";

// Redis client instance
let redisClient: Redis | null = null;

/**
 * Initialize Redis connection
 */
export async function initializeRedis(): Promise<void> {
  try {
    redisClient = await connect({
      hostname: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.database,
    });

    // Test connection
    await redisClient.ping();

    logger.info("Redis connection initialized successfully", {
      host: config.redis.host,
      port: config.redis.port,
      database: config.redis.database,
    });
  } catch (error) {
    logger.error("Failed to initialize Redis connection", error as Error);
    throw error;
  }
}

/**
 * Get Redis client instance
 */
export function getRedisClient(): Redis {
  if (!redisClient) {
    throw new Error("Redis client not initialized. Call initializeRedis() first.");
  }
  return redisClient;
}

/**
 * Set a key-value pair with optional expiration and tenant isolation
 */
export async function set(
  key: string,
  value: string | number | object,
  expirationSeconds?: number,
  tenantId?: string,
): Promise<void> {
  const client = getRedisClient();
  const finalKey = tenantId ? `billing:tenant:${tenantId}:${key}` : `billing:${key}`;
  
  try {
    const serializedValue = typeof value === "object" ? JSON.stringify(value) : String(value);
    
    if (expirationSeconds) {
      await client.setex(finalKey, expirationSeconds, serializedValue);
    } else {
      await client.set(finalKey, serializedValue);
    }
    
    logger.debug("Redis SET operation completed", {
      key: finalKey,
      expiration: expirationSeconds,
      tenantId,
    });
  } catch (error) {
    logger.error("Redis SET operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Get a value by key with tenant isolation
 */
export async function get(key: string, tenantId?: string): Promise<string | null> {
  const client = getRedisClient();
  const finalKey = tenantId ? `billing:tenant:${tenantId}:${key}` : `billing:${key}`;
  
  try {
    const value = await client.get(finalKey);
    
    logger.debug("Redis GET operation completed", {
      key: finalKey,
      found: value !== null,
      tenantId,
    });
    
    return value;
  } catch (error) {
    logger.error("Redis GET operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Get a value and parse as JSON
 */
export async function getJSON<T = unknown>(key: string, tenantId?: string): Promise<T | null> {
  const value = await get(key, tenantId);
  
  if (value === null) {
    return null;
  }
  
  try {
    return JSON.parse(value) as T;
  } catch (error) {
    logger.error("Failed to parse JSON from Redis", {
      key: tenantId ? `billing:tenant:${tenantId}:${key}` : `billing:${key}`,
      value: value.substring(0, 100),
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Delete a key with tenant isolation
 */
export async function del(key: string, tenantId?: string): Promise<number> {
  const client = getRedisClient();
  const finalKey = tenantId ? `billing:tenant:${tenantId}:${key}` : `billing:${key}`;
  
  try {
    const result = await client.del(finalKey);
    
    logger.debug("Redis DEL operation completed", {
      key: finalKey,
      deleted: result,
      tenantId,
    });
    
    return result;
  } catch (error) {
    logger.error("Redis DEL operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Check if a key exists
 */
export async function exists(key: string, tenantId?: string): Promise<boolean> {
  const client = getRedisClient();
  const finalKey = tenantId ? `billing:tenant:${tenantId}:${key}` : `billing:${key}`;
  
  try {
    const result = await client.exists(finalKey);
    return result === 1;
  } catch (error) {
    logger.error("Redis EXISTS operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Set expiration for a key
 */
export async function expire(key: string, seconds: number, tenantId?: string): Promise<boolean> {
  const client = getRedisClient();
  const finalKey = tenantId ? `billing:tenant:${tenantId}:${key}` : `billing:${key}`;
  
  try {
    const result = await client.expire(finalKey, seconds);
    return result === 1;
  } catch (error) {
    logger.error("Redis EXPIRE operation failed", {
      key: finalKey,
      seconds,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Increment a numeric value
 */
export async function incr(key: string, tenantId?: string): Promise<number> {
  const client = getRedisClient();
  const finalKey = tenantId ? `billing:tenant:${tenantId}:${key}` : `billing:${key}`;
  
  try {
    const result = await client.incr(finalKey);
    
    logger.debug("Redis INCR operation completed", {
      key: finalKey,
      newValue: result,
      tenantId,
    });
    
    return result;
  } catch (error) {
    logger.error("Redis INCR operation failed", {
      key: finalKey,
      error: (error as Error).message,
      tenantId,
    });
    throw error;
  }
}

/**
 * Cache subscription data
 */
export async function cacheSubscription(
  subscriptionId: string,
  subscriptionData: object,
  tenantId: string,
  expirationSeconds = 3600, // 1 hour default
): Promise<void> {
  await set(`subscription:${subscriptionId}`, subscriptionData, expirationSeconds, tenantId);
}

/**
 * Get cached subscription data
 */
export async function getCachedSubscription<T = unknown>(
  subscriptionId: string,
  tenantId: string,
): Promise<T | null> {
  return getJSON<T>(`subscription:${subscriptionId}`, tenantId);
}

/**
 * Cache invoice data
 */
export async function cacheInvoice(
  invoiceId: string,
  invoiceData: object,
  tenantId: string,
  expirationSeconds = 1800, // 30 minutes default
): Promise<void> {
  await set(`invoice:${invoiceId}`, invoiceData, expirationSeconds, tenantId);
}

/**
 * Get cached invoice data
 */
export async function getCachedInvoice<T = unknown>(
  invoiceId: string,
  tenantId: string,
): Promise<T | null> {
  return getJSON<T>(`invoice:${invoiceId}`, tenantId);
}

/**
 * Cache customer data
 */
export async function cacheCustomer(
  customerId: string,
  customerData: object,
  tenantId: string,
  expirationSeconds = 7200, // 2 hours default
): Promise<void> {
  await set(`customer:${customerId}`, customerData, expirationSeconds, tenantId);
}

/**
 * Get cached customer data
 */
export async function getCachedCustomer<T = unknown>(
  customerId: string,
  tenantId: string,
): Promise<T | null> {
  return getJSON<T>(`customer:${customerId}`, tenantId);
}

/**
 * Cache usage metrics
 */
export async function cacheUsageMetrics(
  metricKey: string,
  metricsData: object,
  tenantId: string,
  expirationSeconds = 300, // 5 minutes default
): Promise<void> {
  await set(`usage:${metricKey}`, metricsData, expirationSeconds, tenantId);
}

/**
 * Get cached usage metrics
 */
export async function getCachedUsageMetrics<T = unknown>(
  metricKey: string,
  tenantId: string,
): Promise<T | null> {
  return getJSON<T>(`usage:${metricKey}`, tenantId);
}

/**
 * Invalidate all cached data for a tenant
 */
export async function invalidateTenantCache(tenantId: string): Promise<void> {
  const client = getRedisClient();
  const pattern = `billing:tenant:${tenantId}:*`;
  
  try {
    const keys = await client.keys(pattern);
    if (keys.length > 0) {
      await client.del(...keys);
      logger.info("Tenant cache invalidated", {
        tenantId,
        keysDeleted: keys.length,
      });
    }
  } catch (error) {
    logger.error("Failed to invalidate tenant cache", {
      tenantId,
      error: (error as Error).message,
    });
    throw error;
  }
}

/**
 * Health check for Redis connection
 */
export async function healthCheck(): Promise<boolean> {
  try {
    if (!redisClient) {
      return false;
    }
    
    const result = await redisClient.ping();
    return result === "PONG";
  } catch (error) {
    logger.error("Redis health check failed", error as Error);
    return false;
  }
}

/**
 * Close Redis connection
 */
export function closeRedis(): void {
  if (redisClient) {
    redisClient.close();
    redisClient = null;
    logger.info("Redis connection closed");
  }
}
