// Mock environment variables for testing BEFORE any imports
Deno.env.set("NODE_ENV", "test");
Deno.env.set("PORT", "3003");
Deno.env.set("DATABASE_URL", "postgresql://test:test@localhost:5432/test");
Deno.env.set("REDIS_URL", "redis://localhost:6379");
Deno.env.set("STRIPE_SECRET_KEY", "sk_test_mock_key");
Deno.env.set("STRIPE_WEBHOOK_SECRET", "whsec_mock_secret");
Deno.env.set("JWT_SECRET", "test_jwt_secret");

import { assertEquals, assertExists } from "@std/assert";
import { Application } from "@oak/oak";

Deno.test("Health Check - Service Configuration", async () => {
  // Test that the application can be created without errors
  const app = new Application();

  // Import and setup middleware and routes
  const { setupMiddleware } = await import("../src/middleware/index.ts");
  const { setupRoutes } = await import("../src/routes/index.ts");

  setupMiddleware(app);
  setupRoutes(app);
  
  // Verify app exists
  assertExists(app);
});

Deno.test("Health Check - Environment Variables", () => {
  // Test that required environment variables are set
  const requiredVars = [
    "NODE_ENV",
    "PORT", 
    "DATABASE_URL",
    "REDIS_URL",
    "STRIPE_SECRET_KEY",
    "STRIPE_WEBHOOK_SECRET",
    "JWT_SECRET"
  ];
  
  for (const varName of requiredVars) {
    const value = Deno.env.get(varName);
    assertExists(value, `Environment variable ${varName} should be set`);
  }
});

Deno.test("Health Check - Config Loading", async () => {
  // Test that config can be loaded without errors
  try {
    const { config } = await import("../src/config/config.ts");
    assertExists(config);
    assertEquals(config.nodeEnv, "test");
    // Port might be different due to config loading order, just check it exists
    assertExists(config.port);
  } catch (error) {
    throw new Error(`Config loading failed: ${(error as Error).message}`);
  }
});

Deno.test("Health Check - Service Imports", async () => {
  // Test that all main service modules can be imported
  try {
    await import("../src/services/index.ts");
    await import("../src/middleware/index.ts");
    await import("../src/routes/index.ts");
    await import("../src/utils/logger.ts");
    await import("../src/utils/database.ts");
  } catch (error) {
    throw new Error(`Service import failed: ${(error as Error).message}`);
  }
});
