// Mock environment variables for testing BEFORE any imports
Deno.env.set("NODE_ENV", "test");
Deno.env.set("PORT", "3003");
Deno.env.set("DATABASE_URL", "postgresql://test:test@localhost:5432/test");
Deno.env.set("REDIS_URL", "redis://localhost:6379");
Deno.env.set("STRIPE_SECRET_KEY", "sk_test_mock_key");
Deno.env.set("STRIPE_WEBHOOK_SECRET", "whsec_mock_secret");
Deno.env.set("JWT_SECRET", "test_jwt_secret");

import { assertEquals, assertExists } from "@std/assert";

Deno.test("Performance - Module Import Speed", async () => {
  const startTime = performance.now();

  // Import core modules without starting the server
  await import("../src/config/config.ts");
  await import("../src/middleware/index.ts");
  await import("../src/routes/index.ts");
  await import("../src/services/index.ts");

  const endTime = performance.now();
  const importTime = endTime - startTime;

  console.log(`Module import time: ${importTime.toFixed(2)}ms`);

  // Should import in reasonable time (less than 1 second)
  assertEquals(importTime < 1000, true, `Import time ${importTime}ms should be less than 1000ms`);
});

Deno.test("Performance - Config Loading Speed", async () => {
  const iterations = 100;
  const times: number[] = [];
  
  for (let i = 0; i < iterations; i++) {
    const startTime = performance.now();
    
    // Re-import config (this will use cache after first import)
    const { config } = await import("../src/config/config.ts");
    assertExists(config);
    
    const endTime = performance.now();
    times.push(endTime - startTime);
  }
  
  const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
  const maxTime = Math.max(...times);
  
  console.log(`Config loading - Average: ${avgTime.toFixed(2)}ms, Max: ${maxTime.toFixed(2)}ms`);
  
  // Average should be very fast due to caching
  assertEquals(avgTime < 10, true, `Average config load time ${avgTime}ms should be less than 10ms`);
});

Deno.test("Performance - Service Module Import", async () => {
  const startTime = performance.now();

  // Import service modules without initialization
  const serviceModule = await import("../src/services/index.ts");
  const stripeModule = await import("../src/services/stripeService.ts");
  const subscriptionModule = await import("../src/services/subscriptionService.ts");

  assertExists(serviceModule);
  assertExists(stripeModule);
  assertExists(subscriptionModule);

  const endTime = performance.now();
  const importTime = endTime - startTime;

  console.log(`Service module import time: ${importTime.toFixed(2)}ms`);

  // Should import quickly
  assertEquals(importTime < 100, true, `Service import time ${importTime}ms should be less than 100ms`);
});

Deno.test("Performance - Memory Usage Check", () => {
  const memUsage = Deno.memoryUsage();
  
  console.log(`Memory usage:`, {
    rss: `${(memUsage.rss / 1024 / 1024).toFixed(2)}MB`,
    heapTotal: `${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`,
    heapUsed: `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
    external: `${(memUsage.external / 1024 / 1024).toFixed(2)}MB`
  });
  
  // Basic memory usage checks (should be reasonable for a service)
  assertEquals(memUsage.rss > 0, true, "RSS memory should be positive");
  assertEquals(memUsage.heapUsed > 0, true, "Heap used should be positive");
  
  // Memory usage should be reasonable (less than 400MB for service with dependencies)
  const rssMB = memUsage.rss / 1024 / 1024;
  assertEquals(rssMB < 400, true, `RSS memory ${rssMB.toFixed(2)}MB should be less than 400MB`);
});
