# Server Configuration
NODE_ENV=development
DASHBOARD_PORT=3000
SERVE_STATIC=false

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_analytics
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=5000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=dashboard:
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=100
REDIS_OFFLINE_QUEUE=true

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=dashboard-api
JWT_AUDIENCE=dashboard-users

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Configuration
SESSION_TIMEOUT=3600000
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
PASSWORD_MIN_LENGTH=8
REQUIRE_MFA=false

# Service URLs Configuration
ANALYTICS_SERVICE_URL=http://localhost:3002
ANALYTICS_SERVICE_TIMEOUT=30000
LINK_TRACKING_SERVICE_URL=http://localhost:8080
LINK_TRACKING_SERVICE_TIMEOUT=30000
INTEGRATION_SERVICE_URL=http://localhost:3003
INTEGRATION_SERVICE_TIMEOUT=30000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
