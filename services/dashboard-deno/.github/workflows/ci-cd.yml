name: Dashboard Service Deno - CI/CD

on:
  push:
    branches: [main, develop]
    paths:
      - 'services/dashboard-deno/**'
      - '.github/workflows/dashboard-deno-ci-cd.yml'
  pull_request:
    branches: [main]
    paths:
      - 'services/dashboard-deno/**'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/dashboard-deno
  DENO_VERSION: 2.4.0

jobs:
  test:
    name: Test and Quality Checks
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: services/dashboard-deno
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Deno
      uses: denoland/setup-deno@v1
      with:
        deno-version: ${{ env.DENO_VERSION }}

    - name: Cache Deno dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/deno
        key: ${{ runner.os }}-deno-${{ hashFiles('services/dashboard-deno/deno.json') }}
        restore-keys: |
          ${{ runner.os }}-deno-

    - name: Install dependencies
      run: deno cache --import-map=deno.json src/main.ts

    - name: Run linting
      run: deno lint

    - name: Run formatting check
      run: deno fmt --check

    - name: Run type checking
      run: deno check src/main.ts

    - name: Run tests
      run: |
        deno test --allow-net --allow-env --allow-read --coverage=coverage tests/
        deno coverage coverage --lcov --output=coverage.lcov

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: services/dashboard-deno/coverage.lcov
        flags: dashboard-deno
        name: dashboard-deno-coverage

    - name: Security audit
      run: |
        # Check for known vulnerabilities in dependencies
        deno info --json src/main.ts | jq '.modules[].specifier' | grep -E '^https?://' | sort -u > deps.txt
        echo "External dependencies:"
        cat deps.txt

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push'
    defaults:
      run:
        working-directory: services/dashboard-deno
    
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: services/dashboard-deno
        file: services/dashboard-deno/Dockerfile.deno
        target: production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: spdx-json
        output-file: sbom.spdx.json

    - name: Upload SBOM
      uses: actions/upload-artifact@v3
      with:
        name: sbom
        path: sbom.spdx.json

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push'
    
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build.outputs.image-tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, build, security-scan]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: Deploy to staging
      run: |
        export KUBECONFIG=kubeconfig
        cd services/dashboard-deno/k8s
        
        # Update image tag in deployment
        sed -i "s|dashboard-deno:latest|${{ needs.build.outputs.image-tag }}|g" deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f deployment.yaml -n ecommerce-analytics-staging
        kubectl apply -f hpa.yaml -n ecommerce-analytics-staging
        
        # Wait for rollout to complete
        kubectl rollout status deployment/dashboard-deno -n ecommerce-analytics-staging --timeout=300s

    - name: Run smoke tests
      run: |
        export KUBECONFIG=kubeconfig
        
        # Get service endpoint
        ENDPOINT=$(kubectl get service dashboard-deno-service -n ecommerce-analytics-staging -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        
        # Wait for service to be ready
        sleep 30
        
        # Run basic health checks
        curl -f http://$ENDPOINT:3000/health || exit 1
        curl -f http://$ENDPOINT:3000/ready || exit 1
        curl -f http://$ENDPOINT:3000/metrics || exit 1

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, build, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'

    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig

    - name: Deploy to production
      run: |
        export KUBECONFIG=kubeconfig
        cd services/dashboard-deno/k8s
        
        # Update image tag in deployment
        sed -i "s|dashboard-deno:latest|${{ needs.build.outputs.image-tag }}|g" deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f deployment.yaml -n ecommerce-analytics
        kubectl apply -f hpa.yaml -n ecommerce-analytics
        
        # Wait for rollout to complete
        kubectl rollout status deployment/dashboard-deno -n ecommerce-analytics --timeout=600s

    - name: Run production smoke tests
      run: |
        export KUBECONFIG=kubeconfig
        
        # Get service endpoint
        ENDPOINT=$(kubectl get service dashboard-deno-service -n ecommerce-analytics -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        
        # Wait for service to be ready
        sleep 60
        
        # Run comprehensive health checks
        curl -f http://$ENDPOINT:3000/health || exit 1
        curl -f http://$ENDPOINT:3000/ready || exit 1
        curl -f http://$ENDPOINT:3000/metrics || exit 1
        
        # Test API endpoints (with auth)
        # Note: Add actual API tests with proper authentication

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      if: success()
      with:
        status: success
        channel: '#deployments'
        text: |
          ✅ Dashboard Service Deno successfully deployed to production
          Image: ${{ needs.build.outputs.image-tag }}
          Commit: ${{ github.sha }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify deployment failure
      uses: 8398a7/action-slack@v3
      if: failure()
      with:
        status: failure
        channel: '#deployments'
        text: |
          ❌ Dashboard Service Deno deployment to production failed
          Image: ${{ needs.build.outputs.image-tag }}
          Commit: ${{ github.sha }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Deno
      uses: denoland/setup-deno@v1
      with:
        deno-version: ${{ env.DENO_VERSION }}

    - name: Run performance tests
      working-directory: services/dashboard-deno
      run: |
        # Run performance test suite
        deno test tests/performance_test.ts --allow-net --allow-env --allow-read
        
        # Generate performance report
        echo "## Performance Test Results" >> $GITHUB_STEP_SUMMARY
        echo "Performance tests completed successfully" >> $GITHUB_STEP_SUMMARY

    - name: Load testing with k6
      uses: grafana/k6-action@v0.3.1
      with:
        filename: services/dashboard-deno/tests/load-test.js
      env:
        K6_CLOUD_TOKEN: ${{ secrets.K6_CLOUD_TOKEN }}
