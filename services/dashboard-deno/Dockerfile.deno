# Multi-stage Dockerfile for Deno Dashboard Service
FROM denoland/deno:2.4.0 AS base

# Set working directory
WORKDIR /app

# Create non-root user for security
RUN groupadd -r deno && useradd -r -g deno -s /bin/false deno

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Development stage
FROM base AS development

# Copy configuration files
COPY deno.json ./

# Copy source code
COPY . .

# Cache dependencies
RUN deno cache --import-map=deno.json src/main.ts

# Create necessary directories and set permissions
RUN mkdir -p logs uploads && \
    chown -R deno:deno /app

# Switch to non-root user
USER deno

# Expose ports
EXPOSE 3000
EXPOSE 9229

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Development command with debugging
CMD ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "--allow-write", "--inspect=0.0.0.0:9229", "src/main.ts"]

# Production stage
FROM base AS production

# Copy configuration files
COPY deno.json ./

# Copy source code
COPY src/ ./src/

# Cache dependencies for production
RUN deno cache --import-map=deno.json src/main.ts

# Create necessary directories and set permissions
RUN mkdir -p logs && \
    chown -R deno:deno /app

# Switch to non-root user
USER deno

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Production command
CMD ["deno", "run", "--allow-net", "--allow-env", "--allow-read", "--allow-write", "src/main.ts"]

# Test stage
FROM production AS test

# Switch back to root for test dependencies
USER root

# Install test dependencies if needed
RUN apt-get update && apt-get install -y git && rm -rf /var/lib/apt/lists/*

# Copy test files
COPY tests/ ./tests/

# Switch back to deno user
USER deno

# Run tests
CMD ["deno", "test", "--allow-net", "--allow-env", "--allow-read", "--allow-write"]
