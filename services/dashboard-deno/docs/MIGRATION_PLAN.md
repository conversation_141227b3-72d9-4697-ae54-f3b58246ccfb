# Dashboard Service Deno 2 Migration Plan

## Overview
This document outlines the migration plan for converting the Dashboard Service from Node.js/Express to Deno 2/Oak framework, following the successful patterns established in the Admin Service migration.

## Current Express.js Architecture Analysis

### Main Application Structure
- **Entry Point**: `src/index.js` - Express app with middleware stack
- **Port**: 3000 (configurable via DASHBOARD_PORT)
- **Database**: PostgreSQL with connection pooling
- **Cache**: Redis for session management and caching
- **Authentication**: JWT-based with middleware protection

### Route Structure Analysis

#### 1. Main Routes (`/api/`)
- **Base Route**: `/` - Service info and endpoint listing
- **Authentication**: All routes protected except `/api/auth`

#### 2. Authentication Routes (`/api/auth`)
- `POST /register` - User registration with validation
- `POST /login` - User login with JWT token generation
- OAuth2 endpoints in `/auth/oauth2.js`

#### 3. Dashboard Routes (`/api/dashboard`)
- `GET /overview` - Dashboard overview with metrics
- `GET /metrics` - Key performance metrics
- `GET /alerts` - System alerts and notifications
- `GET /activity` - Recent activity feed
- `GET /top-links` - Top performing links

#### 4. Links Routes (`/api/links`)
- `GET /` - List user's links with pagination
- `POST /` - Create new link
- `GET /:id` - Get specific link details
- `PUT /:id` - Update link
- `DELETE /:id` - Delete link
- `GET /:id/analytics` - Link analytics
- `POST /:id/qr-code` - Generate QR code

#### 5. Analytics Routes (`/api/analytics`)
- `GET /summary` - Analytics summary
- `GET /links/:linkId` - Link-specific analytics
- `GET /attribution/:trackingId` - Attribution data
- `GET /reports/performance` - Performance reports
- `GET /reports/conversion-funnel` - Funnel analysis

#### 6. Integrations Routes (`/api/integrations`)
- `GET /` - List integrations
- `POST /` - Create integration
- `GET /:id` - Get integration details
- `PUT /:id` - Update integration
- `DELETE /:id` - Delete integration
- `POST /:id/test` - Test integration

#### 7. Users Routes (`/api/users`)
- `GET /profile` - User profile
- `PUT /profile` - Update profile
- `POST /change-password` - Change password
- `GET /stats` - User statistics

### Middleware Stack Analysis

#### 1. Security Middleware
- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Rate Limiting**: Request rate limiting
- **Authentication**: JWT verification

#### 2. Request Processing
- **Body Parsing**: JSON and URL-encoded
- **Compression**: Response compression
- **Logging**: Request/response logging
- **Metrics**: Prometheus metrics collection

#### 3. Error Handling
- **Global Error Handler**: Centralized error processing
- **Validation**: Joi schema validation
- **Database Error Mapping**: PostgreSQL error codes

### Service Dependencies Analysis

#### 1. External Services (via axios)
- **Analytics Service**: `http://analytics:3002`
- **Link Tracking Service**: `http://link-tracking:8080`
- **Integration Service**: `http://integration:3001`

#### 2. Database Operations
- **Multi-tenant Queries**: tenant_id filtering
- **Connection Pooling**: PostgreSQL pool management
- **Transaction Support**: Database transactions

#### 3. Redis Operations
- **Session Management**: User sessions
- **Rate Limiting**: Request counting
- **Caching**: Dashboard data caching

## Migration Strategy

### Phase 1: Foundation Setup ✅
- [x] Create dashboard-deno directory structure
- [x] Configure deno.json with Oak framework
- [x] Set up TypeScript configuration
- [x] Create database and Redis utilities

### Phase 2: Core Application Migration
- [ ] Create main Oak application (`src/main.ts`)
- [ ] Implement middleware stack (auth, CORS, rate limiting, logging)
- [ ] Replace axios with native fetch API
- [ ] Migrate error handling patterns

### Phase 3: Route Migration
- [ ] Migrate authentication routes
- [ ] Migrate dashboard routes and services
- [ ] Migrate links routes (proxy to link-tracking service)
- [ ] Migrate analytics routes (proxy to analytics service)
- [ ] Migrate integrations routes (proxy to integration service)
- [ ] Migrate users routes

### Phase 4: Service Layer Migration
- [ ] Migrate DashboardService class
- [ ] Implement HTTP client utilities (fetch-based)
- [ ] Migrate validation schemas (Joi to Zod)
- [ ] Implement metrics collection

### Phase 5: Testing and Validation
- [ ] Create comprehensive test suite
- [ ] Performance benchmarking
- [ ] API compatibility testing
- [ ] Multi-tenant functionality validation

## Key Migration Considerations

### 1. Axios to Fetch Migration
```typescript
// Before (axios)
const response = await analyticsService.get('/api/analytics/summary', {
  params: { tenant_id: tenantId, date_from: dateFrom, date_to: dateTo }
});

// After (fetch)
const url = new URL('/api/analytics/summary', config.services.analytics.baseUrl);
url.searchParams.set('tenant_id', tenantId);
url.searchParams.set('date_from', dateFrom);
url.searchParams.set('date_to', dateTo);

const response = await fetch(url.toString(), {
  method: 'GET',
  headers: { 'Content-Type': 'application/json' },
  signal: AbortSignal.timeout(config.services.analytics.timeout)
});
```

### 2. Express to Oak Route Migration
```typescript
// Before (Express)
router.get('/overview', async (req, res, next) => {
  try {
    const overview = await dashboardService.getOverview(req.user, req.query);
    res.json({ success: true, data: overview });
  } catch (error) {
    next(error);
  }
});

// After (Oak)
router.get('/api/dashboard/overview', async (ctx) => {
  const url = new URL(ctx.request.url);
  const overview = await dashboardService.getOverview(ctx.state.user, {
    dateFrom: url.searchParams.get('date_from'),
    dateTo: url.searchParams.get('date_to'),
    period: url.searchParams.get('period') || '30d'
  });
  
  ctx.response.body = { success: true, data: overview };
});
```

### 3. Middleware Migration
```typescript
// Before (Express)
app.use('/api', authMiddleware);

// After (Oak)
app.use(authMiddleware);
```

### 4. Multi-tenant Database Queries
- Maintain existing tenant_id filtering patterns
- Use established database utility functions
- Preserve transaction support

## Success Criteria
- [ ] 100% API compatibility with existing endpoints
- [ ] >90% startup time improvement (target: 97%+ like admin service)
- [ ] Zero breaking changes to frontend applications
- [ ] Complete test coverage with 100% pass rate
- [ ] Production-ready deployment configuration

## Dependencies Compatibility
- **Oak Framework**: ✅ Direct replacement for Express
- **PostgreSQL**: ✅ Using postgres@v0.19.3
- **Redis**: ✅ Using redis@v0.32.3
- **JWT**: ✅ Using djwt@v3.0.2
- **Validation**: ✅ Joi → Zod migration
- **HTTP Client**: ✅ axios → native fetch
- **Logging**: ✅ winston → @std/log

## Timeline
- **Week 1**: Phase 1 & 2 (Foundation and Core App)
- **Week 2-3**: Phase 3 & 4 (Routes and Services)
- **Week 4**: Phase 5 (Testing and Validation)
- **Week 5**: Deployment and Production Setup
