apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: dashboard-deno-hpa
  namespace: ecommerce-analytics
  labels:
    app: dashboard-deno
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: dashboard-deno
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min

---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: dashboard-deno-metrics
  namespace: ecommerce-analytics
  labels:
    app: dashboard-deno
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: dashboard-deno
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
    honorLabels: true
  namespaceSelector:
    matchNames:
    - ecommerce-analytics

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: dashboard-deno-alerts
  namespace: ecommerce-analytics
  labels:
    app: dashboard-deno
    prometheus: kube-prometheus
spec:
  groups:
  - name: dashboard-deno.rules
    interval: 30s
    rules:
    - alert: DashboardDenoHighErrorRate
      expr: |
        (
          rate(http_requests_total{app="dashboard-deno",status_code=~"5.."}[5m]) /
          rate(http_requests_total{app="dashboard-deno"}[5m])
        ) > 0.05
      for: 5m
      labels:
        severity: warning
        service: dashboard-deno
      annotations:
        summary: "Dashboard Deno service has high error rate"
        description: "Dashboard Deno service error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

    - alert: DashboardDenoHighLatency
      expr: |
        histogram_quantile(0.95, 
          rate(http_request_duration_seconds_bucket{app="dashboard-deno"}[5m])
        ) > 0.5
      for: 5m
      labels:
        severity: warning
        service: dashboard-deno
      annotations:
        summary: "Dashboard Deno service has high latency"
        description: "Dashboard Deno service 95th percentile latency is {{ $value }}s for the last 5 minutes"

    - alert: DashboardDenoDown
      expr: up{app="dashboard-deno"} == 0
      for: 1m
      labels:
        severity: critical
        service: dashboard-deno
      annotations:
        summary: "Dashboard Deno service is down"
        description: "Dashboard Deno service has been down for more than 1 minute"

    - alert: DashboardDenoHighMemoryUsage
      expr: |
        (
          container_memory_working_set_bytes{pod=~"dashboard-deno-.*"} /
          container_spec_memory_limit_bytes{pod=~"dashboard-deno-.*"}
        ) > 0.9
      for: 5m
      labels:
        severity: warning
        service: dashboard-deno
      annotations:
        summary: "Dashboard Deno service has high memory usage"
        description: "Dashboard Deno service memory usage is {{ $value | humanizePercentage }} for the last 5 minutes"

    - alert: DashboardDenoHighCPUUsage
      expr: |
        rate(container_cpu_usage_seconds_total{pod=~"dashboard-deno-.*"}[5m]) > 0.8
      for: 5m
      labels:
        severity: warning
        service: dashboard-deno
      annotations:
        summary: "Dashboard Deno service has high CPU usage"
        description: "Dashboard Deno service CPU usage is {{ $value | humanizePercentage }} for the last 5 minutes"

    - alert: DashboardDenoRestarting
      expr: |
        rate(kube_pod_container_status_restarts_total{pod=~"dashboard-deno-.*"}[15m]) > 0
      for: 5m
      labels:
        severity: warning
        service: dashboard-deno
      annotations:
        summary: "Dashboard Deno service is restarting frequently"
        description: "Dashboard Deno service has restarted {{ $value }} times in the last 15 minutes"

    - alert: DashboardDenoLowThroughput
      expr: |
        rate(http_requests_total{app="dashboard-deno"}[5m]) < 10
      for: 10m
      labels:
        severity: info
        service: dashboard-deno
      annotations:
        summary: "Dashboard Deno service has low throughput"
        description: "Dashboard Deno service throughput is {{ $value }} requests/second for the last 5 minutes"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dashboard-deno-grafana-dashboard
  namespace: ecommerce-analytics
  labels:
    app: dashboard-deno
    grafana_dashboard: "1"
data:
  dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Dashboard Service - Deno 2",
        "tags": ["dashboard", "deno", "ecommerce"],
        "style": "dark",
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(http_requests_total{app=\"dashboard-deno\"}[5m])",
                "legendFormat": "{{method}} {{route}}"
              }
            ],
            "yAxes": [
              {
                "label": "Requests/sec"
              }
            ]
          },
          {
            "id": 2,
            "title": "Response Time",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{app=\"dashboard-deno\"}[5m]))",
                "legendFormat": "95th percentile"
              },
              {
                "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{app=\"dashboard-deno\"}[5m]))",
                "legendFormat": "50th percentile"
              }
            ],
            "yAxes": [
              {
                "label": "Seconds"
              }
            ]
          },
          {
            "id": 3,
            "title": "Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(http_requests_total{app=\"dashboard-deno\",status_code=~\"5..\"}[5m])",
                "legendFormat": "5xx errors"
              },
              {
                "expr": "rate(http_requests_total{app=\"dashboard-deno\",status_code=~\"4..\"}[5m])",
                "legendFormat": "4xx errors"
              }
            ],
            "yAxes": [
              {
                "label": "Errors/sec"
              }
            ]
          },
          {
            "id": 4,
            "title": "Memory Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "container_memory_working_set_bytes{pod=~\"dashboard-deno-.*\"} / 1024 / 1024",
                "legendFormat": "{{pod}}"
              }
            ],
            "yAxes": [
              {
                "label": "MB"
              }
            ]
          },
          {
            "id": 5,
            "title": "CPU Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(container_cpu_usage_seconds_total{pod=~\"dashboard-deno-.*\"}[5m]) * 100",
                "legendFormat": "{{pod}}"
              }
            ],
            "yAxes": [
              {
                "label": "Percent"
              }
            ]
          },
          {
            "id": 6,
            "title": "Active Users",
            "type": "singlestat",
            "targets": [
              {
                "expr": "active_users_current{app=\"dashboard-deno\"}",
                "legendFormat": "Active Users"
              }
            ]
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }
