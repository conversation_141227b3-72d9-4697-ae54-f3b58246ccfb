#!/bin/bash

# Dashboard Service Deno - Production Deployment Script
# Usage: ./scripts/deploy.sh [environment] [image_tag]

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
K8S_DIR="$PROJECT_ROOT/k8s"

# Default values
ENVIRONMENT="${1:-staging}"
IMAGE_TAG="${2:-latest}"
NAMESPACE="ecommerce-analytics"
SERVICE_NAME="dashboard-deno"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation functions
validate_environment() {
    case "$ENVIRONMENT" in
        staging|production)
            log_info "Deploying to $ENVIRONMENT environment"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
            exit 1
            ;;
    esac
}

validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if kubectl is configured
    if ! kubectl cluster-info &> /dev/null; then
        log_error "kubectl is not configured or cluster is not accessible"
        exit 1
    fi
    
    # Check if namespace exists
    if [ "$ENVIRONMENT" = "staging" ]; then
        NAMESPACE="ecommerce-analytics-staging"
    fi
    
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "Namespace $NAMESPACE does not exist, creating it..."
        kubectl create namespace "$NAMESPACE"
    fi
    
    log_success "Prerequisites validated"
}

validate_image() {
    log_info "Validating Docker image: $IMAGE_TAG"
    
    # Check if image exists (this would be more sophisticated in real deployment)
    # For now, we'll just validate the tag format
    if [[ ! "$IMAGE_TAG" =~ ^[a-zA-Z0-9._-]+:[a-zA-Z0-9._-]+$ ]] && [ "$IMAGE_TAG" != "latest" ]; then
        log_error "Invalid image tag format: $IMAGE_TAG"
        exit 1
    fi
    
    log_success "Image tag validated"
}

# Deployment functions
prepare_manifests() {
    log_info "Preparing Kubernetes manifests..."
    
    # Create temporary directory for processed manifests
    TEMP_DIR=$(mktemp -d)
    cp -r "$K8S_DIR"/* "$TEMP_DIR/"
    
    # Update image tag in deployment manifest
    sed -i.bak "s|dashboard-deno:latest|ghcr.io/cavelltopdev/realclickninja/dashboard-deno:$IMAGE_TAG|g" "$TEMP_DIR/deployment.yaml"
    
    # Update namespace if staging
    if [ "$ENVIRONMENT" = "staging" ]; then
        sed -i.bak "s|namespace: ecommerce-analytics|namespace: ecommerce-analytics-staging|g" "$TEMP_DIR"/*.yaml
    fi
    
    # Set environment-specific resource limits
    if [ "$ENVIRONMENT" = "production" ]; then
        # Production: Higher resource limits
        sed -i.bak 's|memory: "128Mi"|memory: "256Mi"|g' "$TEMP_DIR/deployment.yaml"
        sed -i.bak 's|cpu: "200m"|cpu: "500m"|g' "$TEMP_DIR/deployment.yaml"
        sed -i.bak 's|replicas: 3|replicas: 5|g' "$TEMP_DIR/deployment.yaml"
    else
        # Staging: Lower resource limits
        sed -i.bak 's|memory: "128Mi"|memory: "64Mi"|g' "$TEMP_DIR/deployment.yaml"
        sed -i.bak 's|cpu: "200m"|cpu: "100m"|g' "$TEMP_DIR/deployment.yaml"
        sed -i.bak 's|replicas: 3|replicas: 2|g' "$TEMP_DIR/deployment.yaml"
    fi
    
    # Remove backup files
    find "$TEMP_DIR" -name "*.bak" -delete
    
    log_success "Manifests prepared in $TEMP_DIR"
    echo "$TEMP_DIR"
}

deploy_service() {
    local temp_dir="$1"
    
    log_info "Deploying $SERVICE_NAME to $ENVIRONMENT..."
    
    # Apply ConfigMaps and Secrets first (if any)
    if ls "$temp_dir"/*configmap*.yaml 1> /dev/null 2>&1; then
        log_info "Applying ConfigMaps..."
        kubectl apply -f "$temp_dir"/*configmap*.yaml -n "$NAMESPACE"
    fi
    
    if ls "$temp_dir"/*secret*.yaml 1> /dev/null 2>&1; then
        log_info "Applying Secrets..."
        kubectl apply -f "$temp_dir"/*secret*.yaml -n "$NAMESPACE"
    fi
    
    # Apply ServiceAccount and RBAC
    if ls "$temp_dir"/*rbac*.yaml 1> /dev/null 2>&1; then
        log_info "Applying RBAC..."
        kubectl apply -f "$temp_dir"/*rbac*.yaml -n "$NAMESPACE"
    fi
    
    # Apply main deployment
    log_info "Applying deployment..."
    kubectl apply -f "$temp_dir/deployment.yaml" -n "$NAMESPACE"
    
    # Apply HPA and monitoring
    log_info "Applying HPA and monitoring..."
    kubectl apply -f "$temp_dir/hpa.yaml" -n "$NAMESPACE"
    
    log_success "Manifests applied successfully"
}

wait_for_rollout() {
    log_info "Waiting for deployment rollout to complete..."
    
    local timeout=600 # 10 minutes
    if [ "$ENVIRONMENT" = "staging" ]; then
        timeout=300 # 5 minutes for staging
    fi
    
    if kubectl rollout status deployment/"$SERVICE_NAME" -n "$NAMESPACE" --timeout="${timeout}s"; then
        log_success "Deployment rollout completed successfully"
    else
        log_error "Deployment rollout failed or timed out"
        
        # Show recent events and pod status for debugging
        log_info "Recent events:"
        kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' | tail -10
        
        log_info "Pod status:"
        kubectl get pods -l app="$SERVICE_NAME" -n "$NAMESPACE"
        
        exit 1
    fi
}

run_health_checks() {
    log_info "Running health checks..."
    
    # Get service endpoint
    local service_name="${SERVICE_NAME}-service"
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "Health check attempt $attempt/$max_attempts"
        
        # Port forward to test the service
        kubectl port-forward service/"$service_name" 8080:3000 -n "$NAMESPACE" &
        local port_forward_pid=$!
        
        sleep 5
        
        # Test health endpoints
        if curl -f http://localhost:8080/health > /dev/null 2>&1 && \
           curl -f http://localhost:8080/ready > /dev/null 2>&1 && \
           curl -f http://localhost:8080/metrics > /dev/null 2>&1; then
            
            kill $port_forward_pid 2>/dev/null || true
            log_success "Health checks passed"
            return 0
        fi
        
        kill $port_forward_pid 2>/dev/null || true
        sleep 10
        ((attempt++))
    done
    
    log_error "Health checks failed after $max_attempts attempts"
    return 1
}

cleanup() {
    local temp_dir="$1"
    
    if [ -n "$temp_dir" ] && [ -d "$temp_dir" ]; then
        log_info "Cleaning up temporary files..."
        rm -rf "$temp_dir"
    fi
}

rollback() {
    log_warning "Rolling back deployment..."
    
    if kubectl rollout undo deployment/"$SERVICE_NAME" -n "$NAMESPACE"; then
        log_success "Rollback initiated"
        wait_for_rollout
    else
        log_error "Rollback failed"
        exit 1
    fi
}

# Main deployment flow
main() {
    log_info "Starting deployment of $SERVICE_NAME"
    log_info "Environment: $ENVIRONMENT"
    log_info "Image tag: $IMAGE_TAG"
    log_info "Namespace: $NAMESPACE"
    
    # Validation
    validate_environment
    validate_prerequisites
    validate_image
    
    # Prepare and deploy
    local temp_dir
    temp_dir=$(prepare_manifests)
    
    # Set up cleanup trap
    trap "cleanup '$temp_dir'" EXIT
    
    # Deploy
    deploy_service "$temp_dir"
    wait_for_rollout
    
    # Health checks
    if ! run_health_checks; then
        log_error "Health checks failed, rolling back..."
        rollback
        exit 1
    fi
    
    log_success "Deployment completed successfully!"
    log_info "Service is running in $ENVIRONMENT environment"
    
    # Show deployment status
    kubectl get deployment "$SERVICE_NAME" -n "$NAMESPACE"
    kubectl get pods -l app="$SERVICE_NAME" -n "$NAMESPACE"
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
