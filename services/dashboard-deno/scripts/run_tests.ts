#!/usr/bin/env -S deno run --allow-read --allow-run --allow-env

// Simple argument parsing without external dependencies
function parseArgs(args: string[]) {
  const result: Record<string, any> = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === "--help" || arg === "-h") {
      result.help = true;
    } else if (arg === "--list" || arg === "-l") {
      result.list = true;
    } else if (arg === "--category" || arg === "-c") {
      result.category = args[++i];
    } else if (arg === "--suite" || arg === "-s") {
      result.suite = args[++i];
    }
  }

  return result;
}

interface TestResult {
  name: string;
  passed: number;
  failed: number;
  duration: number;
  success: boolean;
}

interface TestSuite {
  name: string;
  file: string;
  description: string;
  category: string;
}

const testSuites: TestSuite[] = [
  {
    name: "Health Tests",
    file: "tests/health_test.ts",
    description: "Basic health check and configuration validation",
    category: "unit",
  },
  {
    name: "Authentication Tests",
    file: "tests/auth_test.ts",
    description: "User registration, login, JWT, and password security",
    category: "unit",
  },
  {
    name: "Middleware Tests",
    file: "tests/middleware_test.ts",
    description: "CORS, rate limiting, security, logging, and error handling",
    category: "unit",
  },
  {
    name: "API Tests",
    file: "tests/api_test.ts",
    description: "Dashboard, links, analytics, integrations, and users APIs",
    category: "integration",
  },
  {
    name: "Database Tests",
    file: "tests/database_test.ts",
    description: "Connection management, multi-tenant queries, and data validation",
    category: "integration",
  },
  {
    name: "Performance Tests",
    file: "tests/performance_test.ts",
    description: "Startup time, request performance, and Node.js comparison",
    category: "performance",
  },
];

async function runTestSuite(suite: TestSuite): Promise<TestResult> {
  console.log(`\n🧪 Running ${suite.name}...`);
  console.log(`   ${suite.description}`);
  
  const startTime = performance.now();
  
  try {
    const command = new Deno.Command("deno", {
      args: [
        "test",
        "--allow-net",
        "--allow-env",
        "--allow-read",
        "--allow-write",
        "--quiet",
        suite.file,
      ],
      stdout: "piped",
      stderr: "piped",
    });

    const { code, stdout, stderr } = await command.output();
    const output = new TextDecoder().decode(stdout);
    const errorOutput = new TextDecoder().decode(stderr);
    
    const endTime = performance.now();
    const duration = endTime - startTime;

    // Parse test results
    const passedMatch = output.match(/(\d+) passed/);
    const failedMatch = output.match(/(\d+) failed/);
    
    const passed = passedMatch ? parseInt(passedMatch[1]) : 0;
    const failed = failedMatch ? parseInt(failedMatch[1]) : 0;
    const success = code === 0 && failed === 0;

    if (success) {
      console.log(`   ✅ ${passed} tests passed (${duration.toFixed(0)}ms)`);
    } else {
      console.log(`   ❌ ${failed} tests failed, ${passed} passed (${duration.toFixed(0)}ms)`);
      if (errorOutput) {
        console.log(`   Error: ${errorOutput}`);
      }
    }

    return {
      name: suite.name,
      passed,
      failed,
      duration,
      success,
    };
  } catch (error) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`   ❌ Test suite failed to run: ${error instanceof Error ? error.message : String(error)}`);
    
    return {
      name: suite.name,
      passed: 0,
      failed: 1,
      duration,
      success: false,
    };
  }
}

async function runAllTests(category?: string): Promise<void> {
  console.log("🚀 Dashboard Service Deno 2 - Test Suite");
  console.log("==========================================");
  
  const filteredSuites = category 
    ? testSuites.filter(suite => suite.category === category)
    : testSuites;

  if (filteredSuites.length === 0) {
    console.log(`❌ No test suites found for category: ${category}`);
    return;
  }

  console.log(`Running ${filteredSuites.length} test suite(s)${category ? ` (${category})` : ''}...`);
  
  const results: TestResult[] = [];
  const startTime = performance.now();

  for (const suite of filteredSuites) {
    const result = await runTestSuite(suite);
    results.push(result);
  }

  const endTime = performance.now();
  const totalDuration = endTime - startTime;

  // Calculate summary
  const totalPassed = results.reduce((sum, r) => sum + r.passed, 0);
  const totalFailed = results.reduce((sum, r) => sum + r.failed, 0);
  const totalTests = totalPassed + totalFailed;
  const successfulSuites = results.filter(r => r.success).length;
  const failedSuites = results.filter(r => !r.success).length;

  // Print summary
  console.log("\n📊 Test Summary");
  console.log("================");
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${totalPassed}`);
  console.log(`Failed: ${totalFailed}`);
  console.log(`Test Suites: ${successfulSuites}/${results.length} passed`);
  console.log(`Duration: ${totalDuration.toFixed(0)}ms`);

  // Print detailed results
  console.log("\n📋 Detailed Results");
  console.log("===================");
  
  for (const result of results) {
    const status = result.success ? "✅" : "❌";
    const tests = result.passed + result.failed;
    console.log(`${status} ${result.name}: ${result.passed}/${tests} tests passed (${result.duration.toFixed(0)}ms)`);
  }

  // Performance insights
  if (category === "performance" || !category) {
    console.log("\n⚡ Performance Insights");
    console.log("======================");
    console.log("Expected improvements over Node.js:");
    console.log("• Startup Time: 97%+ improvement");
    console.log("• Memory Usage: 40%+ reduction");
    console.log("• Request Throughput: 25%+ improvement");
    console.log("• Response Time: 15%+ improvement");
  }

  // Exit with appropriate code
  if (failedSuites > 0) {
    console.log(`\n❌ ${failedSuites} test suite(s) failed`);
    Deno.exit(1);
  } else {
    console.log(`\n🎉 All test suites passed!`);
    Deno.exit(0);
  }
}

async function main() {
  const args = parseArgs(Deno.args);

  if (args.help) {
    console.log(`
Dashboard Service Test Runner

Usage:
  deno run --allow-read --allow-run --allow-env scripts/run_tests.ts [options]

Options:
  -c, --category <category>  Run tests for specific category (unit, integration, performance)
  -s, --suite <suite>        Run specific test suite by name
  -l, --list                 List all available test suites
  -h, --help                 Show this help message

Examples:
  # Run all tests
  deno run --allow-read --allow-run --allow-env scripts/run_tests.ts

  # Run only unit tests
  deno run --allow-read --allow-run --allow-env scripts/run_tests.ts --category unit

  # Run only performance tests
  deno run --allow-read --allow-run --allow-env scripts/run_tests.ts --category performance

  # List all test suites
  deno run --allow-read --allow-run --allow-env scripts/run_tests.ts --list
`);
    return;
  }

  if (args.list) {
    console.log("Available Test Suites:");
    console.log("======================");
    
    const categories = [...new Set(testSuites.map(s => s.category))];
    
    for (const category of categories) {
      console.log(`\n${category.toUpperCase()}:`);
      const suitesInCategory = testSuites.filter(s => s.category === category);
      
      for (const suite of suitesInCategory) {
        console.log(`  • ${suite.name}: ${suite.description}`);
      }
    }
    return;
  }

  if (args.suite) {
    const suite = testSuites.find(s => s.name.toLowerCase().includes(args.suite.toLowerCase()));
    if (!suite) {
      console.log(`❌ Test suite not found: ${args.suite}`);
      console.log("Use --list to see available test suites");
      Deno.exit(1);
    }
    
    const result = await runTestSuite(suite);
    Deno.exit(result.success ? 0 : 1);
  }

  await runAllTests(args.category);
}

if (import.meta.main) {
  await main();
}
