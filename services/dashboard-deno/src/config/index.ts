import { load } from "@std/dotenv";

// Load environment variables
await load({ export: true });

export interface DatabaseConfig {
  host: string;
  port: number;
  name: string;
  user: string;
  password: string;
  ssl: boolean;
  maxConnections: number;
  idleTimeout: number;
  connectionTimeout: number;
}

export interface RedisConfig {
  host: string;
  port: number;
  password: string;
  db: number;
  keyPrefix: string;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  enableOfflineQueue: boolean;
}

export interface JWTConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
  issuer: string;
  audience: string;
}

export interface CorsConfig {
  origins: string[];
}

export interface RateLimitConfig {
  windowMs: number;
  max: number;
}

export interface SecurityConfig {
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  passwordMinLength: number;
  requireMFA: boolean;
}

export interface ServiceConfig {
  analytics: {
    baseUrl: string;
    timeout: number;
  };
  linkTracking: {
    baseUrl: string;
    timeout: number;
  };
  integration: {
    baseUrl: string;
    timeout: number;
  };
}

export interface Config {
  // Server configuration
  env: string;
  port: number;
  serveStatic: boolean;

  // Database configuration
  database: DatabaseConfig;

  // Redis configuration
  redis: RedisConfig;

  // JWT configuration
  jwt: JWTConfig;

  // CORS configuration
  cors: CorsConfig;

  // Rate limiting configuration
  rateLimit: RateLimitConfig;

  // Security configuration
  security: SecurityConfig;

  // Service URLs configuration
  services: ServiceConfig;

  // Logging configuration
  logging: {
    level: string;
    format: string;
  };
}

const config: Config = {
  // Server configuration
  env: Deno.env.get("NODE_ENV") || "development",
  port: parseInt(Deno.env.get("DASHBOARD_PORT") || "3000"),
  serveStatic: Deno.env.get("SERVE_STATIC") === "true",

  // Database configuration
  database: {
    host: Deno.env.get("DB_HOST") || "localhost",
    port: parseInt(Deno.env.get("DB_PORT") || "5432"),
    name: Deno.env.get("DB_NAME") || "ecommerce_analytics",
    user: Deno.env.get("DB_USER") || "postgres",
    password: Deno.env.get("DB_PASSWORD") || "password",
    ssl: Deno.env.get("DB_SSL") === "true",
    maxConnections: parseInt(Deno.env.get("DB_MAX_CONNECTIONS") || "20"),
    idleTimeout: parseInt(Deno.env.get("DB_IDLE_TIMEOUT") || "30000"),
    connectionTimeout: parseInt(Deno.env.get("DB_CONNECTION_TIMEOUT") || "5000"),
  },

  // Redis configuration
  redis: {
    host: Deno.env.get("REDIS_HOST") || "localhost",
    port: parseInt(Deno.env.get("REDIS_PORT") || "6379"),
    password: Deno.env.get("REDIS_PASSWORD") || "",
    db: parseInt(Deno.env.get("REDIS_DB") || "0"),
    keyPrefix: Deno.env.get("REDIS_KEY_PREFIX") || "dashboard:",
    maxRetriesPerRequest: parseInt(Deno.env.get("REDIS_MAX_RETRIES") || "3"),
    retryDelayOnFailover: parseInt(Deno.env.get("REDIS_RETRY_DELAY") || "100"),
    enableOfflineQueue: Deno.env.get("REDIS_OFFLINE_QUEUE") !== "false",
  },

  // JWT configuration
  jwt: {
    secret: Deno.env.get("JWT_SECRET") || "your-secret-key",
    expiresIn: Deno.env.get("JWT_EXPIRES_IN") || "24h",
    refreshExpiresIn: Deno.env.get("JWT_REFRESH_EXPIRES_IN") || "7d",
    issuer: Deno.env.get("JWT_ISSUER") || "dashboard-api",
    audience: Deno.env.get("JWT_AUDIENCE") || "dashboard-users",
  },

  // CORS configuration
  cors: {
    origins: Deno.env.get("CORS_ORIGINS")?.split(",") || ["http://localhost:3000"],
  },

  // Rate limiting configuration
  rateLimit: {
    windowMs: parseInt(Deno.env.get("RATE_LIMIT_WINDOW_MS") || "900000"), // 15 minutes
    max: parseInt(Deno.env.get("RATE_LIMIT_MAX_REQUESTS") || "100"),
  },

  // Security configuration
  security: {
    sessionTimeout: parseInt(Deno.env.get("SESSION_TIMEOUT") || "3600000"), // 1 hour
    maxLoginAttempts: parseInt(Deno.env.get("MAX_LOGIN_ATTEMPTS") || "5"),
    lockoutDuration: parseInt(Deno.env.get("LOCKOUT_DURATION") || "900000"), // 15 minutes
    passwordMinLength: parseInt(Deno.env.get("PASSWORD_MIN_LENGTH") || "8"),
    requireMFA: Deno.env.get("REQUIRE_MFA") === "true",
  },

  // Service URLs configuration
  services: {
    analytics: {
      baseUrl: Deno.env.get("ANALYTICS_SERVICE_URL") || "http://localhost:3001",
      timeout: parseInt(Deno.env.get("ANALYTICS_SERVICE_TIMEOUT") || "30000"),
    },
    linkTracking: {
      baseUrl: Deno.env.get("LINK_TRACKING_SERVICE_URL") || "http://localhost:3002",
      timeout: parseInt(Deno.env.get("LINK_TRACKING_SERVICE_TIMEOUT") || "30000"),
    },
    integration: {
      baseUrl: Deno.env.get("INTEGRATION_SERVICE_URL") || "http://localhost:3003",
      timeout: parseInt(Deno.env.get("INTEGRATION_SERVICE_TIMEOUT") || "30000"),
    },
  },

  // Logging configuration
  logging: {
    level: Deno.env.get("LOG_LEVEL") || "info",
    format: Deno.env.get("LOG_FORMAT") || "json",
  },
};

export default config;
