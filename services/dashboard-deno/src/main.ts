import { Application } from "@oak/oak";
import { getLogger } from "@std/log";

import config from "./config/index.ts";
import { initializeDatabase } from "./utils/database.ts";
import { initializeRedis } from "./utils/redis.ts";
import { errorHandler } from "./middleware/errorHandler.ts";
import { rateLimitMiddleware } from "./middleware/rateLimit.ts";
import { authMiddleware } from "./middleware/auth.ts";
import { loggingMiddleware } from "./middleware/logging.ts";
import { securityMiddleware } from "./middleware/security.ts";
import { corsMiddleware } from "./middleware/cors.ts";
import { metricsMiddleware } from "./middleware/metrics.ts";

// Import routes
import healthRoutes from "./routes/health.ts";
import authRoutes from "./routes/auth.ts";
import dashboardRoutes from "./routes/dashboard.ts";
import linksRoutes from "./routes/links.ts";
import analyticsRoutes from "./routes/analytics.ts";
import integrationsRoutes from "./routes/integrations.ts";
import usersRoutes from "./routes/users.ts";

const logger = getLogger();

// Create Oak application
const app = new Application();

// Global error handler (must be first)
app.use(errorHandler);

// Security middleware
app.use(securityMiddleware);

// CORS middleware
app.use(corsMiddleware);

// Logging middleware
app.use(loggingMiddleware);

// Rate limiting middleware
app.use(rateLimitMiddleware);

// Metrics middleware
app.use(metricsMiddleware);

// Request timing middleware
app.use(async (ctx, next) => {
  const start = Date.now();
  await next();
  const duration = Date.now() - start;
  ctx.response.headers.set("X-Response-Time", `${duration}ms`);
});

// Health check routes (no auth required)
app.use(healthRoutes.routes());
app.use(healthRoutes.allowedMethods());

// Authentication routes (no auth required)
app.use(authRoutes.routes());
app.use(authRoutes.allowedMethods());

// Protected routes (require authentication)
app.use(authMiddleware);

// Dashboard routes
app.use(dashboardRoutes.routes());
app.use(dashboardRoutes.allowedMethods());

// Links routes
app.use(linksRoutes.routes());
app.use(linksRoutes.allowedMethods());

// Analytics routes
app.use(analyticsRoutes.routes());
app.use(analyticsRoutes.allowedMethods());

// Integrations routes
app.use(integrationsRoutes.routes());
app.use(integrationsRoutes.allowedMethods());

// Users routes
app.use(usersRoutes.routes());
app.use(usersRoutes.allowedMethods());

// Default API info route
app.use(async (ctx, next) => {
  if (ctx.request.url.pathname === "/api" || ctx.request.url.pathname === "/api/") {
    ctx.response.body = {
      success: true,
      service: "dashboard-api",
      version: "1.0.0",
      environment: config.env,
      user: ctx.state.user ? {
        id: ctx.state.user.id,
        email: ctx.state.user.email,
        role: ctx.state.user.role,
      } : null,
      endpoints: [
        "GET /api/dashboard/overview",
        "GET /api/dashboard/metrics",
        "GET /api/dashboard/alerts",
        "GET /api/dashboard/activity",
        "GET /api/dashboard/top-links",
        "GET /api/links",
        "POST /api/links",
        "GET /api/analytics/summary",
        "GET /api/integrations",
        "POST /api/integrations",
        "GET /api/users/profile",
      ],
    };
    return;
  }
  await next();
});

// 404 handler for API routes
app.use(async (ctx, next) => {
  await next();
  if (ctx.response.status === 404 && ctx.request.url.pathname.startsWith("/api/")) {
    ctx.response.status = 404;
    ctx.response.body = {
      success: false,
      error: "API endpoint not found",
      path: ctx.request.url.pathname,
      method: ctx.request.method,
    };
  }
});

// Graceful shutdown handling
const controller = new AbortController();
const { signal } = controller;

// Handle shutdown signals
const handleShutdown = async (signal: string) => {
  logger.info(`Received ${signal}, starting graceful shutdown...`);
  
  try {
    // Close database connections
    const { closeDatabase } = await import("./utils/database.ts");
    await closeDatabase();
    
    // Close Redis connection
    const { closeRedis } = await import("./utils/redis.ts");
    await closeRedis();
    
    logger.info("Graceful shutdown completed");
    Deno.exit(0);
  } catch (error) {
    logger.error("Error during graceful shutdown:", error);
    Deno.exit(1);
  }
};

// Register signal handlers
Deno.addSignalListener("SIGINT", () => handleShutdown("SIGINT"));
Deno.addSignalListener("SIGTERM", () => handleShutdown("SIGTERM"));

// Handle uncaught exceptions
globalThis.addEventListener("error", (event) => {
  logger.error("Uncaught exception:", event.error);
  Deno.exit(1);
});

globalThis.addEventListener("unhandledrejection", (event) => {
  logger.error("Unhandled promise rejection:", event.reason);
  Deno.exit(1);
});

// Start server
async function startServer() {
  try {
    // Initialize database connection
    await initializeDatabase();
    logger.info("Database connection established");

    // Initialize Redis connection
    await initializeRedis();
    logger.info("Redis connection established");

    // Start HTTP server
    logger.info(`Dashboard API server starting on port ${config.port}`);
    logger.info(`Environment: ${config.env}`);
    logger.info(`Process ID: ${Deno.pid}`);

    await app.listen({ 
      port: config.port,
      signal,
    });

  } catch (error) {
    logger.error("Failed to start server:", error);
    Deno.exit(1);
  }
}

// Start the server if this file is run directly
if (import.meta.main) {
  await startServer();
}

export default app;
