import { Context, Middleware, RouterContext } from "@oak/oak";
import { verify } from "djwt";
import { createAuthenticationError, createAuthorizationError } from "./errorHandler.ts";
import { getUserById } from "../utils/database.ts";
import config from "../config/index.ts";

export interface JWTPayload {
  sub: string; // user ID
  userId: string; // for backward compatibility
  email: string;
  role: string;
  iat: number;
  exp: number;
  iss: string;
  aud: string;
}

export interface User {
  id: string;
  email: string;
  role: string;
  is_active: boolean;
  tenant_id?: string;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
}

export const authMiddleware: Middleware = async (ctx: Context, next) => {
  const authHeader = ctx.request.headers.get("Authorization");
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    throw createAuthenticationError("Authorization header required");
  }
  
  const token = authHeader.substring(7);
  
  try {
    // Create crypto key for JWT verification
    const key = await crypto.subtle.importKey(
      "raw",
      new TextEncoder().encode(config.jwt.secret),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign", "verify"]
    );
    
    // Verify JWT token
    const payload = await verify(token, key) as unknown as JWTPayload;
    
    // Validate token claims
    if (payload.iss !== config.jwt.issuer || payload.aud !== config.jwt.audience) {
      throw createAuthenticationError("Invalid token claims");
    }
    
    // Get user from database
    const userId = payload.sub || payload.userId;
    const user = await getUserById(userId);
    
    if (!user) {
      throw createAuthenticationError("User not found");
    }
    
    if (!user.is_active) {
      throw createAuthenticationError("User account is disabled");
    }
    
    // Add user to context state
    ctx.state.user = user;
    ctx.state.token = token;
    
    await next();
  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes("expired")) {
        throw createAuthenticationError("Token expired");
      } else if (error.message.includes("invalid")) {
        throw createAuthenticationError("Invalid token");
      }
    }
    throw createAuthenticationError("Authentication failed");
  }
};

// Middleware to require specific roles
export const requireRole = (allowedRoles: string[]): Middleware => {
  return async (ctx: Context, next) => {
    const user = ctx.state.user as User;
    
    if (!user) {
      throw createAuthenticationError("Authentication required");
    }
    
    if (!allowedRoles.includes(user.role)) {
      throw createAuthorizationError(`Access denied. Required roles: ${allowedRoles.join(", ")}`);
    }
    
    await next();
  };
};

// Middleware to require admin role
export const requireAdmin = (): Middleware => {
  return requireRole(["admin", "super_admin"]);
};

// Middleware to require super admin role
export const requireSuperAdmin = (): Middleware => {
  return requireRole(["super_admin"]);
};

// Middleware to check if user owns resource or is admin
export const requireOwnershipOrAdmin = (getUserIdFromParams: (ctx: Context) => string): Middleware => {
  return async (ctx: Context, next) => {
    const user = ctx.state.user as User;
    
    if (!user) {
      throw createAuthenticationError("Authentication required");
    }
    
    const resourceUserId = getUserIdFromParams(ctx);
    
    // Allow if user owns the resource or is admin
    if (user.id === resourceUserId || ["admin", "super_admin"].includes(user.role)) {
      await next();
    } else {
      throw createAuthorizationError("Access denied");
    }
  };
};

// Middleware to check tenant access
export const requireTenantAccess = (getTenantIdFromParams: (ctx: Context) => string): Middleware => {
  return async (ctx: Context, next) => {
    const user = ctx.state.user as User;
    
    if (!user) {
      throw createAuthenticationError("Authentication required");
    }
    
    const resourceTenantId = getTenantIdFromParams(ctx);
    const userTenantId = user.tenant_id || user.id;
    
    // Allow if user belongs to the tenant or is admin
    if (userTenantId === resourceTenantId || ["admin", "super_admin"].includes(user.role)) {
      await next();
    } else {
      throw createAuthorizationError("Access denied to this tenant's resources");
    }
  };
};

// Helper function to extract user ID from URL params
export const getUserIdFromUrlParams = (ctx: RouterContext<string>): string => {
  const userId = ctx.params.userId || ctx.params.id;
  if (!userId) {
    throw createAuthenticationError("User ID not found in request");
  }
  return userId;
};

// Helper function to extract tenant ID from query params
export const getTenantIdFromQuery = (ctx: Context): string => {
  const url = new URL(ctx.request.url);
  const tenantId = url.searchParams.get("tenant_id");
  if (!tenantId) {
    throw createAuthenticationError("Tenant ID not found in request");
  }
  return tenantId;
};

// Helper function to get tenant ID from user context
export const getUserTenantId = (user: User): string => {
  return user.tenant_id || user.id;
};

// Middleware to add tenant context to requests
export const addTenantContext: Middleware = async (ctx: Context, next) => {
  const user = ctx.state.user as User;
  
  if (user) {
    ctx.state.tenantId = getUserTenantId(user);
  }
  
  await next();
};

// Optional authentication middleware (doesn't throw if no token)
export const optionalAuth: Middleware = async (ctx: Context, next) => {
  const authHeader = ctx.request.headers.get("Authorization");
  
  if (authHeader && authHeader.startsWith("Bearer ")) {
    try {
      await authMiddleware(ctx, async () => {});
    } catch (error) {
      // Ignore auth errors for optional auth
      ctx.state.user = null;
    }
  }
  
  await next();
};
