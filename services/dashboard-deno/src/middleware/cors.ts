import { Context, Middleware } from "@oak/oak";
import config from "../config/index.ts";

export const corsMiddleware: Middleware = async (ctx: Context, next) => {
  const origin = ctx.request.headers.get("Origin");

  // Check if origin is allowed
  const isAllowedOrigin = config.cors.origins.includes("*") || 
    (origin && config.cors.origins.includes(origin));

  if (isAllowedOrigin) {
    // Set CORS headers
    ctx.response.headers.set("Access-Control-Allow-Origin", origin || "*");
    ctx.response.headers.set("Access-Control-Allow-Credentials", "true");
    ctx.response.headers.set(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, PATCH, OPTIONS"
    );
    ctx.response.headers.set(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-API-Key"
    );
    ctx.response.headers.set("Access-Control-Max-Age", "86400"); // 24 hours
  }

  // Handle preflight requests
  if (ctx.request.method === "OPTIONS") {
    ctx.response.status = 204;
    return;
  }

  await next();
};
