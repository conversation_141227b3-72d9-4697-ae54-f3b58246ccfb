import { Context, Middleware } from "@oak/oak";
import { getLogger } from "@std/log";

const logger = getLogger();

export interface AppError extends Error {
  status?: number;
  statusCode?: number;
  code?: string;
}

export const errorHandler: Middleware = async (ctx: Context, next) => {
  try {
    await next();
  } catch (error) {
    const appError = error as AppError;
    
    // Determine status code
    const status = appError.status || appError.statusCode || 500;
    
    // Log error
    if (status >= 500) {
      logger.error("Server error:", {
        error: appError.message,
        stack: appError.stack,
        url: ctx.request.url.toString(),
        method: ctx.request.method,
        userAgent: ctx.request.headers.get("user-agent"),
        ip: ctx.request.ip,
      });
    } else {
      logger.warn("Client error:", {
        error: appError.message,
        status,
        url: ctx.request.url.toString(),
        method: ctx.request.method,
        ip: ctx.request.ip,
      });
    }

    // Set response status and body
    ctx.response.status = status;
    ctx.response.body = {
      success: false,
      error: getErrorMessage(appError, status),
      ...(Deno.env.get("NODE_ENV") === "development" && { stack: appError.stack }),
    };
  }
};

function getErrorMessage(error: AppError, status: number): string {
  // Handle specific error types
  if (error.name === "ValidationError") {
    return error.message;
  } else if (error.name === "UnauthorizedError" || error.name === "JsonWebTokenError") {
    return "Unauthorized";
  } else if (error.name === "TokenExpiredError") {
    return "Token expired";
  } else if (error.name === "NotFoundError") {
    return error.message;
  } else if (error.code === "23505") { // PostgreSQL unique violation
    return "Resource already exists";
  } else if (error.code === "23503") { // PostgreSQL foreign key violation
    return "Invalid reference";
  } else if (status === 400) {
    return error.message || "Bad request";
  } else if (status === 401) {
    return "Unauthorized";
  } else if (status === 403) {
    return "Forbidden";
  } else if (status === 404) {
    return "Not found";
  } else if (status === 409) {
    return "Conflict";
  } else if (status === 429) {
    return "Too many requests";
  } else {
    return "Internal server error";
  }
}

// Helper functions to create specific errors
export function createValidationError(message: string): AppError {
  const error = new Error(message) as AppError;
  error.name = "ValidationError";
  error.status = 400;
  return error;
}

export function createAuthenticationError(message: string): AppError {
  const error = new Error(message) as AppError;
  error.name = "UnauthorizedError";
  error.status = 401;
  return error;
}

export function createAuthorizationError(message: string): AppError {
  const error = new Error(message) as AppError;
  error.name = "ForbiddenError";
  error.status = 403;
  return error;
}

export function createNotFoundError(message: string): AppError {
  const error = new Error(message) as AppError;
  error.name = "NotFoundError";
  error.status = 404;
  return error;
}

export function createConflictError(message: string): AppError {
  const error = new Error(message) as AppError;
  error.name = "ConflictError";
  error.status = 409;
  return error;
}

export function createRateLimitError(message: string): AppError {
  const error = new Error(message) as AppError;
  error.name = "RateLimitError";
  error.status = 429;
  error.code = "RATE_LIMIT_EXCEEDED";
  return error;
}

// Validation helpers
export function validateRequired(value: unknown, fieldName: string): void {
  if (value === undefined || value === null || value === "") {
    throw createValidationError(`${fieldName} is required`);
  }
}

export function validateEmail(email: string): void {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw createValidationError("Invalid email format");
  }
}

export function validatePassword(password: string): void {
  if (password.length < 8) {
    throw createValidationError("Password must be at least 8 characters long");
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    throw createValidationError("Password must contain at least one lowercase letter");
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    throw createValidationError("Password must contain at least one uppercase letter");
  }
  
  if (!/(?=.*\d)/.test(password)) {
    throw createValidationError("Password must contain at least one number");
  }
}

export function validateUUID(uuid: string, fieldName: string): void {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(uuid)) {
    throw createValidationError(`${fieldName} must be a valid UUID`);
  }
}

export function validatePositiveInteger(value: unknown, fieldName: string): number {
  const num = Number(value);
  if (!Number.isInteger(num) || num <= 0) {
    throw createValidationError(`${fieldName} must be a positive integer`);
  }
  return num;
}

export function validateDateString(dateString: string, fieldName: string): Date {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    throw createValidationError(`${fieldName} must be a valid date`);
  }
  return date;
}
