import { Context, Middleware } from "@oak/oak";
import { getLogger } from "@std/log";

const logger = getLogger();

export const loggingMiddleware: Middleware = async (ctx: Context, next) => {
  const start = Date.now();
  const method = ctx.request.method;
  const url = ctx.request.url.toString();
  const userAgent = ctx.request.headers.get("user-agent") || "";
  const ip = ctx.request.ip;
  
  // Log request start
  logger.debug("Request started", {
    method,
    url,
    ip,
    userAgent,
  });

  try {
    await next();
  } finally {
    const duration = Date.now() - start;
    const status = ctx.response.status;
    const contentLength = ctx.response.headers.get("content-length") || "0";
    
    // Determine log level based on status code
    const logLevel = status >= 500 ? "error" : status >= 400 ? "warn" : "info";
    
    // Log request completion
    logger[logLevel]("Request completed", {
      method,
      url,
      status,
      duration: `${duration}ms`,
      contentLength,
      ip,
      userAgent,
      user: ctx.state.user ? {
        id: ctx.state.user.id,
        email: ctx.state.user.email,
        role: ctx.state.user.role,
      } : null,
    });
  }
};
