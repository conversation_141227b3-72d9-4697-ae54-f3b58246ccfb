import { Context, Middleware } from "@oak/oak";

// Simple in-memory metrics storage (in production, use Prometheus)
interface Metrics {
  httpRequests: Map<string, number>;
  httpDuration: Map<string, number[]>;
  authAttempts: Map<string, number>;
  activeUsers: Set<string>;
}

const metrics: Metrics = {
  httpRequests: new Map(),
  httpDuration: new Map(),
  authAttempts: new Map(),
  activeUsers: new Set(),
};

export const metricsMiddleware: Middleware = async (ctx: Context, next) => {
  const start = Date.now();
  const method = ctx.request.method;
  const route = ctx.request.url.pathname;
  
  try {
    await next();
  } finally {
    const duration = Date.now() - start;
    const status = ctx.response.status;
    
    // Record HTTP request metrics
    recordHttpRequest(method, route, status, duration);
    
    // Record active user if authenticated
    if (ctx.state.user) {
      metrics.activeUsers.add(ctx.state.user.id);
    }
  }
};

export function recordHttpRequest(method: string, route: string, statusCode: number, duration: number): void {
  const key = `${method}:${route}:${statusCode}`;
  
  // Increment request count
  const currentCount = metrics.httpRequests.get(key) || 0;
  metrics.httpRequests.set(key, currentCount + 1);
  
  // Record duration
  const durations = metrics.httpDuration.get(key) || [];
  durations.push(duration);
  
  // Keep only last 100 durations to prevent memory leak
  if (durations.length > 100) {
    durations.shift();
  }
  
  metrics.httpDuration.set(key, durations);
}

export function recordAuthentication(type: string, status: string): void {
  const key = `${type}:${status}`;
  const currentCount = metrics.authAttempts.get(key) || 0;
  metrics.authAttempts.set(key, currentCount + 1);
}

export function setActiveUsers(_count: number): void {
  // This would be called periodically to update active user count
  // For now, we just track unique users in the current session
}

export function getMetrics(): string {
  const lines: string[] = [];
  
  // HTTP request metrics
  lines.push("# HELP http_requests_total Total number of HTTP requests");
  lines.push("# TYPE http_requests_total counter");
  
  for (const [key, count] of metrics.httpRequests.entries()) {
    const [method, route, statusCode] = key.split(":");
    lines.push(`http_requests_total{method="${method}",route="${route}",status_code="${statusCode}"} ${count}`);
  }
  
  // HTTP duration metrics
  lines.push("# HELP http_request_duration_seconds HTTP request duration in seconds");
  lines.push("# TYPE http_request_duration_seconds histogram");
  
  for (const [key, durations] of metrics.httpDuration.entries()) {
    const [method, route, statusCode] = key.split(":");
    const avg = durations.reduce((a, b) => a + b, 0) / durations.length / 1000; // Convert to seconds
    lines.push(`http_request_duration_seconds{method="${method}",route="${route}",status_code="${statusCode}"} ${avg.toFixed(3)}`);
  }
  
  // Authentication metrics
  lines.push("# HELP auth_attempts_total Total number of authentication attempts");
  lines.push("# TYPE auth_attempts_total counter");
  
  for (const [key, count] of metrics.authAttempts.entries()) {
    const [type, status] = key.split(":");
    lines.push(`auth_attempts_total{type="${type}",status="${status}"} ${count}`);
  }
  
  // Active users
  lines.push("# HELP active_users_current Current number of active users");
  lines.push("# TYPE active_users_current gauge");
  lines.push(`active_users_current ${metrics.activeUsers.size}`);
  
  return lines.join("\n") + "\n";
}

// Clean up old metrics periodically
setInterval(() => {
  // Clear active users (they'll be re-added on next request)
  metrics.activeUsers.clear();
  
  // Clean up old duration data
  for (const [key, durations] of metrics.httpDuration.entries()) {
    if (durations.length > 50) {
      metrics.httpDuration.set(key, durations.slice(-50));
    }
  }
}, 5 * 60 * 1000); // Every 5 minutes
