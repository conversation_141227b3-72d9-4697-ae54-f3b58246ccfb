import { Context, Middleware } from "@oak/oak";
import { incrementRateLimit } from "../utils/redis.ts";
import { createRateLimitError } from "./errorHandler.ts";
import config from "../config/index.ts";

export const rateLimitMiddleware: Middleware = async (ctx: Context, next) => {
  const identifier = ctx.request.ip;
  const windowMs = config.rateLimit.windowMs;
  const maxRequests = config.rateLimit.max;
  
  try {
    const currentCount = await incrementRateLimit(identifier, Math.floor(windowMs / 1000));
    
    // Add rate limit headers
    ctx.response.headers.set("X-RateLimit-Limit", maxRequests.toString());
    ctx.response.headers.set("X-RateLimit-Remaining", Math.max(0, maxRequests - currentCount).toString());
    ctx.response.headers.set("X-RateLimit-Reset", new Date(Date.now() + windowMs).toISOString());
    
    if (currentCount > maxRequests) {
      throw createRateLimitError("Too many requests from this IP, please try again later");
    }
    
    await next();
  } catch (error) {
    if (error instanceof Error && "code" in error && error.code === "RATE_LIMIT_EXCEEDED") {
      throw error;
    }
    // If Redis is down, allow the request to proceed
    await next();
  }
};

export const authRateLimitMiddleware: Middleware = async (ctx: Context, next) => {
  const identifier = ctx.request.ip;
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxRequests = 5; // 5 attempts per window
  
  try {
    const currentCount = await incrementRateLimit(`auth:${identifier}`, Math.floor(windowMs / 1000));
    
    if (currentCount > maxRequests) {
      throw createRateLimitError("Too many authentication attempts, please try again later");
    }
    
    await next();
  } catch (error) {
    if (error instanceof Error && "code" in error && error.code === "RATE_LIMIT_EXCEEDED") {
      throw error;
    }
    // If Redis is down, allow the request to proceed
    await next();
  }
};
