import { Context, Middleware } from "@oak/oak";

export const securityMiddleware: Middleware = async (ctx: Context, next) => {
  // Security headers
  ctx.response.headers.set("X-Content-Type-Options", "nosniff");
  ctx.response.headers.set("X-Frame-Options", "DENY");
  ctx.response.headers.set("X-XSS-Protection", "1; mode=block");
  ctx.response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  ctx.response.headers.set("Permissions-Policy", "geolocation=(), microphone=(), camera=()");
  
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self'",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join("; ");
  
  ctx.response.headers.set("Content-Security-Policy", csp);
  
  // Strict Transport Security (HTTPS only)
  if (ctx.request.secure) {
    ctx.response.headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
  }
  
  await next();
};
