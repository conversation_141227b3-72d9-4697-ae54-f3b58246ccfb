import { Router } from "@oak/oak";
import { getLogger } from "@std/log";
import { analyticsService, handleServiceError } from "../utils/httpClient.ts";
import { recordHttpRequest } from "../middleware/metrics.ts";
import { User } from "../middleware/auth.ts";

const logger = getLogger();
const router = new Router();

// GET /api/analytics/summary - Get analytics summary
router.get("/api/analytics/summary", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const dateFrom = url.searchParams.get("date_from");
    const dateTo = url.searchParams.get("date_to");
    const groupBy = url.searchParams.get("group_by") || "day";
    
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Fetching analytics summary", {
      userId: user.id,
      tenantId,
      dateFrom,
      dateTo,
      groupBy,
    });

    // Make request to analytics service
    const response = await analyticsService.get("/api/analytics/summary", {
      params: {
        tenant_id: tenantId,
        date_from: dateFrom || undefined,
        date_to: dateTo || undefined,
        group_by: groupBy,
      },
    });

    ctx.response.body = {
      success: true,
      data: response.data,
    };

    recordHttpRequest("GET", "/api/analytics/summary", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/analytics/summary", 500, Date.now() - start);
    handleServiceError(error, "Analytics");
  }
});

// GET /api/analytics/links/:linkId - Get link-specific analytics
router.get("/api/analytics/links/:linkId", async (ctx) => {
  const start = Date.now();
  
  try {
    const { linkId } = ctx.params;
    const url = new URL(ctx.request.url);
    const dateFrom = url.searchParams.get("date_from");
    const dateTo = url.searchParams.get("date_to");
    const groupBy = url.searchParams.get("group_by") || "day";
    
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Fetching link analytics", {
      userId: user.id,
      tenantId,
      linkId,
      dateFrom,
      dateTo,
      groupBy,
    });

    // Make request to analytics service
    const response = await analyticsService.get(`/api/analytics/links/${linkId}`, {
      params: {
        tenant_id: tenantId,
        date_from: dateFrom || undefined,
        date_to: dateTo || undefined,
        group_by: groupBy,
      },
    });

    ctx.response.body = {
      success: true,
      data: response.data,
    };

    recordHttpRequest("GET", "/api/analytics/links/:linkId", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/analytics/links/:linkId", 500, Date.now() - start);
    handleServiceError(error, "Analytics");
  }
});

// GET /api/analytics/reports/performance - Get performance report
router.get("/api/analytics/reports/performance", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const dateFrom = url.searchParams.get("date_from");
    const dateTo = url.searchParams.get("date_to");
    const groupBy = url.searchParams.get("group_by") || "link";
    const metrics = url.searchParams.get("metrics") || "clicks,conversions,revenue";
    const platform = url.searchParams.get("platform");
    
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Generating performance report", {
      userId: user.id,
      tenantId,
      dateFrom,
      dateTo,
      groupBy,
      metrics,
      platform,
    });

    // Make request to analytics service
    const response = await analyticsService.get("/api/reports/performance", {
      params: {
        tenant_id: tenantId,
        date_from: dateFrom || undefined,
        date_to: dateTo || undefined,
        group_by: groupBy,
        metrics,
        platform: platform || undefined,
      },
    });

    ctx.response.body = {
      success: true,
      data: response.data,
    };

    recordHttpRequest("GET", "/api/analytics/reports/performance", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/analytics/reports/performance", 500, Date.now() - start);
    handleServiceError(error, "Analytics");
  }
});

// GET /api/analytics/reports/conversion-funnel - Get conversion funnel
router.get("/api/analytics/reports/conversion-funnel", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const dateFrom = url.searchParams.get("date_from");
    const dateTo = url.searchParams.get("date_to");
    const platform = url.searchParams.get("platform");
    
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Generating conversion funnel", {
      userId: user.id,
      tenantId,
      dateFrom,
      dateTo,
      platform,
    });

    // Make request to analytics service
    const response = await analyticsService.get("/api/reports/conversion-funnel", {
      params: {
        tenant_id: tenantId,
        date_from: dateFrom || undefined,
        date_to: dateTo || undefined,
        platform: platform || undefined,
      },
    });

    ctx.response.body = {
      success: true,
      data: response.data,
    };

    recordHttpRequest("GET", "/api/analytics/reports/conversion-funnel", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/analytics/reports/conversion-funnel", 500, Date.now() - start);
    handleServiceError(error, "Analytics");
  }
});

export default router;
