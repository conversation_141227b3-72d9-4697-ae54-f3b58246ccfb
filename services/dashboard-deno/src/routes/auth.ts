import { Router } from "@oak/oak";
import { create } from "djwt";
import { hash, compare } from "bcrypt";
import { z } from "zod";
import { 
  getUserByEmail, 
  updateUserLoginAttempts, 
  updateUserLastLogin,
  query
} from "../utils/database.ts";
import { 
  incrementLoginAttempts, 
  getLoginAttempts, 
  clearLoginAttempts 
} from "../utils/redis.ts";
import { 
  createAuthenticationError, 
  createValidationError,
  validateRequired,
  validateEmail,
  validatePassword,
} from "../middleware/errorHandler.ts";
import { authRateLimitMiddleware } from "../middleware/rateLimit.ts";
import { recordAuthentication } from "../middleware/metrics.ts";
import config from "../config/index.ts";

const router = new Router();

// Apply auth rate limiting to all auth routes
router.use("/api/auth", authRateLimitMiddleware);

// Validation schemas using Zod
const registerSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  firstName: z.string().min(2, "First name must be at least 2 characters").max(50),
  lastName: z.string().min(2, "Last name must be at least 2 characters").max(50),
  companyName: z.string().max(100).optional(),
});

const loginSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(1, "Password is required"),
});

// POST /api/auth/register - User registration
router.post("/api/auth/register", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    
    // Validate input
    const validatedData = registerSchema.parse(body);
    const { email, password, firstName, lastName, companyName } = validatedData;

    // Check if user already exists
    const existingUser = await getUserByEmail(email.toLowerCase());
    if (existingUser) {
      recordAuthentication("register", "user_exists");
      throw createValidationError("User with this email already exists");
    }

    // Hash password
    const hashedPassword = await hash(password);

    // Create user
    const userQuery = `
      INSERT INTO users (email, password_hash, first_name, last_name, company_name, role, is_active, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $8)
      RETURNING id, email, first_name, last_name, company_name, role, is_active, created_at
    `;

    interface NewUser {
      id: string;
      email: string;
      first_name: string;
      last_name: string;
      company_name: string | null;
      role: string;
      is_active: boolean;
      created_at: Date;
    }

    const users = await query<NewUser>(userQuery, [
      email.toLowerCase(),
      hashedPassword,
      firstName,
      lastName,
      companyName || null,
      'user', // Default role
      true,
      new Date()
    ]);

    const user = users[0];

    // Generate JWT token
    const key = await crypto.subtle.importKey(
      "raw",
      new TextEncoder().encode(config.jwt.secret),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign", "verify"]
    );

    const token = await create(
      { alg: "HS256", typ: "JWT" },
      {
        sub: user.id,
        userId: user.id, // for backward compatibility
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
        iss: config.jwt.issuer,
        aud: config.jwt.audience,
      },
      key
    );

    recordAuthentication("register", "success");

    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      message: "User registered successfully",
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          companyName: user.company_name,
          role: user.role,
          createdAt: user.created_at,
        },
        token,
      },
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      recordAuthentication("register", "validation_error");
      throw createValidationError(error.errors[0].message);
    }
    throw error;
  }
});

// POST /api/auth/login - User login
router.post("/api/auth/login", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    
    // Validate input
    const validatedData = loginSchema.parse(body);
    const { email, password } = validatedData;

    // Check rate limiting for this IP
    const loginAttempts = await getLoginAttempts(ctx.request.ip);
    if (loginAttempts >= config.security.maxLoginAttempts) {
      recordAuthentication("login", "rate_limited");
      throw createAuthenticationError("Too many login attempts. Please try again later.");
    }

    // Get user from database
    const user = await getUserByEmail(email.toLowerCase());
    if (!user) {
      await incrementLoginAttempts(ctx.request.ip);
      recordAuthentication("login", "user_not_found");
      throw createAuthenticationError("Invalid email or password");
    }

    // Check if user is active
    if (!user.is_active) {
      recordAuthentication("login", "user_inactive");
      throw createAuthenticationError("User account is disabled");
    }

    // Check if user is locked
    if (user.locked_until && new Date() < user.locked_until) {
      recordAuthentication("login", "user_locked");
      throw createAuthenticationError("Account is temporarily locked. Please try again later.");
    }

    // Verify password
    const isValidPassword = await compare(password, user.password_hash);

    if (!isValidPassword) {
      await incrementLoginAttempts(ctx.request.ip);
      await updateUserLoginAttempts(user.id, user.login_attempts + 1);
      recordAuthentication("login", "invalid_password");
      throw createAuthenticationError("Invalid email or password");
    }

    // Clear login attempts and update last login
    await clearLoginAttempts(ctx.request.ip);
    await updateUserLastLogin(user.id);

    // Generate JWT token
    const key = await crypto.subtle.importKey(
      "raw",
      new TextEncoder().encode(config.jwt.secret),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign", "verify"]
    );

    const token = await create(
      { alg: "HS256", typ: "JWT" },
      {
        sub: user.id,
        userId: user.id, // for backward compatibility
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
        iss: config.jwt.issuer,
        aud: config.jwt.audience,
      },
      key
    );

    recordAuthentication("login", "success");

    ctx.response.body = {
      success: true,
      message: "Login successful",
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          lastLogin: new Date(),
        },
        token,
      },
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      recordAuthentication("login", "validation_error");
      throw createValidationError(error.errors[0].message);
    }
    throw error;
  }
});

export default router;
