import { Router } from "@oak/oak";
import { getLogger } from "@std/log";
import { DashboardService } from "../services/dashboardService.ts";
import { recordHttpRequest } from "../middleware/metrics.ts";
import { User } from "../middleware/auth.ts";

const logger = getLogger();
const router = new Router();
const dashboardService = new DashboardService();

// GET /api/dashboard/overview - Get dashboard overview
router.get("/api/dashboard/overview", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const dateFrom = url.searchParams.get("date_from");
    const dateTo = url.searchParams.get("date_to");
    const period = url.searchParams.get("period") || "30d";

    const user = ctx.state.user as User;

    logger.debug("Fetching dashboard overview", {
      userId: user.id,
      dateFrom,
      dateTo,
      period,
    });

    const overview = await dashboardService.getOverview(user, {
      dateFrom: dateFrom || undefined,
      dateTo: dateTo || undefined,
      period,
    });

    ctx.response.body = {
      success: true,
      data: overview,
    };

    recordHttpRequest("GET", "/api/dashboard/overview", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/dashboard/overview", 500, Date.now() - start);
    throw error;
  }
});

// GET /api/dashboard/metrics - Get key metrics
router.get("/api/dashboard/metrics", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const dateFrom = url.searchParams.get("date_from");
    const dateTo = url.searchParams.get("date_to");
    const comparePeriod = url.searchParams.get("compare_period") === "true";

    const user = ctx.state.user as User;

    logger.debug("Fetching dashboard metrics", {
      userId: user.id,
      dateFrom,
      dateTo,
      comparePeriod,
    });

    const metrics = await dashboardService.getMetrics(user, {
      dateFrom: dateFrom || undefined,
      dateTo: dateTo || undefined,
      comparePeriod,
    });

    ctx.response.body = {
      success: true,
      data: metrics,
    };

    recordHttpRequest("GET", "/api/dashboard/metrics", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/dashboard/metrics", 500, Date.now() - start);
    throw error;
  }
});

// GET /api/dashboard/alerts - Get dashboard alerts
router.get("/api/dashboard/alerts", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const limit = parseInt(url.searchParams.get("limit") || "5");

    const user = ctx.state.user as User;

    logger.debug("Fetching dashboard alerts", {
      userId: user.id,
      limit,
    });

    const alerts = await dashboardService.getAlerts(user, { limit });

    ctx.response.body = {
      success: true,
      data: alerts,
    };

    recordHttpRequest("GET", "/api/dashboard/alerts", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/dashboard/alerts", 500, Date.now() - start);
    throw error;
  }
});

// GET /api/dashboard/activity - Get recent activity
router.get("/api/dashboard/activity", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const limit = parseInt(url.searchParams.get("limit") || "10");

    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Fetching dashboard activity", {
      userId: user.id,
      tenantId,
      limit,
    });

    // Import here to avoid circular dependency
    const { getRecentActivity } = await import("../utils/database.ts");
    const activity = await getRecentActivity(tenantId, limit);

    ctx.response.body = {
      success: true,
      data: activity,
    };

    recordHttpRequest("GET", "/api/dashboard/activity", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/dashboard/activity", 500, Date.now() - start);
    throw error;
  }
});

// GET /api/dashboard/top-links - Get top performing links
router.get("/api/dashboard/top-links", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const metric = url.searchParams.get("metric") as 'clicks' | 'conversions' | 'conversion_rate' || 'clicks';
    const limit = parseInt(url.searchParams.get("limit") || "5");

    const user = ctx.state.user as User;

    logger.debug("Fetching top links", {
      userId: user.id,
      metric,
      limit,
    });

    const topLinks = await dashboardService.getTopLinks(user, { metric, limit });

    ctx.response.body = {
      success: true,
      data: topLinks,
    };

    recordHttpRequest("GET", "/api/dashboard/top-links", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/dashboard/top-links", 500, Date.now() - start);
    throw error;
  }
});

export default router;
