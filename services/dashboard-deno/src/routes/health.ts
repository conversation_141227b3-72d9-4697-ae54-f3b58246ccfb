import { Router } from "@oak/oak";
import { checkDatabaseHealth } from "../utils/database.ts";
import { checkRedisHealth } from "../utils/redis.ts";
import { getMetrics } from "../middleware/metrics.ts";
import config from "../config/index.ts";

const router = new Router();

// Health check endpoint
router.get("/health", async (ctx) => {
  const health = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: config.env,
    uptime: Math.floor(performance.now() / 1000),
    checks: {
      database: false,
      redis: false,
    },
  };

  try {
    // Check database health
    health.checks.database = await checkDatabaseHealth();
    
    // Check Redis health
    health.checks.redis = await checkRedisHealth();
    
    // Determine overall health status
    const allHealthy = Object.values(health.checks).every(check => check === true);
    health.status = allHealthy ? "healthy" : "unhealthy";
    
    ctx.response.status = allHealthy ? 200 : 503;
    ctx.response.body = health;
  } catch (error) {
    health.status = "unhealthy";
    ctx.response.status = 503;
    ctx.response.body = {
      ...health,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

// Readiness check endpoint
router.get("/ready", async (ctx) => {
  try {
    // Check if all dependencies are ready
    const dbReady = await checkDatabaseHealth();
    const redisReady = await checkRedisHealth();
    
    if (dbReady && redisReady) {
      ctx.response.status = 200;
      ctx.response.body = {
        status: "ready",
        timestamp: new Date().toISOString(),
      };
    } else {
      ctx.response.status = 503;
      ctx.response.body = {
        status: "not ready",
        timestamp: new Date().toISOString(),
        checks: {
          database: dbReady,
          redis: redisReady,
        },
      };
    }
  } catch (error) {
    ctx.response.status = 503;
    ctx.response.body = {
      status: "not ready",
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

// Liveness check endpoint
router.get("/live", async (ctx) => {
  ctx.response.status = 200;
  ctx.response.body = {
    status: "alive",
    timestamp: new Date().toISOString(),
    pid: Deno.pid,
  };
});

// Ping endpoint
router.get("/ping", async (ctx) => {
  ctx.response.status = 200;
  ctx.response.body = "pong";
});

// Metrics endpoint (Prometheus format)
router.get("/metrics", async (ctx) => {
  try {
    ctx.response.headers.set("Content-Type", "text/plain; version=0.0.4; charset=utf-8");
    ctx.response.body = getMetrics();
  } catch (error) {
    ctx.response.status = 500;
    ctx.response.body = `# Error generating metrics: ${error instanceof Error ? error.message : "Unknown error"}`;
  }
});

export default router;
