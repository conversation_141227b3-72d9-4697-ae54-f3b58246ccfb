import { Router } from "@oak/oak";
import { getLogger } from "@std/log";
import { integrationService, handleServiceError } from "../utils/httpClient.ts";
import { recordHttpRequest } from "../middleware/metrics.ts";
import { User } from "../middleware/auth.ts";

const logger = getLogger();
const router = new Router();

// GET /api/integrations - Get user's integrations
router.get("/api/integrations", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const limit = url.searchParams.get("limit") || "50";
    const offset = url.searchParams.get("offset") || "0";
    const platform = url.searchParams.get("platform");
    const status = url.searchParams.get("status");
    
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Fetching user integrations", {
      userId: user.id,
      tenantId,
      limit,
      offset,
      platform,
      status,
    });

    // Make request to integration service
    const response = await integrationService.get("/api/integrations", {
      params: {
        tenant_id: tenantId,
        limit,
        offset,
        platform: platform || undefined,
        status: status || undefined,
      },
    });

    ctx.response.body = {
      success: true,
      data: response.data,
    };

    recordHttpRequest("GET", "/api/integrations", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/integrations", 500, Date.now() - start);
    handleServiceError(error, "Integration");
  }
});

// POST /api/integrations - Create new integration
router.post("/api/integrations", async (ctx) => {
  const start = Date.now();
  
  try {
    const body = await ctx.request.body.json();
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    const integrationData = {
      ...body,
      tenant_id: tenantId,
    };

    logger.info("Creating new integration", {
      userId: user.id,
      tenantId,
      platform: body.platform,
      name: body.name,
    });

    // Make request to integration service
    const response = await integrationService.post("/api/integrations", integrationData);

    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      data: response.data,
      message: "Integration created successfully",
    };

    recordHttpRequest("POST", "/api/integrations", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("POST", "/api/integrations", 500, Date.now() - start);
    handleServiceError(error, "Integration");
  }
});

// GET /api/integrations/:id - Get specific integration details
router.get("/api/integrations/:id", async (ctx) => {
  const start = Date.now();
  
  try {
    const { id } = ctx.params;
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Fetching integration details", {
      userId: user.id,
      tenantId,
      integrationId: id,
    });

    // Make request to integration service
    const response = await integrationService.get(`/api/integrations/${id}`, {
      params: { tenant_id: tenantId },
    });

    ctx.response.body = {
      success: true,
      data: response.data,
    };

    recordHttpRequest("GET", "/api/integrations/:id", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/integrations/:id", 500, Date.now() - start);
    handleServiceError(error, "Integration");
  }
});

// PUT /api/integrations/:id - Update integration
router.put("/api/integrations/:id", async (ctx) => {
  const start = Date.now();
  
  try {
    const { id } = ctx.params;
    const body = await ctx.request.body.json();
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    const updateData = {
      ...body,
      tenant_id: tenantId,
    };

    logger.info("Updating integration", {
      userId: user.id,
      tenantId,
      integrationId: id,
    });

    // Make request to integration service
    const response = await integrationService.put(`/api/integrations/${id}`, updateData);

    ctx.response.body = {
      success: true,
      data: response.data,
      message: "Integration updated successfully",
    };

    recordHttpRequest("PUT", "/api/integrations/:id", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("PUT", "/api/integrations/:id", 500, Date.now() - start);
    handleServiceError(error, "Integration");
  }
});

// DELETE /api/integrations/:id - Delete integration
router.delete("/api/integrations/:id", async (ctx) => {
  const start = Date.now();
  
  try {
    const { id } = ctx.params;
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.info("Deleting integration", {
      userId: user.id,
      tenantId,
      integrationId: id,
    });

    // Make request to integration service
    await integrationService.delete(`/api/integrations/${id}`, {
      params: { tenant_id: tenantId },
    });

    ctx.response.body = {
      success: true,
      message: "Integration deleted successfully",
    };

    recordHttpRequest("DELETE", "/api/integrations/:id", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("DELETE", "/api/integrations/:id", 500, Date.now() - start);
    handleServiceError(error, "Integration");
  }
});

// POST /api/integrations/:id/test - Test integration
router.post("/api/integrations/:id/test", async (ctx) => {
  const start = Date.now();
  
  try {
    const { id } = ctx.params;
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.info("Testing integration", {
      userId: user.id,
      tenantId,
      integrationId: id,
    });

    // Make request to integration service
    const response = await integrationService.post(`/api/integrations/${id}/test`, {
      tenant_id: tenantId,
    });

    ctx.response.body = {
      success: true,
      data: response.data,
      message: "Integration test completed",
    };

    recordHttpRequest("POST", "/api/integrations/:id/test", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("POST", "/api/integrations/:id/test", 500, Date.now() - start);
    handleServiceError(error, "Integration");
  }
});

export default router;
