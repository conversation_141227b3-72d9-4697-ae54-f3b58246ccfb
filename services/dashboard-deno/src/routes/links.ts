import { Router, RouterContext } from "@oak/oak";
import { getLogger } from "@std/log";
import { linkTrackingService, handleServiceError } from "../utils/httpClient.ts";
import { recordHttpRequest } from "../middleware/metrics.ts";
import { User } from "../middleware/auth.ts";

const logger = getLogger();
const router = new Router();

// GET /api/links - Get user's links
router.get("/api/links", async (ctx) => {
  const start = Date.now();
  
  try {
    const url = new URL(ctx.request.url);
    const limit = url.searchParams.get("limit") || "50";
    const offset = url.searchParams.get("offset") || "0";
    const search = url.searchParams.get("search");
    const sortBy = url.searchParams.get("sort_by") || "created_at";
    const sortOrder = url.searchParams.get("sort_order") || "desc";

    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Fetching user links", {
      userId: user.id,
      tenantId,
      limit,
      offset,
      search,
      sortBy,
      sortOrder,
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.get("/api/v1/links/list", {
      params: {
        tenant_id: tenantId,
        limit,
        offset,
        search: search || undefined,
        sort_by: sortBy,
        sort_order: sortOrder,
      },
    });

    ctx.response.body = {
      success: true,
      data: response.data,
    };

    recordHttpRequest("GET", "/api/links", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/links", 500, Date.now() - start);
    handleServiceError(error, "Link Tracking");
  }
});

// POST /api/links - Create new link
router.post("/api/links", async (ctx) => {
  const start = Date.now();
  
  try {
    const body = await ctx.request.body.json();
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    const linkData = {
      ...body,
      tenant_id: tenantId,
    };

    logger.info("Creating new link", {
      userId: user.id,
      tenantId,
      originalUrl: body.original_url,
      title: body.title,
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.post("/api/v1/links", linkData);

    ctx.response.status = 201;
    ctx.response.body = {
      success: true,
      data: response.data,
      message: "Link created successfully",
    };

    recordHttpRequest("POST", "/api/links", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("POST", "/api/links", 500, Date.now() - start);
    handleServiceError(error, "Link Tracking");
  }
});

// GET /api/links/:id - Get specific link details
router.get("/api/links/:id", async (ctx: RouterContext<"/api/links/:id">) => {
  const start = Date.now();

  try {
    const { id } = ctx.params;
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Fetching link details", {
      userId: user.id,
      tenantId,
      linkId: id,
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.get(`/api/v1/links/${id}`, {
      params: { tenant_id: tenantId },
    });

    ctx.response.body = {
      success: true,
      data: response.data,
    };

    recordHttpRequest("GET", "/api/links/:id", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/links/:id", 500, Date.now() - start);
    handleServiceError(error, "Link Tracking");
  }
});

// PUT /api/links/:id - Update link
router.put("/api/links/:id", async (ctx: RouterContext<"/api/links/:id">) => {
  const start = Date.now();

  try {
    const { id } = ctx.params;
    const body = await ctx.request.body.json();
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    const updateData = {
      ...body,
      tenant_id: tenantId,
    };

    logger.info("Updating link", {
      userId: user.id,
      tenantId,
      linkId: id,
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.put(`/api/v1/links/${id}`, updateData);

    ctx.response.body = {
      success: true,
      data: response.data,
      message: "Link updated successfully",
    };

    recordHttpRequest("PUT", "/api/links/:id", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("PUT", "/api/links/:id", 500, Date.now() - start);
    handleServiceError(error, "Link Tracking");
  }
});

// DELETE /api/links/:id - Delete link
router.delete("/api/links/:id", async (ctx: RouterContext<"/api/links/:id">) => {
  const start = Date.now();

  try {
    const { id } = ctx.params;
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.info("Deleting link", {
      userId: user.id,
      tenantId,
      linkId: id,
    });

    // Make request to link-tracking service
    await linkTrackingService.delete(`/api/v1/links/${id}`, {
      params: { tenant_id: tenantId },
    });

    ctx.response.body = {
      success: true,
      message: "Link deleted successfully",
    };

    recordHttpRequest("DELETE", "/api/links/:id", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("DELETE", "/api/links/:id", 500, Date.now() - start);
    handleServiceError(error, "Link Tracking");
  }
});

// GET /api/links/:id/analytics - Get link analytics
router.get("/api/links/:id/analytics", async (ctx: RouterContext<"/api/links/:id/analytics">) => {
  const start = Date.now();

  try {
    const { id } = ctx.params;
    const url = new URL(ctx.request.url);
    const dateFrom = url.searchParams.get("date_from");
    const dateTo = url.searchParams.get("date_to");
    
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Fetching link analytics", {
      userId: user.id,
      tenantId,
      linkId: id,
      dateFrom,
      dateTo,
    });

    // Make request to link-tracking service
    const response = await linkTrackingService.get(`/api/v1/links/${id}/analytics`, {
      params: {
        tenant_id: tenantId,
        date_from: dateFrom || undefined,
        date_to: dateTo || undefined,
      },
    });

    ctx.response.body = {
      success: true,
      data: response.data,
    };

    recordHttpRequest("GET", "/api/links/:id/analytics", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/links/:id/analytics", 500, Date.now() - start);
    handleServiceError(error, "Link Tracking");
  }
});

export default router;
