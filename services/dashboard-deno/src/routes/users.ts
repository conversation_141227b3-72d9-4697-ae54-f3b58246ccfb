import { Router } from "@oak/oak";
import { getLogger } from "@std/log";
import { hash, compare } from "bcrypt";
import { z } from "zod";
import { query, getUserById } from "../utils/database.ts";
import { recordHttpRequest } from "../middleware/metrics.ts";
import { createValidationError } from "../middleware/errorHandler.ts";
import { User } from "../middleware/auth.ts";

const logger = getLogger();
const router = new Router();

// Validation schemas
const updateProfileSchema = z.object({
  firstName: z.string().min(2).max(50).optional(),
  lastName: z.string().min(2).max(50).optional(),
  company: z.string().max(100).optional(),
});

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "New password must be at least 8 characters"),
});

// GET /api/users/profile - Get user profile
router.get("/api/users/profile", async (ctx) => {
  const start = Date.now();
  
  try {
    const user = ctx.state.user as User;

    logger.debug("Fetching user profile", {
      userId: user.id,
    });

    // Get fresh user data from database
    const userData = await getUserById(user.id);
    
    if (!userData) {
      ctx.response.status = 404;
      ctx.response.body = {
        success: false,
        error: "User not found",
      };
      return;
    }

    ctx.response.body = {
      success: true,
      data: {
        id: userData.id,
        email: userData.email,
        role: userData.role,
        isActive: userData.is_active,
        createdAt: userData.created_at,
        updatedAt: userData.updated_at,
        lastLogin: userData.last_login,
      },
    };

    recordHttpRequest("GET", "/api/users/profile", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/users/profile", 500, Date.now() - start);
    throw error;
  }
});

// PUT /api/users/profile - Update user profile
router.put("/api/users/profile", async (ctx) => {
  const start = Date.now();
  
  try {
    const body = await ctx.request.body.json();
    const user = ctx.state.user as User;

    // Validate input
    const validatedData = updateProfileSchema.parse(body);
    const { firstName, lastName, company } = validatedData;

    logger.info("Updating user profile", {
      userId: user.id,
      firstName,
      lastName,
      company,
    });

    // Build update query dynamically
    const updates: string[] = [];
    const values: unknown[] = [];
    let paramIndex = 1;

    if (firstName !== undefined) {
      updates.push(`first_name = $${paramIndex++}`);
      values.push(firstName);
    }

    if (lastName !== undefined) {
      updates.push(`last_name = $${paramIndex++}`);
      values.push(lastName);
    }

    if (company !== undefined) {
      updates.push(`company_name = $${paramIndex++}`);
      values.push(company);
    }

    if (updates.length === 0) {
      throw createValidationError("No valid fields to update");
    }

    updates.push(`updated_at = $${paramIndex++}`);
    values.push(new Date());
    values.push(user.id);

    const updateQuery = `
      UPDATE users
      SET ${updates.join(", ")}
      WHERE id = $${paramIndex}
      RETURNING id, email, first_name, last_name, company_name, role, is_active, created_at, updated_at
    `;

    interface UpdatedUser {
      id: string;
      email: string;
      first_name: string;
      last_name: string;
      company_name: string | null;
      role: string;
      is_active: boolean;
      created_at: Date;
      updated_at: Date;
    }

    const updatedUsers = await query<UpdatedUser>(updateQuery, values);
    const updatedUser = updatedUsers[0];

    ctx.response.body = {
      success: true,
      message: "Profile updated successfully",
      data: {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.first_name,
        lastName: updatedUser.last_name,
        company: updatedUser.company_name,
        role: updatedUser.role,
        isActive: updatedUser.is_active,
        createdAt: updatedUser.created_at,
        updatedAt: updatedUser.updated_at,
      },
    };

    recordHttpRequest("PUT", "/api/users/profile", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("PUT", "/api/users/profile", 500, Date.now() - start);
    if (error instanceof z.ZodError) {
      throw createValidationError(error.errors[0].message);
    }
    throw error;
  }
});

// POST /api/users/change-password - Change user password
router.post("/api/users/change-password", async (ctx) => {
  const start = Date.now();
  
  try {
    const body = await ctx.request.body.json();
    const user = ctx.state.user as User;

    // Validate input
    const validatedData = changePasswordSchema.parse(body);
    const { currentPassword, newPassword } = validatedData;

    logger.info("Changing user password", {
      userId: user.id,
    });

    // Get current user data to verify password
    const userData = await getUserById(user.id);
    if (!userData) {
      ctx.response.status = 404;
      ctx.response.body = {
        success: false,
        error: "User not found",
      };
      return;
    }

    // Verify current password
    const isValidPassword = await compare(currentPassword, userData.password_hash);
    if (!isValidPassword) {
      throw createValidationError("Current password is incorrect");
    }

    // Hash new password
    const hashedNewPassword = await hash(newPassword);

    // Update password in database
    await query(
      "UPDATE users SET password_hash = $1, updated_at = $2 WHERE id = $3",
      [hashedNewPassword, new Date(), user.id]
    );

    ctx.response.body = {
      success: true,
      message: "Password changed successfully",
    };

    recordHttpRequest("POST", "/api/users/change-password", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("POST", "/api/users/change-password", 500, Date.now() - start);
    if (error instanceof z.ZodError) {
      throw createValidationError(error.errors[0].message);
    }
    throw error;
  }
});

// GET /api/users/stats - Get user statistics
router.get("/api/users/stats", async (ctx) => {
  const start = Date.now();
  
  try {
    const user = ctx.state.user as User;
    const tenantId = user.tenant_id || user.id;

    logger.debug("Fetching user statistics", {
      userId: user.id,
      tenantId,
    });

    interface LinkStats {
      total_links: string;
      active_links: string;
      total_clicks: string;
      total_conversions: string;
    }

    interface IntegrationStats {
      total_integrations: string;
      active_integrations: string;
    }

    interface ActivityStats {
      recent_clicks: string;
    }

    // Get user's link statistics
    const linkStatsQuery = `
      SELECT
        COUNT(*) as total_links,
        COUNT(CASE WHEN l.is_active THEN 1 END) as active_links,
        COALESCE(SUM(l.total_clicks), 0) as total_clicks,
        COALESCE(SUM(l.total_conversions), 0) as total_conversions
      FROM links l
      WHERE l.tenant_id = $1
    `;

    const linkStatsResult = await query<LinkStats>(linkStatsQuery, [tenantId]);
    const linkStats = linkStatsResult[0] || {} as LinkStats;

    // Get integration statistics
    const integrationStatsQuery = `
      SELECT
        COUNT(*) as total_integrations,
        COUNT(CASE WHEN i.is_active THEN 1 END) as active_integrations
      FROM integrations i
      WHERE i.tenant_id = $1
    `;

    const integrationStatsResult = await query<IntegrationStats>(integrationStatsQuery, [tenantId]);
    const integrationStats = integrationStatsResult[0] || {} as IntegrationStats;

    // Get recent activity count
    const activityQuery = `
      SELECT COUNT(*) as recent_clicks
      FROM clicks c
      JOIN links l ON c.link_id = l.id
      WHERE l.tenant_id = $1 AND c.clicked_at >= NOW() - INTERVAL '24 hours'
    `;

    const activityResult = await query<ActivityStats>(activityQuery, [tenantId]);
    const recentClicks = parseInt(activityResult[0]?.recent_clicks || "0");

    ctx.response.body = {
      success: true,
      data: {
        links: {
          total: parseInt(linkStats.total_links || "0"),
          active: parseInt(linkStats.active_links || "0"),
          total_clicks: parseInt(linkStats.total_clicks || "0"),
          total_conversions: parseInt(linkStats.total_conversions || "0"),
        },
        integrations: {
          total: parseInt(integrationStats.total_integrations || "0"),
          active: parseInt(integrationStats.active_integrations || "0"),
        },
        activity: {
          recent_clicks: recentClicks,
        },
        account: {
          member_since: user.created_at,
          last_login: user.last_login,
        },
      },
    };

    recordHttpRequest("GET", "/api/users/stats", ctx.response.status, Date.now() - start);
  } catch (error) {
    recordHttpRequest("GET", "/api/users/stats", 500, Date.now() - start);
    throw error;
  }
});

export default router;
