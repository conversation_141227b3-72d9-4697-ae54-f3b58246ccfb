import { getLogger } from "@std/log";
import { analyticsService, linkTrackingService, integrationService, handleServiceError } from "../utils/httpClient.ts";
import { getRecentActivity, getTopLinks } from "../utils/database.ts";
import { getCachedDashboardData, cacheDashboardData } from "../utils/redis.ts";
import { User } from "../middleware/auth.ts";

const logger = getLogger();

// Helper function to get tenant ID from user
function getUserTenantId(user: User): string {
  return user.tenant_id || user.id;
}

// Service response interfaces
interface AnalyticsData {
  summary?: {
    total_clicks?: number;
    total_conversions?: number;
    total_revenue?: number;
    conversion_rate?: number;
    avg_order_value?: number;
  };
  trend?: Array<{
    date: string;
    clicks: number;
    conversions: number;
    revenue: number;
  }>;
  platforms?: Array<{
    platform: string;
    clicks: number;
    conversions: number;
    revenue: number;
  }>;
  geographic?: Array<{
    country: string;
    clicks: number;
    conversions: number;
    revenue: number;
  }>;
}

interface LinksData {
  total?: number;
  links?: unknown[];
}

interface Integration {
  id: string;
  name: string;
  platform: string;
  is_active: boolean;
}

interface TopLink {
  id: string;
  title: string;
  short_code: string;
  original_url: string;
  total_clicks: number;
  total_conversions: number;
  conversion_rate: number;
  total_revenue: number;
}

export interface DashboardOverview {
  period: string;
  summary: {
    total_clicks: number;
    total_conversions: number;
    total_revenue: number;
    conversion_rate: number;
    total_links: number;
    active_integrations: number;
  };
  trend: Array<{
    date: string;
    clicks: number;
    conversions: number;
    revenue: number;
  }>;
  platforms: Array<{
    platform: string;
    clicks: number;
    conversions: number;
    revenue: number;
  }>;
  recent_activity: Array<{
    type: 'click' | 'conversion';
    id: string;
    created_at: Date;
    country?: string;
    device_type?: string;
    link_title?: string;
    short_code?: string;
    revenue?: number;
  }>;
  quick_stats: {
    avg_order_value: number;
    top_country: string;
    best_performing_day: string;
  };
}

export interface DashboardMetrics {
  current: {
    total_clicks: number;
    total_conversions: number;
    total_revenue: number;
    conversion_rate: number;
    avg_order_value: number;
  };
  comparison?: {
    total_clicks: number;
    total_conversions: number;
    total_revenue: number;
    conversion_rate: number;
    avg_order_value: number;
    change_percentage: {
      clicks: number;
      conversions: number;
      revenue: number;
      conversion_rate: number;
    };
  };
}

export interface DashboardAlert {
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  created_at: string;
  action_url?: string;
}

export class DashboardService {
  async getOverview(user: User, options: {
    dateFrom?: string;
    dateTo?: string;
    period?: string;
  } = {}): Promise<DashboardOverview> {
    try {
      const { dateFrom, dateTo, period = '30d' } = options;
      const tenantId = getUserTenantId(user);

      // Check cache first
      const cacheKey = `overview:${period}:${dateFrom || ''}:${dateTo || ''}`;
      const cached = await getCachedDashboardData<DashboardOverview>(tenantId, cacheKey);
      if (cached) {
        logger.debug("Returning cached dashboard overview", { tenantId, period });
        return cached;
      }

      // Calculate date range
      const endDate = dateTo ? new Date(dateTo) : new Date();
      const startDate = dateFrom ? new Date(dateFrom) : new Date(Date.now() - this.getPeriodMs(period));

      const dateFromStr = startDate.toISOString().split('T')[0];
      const dateToStr = endDate.toISOString().split('T')[0];

      // Fetch data from multiple services in parallel
      const [analyticsResponse, linksResponse, integrationsResponse] = await Promise.allSettled([
        analyticsService.get('/api/analytics/summary', {
          params: { tenant_id: tenantId, date_from: dateFromStr, date_to: dateToStr }
        }),
        linkTrackingService.get('/api/links', {
          params: { tenant_id: tenantId, limit: 1 }
        }),
        integrationService.get('/api/integrations', {
          params: { tenant_id: tenantId }
        })
      ]);

      // Process analytics data
      const analytics: AnalyticsData = analyticsResponse.status === 'fulfilled'
        ? (analyticsResponse.value.data as AnalyticsData)
        : { summary: {}, trend: [], platforms: [], geographic: [] };

      // Process links data
      const totalLinks = linksResponse.status === 'fulfilled'
        ? ((linksResponse.value.data as LinksData)?.total || 0)
        : 0;

      // Process integrations data
      const integrations: Integration[] = integrationsResponse.status === 'fulfilled'
        ? (Array.isArray((integrationsResponse.value.data as Integration[])) ? (integrationsResponse.value.data as Integration[]) : [])
        : [];

      // Get recent activity from database
      const recentActivity = await getRecentActivity(tenantId, 10);

      const overview: DashboardOverview = {
        period,
        summary: {
          total_clicks: analytics.summary?.total_clicks || 0,
          total_conversions: analytics.summary?.total_conversions || 0,
          total_revenue: analytics.summary?.total_revenue || 0,
          conversion_rate: analytics.summary?.conversion_rate || 0,
          total_links: totalLinks,
          active_integrations: integrations.filter((i: Integration) => i.is_active).length
        },
        trend: analytics.trend || [],
        platforms: analytics.platforms || [],
        recent_activity: recentActivity,
        quick_stats: {
          avg_order_value: analytics.summary?.avg_order_value || 0,
          top_country: this.getTopCountryFromTrend(analytics.geographic || []),
          best_performing_day: this.getBestPerformingDay(analytics.trend || [])
        }
      };

      // Cache the result
      await cacheDashboardData(tenantId, cacheKey, overview, 300); // 5 minutes

      return overview;
    } catch (error) {
      logger.error('Failed to get dashboard overview', {
        userId: user.id,
        tenantId: getUserTenantId(user),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  async getMetrics(user: User, options: {
    dateFrom?: string;
    dateTo?: string;
    comparePeriod?: boolean;
  } = {}): Promise<DashboardMetrics> {
    try {
      const { dateFrom, dateTo, comparePeriod = false } = options;
      const tenantId = getUserTenantId(user);

      // Get current period metrics
      const currentMetrics = await analyticsService.get('/api/analytics/summary', {
        params: { tenant_id: tenantId, date_from: dateFrom, date_to: dateTo }
      });

      let comparison: DashboardMetrics['comparison'] = undefined;
      if (comparePeriod && dateFrom && dateTo) {
        // Calculate previous period dates
        const startDate = new Date(dateFrom);
        const endDate = new Date(dateTo);
        const periodLength = endDate.getTime() - startDate.getTime();
        
        const prevEndDate = new Date(startDate.getTime() - 1);
        const prevStartDate = new Date(prevEndDate.getTime() - periodLength);

        try {
          const prevMetrics = await analyticsService.get('/api/analytics/summary', {
            params: { 
              tenant_id: tenantId, 
              date_from: prevStartDate.toISOString().split('T')[0], 
              date_to: prevEndDate.toISOString().split('T')[0] 
            }
          });

          const current = (currentMetrics.data as AnalyticsData).summary;
          const previous = (prevMetrics.data as AnalyticsData).summary;

          comparison = {
            total_clicks: previous?.total_clicks || 0,
            total_conversions: previous?.total_conversions || 0,
            total_revenue: previous?.total_revenue || 0,
            conversion_rate: previous?.conversion_rate || 0,
            avg_order_value: previous?.avg_order_value || 0,
            change_percentage: {
              clicks: this.calculatePercentageChange(previous?.total_clicks || 0, current?.total_clicks || 0),
              conversions: this.calculatePercentageChange(previous?.total_conversions || 0, current?.total_conversions || 0),
              revenue: this.calculatePercentageChange(previous?.total_revenue || 0, current?.total_revenue || 0),
              conversion_rate: this.calculatePercentageChange(previous?.conversion_rate || 0, current?.conversion_rate || 0),
            }
          };
        } catch (error) {
          logger.warn('Failed to get comparison metrics', { error });
        }
      }

      const summary = (currentMetrics.data as AnalyticsData).summary || {};
      return {
        current: {
          total_clicks: summary.total_clicks || 0,
          total_conversions: summary.total_conversions || 0,
          total_revenue: summary.total_revenue || 0,
          conversion_rate: summary.conversion_rate || 0,
          avg_order_value: summary.avg_order_value || 0,
        },
        comparison
      };
    } catch (error) {
      handleServiceError(error, 'Analytics');
    }
  }

  async getAlerts(user: User, options: { limit?: number } = {}): Promise<DashboardAlert[]> {
    try {
      const { limit = 5 } = options;
      const tenantId = getUserTenantId(user);
      
      // This is a simplified version - in production you'd have a more sophisticated alerting system
      const alerts: DashboardAlert[] = [];

      // Check for low-performing links
      const topLinks = await getTopLinks(tenantId, 'conversion_rate', 3);
      if (topLinks.length > 0 && topLinks[0].conversion_rate < 1) {
        alerts.push({
          type: 'warning',
          title: 'Low Conversion Rate',
          message: 'Your top link has a conversion rate below 1%',
          created_at: new Date().toISOString(),
          action_url: '/analytics'
        });
      }

      // Check for recent high-value conversions
      const recentActivity = await getRecentActivity(tenantId, 10);
      const highValueConversions = recentActivity.filter(
        activity => activity.type === 'conversion' && (activity.revenue || 0) > 100
      );

      if (highValueConversions.length > 0) {
        alerts.push({
          type: 'success',
          title: 'High-Value Conversion',
          message: `You have ${highValueConversions.length} high-value conversions today`,
          created_at: new Date().toISOString(),
          action_url: '/dashboard/activity'
        });
      }

      return alerts.slice(0, limit);
    } catch (error) {
      logger.error('Failed to get alerts', {
        userId: user.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  async getTopLinks(user: User, options: {
    metric?: 'clicks' | 'conversions' | 'conversion_rate';
    limit?: number;
  } = {}): Promise<TopLink[]> {
    try {
      const { metric = 'clicks', limit = 5 } = options;
      const tenantId = getUserTenantId(user);

      return await getTopLinks(tenantId, metric, limit);
    } catch (error) {
      logger.error('Failed to get top links', {
        userId: user.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  private getPeriodMs(period: string): number {
    const periods: Record<string, number> = {
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000,
    };
    return periods[period] || periods['30d'];
  }

  private getTopCountryFromTrend(geographic: Array<{country: string; clicks: number}>): string {
    if (!geographic.length) return 'N/A';
    return geographic.sort((a, b) => b.clicks - a.clicks)[0]?.country || 'N/A';
  }

  private getBestPerformingDay(trend: Array<{date: string; conversions: number}>): string {
    if (!trend.length) return 'N/A';
    return trend.sort((a, b) => b.conversions - a.conversions)[0]?.date || 'N/A';
  }

  private calculatePercentageChange(oldValue: number, newValue: number): number {
    if (oldValue === 0) return newValue > 0 ? 100 : 0;
    return ((newValue - oldValue) / oldValue) * 100;
  }
}
