import { Pool, Client } from "postgres";
import { getLogger } from "@std/log";
import config from "../config/index.ts";

const logger = getLogger();

let pool: Pool;

export async function initializeDatabase(): Promise<void> {
  try {
    // Create connection pool
    pool = new Pool({
      user: config.database.user,
      password: config.database.password,
      database: config.database.name,
      hostname: config.database.host,
      port: config.database.port,
      tls: config.database.ssl ? { enabled: true } : undefined,
    }, config.database.maxConnections);

    // Test connection
    const client = await pool.connect();
    try {
      await client.queryObject("SELECT 1");
    } finally {
      client.end();
    }

    logger.info("Database pool initialized successfully");
  } catch (error) {
    logger.error("Failed to initialize database:", error);
    throw error;
  }
}

export function getPool(): Pool {
  if (!pool) {
    throw new Error("Database pool not initialized. Call initializeDatabase() first.");
  }
  return pool;
}

export async function getClient(): Promise<Client> {
  return await getPool().connect();
}

// Multi-tenant query helper
export async function queryWithTenant<T = unknown>(
  query: string,
  tenantId: string,
  params: unknown[] = []
): Promise<T[]> {
  const client = await getClient();
  try {
    // Ensure tenant_id is included in the query
    const tenantParams = [tenantId, ...params];
    const result = await client.queryObject<T>(query, tenantParams);
    return result.rows;
  } finally {
    client.end();
  }
}

// Generic query helper
export async function query<T = unknown>(
  sql: string,
  params: unknown[] = []
): Promise<T[]> {
  const client = await getClient();
  try {
    const result = await client.queryObject<T>(sql, params);
    return result.rows;
  } finally {
    client.end();
  }
}

// Transaction helper
export async function transaction<T>(
  callback: (client: Client) => Promise<T>
): Promise<T> {
  const client = await getClient();
  try {
    await client.queryObject("BEGIN");
    const result = await callback(client);
    await client.queryObject("COMMIT");
    return result;
  } catch (error) {
    await client.queryObject("ROLLBACK");
    throw error;
  } finally {
    client.end();
  }
}

// Health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const client = await getClient();
    try {
      await client.queryObject("SELECT 1");
      return true;
    } finally {
      client.end();
    }
  } catch (error) {
    logger.error("Database health check failed:", error);
    return false;
  }
}

// Close pool
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    logger.info("Database pool closed");
  }
}

// Dashboard-specific database operations
export interface User {
  id: string;
  email: string;
  password_hash: string;
  role: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
  login_attempts: number;
  locked_until?: Date;
  tenant_id?: string;
}

export interface DashboardMetrics {
  total_clicks: number;
  total_conversions: number;
  total_revenue: number;
  conversion_rate: number;
  total_links: number;
  active_integrations: number;
  avg_order_value: number;
}

export interface ActivityRecord {
  type: 'click' | 'conversion';
  id: string;
  created_at: Date;
  country?: string;
  device_type?: string;
  link_title?: string;
  short_code?: string;
  revenue?: number;
}

// Get user by ID with tenant isolation
export async function getUserById(userId: string): Promise<User | null> {
  const users = await query<User>(
    "SELECT * FROM users WHERE id = $1 AND is_active = true",
    [userId]
  );
  return users[0] || null;
}

// Get user by email
export async function getUserByEmail(email: string): Promise<User | null> {
  const users = await query<User>(
    "SELECT * FROM users WHERE email = $1 AND is_active = true",
    [email]
  );
  return users[0] || null;
}

// Get recent activity for tenant
export async function getRecentActivity(
  tenantId: string,
  limit: number = 10
): Promise<ActivityRecord[]> {
  const activityQuery = `
    SELECT
      'click' as type,
      c.id,
      c.clicked_at as created_at,
      c.country,
      c.device_type,
      l.title as link_title,
      l.short_code,
      null as revenue
    FROM clicks c
    JOIN links l ON c.link_id = l.id
    WHERE l.tenant_id = $1

    UNION ALL

    SELECT
      'conversion' as type,
      a.id,
      a.created_at,
      c.country,
      c.device_type,
      l.title as link_title,
      l.short_code,
      o.total_amount as revenue
    FROM attributions a
    JOIN clicks c ON a.click_id = c.id
    JOIN links l ON a.link_id = l.id
    JOIN orders o ON a.order_id = o.id
    WHERE l.tenant_id = $1

    ORDER BY created_at DESC
    LIMIT $2
  `;

  return await query<ActivityRecord>(activityQuery, [tenantId, limit]);
}

// Get top performing links for tenant
export async function getTopLinks(
  tenantId: string,
  metric: 'clicks' | 'conversions' | 'conversion_rate' = 'clicks',
  limit: number = 5
): Promise<any[]> {
  let orderBy = "total_clicks DESC";
  
  if (metric === 'conversions') {
    orderBy = "total_conversions DESC";
  } else if (metric === 'conversion_rate') {
    orderBy = "conversion_rate DESC";
  }

  const topLinksQuery = `
    SELECT 
      l.id,
      l.title,
      l.short_code,
      l.original_url,
      COUNT(DISTINCT c.id) as total_clicks,
      COUNT(DISTINCT a.id) as total_conversions,
      CASE 
        WHEN COUNT(DISTINCT c.id) > 0 
        THEN (COUNT(DISTINCT a.id)::float / COUNT(DISTINCT c.id)::float) * 100 
        ELSE 0 
      END as conversion_rate,
      COALESCE(SUM(o.total_amount), 0) as total_revenue
    FROM links l
    LEFT JOIN clicks c ON l.id = c.link_id
    LEFT JOIN attributions a ON c.id = a.click_id
    LEFT JOIN orders o ON a.order_id = o.id
    WHERE l.tenant_id = $1
    GROUP BY l.id, l.title, l.short_code, l.original_url
    ORDER BY ${orderBy}
    LIMIT $2
  `;

  return await query(topLinksQuery, [tenantId, limit]);
}

// Update user login attempts
export async function updateUserLoginAttempts(
  userId: string,
  attempts: number,
  lockedUntil?: Date
): Promise<void> {
  await query(
    "UPDATE users SET login_attempts = $1, locked_until = $2, updated_at = NOW() WHERE id = $3",
    [attempts, lockedUntil, userId]
  );
}

// Update user last login
export async function updateUserLastLogin(userId: string): Promise<void> {
  await query(
    "UPDATE users SET last_login = NOW(), login_attempts = 0, locked_until = NULL, updated_at = NOW() WHERE id = $1",
    [userId]
  );
}
