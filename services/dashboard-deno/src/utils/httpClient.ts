import { getLogger } from "@std/log";
import config from "../config/index.ts";

const logger = getLogger();

export interface HttpClientOptions {
  baseUrl: string;
  timeout: number;
  headers?: Record<string, string>;
}

export interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: unknown;
  params?: Record<string, string | number | undefined>;
  timeout?: number;
}

export interface HttpResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

export class HttpClient {
  private baseUrl: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor(options: HttpClientOptions) {
    this.baseUrl = options.baseUrl.replace(/\/$/, ""); // Remove trailing slash
    this.timeout = options.timeout;
    this.defaultHeaders = {
      "Content-Type": "application/json",
      "User-Agent": "dashboard-api/1.0.0",
      ...options.headers,
    };
  }

  async request<T = unknown>(endpoint: string, options: RequestOptions = {}): Promise<HttpResponse<T>> {
    const {
      method = "GET",
      headers = {},
      body,
      params,
      timeout = this.timeout,
    } = options;

    // Build URL with query parameters
    const url = new URL(endpoint, this.baseUrl);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          url.searchParams.set(key, String(value));
        }
      });
    }

    // Prepare headers
    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers,
    };

    // Prepare body
    let requestBody: string | undefined;
    if (body && method !== "GET" && method !== "HEAD") {
      if (typeof body === "string") {
        requestBody = body;
      } else {
        requestBody = JSON.stringify(body);
      }
    }

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      logger.debug("HTTP request", {
        method,
        url: url.toString(),
        headers: requestHeaders,
        body: requestBody ? JSON.stringify(body) : undefined,
      });

      const response = await fetch(url.toString(), {
        method,
        headers: requestHeaders,
        body: requestBody,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Parse response body
      let responseData: T;
      const contentType = response.headers.get("content-type") || "";
      
      if (contentType.includes("application/json")) {
        responseData = await response.json() as T;
      } else {
        responseData = await response.text() as T;
      }

      logger.debug("HTTP response", {
        status: response.status,
        statusText: response.statusText,
        data: responseData,
      });

      // Check if response is successful
      if (!response.ok) {
        throw new HttpError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          responseData
        );
      }

      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof DOMException && error.name === "AbortError") {
        throw new HttpError(`Request timeout after ${timeout}ms`, 408);
      }
      
      if (error instanceof HttpError) {
        throw error;
      }
      
      throw new HttpError(
        `Request failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        0
      );
    }
  }

  get<T = unknown>(endpoint: string, options: Omit<RequestOptions, "method"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "GET" });
  }

  post<T = unknown>(endpoint: string, body?: unknown, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "POST", body });
  }

  put<T = unknown>(endpoint: string, body?: unknown, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "PUT", body });
  }

  patch<T = unknown>(endpoint: string, body?: unknown, options: Omit<RequestOptions, "method" | "body"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "PATCH", body });
  }

  delete<T = unknown>(endpoint: string, options: Omit<RequestOptions, "method"> = {}): Promise<HttpResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "DELETE" });
  }
}

export class HttpError extends Error {
  public status: number;
  public data?: unknown;

  constructor(message: string, status: number, data?: unknown) {
    super(message);
    this.name = "HttpError";
    this.status = status;
    this.data = data;
  }
}

// Create service clients
export const analyticsService = new HttpClient({
  baseUrl: config.services.analytics.baseUrl,
  timeout: config.services.analytics.timeout,
});

export const linkTrackingService = new HttpClient({
  baseUrl: config.services.linkTracking.baseUrl,
  timeout: config.services.linkTracking.timeout,
});

export const integrationService = new HttpClient({
  baseUrl: config.services.integration.baseUrl,
  timeout: config.services.integration.timeout,
});

// Helper function to handle service errors
export function handleServiceError(error: unknown, serviceName: string): never {
  if (error instanceof HttpError) {
    logger.error(`${serviceName} service error:`, {
      status: error.status,
      message: error.message,
      data: error.data,
    });
    
    // Map service errors to appropriate client errors
    if (error.status >= 500) {
      throw new Error(`${serviceName} service is temporarily unavailable`);
    } else if (error.status === 404) {
      throw new Error("Resource not found");
    } else if (error.status === 403) {
      throw new Error("Access denied");
    } else if (error.status === 401) {
      throw new Error("Authentication required");
    } else {
      throw new Error(`${serviceName} service error: ${error.message}`);
    }
  }
  
  logger.error(`${serviceName} service unexpected error:`, error);
  throw new Error(`${serviceName} service is temporarily unavailable`);
}
