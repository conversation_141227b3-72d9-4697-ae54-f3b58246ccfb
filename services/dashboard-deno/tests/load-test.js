import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 50 }, // Ramp up to 50 users
    { duration: '5m', target: 50 }, // Stay at 50 users
    { duration: '2m', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    http_req_failed: ['rate<0.05'],   // Error rate must be below 5%
    errors: ['rate<0.05'],            // Custom error rate must be below 5%
  },
};

// Base URL - will be overridden by environment variable in CI/CD
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'TestPass123!' },
  { email: '<EMAIL>', password: 'TestPass123!' },
  { email: '<EMAIL>', password: 'TestPass123!' },
];

// Helper function to get random test user
function getRandomUser() {
  return testUsers[Math.floor(Math.random() * testUsers.length)];
}

// Helper function to authenticate and get JWT token
function authenticate() {
  const user = getRandomUser();
  const loginPayload = JSON.stringify({
    email: user.email,
    password: user.password,
  });

  const loginResponse = http.post(`${BASE_URL}/api/auth/login`, loginPayload, {
    headers: { 'Content-Type': 'application/json' },
  });

  if (loginResponse.status === 200) {
    const responseBody = JSON.parse(loginResponse.body);
    return responseBody.data.token;
  }
  
  return null;
}

// Main test function
export default function () {
  // Test 1: Health check endpoints
  testHealthEndpoints();
  
  // Test 2: Authentication flow
  testAuthentication();
  
  // Test 3: Dashboard API endpoints (authenticated)
  testDashboardAPI();
  
  // Test 4: Concurrent requests
  testConcurrentRequests();
  
  sleep(1);
}

function testHealthEndpoints() {
  const endpoints = [
    '/health',
    '/ready',
    '/live',
    '/ping',
    '/metrics',
  ];

  endpoints.forEach(endpoint => {
    const response = http.get(`${BASE_URL}${endpoint}`);
    
    const success = check(response, {
      [`${endpoint} status is 200`]: (r) => r.status === 200,
      [`${endpoint} response time < 100ms`]: (r) => r.timings.duration < 100,
    });

    errorRate.add(!success);
    responseTime.add(response.timings.duration);
  });
}

function testAuthentication() {
  // Test login
  const user = getRandomUser();
  const loginPayload = JSON.stringify({
    email: user.email,
    password: user.password,
  });

  const loginResponse = http.post(`${BASE_URL}/api/auth/login`, loginPayload, {
    headers: { 'Content-Type': 'application/json' },
  });

  const loginSuccess = check(loginResponse, {
    'login status is 200': (r) => r.status === 200,
    'login response time < 200ms': (r) => r.timings.duration < 200,
    'login returns token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data && body.data.token;
      } catch (e) {
        return false;
      }
    },
  });

  errorRate.add(!loginSuccess);
  responseTime.add(loginResponse.timings.duration);
}

function testDashboardAPI() {
  // Get authentication token
  const token = authenticate();
  
  if (!token) {
    console.log('Failed to authenticate, skipping dashboard API tests');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  const dashboardEndpoints = [
    '/api/dashboard/overview',
    '/api/dashboard/metrics',
    '/api/dashboard/alerts',
    '/api/dashboard/activity',
    '/api/dashboard/top-links',
    '/api/users/profile',
    '/api/users/stats',
  ];

  dashboardEndpoints.forEach(endpoint => {
    const response = http.get(`${BASE_URL}${endpoint}`, { headers });
    
    const success = check(response, {
      [`${endpoint} status is 200`]: (r) => r.status === 200,
      [`${endpoint} response time < 300ms`]: (r) => r.timings.duration < 300,
      [`${endpoint} returns JSON`]: (r) => {
        try {
          JSON.parse(r.body);
          return true;
        } catch (e) {
          return false;
        }
      },
    });

    errorRate.add(!success);
    responseTime.add(response.timings.duration);
  });
}

function testConcurrentRequests() {
  const token = authenticate();
  
  if (!token) {
    return;
  }

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  // Simulate concurrent requests to different endpoints
  const requests = [
    ['GET', '/api/dashboard/overview'],
    ['GET', '/api/dashboard/metrics'],
    ['GET', '/api/dashboard/activity'],
    ['GET', '/api/users/profile'],
    ['GET', '/health'],
  ];

  const responses = http.batch(
    requests.map(([method, url]) => ({
      method,
      url: `${BASE_URL}${url}`,
      headers: url.startsWith('/api/') ? headers : {},
    }))
  );

  responses.forEach((response, index) => {
    const endpoint = requests[index][1];
    const success = check(response, {
      [`concurrent ${endpoint} status is 200`]: (r) => r.status === 200,
      [`concurrent ${endpoint} response time < 500ms`]: (r) => r.timings.duration < 500,
    });

    errorRate.add(!success);
    responseTime.add(response.timings.duration);
  });
}

// Setup function - runs once before the test
export function setup() {
  console.log('Starting load test for Dashboard Service Deno');
  console.log(`Base URL: ${BASE_URL}`);
  
  // Verify service is accessible
  const healthResponse = http.get(`${BASE_URL}/health`);
  if (healthResponse.status !== 200) {
    throw new Error(`Service not accessible: ${healthResponse.status}`);
  }
  
  console.log('Service is accessible, starting load test...');
}

// Teardown function - runs once after the test
export function teardown() {
  console.log('Load test completed');
}

// Handle summary - custom summary output
export function handleSummary(data) {
  return {
    'stdout': textSummary(data, { indent: ' ', enableColors: true }),
    'load-test-results.json': JSON.stringify(data),
  };
}

function textSummary(data, options = {}) {
  const indent = options.indent || '';
  const enableColors = options.enableColors || false;
  
  let summary = `${indent}Load Test Summary:\n`;
  summary += `${indent}==================\n`;
  summary += `${indent}Total Requests: ${data.metrics.http_reqs.count}\n`;
  summary += `${indent}Failed Requests: ${data.metrics.http_req_failed.count}\n`;
  summary += `${indent}Average Response Time: ${data.metrics.http_req_duration.avg.toFixed(2)}ms\n`;
  summary += `${indent}95th Percentile: ${data.metrics.http_req_duration['p(95)'].toFixed(2)}ms\n`;
  summary += `${indent}Requests/sec: ${data.metrics.http_reqs.rate.toFixed(2)}\n`;
  summary += `${indent}Error Rate: ${(data.metrics.http_req_failed.rate * 100).toFixed(2)}%\n`;
  
  // Check if thresholds passed
  const thresholdsPassed = Object.values(data.thresholds).every(t => t.ok);
  summary += `${indent}Thresholds: ${thresholdsPassed ? 'PASSED' : 'FAILED'}\n`;
  
  return summary;
}
