# Fresh Dashboard Dockerfile
FROM denoland/deno:1.38.3

# Set working directory
WORKDIR /app

# Copy dependency files
COPY deno.json deno.lock* ./

# Cache dependencies
RUN deno cache --reload deno.json

# Copy source code
COPY . .

# Cache the main application
RUN deno cache main.ts

# Create non-root user
RUN groupadd -r freshuser && useradd -r -g freshuser freshuser
RUN chown -R freshuser:freshuser /app
USER freshuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD deno run --allow-net --allow-env health_check.ts

# Start the application
CMD ["run", "-A", "main.ts"]
