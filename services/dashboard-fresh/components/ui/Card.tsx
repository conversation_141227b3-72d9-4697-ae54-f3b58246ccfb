import { ComponentChildren } from "preact";

interface CardProps {
  children: ComponentChildren;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  shadow?: 'none' | 'soft' | 'medium' | 'strong';
  border?: boolean;
  hover?: boolean;
}

interface CardHeaderProps {
  children: ComponentChildren;
  className?: string;
}

interface CardContentProps {
  children: ComponentChildren;
  className?: string;
}

interface CardFooterProps {
  children: ComponentChildren;
  className?: string;
}

const paddingClasses = {
  none: '',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
  xl: 'p-10',
};

const shadowClasses = {
  none: '',
  soft: 'shadow-soft dark:shadow-dark-soft',
  medium: 'shadow-medium dark:shadow-dark-medium',
  strong: 'shadow-strong',
};

export default function Card({
  children,
  className = '',
  padding = 'md',
  shadow = 'soft',
  border = true,
  hover = false,
}: CardProps) {
  const baseClasses = 'bg-white dark:bg-gray-800 rounded-lg transition-all duration-200';
  const borderClasses = border ? 'border border-gray-200 dark:border-gray-700' : '';
  const hoverClasses = hover ? 'hover:shadow-medium dark:hover:shadow-dark-medium hover:-translate-y-1' : '';

  return (
    <div
      class={`
        ${baseClasses}
        ${paddingClasses[padding]}
        ${shadowClasses[shadow]}
        ${borderClasses}
        ${hoverClasses}
        ${className}
      `}
    >
      {children}
    </div>
  );
}

export function CardHeader({
  children,
  className = '',
}: CardHeaderProps) {
  return (
    <div class={`border-b border-gray-200 dark:border-gray-700 pb-4 mb-6 ${className}`}>
      {children}
    </div>
  );
}

export function CardContent({
  children,
  className = '',
}: CardContentProps) {
  return (
    <div class={className}>
      {children}
    </div>
  );
}

export function CardFooter({
  children,
  className = '',
}: CardFooterProps) {
  return (
    <div class={`border-t border-gray-200 dark:border-gray-700 pt-4 mt-6 ${className}`}>
      {children}
    </div>
  );
}

// Specialized card variants
interface AuthCardProps {
  children: ComponentChildren;
  title?: string;
  subtitle?: string;
  className?: string;
}

export function AuthCard({
  children,
  title,
  subtitle,
  className = '',
}: AuthCardProps) {
  return (
    <Card 
      className={`max-w-md mx-auto ${className}`}
      shadow="medium"
      padding="lg"
    >
      {(title || subtitle) && (
        <CardHeader className="text-center border-b-0 pb-6">
          {title && (
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              {title}
            </h1>
          )}
          {subtitle && (
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {subtitle}
            </p>
          )}
        </CardHeader>
      )}
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
}

interface DashboardCardProps {
  children: ComponentChildren;
  title?: string;
  action?: ComponentChildren;
  className?: string;
  loading?: boolean;
}

export function DashboardCard({
  children,
  title,
  action,
  className = '',
  loading = false,
}: DashboardCardProps) {
  return (
    <Card className={className} hover>
      {(title || action) && (
        <CardHeader className="flex items-center justify-between">
          {title && (
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h3>
          )}
          {action && (
            <div class="flex-shrink-0">
              {action}
            </div>
          )}
        </CardHeader>
      )}
      <CardContent>
        {loading ? (
          <div class="animate-pulse space-y-4">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );
}

interface AlertCardProps {
  children: ComponentChildren;
  variant: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

export function AlertCard({
  children,
  variant,
  title,
  dismissible = false,
  onDismiss,
  className = '',
}: AlertCardProps) {
  const variantClasses = {
    info: 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800',
    success: 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800',
    warning: 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800',
    error: 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800',
  };

  const iconClasses = {
    info: 'text-blue-600 dark:text-blue-400',
    success: 'text-green-600 dark:text-green-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    error: 'text-red-600 dark:text-red-400',
  };

  const textClasses = {
    info: 'text-blue-800 dark:text-blue-200',
    success: 'text-green-800 dark:text-green-200',
    warning: 'text-yellow-800 dark:text-yellow-200',
    error: 'text-red-800 dark:text-red-200',
  };

  return (
    <Card
      className={`${variantClasses[variant]} ${className}`}
      border={true}
      shadow="none"
      padding="md"
    >
      <div class="flex items-start">
        <div class={`flex-shrink-0 ${iconClasses[variant]}`}>
          {variant === 'info' && (
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          )}
          {variant === 'success' && (
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          )}
          {variant === 'warning' && (
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          )}
          {variant === 'error' && (
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          )}
        </div>
        
        <div class="ml-3 flex-1">
          {title && (
            <h3 class={`text-sm font-medium ${textClasses[variant]} mb-1`}>
              {title}
            </h3>
          )}
          <div class={`text-sm ${textClasses[variant]}`}>
            {children}
          </div>
        </div>

        {dismissible && onDismiss && (
          <button
            type="button"
            onClick={onDismiss}
            class={`ml-3 flex-shrink-0 ${iconClasses[variant]} hover:opacity-75 focus:outline-none`}
            aria-label="Dismiss"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        )}
      </div>
    </Card>
  );
}
