import { ComponentChildren } from "preact";

type InputSize = 'sm' | 'md' | 'lg';
type InputVariant = 'default' | 'error' | 'success';

interface InputProps {
  id?: string;
  name?: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  value?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  autoComplete?: string;
  autoFocus?: boolean;
  size?: InputSize;
  variant?: InputVariant;
  className?: string;
  onInput?: (e: Event) => void;
  onChange?: (e: Event) => void;
  onFocus?: (e: Event) => void;
  onBlur?: (e: Event) => void;
  'aria-label'?: string;
  'aria-describedby'?: string;
  'data-testid'?: string;
}

interface InputGroupProps {
  label?: string;
  error?: string;
  hint?: string;
  required?: boolean;
  children: ComponentChildren;
  className?: string;
}

const sizeClasses = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-3 py-2 text-sm',
  lg: 'px-4 py-3 text-base',
};

const variantClasses = {
  default: 'border-gray-300 focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:focus:border-primary-400 dark:focus:ring-primary-400',
  error: 'border-error-300 focus:border-error-500 focus:ring-error-500 dark:border-error-600 dark:focus:border-error-400 dark:focus:ring-error-400',
  success: 'border-success-300 focus:border-success-500 focus:ring-success-500 dark:border-success-600 dark:focus:border-success-400 dark:focus:ring-success-400',
};

export default function Input({
  id,
  name,
  type = 'text',
  value,
  placeholder,
  disabled = false,
  required = false,
  autoComplete,
  autoFocus = false,
  size = 'md',
  variant = 'default',
  className = '',
  onInput,
  onChange,
  onFocus,
  onBlur,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
  'data-testid': dataTestId,
}: InputProps) {
  const baseClasses = 'block w-full rounded-lg border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed';

  return (
    <input
      id={id}
      name={name}
      type={type}
      value={value}
      placeholder={placeholder}
      disabled={disabled}
      required={required}
      autoComplete={autoComplete}
      autoFocus={autoFocus}
      onInput={onInput}
      onChange={onChange}
      onFocus={onFocus}
      onBlur={onBlur}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      data-testid={dataTestId}
      class={`
        ${baseClasses}
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${className}
      `}
    />
  );
}

export function InputGroup({
  label,
  error,
  hint,
  required = false,
  children,
  className = '',
}: InputGroupProps) {
  const inputId = `input-${Math.random().toString(36).substr(2, 9)}`;
  const errorId = error ? `${inputId}-error` : undefined;
  const hintId = hint ? `${inputId}-hint` : undefined;
  const describedBy = [errorId, hintId].filter(Boolean).join(' ');

  return (
    <div class={`space-y-2 ${className}`}>
      {label && (
        <label
          htmlFor={inputId}
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          {label}
          {required && (
            <span class="text-error-500 ml-1" aria-label="required">*</span>
          )}
        </label>
      )}

      <div class="relative">
        {children}
      </div>

      {hint && !error && (
        <p
          id={hintId}
          class="text-xs text-gray-500 dark:text-gray-400"
        >
          {hint}
        </p>
      )}

      {error && (
        <p
          id={errorId}
          class="text-xs text-error-600 dark:text-error-400 flex items-center"
          role="alert"
        >
          <svg
            class="w-4 h-4 mr-1 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
}

// Password input with visibility toggle
interface PasswordInputProps extends Omit<InputProps, 'type'> {
  showPassword?: boolean;
  onToggleVisibility?: () => void;
}

export function PasswordInput({
  showPassword = false,
  onToggleVisibility,
  className = '',
  ...props
}: PasswordInputProps) {
  return (
    <div class="relative">
      <Input
        {...props}
        type={showPassword ? 'text' : 'password'}
        className={`pr-10 ${className}`}
      />
      {onToggleVisibility && (
        <button
          type="button"
          onClick={onToggleVisibility}
          class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:text-gray-600 dark:focus:text-gray-300"
          aria-label={showPassword ? "Hide password" : "Show password"}
        >
          {showPassword ? (
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
            </svg>
          ) : (
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          )}
        </button>
      )}
    </div>
  );
}
