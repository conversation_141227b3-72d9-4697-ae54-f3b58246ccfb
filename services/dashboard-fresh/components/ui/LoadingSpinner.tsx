import { ComponentChildren } from "preact";

type SpinnerSize = 'sm' | 'md' | 'lg' | 'xl';
type SpinnerVariant = 'primary' | 'white' | 'gray';

interface LoadingSpinnerProps {
  size?: SpinnerSize;
  variant?: SpinnerVariant;
  className?: string;
}

interface LoadingStateProps {
  loading: boolean;
  children: ComponentChildren;
  spinner?: LoadingSpinnerProps;
  overlay?: boolean;
  message?: string;
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
};

const variantClasses = {
  primary: 'text-primary-600 dark:text-primary-400',
  white: 'text-white',
  gray: 'text-gray-400 dark:text-gray-500',
};

export function LoadingSpinner({
  size = 'md',
  variant = 'primary',
  className = ''
}: LoadingSpinnerProps) {
  return (
    <svg
      class={`animate-spin ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      />
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
}

export function LoadingState({
  loading,
  children,
  spinner = {},
  overlay = false,
  message
}: LoadingStateProps) {
  if (!loading) {
    return <>{children}</>;
  }

  if (overlay) {
    return (
      <div class="relative">
        {children}
        <div class="absolute inset-0 bg-white/75 dark:bg-gray-900/75 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
          <div class="flex flex-col items-center space-y-3">
            <LoadingSpinner {...spinner} />
            {message && (
              <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">
                {message}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div class="flex items-center justify-center py-12">
      <div class="flex flex-col items-center space-y-3">
        <LoadingSpinner {...spinner} />
        {message && (
          <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">
            {message}
          </p>
        )}
      </div>
    </div>
  );
}

// Skeleton loading components
export function SkeletonCard() {
  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft dark:shadow-dark-soft p-6 border border-gray-200 dark:border-gray-700">
      <div class="animate-pulse">
        <div class="flex items-center justify-between mb-4">
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
        </div>
        <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
        <div class="space-y-2">
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        </div>
      </div>
    </div>
  );
}

export function SkeletonTable({ rows = 5, columns = 4 }: { rows?: number; columns?: number }) {
  return (
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft dark:shadow-dark-soft border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="animate-pulse">
        {/* Header */}
        <div class="bg-gray-50 dark:bg-gray-900 px-6 py-3 border-b border-gray-200 dark:border-gray-700">
          <div class="flex space-x-4">
            {[...Array(columns)].map((_, i) => (
              <div key={i} class="h-4 bg-gray-200 dark:bg-gray-700 rounded flex-1"></div>
            ))}
          </div>
        </div>
        
        {/* Rows */}
        <div class="divide-y divide-gray-200 dark:divide-gray-700">
          {[...Array(rows)].map((_, i) => (
            <div key={i} class="px-6 py-4">
              <div class="flex space-x-4">
                {[...Array(columns)].map((_, j) => (
                  <div key={j} class="h-4 bg-gray-200 dark:bg-gray-700 rounded flex-1"></div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default LoadingSpinner;
