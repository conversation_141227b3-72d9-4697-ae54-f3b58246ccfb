import { ComponentChildren } from "preact";

type BadgeVariant = 'success' | 'warning' | 'error' | 'info' | 'neutral';
type BadgeSize = 'sm' | 'md' | 'lg';

interface StatusBadgeProps {
  variant: BadgeVariant;
  size?: BadgeSize;
  children: ComponentChildren;
  icon?: ComponentChildren;
  pulse?: boolean;
  className?: string;
}

const variantClasses = {
  success: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800',
  warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800',
  error: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border-red-200 dark:border-red-800',
  info: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border-blue-200 dark:border-blue-800',
  neutral: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700',
};

const sizeClasses = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-2.5 py-1.5 text-sm',
  lg: 'px-3 py-2 text-base',
};

const pulseClasses = {
  success: 'animate-pulse bg-green-400',
  warning: 'animate-pulse bg-yellow-400',
  error: 'animate-pulse bg-red-400',
  info: 'animate-pulse bg-blue-400',
  neutral: 'animate-pulse bg-gray-400',
};

export default function StatusBadge({
  variant,
  size = 'md',
  children,
  icon,
  pulse = false,
  className = ''
}: StatusBadgeProps) {
  return (
    <span
      class={`
        inline-flex items-center font-medium rounded-full border transition-colors
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${className}
      `}
    >
      {pulse && (
        <span class={`flex h-2 w-2 mr-2`}>
          <span class={`animate-ping absolute inline-flex h-2 w-2 rounded-full opacity-75 ${pulseClasses[variant]}`}></span>
          <span class={`relative inline-flex rounded-full h-2 w-2 ${pulseClasses[variant]}`}></span>
        </span>
      )}
      {icon && !pulse && (
        <span class="mr-1.5 flex-shrink-0">
          {icon}
        </span>
      )}
      {children}
    </span>
  );
}

// Predefined status badges for common use cases
export function OnlineBadge({ size = 'md' }: { size?: BadgeSize }) {
  return (
    <StatusBadge variant="success" size={size} pulse>
      Online
    </StatusBadge>
  );
}

export function OfflineBadge({ size = 'md' }: { size?: BadgeSize }) {
  return (
    <StatusBadge variant="error" size={size}>
      Offline
    </StatusBadge>
  );
}

export function PendingBadge({ size = 'md' }: { size?: BadgeSize }) {
  return (
    <StatusBadge variant="warning" size={size} pulse>
      Pending
    </StatusBadge>
  );
}

export function ActiveBadge({ size = 'md' }: { size?: BadgeSize }) {
  return (
    <StatusBadge variant="success" size={size}>
      Active
    </StatusBadge>
  );
}

export function InactiveBadge({ size = 'md' }: { size?: BadgeSize }) {
  return (
    <StatusBadge variant="neutral" size={size}>
      Inactive
    </StatusBadge>
  );
}
