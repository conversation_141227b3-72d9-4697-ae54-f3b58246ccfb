version: '3.8'

services:
  dashboard-fresh:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DENO_ENV=production
      - DATABASE_URL=********************************************/ecommerce_analytics
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET:-your-secret-key}
      - ANALYTICS_API_URL=http://analytics-service:3002
      - INTEGRATION_API_URL=http://integration-service:3003
      - BILLING_API_URL=http://billing-service:3004
    depends_on:
      - postgres
      - redis
    networks:
      - dashboard-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: timescale/timescaledb:latest-pg14
    environment:
      - POSTGRES_DB=ecommerce_analytics
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - dashboard-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - dashboard-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Mock services for development
  analytics-service:
    image: nginx:alpine
    ports:
      - "3002:80"
    volumes:
      - ./mock-services/analytics:/usr/share/nginx/html
    networks:
      - dashboard-network

  integration-service:
    image: nginx:alpine
    ports:
      - "3003:80"
    volumes:
      - ./mock-services/integration:/usr/share/nginx/html
    networks:
      - dashboard-network

  billing-service:
    image: nginx:alpine
    ports:
      - "3004:80"
    volumes:
      - ./mock-services/billing:/usr/share/nginx/html
    networks:
      - dashboard-network

volumes:
  postgres_data:
  redis_data:

networks:
  dashboard-network:
    driver: bridge
