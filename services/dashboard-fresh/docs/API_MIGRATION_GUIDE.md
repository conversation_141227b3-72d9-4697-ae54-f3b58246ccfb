# API Migration Guide: React to Fresh

## 🔄 **Migration Overview**

This guide covers migrating API integration patterns from the React frontend to Fresh, maintaining compatibility with existing Deno backend services while leveraging Fresh's server-side capabilities.

## 📊 **Current React API Patterns**

### **React Query + Axios Pattern**
```tsx
// frontend/src/services/api.ts
import axios from 'axios';
import { useQuery } from '@tanstack/react-query';

const dashboardApi = axios.create({
  baseURL: 'http://localhost:3000',
  headers: { 'Content-Type': 'application/json' }
});

export const dashboardService = {
  getOverview: async (params) => {
    const { data } = await dashboardApi.get('/api/dashboard/overview', { params });
    return data;
  }
};

// Component usage
function DashboardPage() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['dashboard', 'overview'],
    queryFn: () => dashboardService.getOverview()
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return <div>{/* Render data */}</div>;
}
```

## 🍃 **Fresh API Patterns**

### **1. Server-Side Data Fetching (Recommended)**

#### **Route with Server-Side Data**
```tsx
// routes/dashboard/index.tsx
import { defineRoute } from "$fresh/server.ts";
import { dashboardService } from "../../services/dashboardService.ts";

export default defineRoute(async (req, ctx) => {
  const user = ctx.state.user;
  
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch data server-side
    const overview = await dashboardService.getOverview(user, {
      period: "30d"
    });

    return (
      <div class="dashboard-page">
        <h1>Dashboard Overview</h1>
        <DashboardMetrics data={overview} />
        <RealtimeUpdates initialData={overview} />
      </div>
    );
  } catch (error) {
    console.error("Dashboard error:", error);
    return (
      <div class="error-page">
        <h1>Error Loading Dashboard</h1>
        <p>Please try again later.</p>
      </div>
    );
  }
});
```

#### **Service Layer (Server-Side)**
```tsx
// services/dashboardService.ts
import { User } from "../types/auth.ts";
import { getCachedDashboardData, cacheDashboardData } from "../utils/redis.ts";

export const dashboardService = {
  async getOverview(user: User, params: { period?: string } = {}) {
    const tenantId = user.tenant_id || user.id;
    const cacheKey = `overview:${tenantId}:${params.period || '30d'}`;
    
    // Try cache first
    const cached = await getCachedDashboardData(tenantId, "overview");
    if (cached) return cached;

    // Fetch from database
    const overview = await this.fetchOverviewFromDB(tenantId, params);
    
    // Cache result
    await cacheDashboardData(tenantId, "overview", overview, 300); // 5 min cache
    
    return overview;
  },

  async fetchOverviewFromDB(tenantId: string, params: any) {
    // Database query logic here
    return {
      totalRevenue: 125000,
      activeUsers: 8420,
      conversionRate: 3.2,
      // ... more metrics
    };
  }
};
```

### **2. Fresh API Routes (Client-Side Fetching)**

#### **API Route Definition**
```tsx
// routes/api/dashboard/metrics.ts
import { HandlerContext } from "$fresh/server.ts";
import { dashboardService } from "../../../services/dashboardService.ts";

export const handler = {
  async GET(req: Request, ctx: HandlerContext) {
    const user = ctx.state.user;
    
    if (!user) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    try {
      const url = new URL(req.url);
      const dateFrom = url.searchParams.get("date_from");
      const dateTo = url.searchParams.get("date_to");
      const comparePeriod = url.searchParams.get("compare_period") === "true";

      const metrics = await dashboardService.getMetrics(user, {
        dateFrom,
        dateTo,
        comparePeriod,
      });

      return Response.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      console.error("Metrics API error:", error);
      return Response.json(
        { success: false, error: "Failed to fetch metrics" },
        { status: 500 }
      );
    }
  },

  async POST(req: Request, ctx: HandlerContext) {
    const user = ctx.state.user;
    
    if (!user) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
      const body = await req.json();
      const result = await dashboardService.updateMetrics(user, body);
      
      return Response.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error("Update metrics error:", error);
      return Response.json(
        { success: false, error: "Failed to update metrics" },
        { status: 500 }
      );
    }
  },
};
```

#### **Island with Client-Side Fetching**
```tsx
// islands/dashboard/RealtimeMetrics.tsx
import { useSignal, useComputed } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { IS_BROWSER } from "$fresh/runtime.ts";

interface MetricsData {
  totalRevenue: number;
  activeUsers: number;
  conversionRate: number;
}

interface RealtimeMetricsProps {
  initialData: MetricsData;
}

export default function RealtimeMetrics({ initialData }: RealtimeMetricsProps) {
  const metrics = useSignal<MetricsData>(initialData);
  const isLoading = useSignal(false);
  const error = useSignal<string | null>(null);

  // Computed values
  const formattedRevenue = useComputed(() => 
    new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(metrics.value.totalRevenue)
  );

  // Fetch updated metrics
  const fetchMetrics = async () => {
    if (!IS_BROWSER) return;
    
    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/dashboard/metrics');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        metrics.value = result.data;
      } else {
        throw new Error(result.error || 'Failed to fetch metrics');
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error';
      console.error('Fetch metrics error:', err);
    } finally {
      isLoading.value = false;
    }
  };

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (!IS_BROWSER) return;

    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div class="realtime-metrics p-6 bg-white rounded-lg shadow">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Live Metrics</h2>
        <button
          onClick={fetchMetrics}
          disabled={isLoading.value}
          class={`px-3 py-1 rounded text-sm ${
            isLoading.value 
              ? 'bg-gray-300 cursor-not-allowed' 
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
        >
          {isLoading.value ? 'Updating...' : 'Refresh'}
        </button>
      </div>

      {error.value && (
        <div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          Error: {error.value}
        </div>
      )}

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="metric-card p-4 bg-gray-50 rounded">
          <h3 class="text-sm font-medium text-gray-600">Total Revenue</h3>
          <p class="text-2xl font-bold text-green-600">{formattedRevenue.value}</p>
        </div>
        
        <div class="metric-card p-4 bg-gray-50 rounded">
          <h3 class="text-sm font-medium text-gray-600">Active Users</h3>
          <p class="text-2xl font-bold text-blue-600">
            {metrics.value.activeUsers.toLocaleString()}
          </p>
        </div>
        
        <div class="metric-card p-4 bg-gray-50 rounded">
          <h3 class="text-sm font-medium text-gray-600">Conversion Rate</h3>
          <p class="text-2xl font-bold text-purple-600">
            {metrics.value.conversionRate.toFixed(2)}%
          </p>
        </div>
      </div>
    </div>
  );
}
```

### **3. Proxy Pattern for External APIs**

#### **Proxy to Existing Deno Services**
```tsx
// routes/api/analytics/[...path].ts
import { HandlerContext } from "$fresh/server.ts";

const ANALYTICS_API_URL = Deno.env.get("ANALYTICS_API_URL") || "http://localhost:3002";

export const handler = {
  async GET(req: Request, ctx: HandlerContext) {
    return proxyRequest(req, ctx, "GET");
  },
  
  async POST(req: Request, ctx: HandlerContext) {
    return proxyRequest(req, ctx, "POST");
  },
  
  async PUT(req: Request, ctx: HandlerContext) {
    return proxyRequest(req, ctx, "PUT");
  },
  
  async DELETE(req: Request, ctx: HandlerContext) {
    return proxyRequest(req, ctx, "DELETE");
  },
};

async function proxyRequest(req: Request, ctx: HandlerContext, method: string) {
  const user = ctx.state.user;
  
  if (!user) {
    return Response.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const url = new URL(req.url);
    const path = ctx.params.path;
    const targetUrl = `${ANALYTICS_API_URL}/api/analytics/${path}${url.search}`;

    const headers = new Headers();
    headers.set("Authorization", req.headers.get("Authorization") || "");
    headers.set("Content-Type", "application/json");
    headers.set("X-Tenant-ID", user.tenant_id || user.id);

    const proxyReq: RequestInit = {
      method,
      headers,
    };

    // Include body for POST/PUT requests
    if (method === "POST" || method === "PUT") {
      proxyReq.body = await req.text();
    }

    const response = await fetch(targetUrl, proxyReq);
    
    // Forward the response
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });
  } catch (error) {
    console.error("Proxy error:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
```

### **4. Real-time Data with Server-Sent Events**

#### **SSE API Route**
```tsx
// routes/api/realtime/metrics.ts
import { HandlerContext } from "$fresh/server.ts";

export const handler = {
  async GET(req: Request, ctx: HandlerContext) {
    const user = ctx.state.user;
    
    if (!user) {
      return new Response("Unauthorized", { status: 401 });
    }

    const stream = new ReadableStream({
      start(controller) {
        // Send initial data
        const data = JSON.stringify({
          timestamp: new Date().toISOString(),
          activeUsers: Math.floor(Math.random() * 1000),
          revenue: Math.floor(Math.random() * 10000),
        });
        
        controller.enqueue(`data: ${data}\n\n`);

        // Send updates every 5 seconds
        const interval = setInterval(() => {
          const data = JSON.stringify({
            timestamp: new Date().toISOString(),
            activeUsers: Math.floor(Math.random() * 1000),
            revenue: Math.floor(Math.random() * 10000),
          });
          
          controller.enqueue(`data: ${data}\n\n`);
        }, 5000);

        // Cleanup on close
        req.signal.addEventListener("abort", () => {
          clearInterval(interval);
          controller.close();
        });
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
      },
    });
  },
};
```

#### **SSE Consumer Island**
```tsx
// islands/realtime/LiveUpdates.tsx
import { useSignal } from "@preact/signals";
import { useEffect } from "preact/hooks";
import { IS_BROWSER } from "$fresh/runtime.ts";

export default function LiveUpdates() {
  const data = useSignal<any>({});
  const connected = useSignal(false);

  useEffect(() => {
    if (!IS_BROWSER) return;

    const eventSource = new EventSource('/api/realtime/metrics');
    
    eventSource.onopen = () => {
      connected.value = true;
    };
    
    eventSource.onmessage = (event) => {
      try {
        const newData = JSON.parse(event.data);
        data.value = newData;
      } catch (error) {
        console.error('Failed to parse SSE data:', error);
      }
    };
    
    eventSource.onerror = () => {
      connected.value = false;
    };

    return () => {
      eventSource.close();
    };
  }, []);

  return (
    <div class="live-updates">
      <div class="flex items-center gap-2 mb-4">
        <div class={`w-3 h-3 rounded-full ${connected.value ? 'bg-green-500' : 'bg-red-500'}`}></div>
        <span class="text-sm text-gray-600">
          {connected.value ? 'Connected' : 'Disconnected'}
        </span>
      </div>
      
      {data.value.timestamp && (
        <div class="space-y-2">
          <p>Last Update: {new Date(data.value.timestamp).toLocaleTimeString()}</p>
          <p>Active Users: {data.value.activeUsers}</p>
          <p>Revenue: ${data.value.revenue}</p>
        </div>
      )}
    </div>
  );
}
```

## 🔄 **Migration Checklist**

### **API Patterns to Migrate**
- [ ] Dashboard overview API
- [ ] Analytics data fetching
- [ ] Real-time metrics
- [ ] User authentication
- [ ] Settings management
- [ ] Export functionality
- [ ] Integration management

### **Fresh Implementation Choices**
- [ ] Server-side data fetching for static content
- [ ] Fresh API routes for dynamic data
- [ ] Proxy routes for external services
- [ ] SSE for real-time updates
- [ ] WebSocket for bidirectional communication

### **Performance Optimizations**
- [ ] Redis caching for frequently accessed data
- [ ] Database query optimization
- [ ] Response compression
- [ ] CDN for static assets
- [ ] Edge deployment for global performance

## 📈 **Performance Benefits**

### **Server-Side Rendering**
- Faster initial page loads
- Better SEO
- Improved mobile performance
- Reduced client-side JavaScript

### **Edge Computing**
- Global content delivery
- Reduced latency
- Better user experience
- Automatic scaling

### **Caching Strategy**
- Redis for application data
- CDN for static assets
- Browser caching for resources
- Database query optimization

---

**Migration Timeline**: 2-3 weeks for API layer  
**Complexity**: Medium  
**Risk Level**: Low (maintains existing contracts)
