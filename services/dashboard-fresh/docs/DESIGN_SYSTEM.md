# Fresh Frontend Design System
## E-commerce Analytics SaaS - Comprehensive Design Guidelines

This document outlines the complete design system for the e-commerce analytics dashboard, built with Tailwind CSS and Fresh framework. **Updated with Login Page Enhancement Implementation.**

### 🎯 **Recent Updates**
- ✅ Enhanced UI Components (Button, Input, Card)
- ✅ Login Page Styling Implementation
- ✅ Dark Mode Integration
- ✅ Responsive Design Patterns
- ✅ Accessibility Compliance (WCAG 2.1 AA)
- ✅ Animation System
- ✅ Component Testing Framework

## 🎨 Color Palette

### Primary Colors
- **Primary 50**: `#eff6ff` - Light backgrounds, subtle highlights
- **Primary 100**: `#dbeafe` - Hover states, light accents
- **Primary 500**: `#3b82f6` - Primary actions, links, focus states
- **Primary 600**: `#2563eb` - Primary button hover, active states
- **Primary 900**: `#1e3a8a` - Dark primary, high contrast text

### Secondary Colors
- **Secondary 50**: `#f8fafc` - Light backgrounds
- **Secondary 100**: `#f1f5f9` - Card backgrounds, subtle borders
- **Secondary 500**: `#64748b` - Secondary text, icons
- **Secondary 600**: `#475569` - Secondary button hover
- **Secondary 900**: `#0f172a` - Dark text, headers

### Status Colors
- **Success**: `#10b981` (green-500) - Success states, positive metrics
- **Warning**: `#f59e0b` (yellow-500) - Warning states, attention needed
- **Error**: `#ef4444` (red-500) - Error states, negative metrics
- **Info**: `#3b82f6` (blue-500) - Information, neutral states

### Dark Mode Colors
- **Background**: `#111827` (gray-900) - Main background
- **Surface**: `#1f2937` (gray-800) - Card backgrounds
- **Border**: `#374151` (gray-700) - Borders, dividers
- **Text Primary**: `#f9fafb` (gray-50) - Primary text
- **Text Secondary**: `#d1d5db` (gray-300) - Secondary text

## 📏 Typography

### Font Families
- **Primary**: Inter (system font stack)
- **Monospace**: 'Fira Code', 'Monaco', monospace

### Font Sizes
- **xs**: 0.75rem (12px) - Small labels, captions
- **sm**: 0.875rem (14px) - Body text, form inputs
- **base**: 1rem (16px) - Default body text
- **lg**: 1.125rem (18px) - Large body text, small headings
- **xl**: 1.25rem (20px) - Card titles, section headers
- **2xl**: 1.5rem (24px) - Page titles, main headings
- **3xl**: 1.875rem (30px) - Hero text, large metrics

### Font Weights
- **normal**: 400 - Body text
- **medium**: 500 - Emphasized text, labels
- **semibold**: 600 - Headings, important text
- **bold**: 700 - Strong emphasis, metrics

## 📐 Spacing & Layout

### Spacing Scale
- **1**: 0.25rem (4px) - Tight spacing
- **2**: 0.5rem (8px) - Small spacing
- **3**: 0.75rem (12px) - Medium-small spacing
- **4**: 1rem (16px) - Default spacing
- **6**: 1.5rem (24px) - Large spacing
- **8**: 2rem (32px) - Extra large spacing
- **12**: 3rem (48px) - Section spacing

### Container Widths
- **max-w-7xl**: 80rem (1280px) - Main content container
- **max-w-4xl**: 56rem (896px) - Form containers
- **max-w-sm**: 24rem (384px) - Narrow content

### Grid System
- **12-column grid** for desktop layouts
- **6-column grid** for tablet layouts
- **4-column grid** for mobile layouts

## 🎯 Component Patterns

### Cards
```tsx
// Basic Card
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft dark:shadow-dark-soft p-6 border border-gray-200 dark:border-gray-700">
  {/* Content */}
</div>

// Interactive Card
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft dark:shadow-dark-soft p-6 border border-gray-200 dark:border-gray-700 hover:shadow-medium dark:hover:shadow-dark-medium transition-all duration-200 cursor-pointer">
  {/* Content */}
</div>
```

### Buttons
```tsx
// Primary Button
<button class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
  Primary Action
</button>

// Secondary Button
<button class="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white font-medium py-2 px-4 rounded-lg transition-colors">
  Secondary Action
</button>
```

### Form Inputs
```tsx
// Text Input
<input class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors" />

// Select
<select class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
  <option>Option 1</option>
</select>
```

### Status Badges
```tsx
// Success Badge
<span class="inline-flex items-center px-2.5 py-1.5 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
  Active
</span>

// Warning Badge
<span class="inline-flex items-center px-2.5 py-1.5 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
  Pending
</span>
```

## 📱 Responsive Design

### Breakpoints
- **xs**: 475px - Extra small devices
- **sm**: 640px - Small devices (phones)
- **md**: 768px - Medium devices (tablets)
- **lg**: 1024px - Large devices (laptops)
- **xl**: 1280px - Extra large devices (desktops)
- **2xl**: 1536px - 2X large devices (large desktops)

### Mobile-First Approach
```tsx
// Mobile-first responsive classes
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
  {/* Responsive grid */}
</div>

// Responsive text sizes
<h1 class="text-2xl md:text-3xl lg:text-4xl font-bold">
  Responsive Heading
</h1>
```

## 🌙 Dark Mode Implementation

### Theme Toggle
- Class-based dark mode using `dark:` prefix
- Persistent theme preference in localStorage
- System preference detection
- Smooth transitions between themes

### Dark Mode Colors
```tsx
// Background colors
bg-white dark:bg-gray-800
bg-gray-50 dark:bg-gray-900

// Text colors
text-gray-900 dark:text-white
text-gray-600 dark:text-gray-300
text-gray-500 dark:text-gray-400

// Border colors
border-gray-200 dark:border-gray-700
border-gray-300 dark:border-gray-600
```

## 📊 Data Visualization

### Chart Containers
```tsx
// Chart wrapper with dark mode support
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft dark:shadow-dark-soft p-6 border border-gray-200 dark:border-gray-700">
  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Chart Title</h3>
  <svg class="w-full h-64 border border-gray-200 dark:border-gray-700 rounded bg-white dark:bg-gray-800">
    {/* D3.js chart content */}
  </svg>
</div>
```

### Chart Color Palette
- **Primary Data**: `#3b82f6` (blue-500)
- **Secondary Data**: `#10b981` (green-500)
- **Tertiary Data**: `#f59e0b` (yellow-500)
- **Quaternary Data**: `#ef4444` (red-500)
- **Neutral Data**: `#6b7280` (gray-500)

## ♿ Accessibility

### Focus States
- Visible focus rings on all interactive elements
- High contrast focus indicators
- Keyboard navigation support

### Color Contrast
- WCAG AA compliance (4.5:1 ratio minimum)
- High contrast mode support
- Color-blind friendly palette

### Screen Reader Support
- Semantic HTML structure
- ARIA labels and descriptions
- Skip navigation links

## 🎭 Animation & Transitions

### Transition Classes
```tsx
// Standard transitions
transition-colors duration-200
transition-all duration-200
transition-opacity duration-150

// Hover effects
hover:shadow-medium hover:scale-105
hover:bg-gray-100 dark:hover:bg-gray-700
```

### Loading States
- Skeleton loading animations
- Pulse effects for loading content
- Smooth state transitions

## 📋 Component Library

### Available Components
- **KPIScorecard**: Metric display cards with trends
- **DataTable**: Sortable, responsive data tables
- **StatusBadge**: Status indicators with variants
- **LoadingSpinner**: Loading states and overlays
- **DarkModeToggle**: Theme switching component
- **Navigation**: Main navigation with user menu
- **Sidebar**: Collapsible sidebar navigation

### Usage Examples
See individual component files in `/components/ui/` and `/islands/` directories for detailed usage examples and props documentation.

## 🔧 Customization

### Extending the Design System
1. Add new colors to `tailwind.config.ts`
2. Create new component variants
3. Update this documentation
4. Test in both light and dark modes
5. Ensure accessibility compliance

### Best Practices
- Use semantic color names (primary, secondary, success, etc.)
- Maintain consistent spacing using the spacing scale
- Test all components in both light and dark modes
- Follow mobile-first responsive design principles
- Ensure proper contrast ratios for accessibility
