# Fresh Dashboard Development Setup

## 🚀 **Quick Start**

### **Prerequisites**
- Deno 2.4+ installed
- PostgreSQL with TimescaleDB
- Redis 6+
- Git

### **Installation**
```bash
# Clone repository
git clone <repository-url>
cd ecommerce-analytics-saas/services/dashboard-fresh

# Install Deno (if not installed)
curl -fsSL https://deno.land/install.sh | sh

# Start development server
deno task dev
```

## 🏗️ **Project Structure**

```
services/dashboard-fresh/
├── deno.json                 # Deno configuration & dependencies
├── fresh.gen.ts             # Auto-generated manifest (don't edit)
├── main.ts                  # Application entry point
├── routes/                  # File-based routing
│   ├── _app.tsx            # Global app wrapper
│   ├── _layout.tsx         # Main layout template
│   ├── _middleware.ts      # Global middleware
│   ├── index.tsx           # Dashboard home page
│   ├── analytics/          # Analytics pages
│   │   ├── index.tsx       # Analytics overview
│   │   ├── cohorts.tsx     # Cohort analysis
│   │   └── d3-dashboard.tsx # D3 visualizations
│   ├── api/                # API routes
│   │   ├── auth/           # Authentication endpoints
│   │   ├── dashboard/      # Dashboard data endpoints
│   │   └── analytics/      # Analytics endpoints
│   └── auth/               # Authentication pages
│       ├── login.tsx       # Login page
│       └── register.tsx    # Registration page
├── islands/                # Interactive client-side components
│   ├── charts/             # D3.js chart components
│   │   ├── D3LineChart.tsx
│   │   ├── D3BarChart.tsx
│   │   └── D3PieChart.tsx
│   ├── dashboard/          # Dashboard widgets
│   │   ├── KPIScorecard.tsx
│   │   ├── RealtimeMetrics.tsx
│   │   └── InteractiveGlobe.tsx
│   └── analytics/          # Analytics components
│       ├── CohortAnalysis.tsx
│       ├── CustomerJourney.tsx
│       └── AttributionAnalysis.tsx
├── components/             # Server-rendered components
│   ├── layout/             # Layout components
│   ├── ui/                 # Reusable UI components
│   └── forms/              # Form components
├── static/                 # Static assets (CSS, images, etc.)
├── utils/                  # Utility functions
│   ├── auth.ts            # Authentication utilities
│   ├── database.ts        # Database connections
│   └── redis.ts           # Redis utilities
├── lib/                    # Shared libraries
└── docs/                   # Documentation
```

## ⚙️ **Configuration**

### **deno.json**
```json
{
  "name": "dashboard-fresh",
  "version": "1.0.0",
  "exports": "./main.ts",
  "tasks": {
    "dev": "deno run -A --watch=static/,routes/ dev.ts",
    "start": "deno run -A main.ts",
    "build": "deno run -A dev.ts build",
    "preview": "deno run -A main.ts",
    "update": "deno run -A -r https://fresh.deno.dev/update ."
  },
  "imports": {
    "$fresh/": "https://deno.land/x/fresh@1.6.1/",
    "preact": "https://esm.sh/preact@10.19.2",
    "preact/": "https://esm.sh/preact@10.19.2/",
    "@preact/signals": "https://esm.sh/*@preact/signals@1.2.1",
    "@preact/signals-core": "https://esm.sh/*@preact/signals-core@1.5.0",
    "tailwindcss": "npm:tailwindcss@3.3.0",
    "tailwindcss/": "npm:/tailwindcss@3.3.0/",
    "tailwindcss/plugin": "npm:/tailwindcss@3.3.0/plugin.js",
    "$std/": "https://deno.land/std@0.208.0/",
    "d3": "https://esm.sh/d3@7.8.5",
    "postgres": "https://deno.land/x/postgres@v0.17.0/mod.ts",
    "redis": "https://deno.land/x/redis@v0.31.0/mod.ts"
  },
  "compilerOptions": {
    "jsx": "react-jsx",
    "jsxImportSource": "preact"
  }
}
```

### **Environment Variables**
Create `.env` file:
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/ecommerce_analytics
TIMESCALE_URL=postgresql://user:password@localhost:5432/ecommerce_analytics

# Redis
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-jwt-secret-key
SESSION_SECRET=your-session-secret

# API URLs
ANALYTICS_API_URL=http://localhost:3002
INTEGRATION_API_URL=http://localhost:3003
BILLING_API_URL=http://localhost:3004

# Environment
DENO_ENV=development
PORT=8000
```

## 🛠️ **Development Commands**

### **Core Commands**
```bash
# Start development server with hot reload
deno task dev

# Start production server
deno task start

# Build for production
deno task build

# Update Fresh framework
deno task update

# Type checking
deno check main.ts

# Format code
deno fmt

# Lint code
deno lint
```

### **Database Commands**
```bash
# Run database migrations
deno run -A scripts/migrate.ts

# Seed development data
deno run -A scripts/seed.ts

# Reset database
deno run -A scripts/reset.ts
```

### **Testing Commands**
```bash
# Run all tests
deno test -A

# Run tests with coverage
deno test -A --coverage=coverage

# Run specific test file
deno test -A tests/auth_test.ts

# Watch mode for tests
deno test -A --watch
```

## 🎨 **Styling with Tailwind CSS**

### **Configuration**
Tailwind is configured in `tailwind.config.ts`:
```typescript
import { type Config } from "tailwindcss";

export default {
  content: [
    "{routes,islands,components}/**/*.{ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a',
        },
      },
    },
  },
} satisfies Config;
```

### **Usage in Components**
```tsx
export default function Button({ children, variant = "primary" }) {
  const baseClasses = "px-4 py-2 rounded-lg font-medium transition-colors";
  const variantClasses = {
    primary: "bg-primary-500 text-white hover:bg-primary-600",
    secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300",
  };

  return (
    <button class={`${baseClasses} ${variantClasses[variant]}`}>
      {children}
    </button>
  );
}
```

## 🏝️ **Working with Islands**

### **Creating an Island**
Islands are interactive components that run on the client:

```tsx
// islands/dashboard/KPICard.tsx
import { useSignal } from "@preact/signals";

interface KPICardProps {
  title: string;
  value: number;
  change: number;
  trend: "up" | "down" | "neutral";
}

export default function KPICard({ title, value, change, trend }: KPICardProps) {
  const expanded = useSignal(false);
  const isHovered = useSignal(false);

  return (
    <div 
      class={`p-6 bg-white rounded-lg shadow-md cursor-pointer transition-all ${
        isHovered.value ? 'shadow-lg scale-105' : ''
      }`}
      onClick={() => expanded.value = !expanded.value}
      onMouseEnter={() => isHovered.value = true}
      onMouseLeave={() => isHovered.value = false}
    >
      <h3 class="text-lg font-semibold text-gray-800">{title}</h3>
      <p class="text-3xl font-bold text-primary-600">{value.toLocaleString()}</p>
      
      {expanded.value && (
        <div class="mt-4 p-4 bg-gray-50 rounded">
          <p class={`text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
            {change > 0 ? '+' : ''}{change}% from last period
          </p>
        </div>
      )}
    </div>
  );
}
```

### **Using Islands in Pages**
```tsx
// routes/dashboard.tsx
import KPICard from "../islands/dashboard/KPICard.tsx";

export default function Dashboard() {
  const kpiData = [
    { title: "Total Revenue", value: 125000, change: 12.5, trend: "up" },
    { title: "Active Users", value: 8420, change: -2.1, trend: "down" },
  ];

  return (
    <div class="p-6">
      <h1 class="text-2xl font-bold mb-6">Dashboard</h1>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiData.map((kpi, index) => (
          <KPICard key={index} {...kpi} />
        ))}
      </div>
    </div>
  );
}
```

## 📊 **D3.js Integration**

### **D3 Chart Island Example**
```tsx
// islands/charts/D3LineChart.tsx
import { useEffect, useRef } from "preact/hooks";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";

interface ChartData {
  date: Date;
  value: number;
}

interface D3LineChartProps {
  data: ChartData[];
  width?: number;
  height?: number;
}

export default function D3LineChart({ 
  data, 
  width = 800, 
  height = 400 
}: D3LineChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!IS_BROWSER || !svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove(); // Clear previous render

    const margin = { top: 20, right: 30, bottom: 40, left: 40 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.date) as [Date, Date])
      .range([0, innerWidth]);

    const yScale = d3.scaleLinear()
      .domain(d3.extent(data, d => d.value) as [number, number])
      .range([innerHeight, 0]);

    // Line generator
    const line = d3.line<ChartData>()
      .x(d => xScale(d.date))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    g.append("g")
      .call(d3.axisLeft(yScale));

    // Add line
    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#3b82f6")
      .attr("stroke-width", 2)
      .attr("d", line);

  }, [data, width, height]);

  // Server-side rendering placeholder
  if (!IS_BROWSER) {
    return (
      <div 
        style={{ width, height }} 
        class="flex items-center justify-center bg-gray-100 rounded"
      >
        <p class="text-gray-500">Loading chart...</p>
      </div>
    );
  }

  return <svg ref={svgRef} width={width} height={height}></svg>;
}
```

## 🔐 **Authentication Setup**

### **Middleware**
```tsx
// routes/_middleware.ts
import { MiddlewareHandlerContext } from "$fresh/server.ts";
import { verifyJWT } from "../utils/auth.ts";

export async function handler(
  req: Request,
  ctx: MiddlewareHandlerContext,
) {
  const authHeader = req.headers.get("Authorization");
  
  if (authHeader?.startsWith("Bearer ")) {
    try {
      const token = authHeader.substring(7);
      const user = await verifyJWT(token);
      ctx.state.user = user;
    } catch (error) {
      console.error("Auth error:", error);
    }
  }
  
  return ctx.next();
}
```

### **Protected Routes**
```tsx
// routes/dashboard.tsx
import { defineRoute } from "$fresh/server.ts";

export default defineRoute(async (req, ctx) => {
  // Redirect to login if not authenticated
  if (!ctx.state.user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  return (
    <div>
      <h1>Welcome, {ctx.state.user.firstName}!</h1>
      {/* Dashboard content */}
    </div>
  );
});
```

## 🚀 **Deployment**

### **Local Development**
```bash
# Start all services
docker-compose up -d

# Start Fresh development server
cd services/dashboard-fresh
deno task dev
```

### **Production Build**
```bash
# Build for production
deno task build

# Start production server
deno task start
```

### **Docker Deployment**
```dockerfile
FROM denoland/deno:1.38.3

WORKDIR /app
COPY . .
RUN deno cache main.ts

EXPOSE 8000
CMD ["run", "-A", "main.ts"]
```

## 📝 **Best Practices**

### **Islands Guidelines**
- Keep islands small and focused
- Use server-side rendering for static content
- Only make components islands if they need interactivity
- Use `IS_BROWSER` guard for client-only APIs

### **Performance Tips**
- Minimize JavaScript bundle size
- Use server-side data fetching when possible
- Implement proper caching strategies
- Optimize images and static assets

### **Code Organization**
- Group related components together
- Use consistent naming conventions
- Keep business logic in utility functions
- Document complex components

---

**Happy coding with Fresh! 🍃**
