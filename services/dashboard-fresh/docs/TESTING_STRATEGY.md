# Fresh Dashboard Testing Strategy

## 🎯 **Testing Overview**

Comprehensive testing strategy to ensure the Fresh migration maintains all existing functionality, performance characteristics, and user experience while leveraging Fresh's server-side rendering benefits.

## 🧪 **Testing Pyramid**

### **1. Unit Tests (Foundation)**
**Scope**: Individual functions, utilities, and isolated components  
**Coverage Target**: 90%+  
**Tools**: Deno's built-in test runner

#### **Component Testing**
```typescript
// tests/components/KPICard_test.ts
import { assertEquals } from "$std/testing/asserts.ts";
import { render } from "@testing-library/preact";
import KPICard from "../../islands/dashboard/KPICard.tsx";

Deno.test("KPICard renders with correct data", () => {
  const props = {
    title: "Total Revenue",
    value: 125000,
    change: 12.5,
    trend: "up" as const,
  };

  const { getByText } = render(<KPICard {...props} />);
  
  assertEquals(getByText("Total Revenue"), true);
  assertEquals(getByText("125,000"), true);
});

Deno.test("KPICard expands on click", async () => {
  const props = {
    title: "Active Users",
    value: 8420,
    change: -2.1,
    trend: "down" as const,
  };

  const { getByRole, queryByText } = render(<KPICard {...props} />);
  
  // Initially collapsed
  assertEquals(queryByText("-2.1% from last period"), null);
  
  // Click to expand
  const card = getByRole("button");
  await userEvent.click(card);
  
  // Should now show details
  assertEquals(queryByText("-2.1% from last period"), true);
});
```

#### **Utility Function Testing**
```typescript
// tests/utils/auth_test.ts
import { assertEquals, assertThrows } from "$std/testing/asserts.ts";
import { verifyJWT, generateJWT } from "../../utils/auth.ts";

Deno.test("JWT generation and verification", async () => {
  const payload = { userId: "123", email: "<EMAIL>" };
  
  const token = await generateJWT(payload);
  const decoded = await verifyJWT(token);
  
  assertEquals(decoded.userId, payload.userId);
  assertEquals(decoded.email, payload.email);
});

Deno.test("JWT verification throws on invalid token", async () => {
  await assertThrows(
    () => verifyJWT("invalid-token"),
    Error,
    "Invalid token"
  );
});
```

#### **Service Layer Testing**
```typescript
// tests/services/dashboard_test.ts
import { assertEquals } from "$std/testing/asserts.ts";
import { dashboardService } from "../../services/dashboardService.ts";

Deno.test("Dashboard service returns overview data", async () => {
  const mockUser = {
    id: "user-123",
    tenant_id: "tenant-456",
    email: "<EMAIL>",
  };

  const overview = await dashboardService.getOverview(mockUser);
  
  assertEquals(typeof overview.totalRevenue, "number");
  assertEquals(typeof overview.activeUsers, "number");
  assertEquals(typeof overview.conversionRate, "number");
});
```

### **2. Integration Tests (API & Database)**
**Scope**: API routes, database interactions, service integrations  
**Coverage Target**: 80%+  
**Tools**: Deno test runner with test database

#### **API Route Testing**
```typescript
// tests/api/dashboard_test.ts
import { assertEquals } from "$std/testing/asserts.ts";
import { createHandler } from "$fresh/server.ts";
import { handler } from "../../routes/api/dashboard/overview.ts";

Deno.test("Dashboard overview API returns data", async () => {
  const req = new Request("http://localhost/api/dashboard/overview", {
    headers: { "Authorization": "Bearer valid-token" },
  });

  const ctx = {
    state: { user: { id: "123", tenant_id: "tenant-456" } },
  };

  const response = await handler.GET(req, ctx);
  const data = await response.json();

  assertEquals(response.status, 200);
  assertEquals(data.success, true);
  assertEquals(typeof data.data.totalRevenue, "number");
});

Deno.test("Dashboard API requires authentication", async () => {
  const req = new Request("http://localhost/api/dashboard/overview");
  const ctx = { state: {} };

  const response = await handler.GET(req, ctx);

  assertEquals(response.status, 401);
});
```

#### **Database Integration Testing**
```typescript
// tests/database/dashboard_queries_test.ts
import { assertEquals } from "$std/testing/asserts.ts";
import { setupTestDB, cleanupTestDB } from "../helpers/test_db.ts";
import { dashboardService } from "../../services/dashboardService.ts";

Deno.test("Dashboard queries work with test data", async () => {
  await setupTestDB();
  
  try {
    const mockUser = { id: "test-user", tenant_id: "test-tenant" };
    const result = await dashboardService.fetchOverviewFromDB("test-tenant", {});
    
    assertEquals(typeof result.totalRevenue, "number");
    assertEquals(result.totalRevenue >= 0, true);
  } finally {
    await cleanupTestDB();
  }
});
```

### **3. End-to-End Tests (User Workflows)**
**Scope**: Complete user journeys, cross-browser testing  
**Coverage Target**: Critical paths 100%  
**Tools**: Playwright or Puppeteer

#### **Authentication Flow**
```typescript
// tests/e2e/auth_flow_test.ts
import { test, expect } from "@playwright/test";

test("User can login and access dashboard", async ({ page }) => {
  // Navigate to login page
  await page.goto("/auth/login");
  
  // Fill login form
  await page.fill('[data-testid="email"]', "<EMAIL>");
  await page.fill('[data-testid="password"]', "password123");
  await page.click('[data-testid="login-button"]');
  
  // Should redirect to dashboard
  await expect(page).toHaveURL("/dashboard");
  await expect(page.locator("h1")).toContainText("Dashboard");
});

test("Unauthenticated user redirects to login", async ({ page }) => {
  await page.goto("/dashboard");
  await expect(page).toHaveURL("/auth/login");
});
```

#### **Dashboard Functionality**
```typescript
// tests/e2e/dashboard_test.ts
import { test, expect } from "@playwright/test";

test("Dashboard loads with KPI cards", async ({ page }) => {
  await page.goto("/dashboard");
  
  // Check KPI cards are present
  await expect(page.locator('[data-testid="kpi-card"]')).toHaveCount(4);
  
  // Check data is loaded
  await expect(page.locator('[data-testid="total-revenue"]')).toContainText("$");
  await expect(page.locator('[data-testid="active-users"]')).toContainText(/\d+/);
});

test("KPI card expands on click", async ({ page }) => {
  await page.goto("/dashboard");
  
  const kpiCard = page.locator('[data-testid="kpi-card"]').first();
  
  // Initially collapsed
  await expect(kpiCard.locator('[data-testid="details"]')).not.toBeVisible();
  
  // Click to expand
  await kpiCard.click();
  
  // Should show details
  await expect(kpiCard.locator('[data-testid="details"]')).toBeVisible();
});
```

#### **D3.js Visualization Testing**
```typescript
// tests/e2e/charts_test.ts
import { test, expect } from "@playwright/test";

test("D3 charts render correctly", async ({ page }) => {
  await page.goto("/analytics/d3-dashboard");
  
  // Wait for charts to load
  await page.waitForSelector('[data-testid="d3-line-chart"] svg');
  
  // Check SVG elements are present
  const svg = page.locator('[data-testid="d3-line-chart"] svg');
  await expect(svg).toBeVisible();
  
  // Check chart has data points
  const dataPoints = svg.locator('circle');
  await expect(dataPoints).toHaveCountGreaterThan(0);
});

test("Chart interactions work", async ({ page }) => {
  await page.goto("/analytics/d3-dashboard");
  
  // Wait for chart to load
  await page.waitForSelector('[data-testid="d3-line-chart"] svg');
  
  // Hover over data point
  const dataPoint = page.locator('[data-testid="d3-line-chart"] svg circle').first();
  await dataPoint.hover();
  
  // Should show tooltip
  await expect(page.locator('[data-testid="chart-tooltip"]')).toBeVisible();
});
```

### **4. Performance Tests**
**Scope**: Load times, rendering performance, memory usage  
**Tools**: Lighthouse, k6, custom performance tests

#### **Page Load Performance**
```typescript
// tests/performance/page_load_test.ts
import { test, expect } from "@playwright/test";

test("Dashboard loads within performance budget", async ({ page }) => {
  const startTime = Date.now();
  
  await page.goto("/dashboard");
  await page.waitForLoadState("networkidle");
  
  const loadTime = Date.now() - startTime;
  
  // Should load within 2 seconds
  expect(loadTime).toBeLessThan(2000);
  
  // Check Lighthouse metrics
  const lighthouse = await page.evaluate(() => {
    return new Promise((resolve) => {
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        resolve(entries);
      }).observe({ entryTypes: ["navigation"] });
    });
  });
  
  // Add assertions for Core Web Vitals
});
```

#### **Load Testing with k6**
```javascript
// tests/load/dashboard_load_test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 0 },   // Ramp down
  ],
};

export default function () {
  const response = http.get('http://localhost:8000/dashboard', {
    headers: { 'Authorization': 'Bearer test-token' },
  });
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });
  
  sleep(1);
}
```

### **5. Visual Regression Tests**
**Scope**: UI consistency, responsive design  
**Tools**: Playwright screenshots, Percy

#### **Visual Testing**
```typescript
// tests/visual/dashboard_visual_test.ts
import { test, expect } from "@playwright/test";

test("Dashboard visual regression", async ({ page }) => {
  await page.goto("/dashboard");
  await page.waitForLoadState("networkidle");
  
  // Take screenshot and compare
  await expect(page).toHaveScreenshot("dashboard-desktop.png");
});

test("Dashboard mobile responsive", async ({ page }) => {
  await page.setViewportSize({ width: 375, height: 667 });
  await page.goto("/dashboard");
  await page.waitForLoadState("networkidle");
  
  await expect(page).toHaveScreenshot("dashboard-mobile.png");
});

test("Charts render consistently", async ({ page }) => {
  await page.goto("/analytics/d3-dashboard");
  await page.waitForSelector('[data-testid="d3-line-chart"] svg');
  
  // Wait for animations to complete
  await page.waitForTimeout(1000);
  
  await expect(page.locator('[data-testid="d3-line-chart"]')).toHaveScreenshot("line-chart.png");
});
```

## 🚀 **Test Execution Strategy**

### **Development Workflow**
```bash
# Run unit tests during development
deno task test:unit

# Run integration tests
deno task test:integration

# Run all tests
deno task test

# Run tests with coverage
deno task test:coverage

# Run E2E tests
deno task test:e2e

# Run performance tests
deno task test:performance

# Run visual regression tests
deno task test:visual
```

### **CI/CD Pipeline**
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: denoland/setup-deno@v1
      - run: deno task test:unit

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: timescale/timescaledb:latest-pg14
      redis:
        image: redis:6-alpine
    steps:
      - uses: actions/checkout@v3
      - uses: denoland/setup-deno@v1
      - run: deno task test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: denoland/setup-deno@v1
      - uses: microsoft/playwright-github-action@v1
      - run: deno task test:e2e

  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: denoland/setup-deno@v1
      - uses: grafana/k6-action@v0.2.0
        with:
          filename: tests/load/dashboard_load_test.js
```

### **Test Data Management**
```typescript
// tests/helpers/test_data.ts
export const mockUser = {
  id: "test-user-123",
  email: "<EMAIL>",
  firstName: "Test",
  lastName: "User",
  tenant_id: "test-tenant-456",
  role: "admin",
};

export const mockDashboardData = {
  totalRevenue: 125000,
  activeUsers: 8420,
  conversionRate: 3.2,
  topLinks: [
    { id: "1", url: "https://example.com/product1", clicks: 1250 },
    { id: "2", url: "https://example.com/product2", clicks: 980 },
  ],
};

export const mockChartData = [
  { date: new Date("2024-01-01"), value: 100 },
  { date: new Date("2024-01-02"), value: 150 },
  { date: new Date("2024-01-03"), value: 120 },
];
```

## 📊 **Success Criteria**

### **Coverage Targets**
- **Unit Tests**: 90%+ code coverage
- **Integration Tests**: 80%+ API coverage
- **E2E Tests**: 100% critical path coverage
- **Performance Tests**: All pages < 2s load time
- **Visual Tests**: 0 unintended UI changes

### **Quality Gates**
- All tests pass in CI/CD
- Performance budgets met
- Accessibility standards (WCAG 2.1 AA)
- Cross-browser compatibility (Chrome, Firefox, Safari, Edge)
- Mobile responsiveness verified

### **Migration Validation**
- [ ] All React functionality preserved
- [ ] D3.js visualizations working
- [ ] Real-time features functional
- [ ] Authentication flows intact
- [ ] Multi-tenant isolation verified
- [ ] Performance improvements achieved

---

**Testing Timeline**: 2-3 weeks parallel to development  
**Test Automation**: 95%+ automated  
**Manual Testing**: Critical user journeys only
