// DO NOT EDIT. This file is generated by Fresh.
// This file SHOULD be checked into source version control.
// This file is automatically updated during development when running `dev.ts`.

import * as $_app from "./routes/_app.tsx";
import * as $_layout from "./routes/_layout.tsx";
import * as $_middleware from "./routes/_middleware.ts";
import * as $analytics_d3_dashboard from "./routes/analytics/d3-dashboard.tsx";
import * as $api_analytics_path_ from "./routes/api/analytics/[...path].ts";
import * as $api_auth_login from "./routes/api/auth/login.ts";
import * as $api_auth_logout from "./routes/api/auth/logout.ts";
import * as $api_dashboard_metrics from "./routes/api/dashboard/metrics.ts";
import * as $api_dashboard_overview from "./routes/api/dashboard/overview.ts";
import * as $api_health from "./routes/api/health.ts";
import * as $api_realtime_metrics from "./routes/api/realtime/metrics.ts";
import * as $auth_login from "./routes/auth/login.tsx";
import * as $index from "./routes/index.tsx";
import * as $auth_LoginForm from "./islands/auth/LoginForm.tsx";
import * as $charts_D3BarChart from "./islands/charts/D3BarChart.tsx";
import * as $charts_D3LineChart from "./islands/charts/D3LineChart.tsx";
import * as $dashboard_KPIScorecard from "./islands/dashboard/KPIScorecard.tsx";
import * as $dashboard_RealtimeMetrics from "./islands/dashboard/RealtimeMetrics.tsx";
import * as $ui_DarkModeToggle from "./islands/ui/DarkModeToggle.tsx";
import { type Manifest } from "$fresh/server.ts";

const manifest = {
  routes: {
    "./routes/_app.tsx": $_app,
    "./routes/_layout.tsx": $_layout,
    "./routes/_middleware.ts": $_middleware,
    "./routes/analytics/d3-dashboard.tsx": $analytics_d3_dashboard,
    "./routes/api/analytics/[...path].ts": $api_analytics_path_,
    "./routes/api/auth/login.ts": $api_auth_login,
    "./routes/api/auth/logout.ts": $api_auth_logout,
    "./routes/api/dashboard/metrics.ts": $api_dashboard_metrics,
    "./routes/api/dashboard/overview.ts": $api_dashboard_overview,
    "./routes/api/health.ts": $api_health,
    "./routes/api/realtime/metrics.ts": $api_realtime_metrics,
    "./routes/auth/login.tsx": $auth_login,
    "./routes/index.tsx": $index,
  },
  islands: {
    "./islands/auth/LoginForm.tsx": $auth_LoginForm,
    "./islands/charts/D3BarChart.tsx": $charts_D3BarChart,
    "./islands/charts/D3LineChart.tsx": $charts_D3LineChart,
    "./islands/dashboard/KPIScorecard.tsx": $dashboard_KPIScorecard,
    "./islands/dashboard/RealtimeMetrics.tsx": $dashboard_RealtimeMetrics,
    "./islands/ui/DarkModeToggle.tsx": $ui_DarkModeToggle,
  },
  baseUrl: import.meta.url,
} satisfies Manifest;

export default manifest;
