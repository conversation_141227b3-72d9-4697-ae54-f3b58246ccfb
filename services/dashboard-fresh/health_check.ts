#!/usr/bin/env -S deno run --allow-net --allow-env

/**
 * Health check script for Fresh Dashboard
 * Used by Docker healthcheck and monitoring systems
 */

const PORT = Deno.env.get("PORT") || "8000";
const HOST = Deno.env.get("HOST") || "localhost";
const HEALTH_URL = `http://${HOST}:${PORT}/api/health`;

interface HealthResponse {
  status: string;
  timestamp: string;
  responseTime: string;
  services: {
    database: { status: string };
    redis: { status: string };
  };
  version: string;
  environment: string;
}

async function checkHealth(): Promise<boolean> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(HEALTH_URL, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Fresh-Dashboard-HealthCheck/1.0'
      }
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      console.error(`Health check failed: HTTP ${response.status}`);
      return false;
    }

    const health: HealthResponse = await response.json();
    
    // Check overall status
    if (health.status !== 'healthy') {
      console.error(`Health check failed: Status is ${health.status}`);
      return false;
    }

    // Check individual services
    if (health.services.database.status !== 'healthy') {
      console.error('Health check failed: Database is unhealthy');
      return false;
    }

    if (health.services.redis.status !== 'healthy') {
      console.error('Health check failed: Redis is unhealthy');
      return false;
    }

    console.log(`✅ Health check passed - ${health.status} (${health.responseTime})`);
    return true;

  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('Health check failed: Request timeout');
    } else {
      console.error(`Health check failed: ${error.message}`);
    }
    return false;
  }
}

// Run health check
const isHealthy = await checkHealth();

// Exit with appropriate code
Deno.exit(isHealthy ? 0 : 1);
