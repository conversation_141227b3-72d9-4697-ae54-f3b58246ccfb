import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import Button from "../../components/ui/Button.tsx";
import Input, { InputGroup, PasswordInput } from "../../components/ui/Input.tsx";
import { AlertCard } from "../../components/ui/Card.tsx";

interface LoginFormProps {
  redirectTo?: string | null;
}

export default function LoginForm({ redirectTo }: LoginFormProps) {
  const email = useSignal("");
  const password = useSignal("");
  const rememberMe = useSignal(false);
  const isLoading = useSignal(false);
  const error = useSignal<string | null>(null);
  const showPassword = useSignal(false);

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!IS_BROWSER) return;

    if (!email.value || !password.value) {
      error.value = "Please fill in all fields";
      return;
    }

    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email.value,
          password: password.value,
          rememberMe: rememberMe.value,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Redirect to dashboard or specified redirect URL
        const redirectUrl = redirectTo || "/";
        window.location.href = redirectUrl;
      } else {
        error.value = result.error || "Login failed";
      }
    } catch (err) {
      console.error("Login error:", err);
      error.value = "Network error. Please try again.";
    } finally {
      isLoading.value = false;
    }
  };

  const fillDemoCredentials = () => {
    email.value = "<EMAIL>";
    password.value = "demo123";
  };

  const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value;
  };

  return (
    <form onSubmit={handleSubmit} class="space-y-6">
      {/* Error Message */}
      {error.value && (
        <AlertCard variant="error" dismissible onDismiss={() => error.value = null}>
          {error.value}
        </AlertCard>
      )}

      {/* Email Field */}
      <div class="space-y-2">
        <label htmlFor="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Email address
          <span class="text-error-500 ml-1" aria-label="required">*</span>
        </label>
        <Input
          id="email"
          name="email"
          type="email"
          autoComplete="email"
          required
          value={email.value}
          onInput={(e) => email.value = (e.target as HTMLInputElement).value}
          placeholder="Enter your email"
          data-testid="email-input"
        />
      </div>

      {/* Password Field */}
      <div class="space-y-2">
        <label htmlFor="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Password
          <span class="text-error-500 ml-1" aria-label="required">*</span>
        </label>
        <PasswordInput
          id="password"
          name="password"
          autoComplete="current-password"
          required
          value={password.value}
          onInput={(e) => password.value = (e.target as HTMLInputElement).value}
          placeholder="Enter your password"
          showPassword={showPassword.value}
          onToggleVisibility={togglePasswordVisibility}
          data-testid="password-input"
        />
      </div>

      {/* Remember Me and Demo Credentials */}
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            checked={rememberMe.value}
            onChange={(e) => rememberMe.value = (e.target as HTMLInputElement).checked}
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-800 dark:text-primary-500 dark:focus:ring-primary-400 transition-colors"
          />
          <label htmlFor="remember-me" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">
            Remember me
          </label>
        </div>

        <Button
          variant="link"
          size="sm"
          onClick={fillDemoCredentials}
          type="button"
          data-testid="demo-credentials-button"
        >
          Use demo credentials
        </Button>
      </div>

      {/* Submit Button */}
      <div>
        <Button
          type="submit"
          variant="primary"
          size="lg"
          disabled={isLoading.value}
          loading={isLoading.value}
          className="w-full"
          data-testid="login-submit-button"
        >
          Sign in
        </Button>
      </div>
    </form>
  );
}
