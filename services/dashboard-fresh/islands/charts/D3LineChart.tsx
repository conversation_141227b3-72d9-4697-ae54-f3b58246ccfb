import { useEffect, useRef } from "preact/hooks";
import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";
import * as d3 from "d3";

interface ChartData {
  timestamp: string;
  value: number;
}

interface D3LineChartProps {
  data: ChartData[];
  width?: number;
  height?: number;
  title?: string;
  color?: string;
  onPointClick?: (point: ChartData) => void;
}

export default function D3LineChart({ 
  data, 
  width = 800, 
  height = 400,
  title = "Time Series Chart",
  color = "#3b82f6",
  onPointClick
}: D3LineChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const hoveredPoint = useSignal<ChartData | null>(null);

  useEffect(() => {
    if (!IS_BROWSER || !svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove(); // Clear previous render

    const margin = { top: 20, right: 30, bottom: 40, left: 60 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Parse dates and prepare data
    const parseTime = d3.timeParse("%Y-%m-%dT%H:%M:%S.%LZ");
    const parsedData = data.map(d => ({
      ...d,
      date: parseTime(d.timestamp) || new Date(d.timestamp)
    }));

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(parsedData, d => d.date) as [Date, Date])
      .range([0, innerWidth]);

    const yScale = d3.scaleLinear()
      .domain(d3.extent(parsedData, d => d.value) as [number, number])
      .nice()
      .range([innerHeight, 0]);

    // Line generator
    const line = d3.line<any>()
      .x(d => xScale(d.date))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Area generator for gradient fill
    const area = d3.area<any>()
      .x(d => xScale(d.date))
      .y0(innerHeight)
      .y1(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add gradient definition
    const gradient = svg.append("defs")
      .append("linearGradient")
      .attr("id", "area-gradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0).attr("y1", 0)
      .attr("x2", 0).attr("y2", innerHeight);

    gradient.append("stop")
      .attr("offset", "0%")
      .attr("stop-color", color)
      .attr("stop-opacity", 0.3);

    gradient.append("stop")
      .attr("offset", "100%")
      .attr("stop-color", color)
      .attr("stop-opacity", 0);

    // Add area
    g.append("path")
      .datum(parsedData)
      .attr("fill", "url(#area-gradient)")
      .attr("d", area);

    // Add axes
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale)
        .tickFormat(d3.timeFormat("%m/%d") as any)
        .ticks(6));

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale)
        .tickFormat(d3.format(".2s"))
        .ticks(5));

    // Style axes
    xAxis.selectAll("text")
      .style("font-size", "12px")
      .style("fill", "#6b7280");

    yAxis.selectAll("text")
      .style("font-size", "12px")
      .style("fill", "#6b7280");

    g.selectAll(".domain")
      .style("stroke", "#e5e7eb");

    g.selectAll(".tick line")
      .style("stroke", "#e5e7eb");

    // Add grid lines
    g.selectAll(".grid-line-x")
      .data(xScale.ticks(6))
      .enter()
      .append("line")
      .attr("class", "grid-line-x")
      .attr("x1", d => xScale(d))
      .attr("x2", d => xScale(d))
      .attr("y1", 0)
      .attr("y2", innerHeight)
      .style("stroke", "#f3f4f6")
      .style("stroke-width", 1);

    g.selectAll(".grid-line-y")
      .data(yScale.ticks(5))
      .enter()
      .append("line")
      .attr("class", "grid-line-y")
      .attr("x1", 0)
      .attr("x2", innerWidth)
      .attr("y1", d => yScale(d))
      .attr("y2", d => yScale(d))
      .style("stroke", "#f3f4f6")
      .style("stroke-width", 1);

    // Add line
    g.append("path")
      .datum(parsedData)
      .attr("fill", "none")
      .attr("stroke", color)
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add data points
    const points = g.selectAll(".data-point")
      .data(parsedData)
      .enter()
      .append("circle")
      .attr("class", "data-point")
      .attr("cx", d => xScale(d.date))
      .attr("cy", d => yScale(d.value))
      .attr("r", 4)
      .attr("fill", color)
      .attr("stroke", "white")
      .attr("stroke-width", 2)
      .style("cursor", "pointer")
      .style("opacity", 0.8);

    // Add hover interactions
    points
      .on("mouseover", function(event, d) {
        d3.select(this)
          .transition()
          .duration(150)
          .attr("r", 6)
          .style("opacity", 1);

        hoveredPoint.value = d;

        // Position tooltip
        if (tooltipRef.current) {
          const tooltip = d3.select(tooltipRef.current);
          tooltip
            .style("opacity", 1)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 10) + "px");
        }
      })
      .on("mouseout", function() {
        d3.select(this)
          .transition()
          .duration(150)
          .attr("r", 4)
          .style("opacity", 0.8);

        hoveredPoint.value = null;

        if (tooltipRef.current) {
          d3.select(tooltipRef.current).style("opacity", 0);
        }
      })
      .on("click", function(event, d) {
        onPointClick?.(d);
      });

    // Add axis labels
    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("y", 0 - margin.left)
      .attr("x", 0 - (innerHeight / 2))
      .attr("dy", "1em")
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#6b7280")
      .text("Value");

    g.append("text")
      .attr("transform", `translate(${innerWidth / 2}, ${innerHeight + margin.bottom})`)
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#6b7280")
      .text("Time");

  }, [data, width, height, color]);

  // Server-side rendering placeholder
  if (!IS_BROWSER) {
    return (
      <div 
        style={{ width, height }} 
        class="flex items-center justify-center bg-gray-100 rounded-lg border-2 border-dashed border-gray-300"
      >
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <p class="mt-2 text-sm text-gray-500">Loading {title}...</p>
        </div>
      </div>
    );
  }

  return (
    <div class="relative" data-testid="d3-line-chart">
      {/* Chart Title */}
      {title && (
        <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">{title}</h3>
      )}
      
      {/* SVG Chart */}
      <svg 
        ref={svgRef} 
        width={width} 
        height={height}
        class="border border-gray-200 rounded-lg bg-white"
      />
      
      {/* Tooltip */}
      <div
        ref={tooltipRef}
        class="absolute pointer-events-none bg-gray-900 text-white text-xs rounded py-2 px-3 shadow-lg opacity-0 transition-opacity z-10"
        data-testid="chart-tooltip"
      >
        {hoveredPoint.value && (
          <div>
            <div class="font-medium">
              {new Date(hoveredPoint.value.timestamp).toLocaleDateString()}
            </div>
            <div>
              Value: {hoveredPoint.value.value.toLocaleString()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
