import { useSignal } from "@preact/signals";

interface KPIScorecardProps {
  title: string;
  value: number;
  change: number;
  trend: "up" | "down" | "neutral";
  format?: "currency" | "number" | "percentage";
}

export default function KPIScorecard({ 
  title, 
  value, 
  change, 
  trend, 
  format = "number" 
}: KPIScorecardProps) {
  const expanded = useSignal(false);
  const isHovered = useSignal(false);

  const formatValue = (val: number, fmt: string) => {
    switch (fmt) {
      case "currency":
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(val);
      case "percentage":
        return `${val.toFixed(1)}%`;
      default:
        return val.toLocaleString();
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return "text-success-600";
      case "down":
        return "text-error-600";
      default:
        return "text-gray-600";
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return (
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 17l9.2-9.2M17 17V7H7" />
          </svg>
        );
      case "down":
        return (
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 7l-9.2 9.2M7 7v10h10" />
          </svg>
        );
      default:
        return (
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
          </svg>
        );
    }
  };

  return (
    <div
      class={`bg-white dark:bg-gray-800 rounded-lg shadow-soft dark:shadow-dark-soft p-6 cursor-pointer transition-all duration-200 border border-gray-100 dark:border-gray-700 ${
        isHovered.value ? 'shadow-medium dark:shadow-dark-medium scale-105' : ''
      }`}
      onClick={() => expanded.value = !expanded.value}
      onMouseEnter={() => isHovered.value = true}
      onMouseLeave={() => isHovered.value = false}
      data-testid="kpi-card"
    >
      {/* Header */}
      <div class="flex items-center justify-between mb-2">
        <h3 class="text-sm font-medium text-gray-600 dark:text-gray-300">{title}</h3>
        <div class={`flex items-center space-x-1 ${getTrendColor(trend)}`}>
          {getTrendIcon(trend)}
          <span class="text-xs font-medium">
            {change > 0 ? '+' : ''}{change.toFixed(1)}%
          </span>
        </div>
      </div>

      {/* Main Value */}
      <div class="mb-4">
        <p
          class="text-3xl font-bold text-gray-900 dark:text-white"
          data-testid={title.toLowerCase().replace(/\s+/g, '-')}
        >
          {formatValue(value, format)}
        </p>
      </div>

      {/* Expanded Details */}
      {expanded.value && (
        <div 
          class="mt-4 pt-4 border-t border-gray-100 animate-slide-up"
          data-testid="details"
        >
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Previous Period:</span>
              <span class="font-medium text-gray-700 dark:text-gray-300">
                {formatValue(value / (1 + change / 100), format)}
              </span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Change:</span>
              <span class={`font-medium ${getTrendColor(trend)}`}>
                {change > 0 ? '+' : ''}{formatValue(value - (value / (1 + change / 100)), format)}
              </span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Trend:</span>
              <span class={`font-medium ${getTrendColor(trend)} capitalize`}>
                {trend}
              </span>
            </div>
          </div>

          {/* Mini Chart Placeholder */}
          <div class="mt-3 h-8 bg-gray-50 dark:bg-gray-700 rounded flex items-center justify-center">
            <span class="text-xs text-gray-400 dark:text-gray-500">7-day trend</span>
          </div>
        </div>
      )}

      {/* Expand Indicator */}
      <div class="flex justify-center mt-2">
        <svg 
          class={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
            expanded.value ? 'rotate-180' : ''
          }`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>
    </div>
  );
}
