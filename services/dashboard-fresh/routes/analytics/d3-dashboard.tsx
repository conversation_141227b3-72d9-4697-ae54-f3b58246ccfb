import { defineRoute } from "$fresh/server.ts";
import D3LineChart from "../../islands/charts/D3LineChart.tsx";
import D3BarChart from "../../islands/charts/D3BarChart.tsx";
import { dashboardService } from "../../services/dashboardService.ts";

export default defineRoute(async (req, ctx) => {
  const user = ctx.state.user;
  
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  try {
    // Fetch analytics data server-side
    const url = new URL(req.url);
    const dateFrom = url.searchParams.get("date_from");
    const dateTo = url.searchParams.get("date_to");

    const metricsData = await dashboardService.getMetrics(user, {
      dateFrom,
      dateTo,
    });

    // Generate sample data for demonstration
    const revenueData = metricsData.revenue.length > 0 ? metricsData.revenue : [
      { timestamp: "2024-01-01T00:00:00Z", value: 12500 },
      { timestamp: "2024-01-02T00:00:00Z", value: 15200 },
      { timestamp: "2024-01-03T00:00:00Z", value: 18900 },
      { timestamp: "2024-01-04T00:00:00Z", value: 16700 },
      { timestamp: "2024-01-05T00:00:00Z", value: 21300 },
      { timestamp: "2024-01-06T00:00:00Z", value: 19800 },
      { timestamp: "2024-01-07T00:00:00Z", value: 23400 },
    ];

    const userActivityData = metricsData.users.length > 0 ? metricsData.users : [
      { timestamp: "2024-01-01T00:00:00Z", value: 450 },
      { timestamp: "2024-01-02T00:00:00Z", value: 520 },
      { timestamp: "2024-01-03T00:00:00Z", value: 680 },
      { timestamp: "2024-01-04T00:00:00Z", value: 590 },
      { timestamp: "2024-01-05T00:00:00Z", value: 720 },
      { timestamp: "2024-01-06T00:00:00Z", value: 650 },
      { timestamp: "2024-01-07T00:00:00Z", value: 780 },
    ];

    const topChannelsData = [
      { label: "Organic Search", value: 3420, color: "#10b981" },
      { label: "Social Media", value: 2890, color: "#3b82f6" },
      { label: "Email", value: 2150, color: "#8b5cf6" },
      { label: "Direct", value: 1680, color: "#f59e0b" },
      { label: "Referral", value: 1240, color: "#ef4444" },
    ];

    const conversionFunnelData = [
      { label: "Visitors", value: 10000, color: "#e5e7eb" },
      { label: "Link Clicks", value: 7500, color: "#d1d5db" },
      { label: "Page Views", value: 5200, color: "#9ca3af" },
      { label: "Sign-ups", value: 1800, color: "#6b7280" },
      { label: "Conversions", value: 420, color: "#374151" },
    ];

    return (
      <div class="analytics-dashboard">
        {/* Page Header */}
        <div class="mb-8">
          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-3xl font-bold text-gray-900">D3.js Analytics Dashboard</h1>
              <p class="text-gray-600 mt-2">
                Interactive data visualizations powered by D3.js and Fresh islands
              </p>
            </div>
            
            {/* Date Range Selector */}
            <div class="flex items-center space-x-4">
              <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="custom">Custom range</option>
              </select>
              
              <button class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm hover:bg-primary-700 transition-colors">
                Export Data
              </button>
            </div>
          </div>
        </div>

        {/* Charts Grid */}
        <div class="space-y-8">
          {/* Revenue Trend */}
          <div class="bg-white rounded-lg shadow-soft p-6">
            <div class="mb-4">
              <h2 class="text-xl font-semibold text-gray-900">Revenue Trend</h2>
              <p class="text-sm text-gray-600">Daily revenue over time</p>
            </div>
            <D3LineChart
              data={revenueData}
              width={800}
              height={300}
              title=""
              color="#10b981"
              onPointClick={(point) => {
                console.log("Revenue point clicked:", point);
                // Handle point click - could open detailed view
              }}
            />
          </div>

          {/* User Activity */}
          <div class="bg-white rounded-lg shadow-soft p-6">
            <div class="mb-4">
              <h2 class="text-xl font-semibold text-gray-900">User Activity</h2>
              <p class="text-sm text-gray-600">Daily active users</p>
            </div>
            <D3LineChart
              data={userActivityData}
              width={800}
              height={300}
              title=""
              color="#3b82f6"
              onPointClick={(point) => {
                console.log("User activity point clicked:", point);
              }}
            />
          </div>

          {/* Two Column Layout */}
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Top Channels */}
            <div class="bg-white rounded-lg shadow-soft p-6">
              <div class="mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Top Traffic Channels</h2>
                <p class="text-sm text-gray-600">Traffic sources by volume</p>
              </div>
              <D3BarChart
                data={topChannelsData}
                width={500}
                height={350}
                title=""
                onBarClick={(bar) => {
                  console.log("Channel clicked:", bar);
                  // Handle bar click - could filter data or navigate
                }}
              />
            </div>

            {/* Conversion Funnel */}
            <div class="bg-white rounded-lg shadow-soft p-6">
              <div class="mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Conversion Funnel</h2>
                <p class="text-sm text-gray-600">User journey stages</p>
              </div>
              <D3BarChart
                data={conversionFunnelData}
                width={500}
                height={350}
                title=""
                onBarClick={(bar) => {
                  console.log("Funnel stage clicked:", bar);
                }}
              />
            </div>
          </div>

          {/* Key Insights */}
          <div class="bg-white rounded-lg shadow-soft p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Key Insights</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-green-200 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-green-800">Revenue Growth</p>
                    <p class="text-xs text-green-600">+23% vs last week</p>
                  </div>
                </div>
              </div>

              <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-200 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-blue-800">User Engagement</p>
                    <p class="text-xs text-blue-600">Peak at 780 users</p>
                  </div>
                </div>
              </div>

              <div class="bg-purple-50 p-4 rounded-lg">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-purple-200 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-purple-800">Top Channel</p>
                    <p class="text-xs text-purple-600">Organic Search leads</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Analytics dashboard error:", error);
    
    return (
      <div class="analytics-error p-6">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h1 class="text-xl font-semibold text-red-800 mb-2">
            Error Loading Analytics
          </h1>
          <p class="text-red-600 mb-4">
            We're having trouble loading your analytics data. Please try again.
          </p>
          <button 
            onClick={() => window.location.reload()} 
            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
});
