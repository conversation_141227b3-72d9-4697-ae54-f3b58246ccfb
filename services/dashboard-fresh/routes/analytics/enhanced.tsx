import { PageProps } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";
import EnhancedAnalyticsDashboard from "../../islands/dashboard/EnhancedAnalyticsDashboard.tsx";

// Enhanced Analytics Dashboard Page for Phase 1 Implementation
// Integrates with the new enhanced analytics backend

export default function EnhancedAnalyticsPage(props: PageProps) {
  return (
    <>
      <Head>
        <title>Enhanced Analytics Dashboard - E-Commerce Analytics</title>
        <meta name="description" content="Comprehensive analytics dashboard with real-time insights, customer lifetime value, and attribution modeling" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        
        {/* Preload critical resources */}
        <link rel="preload" href="/api/analytics/enhanced/dashboard?range=30d" as="fetch" crossOrigin="anonymous" />
        <link rel="preload" href="/api/analytics/enhanced/realtime/revenue" as="fetch" crossOrigin="anonymous" />
        
        {/* Performance optimizations */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="preconnect" href="//fonts.gstatic.com" crossOrigin="" />
        
        {/* Tailwind CSS for styling */}
        <link rel="stylesheet" href="/styles/tailwind.css" />
        
        {/* Custom analytics dashboard styles */}
        <style>{`
          .enhanced-analytics-dashboard {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          }
          
          .metric-card-hover {
            transition: all 0.2s ease-in-out;
          }
          
          .metric-card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          }
          
          .real-time-indicator {
            animation: pulse 2s infinite;
          }
          
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
          
          .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
          }
          
          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }
          
          .dark .loading-shimmer {
            background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
            background-size: 200% 100%;
          }
          
          /* Responsive design improvements */
          @media (max-width: 768px) {
            .enhanced-analytics-dashboard .container {
              padding-left: 1rem;
              padding-right: 1rem;
            }
          }
          
          /* Performance indicators styling */
          .performance-indicator {
            position: relative;
            overflow: hidden;
          }
          
          .performance-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }
          
          .performance-indicator:hover::before {
            left: 100%;
          }
        `}</style>
      </Head>
      
      <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Navigation breadcrumb */}
        <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div class="container mx-auto px-4 py-3">
            <div class="flex items-center space-x-2 text-sm">
              <a href="/" class="text-blue-600 hover:text-blue-800 dark:text-blue-400">
                Dashboard
              </a>
              <span class="text-gray-400">/</span>
              <a href="/analytics" class="text-blue-600 hover:text-blue-800 dark:text-blue-400">
                Analytics
              </a>
              <span class="text-gray-400">/</span>
              <span class="text-gray-600 dark:text-gray-300">Enhanced</span>
            </div>
          </div>
        </nav>
        
        {/* Main dashboard content */}
        <main>
          <EnhancedAnalyticsDashboard />
        </main>
        
        {/* Footer with performance info */}
        <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-8">
          <div class="container mx-auto px-4 py-4">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between text-sm text-gray-600 dark:text-gray-400">
              <div class="flex items-center space-x-4">
                <span>Phase 1: Enhanced Analytics</span>
                <span class="flex items-center">
                  <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  TimescaleDB Enabled
                </span>
              </div>
              <div class="mt-2 sm:mt-0 flex items-center space-x-4">
                <span>Real-time Updates</span>
                <span>Multi-tenant Support</span>
                <a 
                  href="/api/analytics/enhanced/health" 
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                  target="_blank"
                >
                  API Health
                </a>
              </div>
            </div>
          </div>
        </footer>
      </div>
      
      {/* Client-side performance monitoring */}
      <script>{`
        // Performance monitoring for Phase 1 targets
        window.addEventListener('load', function() {
          // Monitor page load performance
          const perfData = performance.getEntriesByType('navigation')[0];
          if (perfData) {
            const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
            console.log('Enhanced Analytics Dashboard Load Time:', loadTime + 'ms');
            
            // Log if we're meeting Phase 1 targets
            if (loadTime < 2000) {
              console.log('✅ Meeting Phase 1 target: Page load < 2s');
            } else {
              console.warn('⚠️ Page load time exceeds Phase 1 target of 2s');
            }
          }
          
          // Monitor API response times
          const originalFetch = window.fetch;
          window.fetch = function(...args) {
            const startTime = performance.now();
            return originalFetch.apply(this, args).then(response => {
              const endTime = performance.now();
              const duration = endTime - startTime;
              
              if (args[0].includes('/api/analytics/enhanced/')) {
                console.log('API Response Time:', args[0], duration + 'ms');
                
                if (duration < 100) {
                  console.log('✅ Meeting Phase 1 target: API response < 100ms');
                } else {
                  console.warn('⚠️ API response time exceeds Phase 1 target of 100ms');
                }
              }
              
              return response;
            });
          };
        });
        
        // Dark mode toggle functionality
        function toggleDarkMode() {
          document.documentElement.classList.toggle('dark');
          localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
        }
        
        // Initialize dark mode from localStorage
        if (localStorage.getItem('darkMode') === 'true') {
          document.documentElement.classList.add('dark');
        }
        
        // Auto-refresh functionality for real-time data
        let autoRefreshEnabled = true;
        let refreshInterval;
        
        function toggleAutoRefresh() {
          autoRefreshEnabled = !autoRefreshEnabled;
          if (autoRefreshEnabled) {
            startAutoRefresh();
          } else {
            stopAutoRefresh();
          }
        }
        
        function startAutoRefresh() {
          refreshInterval = setInterval(() => {
            // Trigger refresh of real-time components
            window.dispatchEvent(new CustomEvent('refreshRealTimeData'));
          }, 60000); // Every minute
        }
        
        function stopAutoRefresh() {
          if (refreshInterval) {
            clearInterval(refreshInterval);
          }
        }
        
        // Start auto-refresh by default
        startAutoRefresh();
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', stopAutoRefresh);
      `}</script>
    </>
  );
}
