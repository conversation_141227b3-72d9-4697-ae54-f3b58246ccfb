import { HandlerContext } from "$fresh/server.ts";
import { getUserTenantId } from "../../../utils/auth.ts";

const ANALYTICS_API_URL = Deno.env.get("ANALYTICS_API_URL") || "http://localhost:3002";

export const handler = {
  async GET(req: Request, ctx: HandlerContext) {
    return proxyRequest(req, ctx, "GET");
  },
  
  async POST(req: Request, ctx: HandlerContext) {
    return proxyRequest(req, ctx, "POST");
  },
  
  async PUT(req: Request, ctx: HandlerContext) {
    return proxyRequest(req, ctx, "PUT");
  },
  
  async DELETE(req: Request, ctx: HandlerContext) {
    return proxyRequest(req, ctx, "DELETE");
  },
};

async function proxyRequest(req: Request, ctx: HandlerContext, method: string) {
  const user = ctx.state.user;
  
  if (!user) {
    return Response.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const url = new URL(req.url);
    const path = ctx.params.path;
    const targetUrl = `${ANALYTICS_API_URL}/api/analytics/${path}${url.search}`;

    // Prepare headers for the proxied request
    const headers = new Headers();
    
    // Copy relevant headers from original request
    const headersToProxy = [
      "content-type",
      "accept",
      "user-agent",
      "accept-language",
      "accept-encoding"
    ];
    
    headersToProxy.forEach(headerName => {
      const value = req.headers.get(headerName);
      if (value) {
        headers.set(headerName, value);
      }
    });

    // Add authentication and tenant information
    headers.set("Authorization", req.headers.get("Authorization") || "");
    headers.set("X-Tenant-ID", getUserTenantId(user));
    headers.set("X-User-ID", user.id);
    headers.set("X-User-Role", user.role);

    // Prepare request options
    const proxyReq: RequestInit = {
      method,
      headers,
    };

    // Include body for POST/PUT requests
    if (method === "POST" || method === "PUT") {
      try {
        const body = await req.text();
        proxyReq.body = body;
      } catch (error) {
        console.error("Error reading request body:", error);
        return Response.json(
          { error: "Invalid request body" },
          { status: 400 }
        );
      }
    }

    // Make the proxied request
    const response = await fetch(targetUrl, proxyReq);
    
    // Prepare response headers
    const responseHeaders = new Headers();
    
    // Copy relevant response headers
    const responseHeadersToProxy = [
      "content-type",
      "cache-control",
      "expires",
      "last-modified",
      "etag"
    ];
    
    responseHeadersToProxy.forEach(headerName => {
      const value = response.headers.get(headerName);
      if (value) {
        responseHeaders.set(headerName, value);
      }
    });

    // Add CORS headers if needed
    responseHeaders.set("Access-Control-Allow-Origin", "*");
    responseHeaders.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    responseHeaders.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

    // Return the proxied response
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
  } catch (error) {
    console.error("Analytics proxy error:", error);
    
    // Return appropriate error response
    if (error instanceof TypeError && error.message.includes("fetch")) {
      return Response.json(
        { error: "Analytics service unavailable" },
        { status: 503 }
      );
    }
    
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
