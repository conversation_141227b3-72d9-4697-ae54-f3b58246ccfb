import { Handlers } from "$fresh/server.ts";

// API Proxy for Enhanced Analytics Dashboard
// Forwards requests to the Analytics Service (port 3002)

const ANALYTICS_SERVICE_URL = Deno.env.get("ANALYTICS_SERVICE_URL") || "http://localhost:3002";

export const handler: Handlers = {
  async GET(req) {
    try {
      const url = new URL(req.url);
      const range = url.searchParams.get("range") || "30d";
      const dateFrom = url.searchParams.get("dateFrom");
      const dateTo = url.searchParams.get("dateTo");
      
      // Extract tenant ID from headers or use default
      const tenantId = req.headers.get("x-tenant-id") || "00000000-0000-0000-0000-000000000001";
      
      // Build query parameters
      const params = new URLSearchParams();
      params.set("range", range);
      if (dateFrom) params.set("dateFrom", dateFrom);
      if (dateTo) params.set("dateTo", dateTo);
      
      // Forward request to Analytics Service
      const analyticsUrl = `${ANALYTICS_SERVICE_URL}/api/enhanced-analytics/dashboard?${params.toString()}`;
      
      const response = await fetch(analyticsUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Tenant-ID": tenantId,
          // Forward any authorization headers
          ...(req.headers.get("authorization") && {
            "Authorization": req.headers.get("authorization")!,
          }),
        },
      });
      
      if (!response.ok) {
        throw new Error(`Analytics service responded with ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Add caching headers for performance
      const headers = new Headers({
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=300", // 5 minutes cache
        "X-Powered-By": "Fresh Dashboard",
      });
      
      return new Response(JSON.stringify(data), {
        status: 200,
        headers,
      });
      
    } catch (error) {
      console.error("Dashboard API proxy error:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to fetch dashboard data",
          message: error instanceof Error ? error.message : "Unknown error",
          timestamp: new Date().toISOString(),
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }
  },
};
