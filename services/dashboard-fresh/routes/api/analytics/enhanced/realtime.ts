import { Handlers } from "$fresh/server.ts";

// API Proxy for Real-time Analytics Data
// Forwards requests to the Analytics Service for real-time revenue tracking

const ANALYTICS_SERVICE_URL = Deno.env.get("ANALYTICS_SERVICE_URL") || "http://localhost:3002";

export const handler: Handlers = {
  async GET(req) {
    try {
      // Extract tenant ID from headers or use default
      const tenantId = req.headers.get("x-tenant-id") || "00000000-0000-0000-0000-000000000001";
      
      // Forward request to Analytics Service
      const analyticsUrl = `${ANALYTICS_SERVICE_URL}/api/enhanced-analytics/realtime/revenue`;
      
      const response = await fetch(analyticsUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Tenant-ID": tenantId,
          // Forward any authorization headers
          ...(req.headers.get("authorization") && {
            "Authorization": req.headers.get("authorization")!,
          }),
        },
      });
      
      if (!response.ok) {
        throw new Error(`Analytics service responded with ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Add caching headers for real-time data (shorter cache)
      const headers = new Headers({
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=60", // 1 minute cache for real-time data
        "X-Powered-By": "Fresh Dashboard",
      });
      
      return new Response(JSON.stringify(data), {
        status: 200,
        headers,
      });
      
    } catch (error) {
      console.error("Real-time API proxy error:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to fetch real-time data",
          message: error instanceof Error ? error.message : "Unknown error",
          timestamp: new Date().toISOString(),
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }
  },
};
