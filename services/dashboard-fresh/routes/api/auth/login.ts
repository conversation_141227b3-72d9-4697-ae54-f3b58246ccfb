import { HandlerContext } from "$fresh/server.ts";
import { setCookie } from "$std/http/cookie.ts";
import { generateJWT, verifyPassword } from "../../../utils/auth.ts";
import { queryOneWithTenant } from "../../../utils/database.ts";

interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export const handler = {
  async POST(req: Request, ctx: HandlerContext) {
    try {
      const body: LoginRequest = await req.json();
      
      if (!body.email || !body.password) {
        return Response.json(
          { success: false, error: "Email and password are required" },
          { status: 400 }
        );
      }

      // Find user by email
      const user = await queryOneWithTenant(
        "SELECT * FROM users WHERE email = $1 AND is_active = true",
        [body.email.toLowerCase()]
      );

      if (!user) {
        return Response.json(
          { success: false, error: "Invalid email or password" },
          { status: 401 }
        );
      }

      // Verify password
      const isValidPassword = await verifyPassword(body.password, user.password_hash);
      
      if (!isValidPassword) {
        return Response.json(
          { success: false, error: "Invalid email or password" },
          { status: 401 }
        );
      }

      // Generate JWT token
      const tokenExpiry = body.rememberMe ? "30d" : "7d";
      const token = await generateJWT(user, tokenExpiry);

      // Prepare user data (exclude sensitive information)
      const userData = {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        companyName: user.company_name,
        role: user.role,
        tenant_id: user.tenant_id,
        isActive: user.is_active,
        emailVerified: user.email_verified,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
      };

      // Create response with cookie
      const response = Response.json({
        success: true,
        data: {
          user: userData,
          token,
          expiresIn: tokenExpiry,
        },
      });

      // Set HTTP-only cookie for browser sessions
      setCookie(response.headers, {
        name: "auth_token",
        value: token,
        httpOnly: true,
        secure: Deno.env.get("DENO_ENV") === "production",
        sameSite: "Lax",
        maxAge: body.rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60, // 30 days or 7 days
        path: "/",
      });

      return response;
    } catch (error) {
      console.error("Login error:", error);
      return Response.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
};
