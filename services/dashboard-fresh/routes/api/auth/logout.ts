import { HandlerContext } from "$fresh/server.ts";
import { deleteCookie } from "$std/http/cookie.ts";

export const handler = {
  async POST(req: Request, ctx: HandlerContext) {
    try {
      // Create response
      const response = Response.json({
        success: true,
        message: "Logged out successfully",
      });

      // Clear the auth cookie
      deleteCookie(response.headers, "auth_token", {
        path: "/",
      });

      return response;
    } catch (error) {
      console.error("Logout error:", error);
      return Response.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
};
