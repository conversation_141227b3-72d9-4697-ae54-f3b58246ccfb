import { HandlerContext } from "$fresh/server.ts";
import { dashboardService } from "../../../services/dashboardService.ts";

export const handler = {
  async GET(req: Request, ctx: HandlerContext) {
    const user = ctx.state.user;
    
    if (!user) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    try {
      const url = new URL(req.url);
      const dateFrom = url.searchParams.get("date_from");
      const dateTo = url.searchParams.get("date_to");
      const period = url.searchParams.get("period") || "30d";

      const overview = await dashboardService.getOverview(user, {
        dateFrom,
        dateTo,
        period,
      });

      return Response.json({
        success: true,
        data: overview,
      });
    } catch (error) {
      console.error("Dashboard overview API error:", error);
      return Response.json(
        { success: false, error: "Failed to fetch dashboard overview" },
        { status: 500 }
      );
    }
  },
};
