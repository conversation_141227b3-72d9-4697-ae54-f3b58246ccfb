import { HandlerContext } from "$fresh/server.ts";
import { checkDatabaseHealth } from "../../utils/database.ts";
import { checkRedisHealth } from "../../utils/redis.ts";

export const handler = {
  async GET(req: Request, ctx: HandlerContext) {
    const startTime = Date.now();
    
    try {
      // Check database connectivity
      const dbHealthy = await checkDatabaseHealth();
      
      // Check Redis connectivity
      const redisHealthy = await checkRedisHealth();
      
      // Calculate response time
      const responseTime = Date.now() - startTime;
      
      // Determine overall health status
      const isHealthy = dbHealthy && redisHealthy;
      const status = isHealthy ? "healthy" : "unhealthy";
      
      const healthData = {
        status,
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        services: {
          database: {
            status: dbHealthy ? "healthy" : "unhealthy",
            type: "PostgreSQL with TimescaleDB",
          },
          redis: {
            status: redisHealthy ? "healthy" : "unhealthy",
            type: "Redis Cache",
          },
        },
        version: "1.0.0",
        environment: Deno.env.get("DENO_ENV") || "development",
      };
      
      return Response.json(healthData, {
        status: isHealthy ? 200 : 503,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
      });
    } catch (error) {
      console.error("Health check error:", error);
      
      const responseTime = Date.now() - startTime;
      
      return Response.json({
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        error: "Health check failed",
        services: {
          database: { status: "unknown" },
          redis: { status: "unknown" },
        },
        version: "1.0.0",
        environment: Deno.env.get("DENO_ENV") || "development",
      }, {
        status: 503,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
      });
    }
  },
};
