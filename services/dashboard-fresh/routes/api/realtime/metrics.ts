import { HandlerContext } from "$fresh/server.ts";
import { getUserTenantId } from "../../../utils/auth.ts";

export const handler = {
  async GET(req: Request, ctx: HandlerContext) {
    const user = ctx.state.user;
    
    if (!user) {
      return new Response("Unauthorized", { status: 401 });
    }

    const tenantId = getUserTenantId(user);

    // Create a readable stream for Server-Sent Events
    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection message
        controller.enqueue(`data: ${JSON.stringify({
          type: 'connected',
          timestamp: new Date().toISOString(),
          message: 'Real-time metrics stream connected'
        })}\n\n`);

        // Function to generate and send mock real-time data
        const sendMetricsUpdate = () => {
          const data = {
            type: 'metrics_update',
            timestamp: new Date().toISOString(),
            tenantId,
            totalRevenue: Math.floor(Math.random() * 50000) + 100000,
            activeUsers: Math.floor(Math.random() * 500) + 200,
            conversionRate: (Math.random() * 5 + 1).toFixed(2),
            totalLinks: Math.floor(Math.random() * 100) + 50,
            recentActivity: [
              {
                id: Date.now().toString(),
                description: `New conversion: $${(Math.random() * 200 + 50).toFixed(2)}`,
                time: 'Just now',
                type: 'conversion'
              }
            ]
          };
          
          try {
            controller.enqueue(`data: ${JSON.stringify(data)}\n\n`);
          } catch (error) {
            console.error('Error sending SSE data:', error);
          }
        };

        // Send initial data
        sendMetricsUpdate();

        // Send updates every 5 seconds
        const interval = setInterval(sendMetricsUpdate, 5000);

        // Send heartbeat every 30 seconds to keep connection alive
        const heartbeat = setInterval(() => {
          try {
            controller.enqueue(`data: ${JSON.stringify({
              type: 'heartbeat',
              timestamp: new Date().toISOString()
            })}\n\n`);
          } catch (error) {
            console.error('Error sending heartbeat:', error);
          }
        }, 30000);

        // Cleanup function
        const cleanup = () => {
          clearInterval(interval);
          clearInterval(heartbeat);
          try {
            controller.close();
          } catch (error) {
            console.error('Error closing SSE stream:', error);
          }
        };

        // Handle client disconnect
        req.signal.addEventListener("abort", cleanup);

        // Handle stream errors
        controller.error = (error: Error) => {
          console.error('SSE stream error:', error);
          cleanup();
        };
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    });
  },
};
