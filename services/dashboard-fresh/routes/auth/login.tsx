import { defineRoute } from "$fresh/server.ts";
import LoginForm from "../../islands/auth/LoginForm.tsx";
import { AuthCard, AlertCard } from "../../components/ui/Card.tsx";
import DarkModeToggle from "../../islands/ui/DarkModeToggle.tsx";

export default defineRoute(async (req, ctx) => {
  // Redirect if already authenticated
  if (ctx.state.isAuthenticated) {
    const url = new URL(req.url);
    const redirect = url.searchParams.get("redirect") || "/";
    return new Response("", {
      status: 302,
      headers: { Location: redirect },
    });
  }

  const url = new URL(req.url);
  const redirect = url.searchParams.get("redirect");
  const error = url.searchParams.get("error");

  return (
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-6 px-4 sm:py-12 sm:px-6 lg:px-8 transition-colors duration-200">
      {/* Dark Mode Toggle */}
      <div class="absolute top-4 right-4 z-10">
        <DarkModeToggle />
      </div>

      {/* Header Section */}
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Logo */}
        <div class="flex justify-center mb-6 sm:mb-8">
          <div class="relative">
            <div class="absolute inset-0 bg-primary-600 dark:bg-primary-500 rounded-2xl blur-lg opacity-20 animate-pulse-slow"></div>
            <div class="relative bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-2xl shadow-medium dark:shadow-dark-medium border border-gray-200 dark:border-gray-700">
              <svg class="h-10 w-10 sm:h-12 sm:w-12 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
        </div>

        <div class="text-center space-y-2">
          <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 font-sans">
            Welcome back
          </h1>
          <p class="text-sm text-gray-600 dark:text-gray-400 px-4 sm:px-0">
            Sign in to access your e-commerce analytics dashboard
          </p>
        </div>
      </div>

      {/* Main Login Card */}
      <div class="mt-6 sm:mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <AuthCard
          className="animate-slide-up mx-4 sm:mx-0"
        >
          {/* Error Message */}
          {error && (
            <div class="mb-6">
              <AlertCard variant="error">
                {error === "invalid_credentials" && "Invalid email or password"}
                {error === "session_expired" && "Your session has expired. Please sign in again."}
                {error === "unauthorized" && "You need to sign in to access this page"}
                {!["invalid_credentials", "session_expired", "unauthorized"].includes(error) && "An error occurred. Please try again."}
              </AlertCard>
            </div>
          )}

          {/* Login Form */}
          <LoginForm redirectTo={redirect} />

          {/* Footer Links */}
          <div class="mt-8">
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-200 dark:border-gray-700" />
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-3 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 font-medium">
                  Need help?
                </span>
              </div>
            </div>

            <div class="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <a
                href="/auth/forgot-password"
                class="text-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800"
              >
                Forgot password?
              </a>
              <a
                href="/auth/register"
                class="text-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800"
              >
                Create account
              </a>
            </div>
          </div>
        </AuthCard>
      </div>

      {/* Demo Credentials */}
      <div class="mt-6 sm:mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <AlertCard
          variant="info"
          title="Demo Credentials"
          className="animate-fade-in mx-4 sm:mx-0"
        >
          <div class="text-sm space-y-1">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> demo123</p>
            <p class="text-xs opacity-75 mt-2">
              Click "Use demo credentials" in the form above for quick access
            </p>
          </div>
        </AlertCard>
      </div>

      {/* Footer */}
      <div class="mt-8 sm:mt-12 text-center px-4">
        <p class="text-xs text-gray-500 dark:text-gray-400">
          © 2024 E-commerce Analytics SaaS. All rights reserved.
        </p>
      </div>
    </div>
  );
});
