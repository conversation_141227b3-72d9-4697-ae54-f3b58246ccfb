#!/bin/bash

# Fresh Dashboard Deployment Script
set -e

echo "🚀 Starting Fresh Dashboard deployment..."

# Configuration
ENVIRONMENT=${1:-development}
DOCKER_COMPOSE_FILE="docker-compose.yml"
if [ "$ENVIRONMENT" = "production" ]; then
    DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    if ! command -v deno &> /dev/null; then
        log_error "Deno is not installed"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    # Unit tests
    log_info "Running unit tests..."
    deno test tests/unit/ --allow-all
    
    # Integration tests
    log_info "Running integration tests..."
    deno test tests/integration/ --allow-all
    
    log_success "All tests passed"
}

# Build application
build_application() {
    log_info "Building Fresh application..."
    
    # Type check
    deno check main.ts
    
    # Build static assets if needed
    if [ -f "build.ts" ]; then
        deno run -A build.ts
    fi
    
    log_success "Application built successfully"
}

# Deploy with Docker Compose
deploy_docker() {
    log_info "Deploying with Docker Compose..."
    
    # Stop existing containers
    docker-compose -f $DOCKER_COMPOSE_FILE down
    
    # Build and start containers
    docker-compose -f $DOCKER_COMPOSE_FILE up --build -d
    
    log_success "Docker containers started"
}

# Wait for services to be ready
wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    # Wait for database
    log_info "Waiting for database..."
    timeout=60
    while ! docker-compose -f $DOCKER_COMPOSE_FILE exec -T postgres pg_isready -U postgres; do
        sleep 1
        timeout=$((timeout - 1))
        if [ $timeout -eq 0 ]; then
            log_error "Database failed to start"
            exit 1
        fi
    done
    
    # Wait for Redis
    log_info "Waiting for Redis..."
    timeout=60
    while ! docker-compose -f $DOCKER_COMPOSE_FILE exec -T redis redis-cli ping; do
        sleep 1
        timeout=$((timeout - 1))
        if [ $timeout -eq 0 ]; then
            log_error "Redis failed to start"
            exit 1
        fi
    done
    
    # Wait for application
    log_info "Waiting for application..."
    timeout=120
    while ! curl -f http://localhost:8000/api/health > /dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -eq 0 ]; then
            log_error "Application failed to start"
            exit 1
        fi
    done
    
    log_success "All services are ready"
}

# Run health checks
run_health_checks() {
    log_info "Running health checks..."
    
    # Application health check
    if deno run --allow-net --allow-env health_check.ts; then
        log_success "Application health check passed"
    else
        log_error "Application health check failed"
        exit 1
    fi
    
    # Database connectivity
    if docker-compose -f $DOCKER_COMPOSE_FILE exec -T postgres psql -U postgres -d ecommerce_analytics -c "SELECT 1;" > /dev/null; then
        log_success "Database connectivity check passed"
    else
        log_error "Database connectivity check failed"
        exit 1
    fi
    
    # Redis connectivity
    if docker-compose -f $DOCKER_COMPOSE_FILE exec -T redis redis-cli ping | grep -q PONG; then
        log_success "Redis connectivity check passed"
    else
        log_error "Redis connectivity check failed"
        exit 1
    fi
}

# Run smoke tests
run_smoke_tests() {
    log_info "Running smoke tests..."
    
    # Test login endpoint
    login_response=$(curl -s -X POST http://localhost:8000/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"demo123"}')
    
    if echo "$login_response" | grep -q '"success":true'; then
        log_success "Login endpoint test passed"
    else
        log_error "Login endpoint test failed"
        exit 1
    fi
    
    # Test dashboard endpoint (requires auth)
    # This would require extracting token from login response
    log_info "Dashboard endpoint test skipped (requires authentication setup)"
    
    log_success "Smoke tests completed"
}

# Show deployment status
show_status() {
    log_info "Deployment Status:"
    echo ""
    docker-compose -f $DOCKER_COMPOSE_FILE ps
    echo ""
    log_info "Application URLs:"
    echo "  🌐 Dashboard: http://localhost:8000"
    echo "  🔍 Health Check: http://localhost:8000/api/health"
    echo "  📊 Database: localhost:5432"
    echo "  🗄️  Redis: localhost:6379"
    echo ""
    log_success "Deployment completed successfully!"
}

# Cleanup function
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "Deployment failed!"
        log_info "Checking container logs..."
        docker-compose -f $DOCKER_COMPOSE_FILE logs --tail=50
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main deployment flow
main() {
    echo "🎯 Environment: $ENVIRONMENT"
    echo "📁 Compose file: $DOCKER_COMPOSE_FILE"
    echo ""
    
    check_prerequisites
    
    if [ "$ENVIRONMENT" != "production" ]; then
        run_tests
    fi
    
    build_application
    deploy_docker
    wait_for_services
    run_health_checks
    run_smoke_tests
    show_status
}

# Run main function
main
