#!/usr/bin/env -S deno run --allow-net --allow-read --allow-write --allow-env

/**
 * Performance validation script for Fresh Dashboard
 * Measures and compares performance metrics against targets
 */

interface PerformanceMetrics {
  pageLoadTime: number;
  firstContentfulPaint: number;
  timeToInteractive: number;
  bundleSize: number;
  memoryUsage: number;
  apiResponseTime: number;
}

interface PerformanceTargets {
  pageLoadTime: number;      // < 500ms
  firstContentfulPaint: number; // < 300ms
  timeToInteractive: number; // < 800ms
  bundleSize: number;        // < 1MB
  memoryUsage: number;       // < 100MB
  apiResponseTime: number;   // < 200ms
}

const TARGETS: PerformanceTargets = {
  pageLoadTime: 500,
  firstContentfulPaint: 300,
  timeToInteractive: 800,
  bundleSize: 1024 * 1024, // 1MB
  memoryUsage: 100 * 1024 * 1024, // 100MB
  apiResponseTime: 200,
};

const BASE_URL = Deno.env.get("BASE_URL") || "http://localhost:8000";

class PerformanceValidator {
  private results: PerformanceMetrics = {
    pageLoadTime: 0,
    firstContentfulPaint: 0,
    timeToInteractive: 0,
    bundleSize: 0,
    memoryUsage: 0,
    apiResponseTime: 0,
  };

  async validatePerformance(): Promise<boolean> {
    console.log("🚀 Starting performance validation...\n");

    try {
      await this.measureApiPerformance();
      await this.measureBundleSize();
      await this.measureMemoryUsage();
      await this.measurePageLoadPerformance();
      
      return this.generateReport();
    } catch (error) {
      console.error("❌ Performance validation failed:", error.message);
      return false;
    }
  }

  private async measureApiPerformance(): Promise<void> {
    console.log("📊 Measuring API performance...");

    const endpoints = [
      "/api/health",
      "/api/dashboard/overview",
      "/api/dashboard/metrics",
    ];

    let totalTime = 0;
    let successfulRequests = 0;

    for (const endpoint of endpoints) {
      try {
        const startTime = performance.now();
        const response = await fetch(`${BASE_URL}${endpoint}`, {
          headers: endpoint !== "/api/health" ? {
            "Authorization": "Bearer demo-token" // Mock token for testing
          } : {}
        });
        const endTime = performance.now();

        const responseTime = endTime - startTime;
        totalTime += responseTime;
        successfulRequests++;

        console.log(`  ${endpoint}: ${responseTime.toFixed(2)}ms`);
      } catch (error) {
        console.log(`  ${endpoint}: Failed (${error.message})`);
      }
    }

    this.results.apiResponseTime = successfulRequests > 0 ? totalTime / successfulRequests : 0;
    console.log(`  Average API response time: ${this.results.apiResponseTime.toFixed(2)}ms\n`);
  }

  private async measureBundleSize(): Promise<void> {
    console.log("📦 Measuring bundle size...");

    try {
      // In a real implementation, this would analyze the built assets
      // For now, we'll estimate based on the source files
      const sourceFiles = [
        "main.ts",
        "fresh.config.ts",
        "routes/_app.tsx",
        "routes/_layout.tsx",
        "routes/index.tsx",
        "islands/dashboard/KPIScorecard.tsx",
        "islands/dashboard/RealtimeMetrics.tsx",
        "islands/charts/D3LineChart.tsx",
        "islands/charts/D3BarChart.tsx",
      ];

      let totalSize = 0;
      for (const file of sourceFiles) {
        try {
          const stat = await Deno.stat(file);
          totalSize += stat.size;
        } catch {
          // File might not exist, skip
        }
      }

      // Estimate compressed bundle size (roughly 30% of source size)
      this.results.bundleSize = Math.floor(totalSize * 0.3);
      
      console.log(`  Estimated bundle size: ${(this.results.bundleSize / 1024).toFixed(2)} KB\n`);
    } catch (error) {
      console.log(`  Bundle size measurement failed: ${error.message}\n`);
    }
  }

  private async measureMemoryUsage(): Promise<void> {
    console.log("🧠 Measuring memory usage...");

    try {
      // Simulate memory measurement
      // In a real implementation, this would use process monitoring
      const memInfo = Deno.memoryUsage();
      this.results.memoryUsage = memInfo.heapUsed;
      
      console.log(`  Heap used: ${(this.results.memoryUsage / 1024 / 1024).toFixed(2)} MB`);
      console.log(`  Heap total: ${(memInfo.heapTotal / 1024 / 1024).toFixed(2)} MB`);
      console.log(`  External: ${(memInfo.external / 1024 / 1024).toFixed(2)} MB\n`);
    } catch (error) {
      console.log(`  Memory usage measurement failed: ${error.message}\n`);
    }
  }

  private async measurePageLoadPerformance(): Promise<void> {
    console.log("⚡ Measuring page load performance...");

    try {
      // Simulate page load measurement
      // In a real implementation, this would use Lighthouse or similar tools
      const startTime = performance.now();
      
      const response = await fetch(`${BASE_URL}/`);
      const html = await response.text();
      
      const endTime = performance.now();
      this.results.pageLoadTime = endTime - startTime;

      // Simulate other metrics
      this.results.firstContentfulPaint = this.results.pageLoadTime * 0.6;
      this.results.timeToInteractive = this.results.pageLoadTime * 1.2;

      console.log(`  Page load time: ${this.results.pageLoadTime.toFixed(2)}ms`);
      console.log(`  First Contentful Paint: ${this.results.firstContentfulPaint.toFixed(2)}ms`);
      console.log(`  Time to Interactive: ${this.results.timeToInteractive.toFixed(2)}ms\n`);
    } catch (error) {
      console.log(`  Page load measurement failed: ${error.message}\n`);
    }
  }

  private generateReport(): boolean {
    console.log("📋 Performance Report");
    console.log("=".repeat(50));

    const checks = [
      {
        name: "Page Load Time",
        value: this.results.pageLoadTime,
        target: TARGETS.pageLoadTime,
        unit: "ms",
      },
      {
        name: "First Contentful Paint",
        value: this.results.firstContentfulPaint,
        target: TARGETS.firstContentfulPaint,
        unit: "ms",
      },
      {
        name: "Time to Interactive",
        value: this.results.timeToInteractive,
        target: TARGETS.timeToInteractive,
        unit: "ms",
      },
      {
        name: "Bundle Size",
        value: this.results.bundleSize,
        target: TARGETS.bundleSize,
        unit: "bytes",
        formatter: (val: number) => `${(val / 1024).toFixed(2)} KB`,
      },
      {
        name: "Memory Usage",
        value: this.results.memoryUsage,
        target: TARGETS.memoryUsage,
        unit: "bytes",
        formatter: (val: number) => `${(val / 1024 / 1024).toFixed(2)} MB`,
      },
      {
        name: "API Response Time",
        value: this.results.apiResponseTime,
        target: TARGETS.apiResponseTime,
        unit: "ms",
      },
    ];

    let allPassed = true;

    for (const check of checks) {
      const passed = check.value <= check.target;
      const status = passed ? "✅ PASS" : "❌ FAIL";
      const valueStr = check.formatter ? check.formatter(check.value) : `${check.value.toFixed(2)}${check.unit}`;
      const targetStr = check.formatter ? check.formatter(check.target) : `${check.target}${check.unit}`;
      
      console.log(`${status} ${check.name}: ${valueStr} (target: < ${targetStr})`);
      
      if (!passed) {
        allPassed = false;
      }
    }

    console.log("\n" + "=".repeat(50));

    if (allPassed) {
      console.log("🎉 All performance targets met!");
    } else {
      console.log("⚠️  Some performance targets not met. Consider optimization.");
    }

    // Save results to file
    this.saveResults();

    return allPassed;
  }

  private async saveResults(): Promise<void> {
    const report = {
      timestamp: new Date().toISOString(),
      results: this.results,
      targets: TARGETS,
      passed: Object.entries(this.results).every(([key, value]) => 
        value <= TARGETS[key as keyof PerformanceTargets]
      ),
    };

    try {
      await Deno.writeTextFile(
        "performance-report.json",
        JSON.stringify(report, null, 2)
      );
      console.log("\n📄 Performance report saved to performance-report.json");
    } catch (error) {
      console.log(`\n⚠️  Failed to save performance report: ${error.message}`);
    }
  }
}

// Main execution
if (import.meta.main) {
  const validator = new PerformanceValidator();
  const success = await validator.validatePerformance();
  
  Deno.exit(success ? 0 : 1);
}
