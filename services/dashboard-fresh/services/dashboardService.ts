import { User, getUserTenantId } from "../utils/auth.ts";
import { queryWithTenant, queryAggregated, queryTimeSeries } from "../utils/database.ts";
import { getCachedDashboardData, cacheDashboardData } from "../utils/redis.ts";

export interface DashboardOverview {
  totalRevenue: number;
  revenueChange: number;
  activeUsers: number;
  usersChange: number;
  conversionRate: number;
  conversionChange: number;
  totalLinks: number;
  linksChange: number;
  recentActivity: ActivityItem[];
  topLinks: TopLink[];
}

export interface ActivityItem {
  id: string;
  description: string;
  time: string;
  type: 'link_created' | 'conversion' | 'integration_added' | 'user_registered';
}

export interface TopLink {
  id: string;
  url: string;
  title: string;
  clicks: number;
  conversions: number;
  conversionRate: number;
}

export interface MetricsData {
  revenue: TimeSeriesPoint[];
  users: TimeSeriesPoint[];
  conversions: TimeSeriesPoint[];
  clicks: TimeSeriesPoint[];
}

export interface TimeSeriesPoint {
  timestamp: string;
  value: number;
}

export class DashboardService {
  async getOverview(
    user: User,
    params: {
      dateFrom?: string;
      dateTo?: string;
      period?: string;
    } = {}
  ): Promise<DashboardOverview> {
    const tenantId = getUserTenantId(user);
    const cacheKey = `overview:${tenantId}:${params.period || '30d'}`;
    
    // Try cache first
    const cached = await getCachedDashboardData<DashboardOverview>(tenantId, "overview");
    if (cached) {
      return cached;
    }

    try {
      // Calculate date range
      const { startDate, endDate, previousStartDate, previousEndDate } = this.getDateRange(params);

      // Fetch current period data
      const [
        currentRevenue,
        currentUsers,
        currentConversions,
        currentLinks,
        recentActivity,
        topLinks
      ] = await Promise.all([
        this.getRevenueMetrics(tenantId, startDate, endDate),
        this.getUserMetrics(tenantId, startDate, endDate),
        this.getConversionMetrics(tenantId, startDate, endDate),
        this.getLinkMetrics(tenantId, startDate, endDate),
        this.getRecentActivity(tenantId, 10),
        this.getTopLinks(tenantId, startDate, endDate, 5)
      ]);

      // Fetch previous period data for comparison
      const [
        previousRevenue,
        previousUsers,
        previousConversions,
        previousLinks
      ] = await Promise.all([
        this.getRevenueMetrics(tenantId, previousStartDate, previousEndDate),
        this.getUserMetrics(tenantId, previousStartDate, previousEndDate),
        this.getConversionMetrics(tenantId, previousStartDate, previousEndDate),
        this.getLinkMetrics(tenantId, previousStartDate, previousEndDate)
      ]);

      // Calculate changes
      const overview: DashboardOverview = {
        totalRevenue: currentRevenue.total,
        revenueChange: this.calculatePercentageChange(currentRevenue.total, previousRevenue.total),
        activeUsers: currentUsers.active,
        usersChange: this.calculatePercentageChange(currentUsers.active, previousUsers.active),
        conversionRate: currentConversions.rate,
        conversionChange: this.calculatePercentageChange(currentConversions.rate, previousConversions.rate),
        totalLinks: currentLinks.total,
        linksChange: this.calculatePercentageChange(currentLinks.total, previousLinks.total),
        recentActivity,
        topLinks
      };

      // Cache result for 5 minutes
      await cacheDashboardData(tenantId, "overview", overview, 300);
      
      return overview;
    } catch (error) {
      console.error("Error fetching dashboard overview:", error);
      
      // Return fallback data
      return {
        totalRevenue: 0,
        revenueChange: 0,
        activeUsers: 0,
        usersChange: 0,
        conversionRate: 0,
        conversionChange: 0,
        totalLinks: 0,
        linksChange: 0,
        recentActivity: [],
        topLinks: []
      };
    }
  }

  async getMetrics(
    user: User,
    params: {
      dateFrom?: string;
      dateTo?: string;
      comparePeriod?: boolean;
    } = {}
  ): Promise<MetricsData> {
    const tenantId = getUserTenantId(user);
    const { startDate, endDate } = this.getDateRange(params);

    try {
      const [revenue, users, conversions, clicks] = await Promise.all([
        this.getRevenueTimeSeries(tenantId, startDate, endDate),
        this.getUserTimeSeries(tenantId, startDate, endDate),
        this.getConversionTimeSeries(tenantId, startDate, endDate),
        this.getClickTimeSeries(tenantId, startDate, endDate)
      ]);

      return { revenue, users, conversions, clicks };
    } catch (error) {
      console.error("Error fetching metrics:", error);
      return {
        revenue: [],
        users: [],
        conversions: [],
        clicks: []
      };
    }
  }

  async getAlerts(
    user: User,
    params: { limit?: number } = {}
  ): Promise<any[]> {
    const tenantId = getUserTenantId(user);
    const limit = params.limit || 5;

    try {
      // This would typically check for various alert conditions
      // For now, return mock alerts
      return [
        {
          id: "1",
          type: "warning",
          title: "Conversion rate dropped",
          message: "Conversion rate is 15% lower than last week",
          timestamp: new Date().toISOString(),
          severity: "medium"
        }
      ];
    } catch (error) {
      console.error("Error fetching alerts:", error);
      return [];
    }
  }

  async getTopLinks(
    tenantId: string,
    startDate: Date,
    endDate: Date,
    limit: number = 5
  ): Promise<TopLink[]> {
    try {
      const results = await queryWithTenant(`
        SELECT 
          l.id,
          l.url,
          l.title,
          COUNT(DISTINCT c.id) as clicks,
          COUNT(DISTINCT conv.id) as conversions,
          CASE 
            WHEN COUNT(DISTINCT c.id) > 0 
            THEN (COUNT(DISTINCT conv.id)::float / COUNT(DISTINCT c.id) * 100)
            ELSE 0 
          END as conversion_rate
        FROM links l
        LEFT JOIN clicks c ON l.id = c.link_id 
          AND c.created_at >= $1 AND c.created_at <= $2
        LEFT JOIN conversions conv ON c.id = conv.click_id
        GROUP BY l.id, l.url, l.title
        ORDER BY clicks DESC
        LIMIT $3
      `, [startDate.toISOString(), endDate.toISOString(), limit], tenantId);

      return results.map((row: any) => ({
        id: row.id,
        url: row.url,
        title: row.title || 'Untitled Link',
        clicks: parseInt(row.clicks) || 0,
        conversions: parseInt(row.conversions) || 0,
        conversionRate: parseFloat(row.conversion_rate) || 0
      }));
    } catch (error) {
      console.error("Error fetching top links:", error);
      return [];
    }
  }

  private async getRevenueMetrics(tenantId: string, startDate: Date, endDate: Date) {
    try {
      const results = await queryWithTenant(`
        SELECT COALESCE(SUM(amount), 0) as total
        FROM conversions 
        WHERE created_at >= $1 AND created_at <= $2
      `, [startDate.toISOString(), endDate.toISOString()], tenantId);

      return { total: parseFloat(results[0]?.total) || 0 };
    } catch (error) {
      console.error("Error fetching revenue metrics:", error);
      return { total: 0 };
    }
  }

  private async getUserMetrics(tenantId: string, startDate: Date, endDate: Date) {
    try {
      const results = await queryWithTenant(`
        SELECT COUNT(DISTINCT user_id) as active
        FROM analytics_events 
        WHERE created_at >= $1 AND created_at <= $2
      `, [startDate.toISOString(), endDate.toISOString()], tenantId);

      return { active: parseInt(results[0]?.active) || 0 };
    } catch (error) {
      console.error("Error fetching user metrics:", error);
      return { active: 0 };
    }
  }

  private async getConversionMetrics(tenantId: string, startDate: Date, endDate: Date) {
    try {
      const results = await queryWithTenant(`
        SELECT 
          COUNT(DISTINCT c.id) as total_clicks,
          COUNT(DISTINCT conv.id) as total_conversions,
          CASE 
            WHEN COUNT(DISTINCT c.id) > 0 
            THEN (COUNT(DISTINCT conv.id)::float / COUNT(DISTINCT c.id) * 100)
            ELSE 0 
          END as rate
        FROM clicks c
        LEFT JOIN conversions conv ON c.id = conv.click_id
        WHERE c.created_at >= $1 AND c.created_at <= $2
      `, [startDate.toISOString(), endDate.toISOString()], tenantId);

      return { 
        rate: parseFloat(results[0]?.rate) || 0,
        total: parseInt(results[0]?.total_conversions) || 0
      };
    } catch (error) {
      console.error("Error fetching conversion metrics:", error);
      return { rate: 0, total: 0 };
    }
  }

  private async getLinkMetrics(tenantId: string, startDate: Date, endDate: Date) {
    try {
      const results = await queryWithTenant(`
        SELECT COUNT(*) as total
        FROM links 
        WHERE created_at >= $1 AND created_at <= $2
      `, [startDate.toISOString(), endDate.toISOString()], tenantId);

      return { total: parseInt(results[0]?.total) || 0 };
    } catch (error) {
      console.error("Error fetching link metrics:", error);
      return { total: 0 };
    }
  }

  private async getRecentActivity(tenantId: string, limit: number): Promise<ActivityItem[]> {
    try {
      // This would typically aggregate from multiple tables
      // For now, return mock data
      return [
        {
          id: "1",
          description: "New link created: Product Launch Campaign",
          time: "2 minutes ago",
          type: "link_created"
        },
        {
          id: "2", 
          description: "Conversion recorded: $125.00",
          time: "5 minutes ago",
          type: "conversion"
        }
      ];
    } catch (error) {
      console.error("Error fetching recent activity:", error);
      return [];
    }
  }

  private async getRevenueTimeSeries(tenantId: string, startDate: Date, endDate: Date): Promise<TimeSeriesPoint[]> {
    try {
      const results = await queryAggregated(
        'conversions',
        ['SUM(amount) as value'],
        'created_at',
        '1 day',
        startDate,
        endDate,
        tenantId
      );

      return results.map((row: any) => ({
        timestamp: row.time_bucket,
        value: parseFloat(row.value) || 0
      }));
    } catch (error) {
      console.error("Error fetching revenue time series:", error);
      return [];
    }
  }

  private async getUserTimeSeries(tenantId: string, startDate: Date, endDate: Date): Promise<TimeSeriesPoint[]> {
    try {
      const results = await queryAggregated(
        'analytics_events',
        ['COUNT(DISTINCT user_id) as value'],
        'created_at',
        '1 day',
        startDate,
        endDate,
        tenantId
      );

      return results.map((row: any) => ({
        timestamp: row.time_bucket,
        value: parseInt(row.value) || 0
      }));
    } catch (error) {
      console.error("Error fetching user time series:", error);
      return [];
    }
  }

  private async getConversionTimeSeries(tenantId: string, startDate: Date, endDate: Date): Promise<TimeSeriesPoint[]> {
    try {
      const results = await queryAggregated(
        'conversions',
        ['COUNT(*) as value'],
        'created_at',
        '1 day',
        startDate,
        endDate,
        tenantId
      );

      return results.map((row: any) => ({
        timestamp: row.time_bucket,
        value: parseInt(row.value) || 0
      }));
    } catch (error) {
      console.error("Error fetching conversion time series:", error);
      return [];
    }
  }

  private async getClickTimeSeries(tenantId: string, startDate: Date, endDate: Date): Promise<TimeSeriesPoint[]> {
    try {
      const results = await queryAggregated(
        'clicks',
        ['COUNT(*) as value'],
        'created_at',
        '1 day',
        startDate,
        endDate,
        tenantId
      );

      return results.map((row: any) => ({
        timestamp: row.time_bucket,
        value: parseInt(row.value) || 0
      }));
    } catch (error) {
      console.error("Error fetching click time series:", error);
      return [];
    }
  }

  private getDateRange(params: any) {
    const now = new Date();
    let startDate: Date;
    let endDate = new Date(params.dateTo || now);

    if (params.dateFrom) {
      startDate = new Date(params.dateFrom);
    } else {
      // Default to last 30 days
      const period = params.period || '30d';
      const days = parseInt(period.replace('d', '')) || 30;
      startDate = new Date(now.getTime() - (days * 24 * 60 * 60 * 1000));
    }

    // Calculate previous period for comparison
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousEndDate = new Date(startDate.getTime() - 1);
    const previousStartDate = new Date(previousEndDate.getTime() - periodLength);

    return { startDate, endDate, previousStartDate, previousEndDate };
  }

  private calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }
}

export const dashboardService = new DashboardService();
