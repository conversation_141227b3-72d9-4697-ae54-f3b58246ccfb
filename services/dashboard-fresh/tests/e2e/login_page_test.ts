/// <reference lib="deno.ns" />
import { assertEquals, assertExists } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { DOMParser } from "https://deno.land/x/deno_dom@v0.1.45/deno-dom-wasm.ts";

Deno.test("Login Page - Responsive Design and Accessibility", async (t: Deno.TestContext) => {
  const baseUrl = "http://localhost:8000";
  
  await t.step("should render login page with proper structure", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    assertEquals(response.status, 200);
    
    const html = await response.text();
    const doc = new DOMParser().parseFromString(html, "text/html");
    
    // Check for essential elements
    assertExists(doc?.querySelector('form'), "Login form should exist");
    assertExists(doc?.querySelector('input[type="email"]'), "Email input should exist");
    assertExists(doc?.querySelector('input[type="password"]'), "Password input should exist");
    assertExists(doc?.querySelector('button[type="submit"]'), "Submit button should exist");
    assertExists(doc?.querySelector('input[type="checkbox"]'), "Remember me checkbox should exist");
  });

  await t.step("should have proper accessibility attributes", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    const html = await response.text();
    const doc = new DOMParser().parseFromString(html, "text/html");
    
    // Check for accessibility attributes
    const emailInput = doc?.querySelector('input[type="email"]');
    const passwordInput = doc?.querySelector('input[type="password"]');
    const submitButton = doc?.querySelector('button[type="submit"]');
    
    assertExists(emailInput?.getAttribute('id'), "Email input should have id");
    assertExists(passwordInput?.getAttribute('id'), "Password input should have id");
    assertExists(submitButton?.getAttribute('data-testid'), "Submit button should have test id");
    
    // Check for labels
    assertExists(doc?.querySelector('label[for="email"]'), "Email label should exist");
    assertExists(doc?.querySelector('label[for="password"]'), "Password label should exist");
  });

  await t.step("should include dark mode toggle", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    const html = await response.text();
    const doc = new DOMParser().parseFromString(html, "text/html");
    
    assertExists(doc?.querySelector('[data-testid="dark-mode-toggle"]'), "Dark mode toggle should exist");
  });

  await t.step("should have responsive design classes", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    const html = await response.text();
    
    // Check for responsive classes
    assertEquals(html.includes("sm:"), true, "Should include small screen responsive classes");
    assertEquals(html.includes("lg:"), true, "Should include large screen responsive classes");
    assertEquals(html.includes("dark:"), true, "Should include dark mode classes");
  });

  await t.step("should include demo credentials section", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    const html = await response.text();
    const doc = new DOMParser().parseFromString(html, "text/html");
    
    assertExists(doc?.querySelector('[data-testid="demo-credentials-button"]'), "Demo credentials button should exist");
    assertEquals(html.includes("<EMAIL>"), true, "Should display demo email");
    assertEquals(html.includes("demo123"), true, "Should display demo password");
  });

  await t.step("should handle error states", async () => {
    const response = await fetch(`${baseUrl}/auth/login?error=invalid_credentials`);
    const html = await response.text();
    
    assertEquals(html.includes("Invalid email or password"), true, "Should display error message");
  });
});

Deno.test("Login Page - Visual Design Elements", async (t: Deno.TestContext) => {
  const baseUrl = "http://localhost:8000";
  
  await t.step("should include proper styling classes", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    const html = await response.text();
    
    // Check for design system classes
    assertEquals(html.includes("bg-primary-600"), true, "Should use primary color classes");
    assertEquals(html.includes("rounded-lg"), true, "Should use rounded corners");
    assertEquals(html.includes("shadow-"), true, "Should use shadow classes");
    assertEquals(html.includes("transition-"), true, "Should use transition classes");
  });

  await t.step("should include animations", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    const html = await response.text();
    
    assertEquals(html.includes("animate-"), true, "Should include animation classes");
    assertEquals(html.includes("animate-slide-up"), true, "Should include slide-up animation");
    assertEquals(html.includes("animate-fade-in"), true, "Should include fade-in animation");
  });

  await t.step("should have proper typography", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    const html = await response.text();
    
    assertEquals(html.includes("font-bold"), true, "Should use bold font weights");
    assertEquals(html.includes("font-medium"), true, "Should use medium font weights");
    assertEquals(html.includes("font-sans"), true, "Should use sans-serif font family");
  });
});

Deno.test("Login Page - Form Functionality", async (t: Deno.TestContext) => {
  const baseUrl = "http://localhost:8000";
  
  await t.step("should have proper form attributes", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    const html = await response.text();
    const doc = new DOMParser().parseFromString(html, "text/html");
    
    const form = doc?.querySelector('form');
    const emailInput = doc?.querySelector('input[type="email"]');
    const passwordInput = doc?.querySelector('input[type="password"]');
    
    assertExists(form, "Form should exist");
    assertEquals(emailInput?.getAttribute('required'), '', "Email input should be required");
    assertEquals(passwordInput?.getAttribute('required'), '', "Password input should be required");
    assertEquals(emailInput?.getAttribute('autocomplete'), 'email', "Email input should have autocomplete");
    assertEquals(passwordInput?.getAttribute('autocomplete'), 'current-password', "Password input should have autocomplete");
  });

  await t.step("should include password visibility toggle", async () => {
    const response = await fetch(`${baseUrl}/auth/login`);
    const html = await response.text();
    const doc = new DOMParser().parseFromString(html, "text/html");
    
    // Check for password toggle button (should be in the password input wrapper)
    const passwordWrapper = doc?.querySelector('input[type="password"]')?.parentElement;
    assertExists(passwordWrapper?.querySelector('button'), "Password visibility toggle should exist");
  });
});
