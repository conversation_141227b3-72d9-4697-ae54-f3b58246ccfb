import { assertEquals, assertExists } from "$std/testing/asserts.ts";
import { generateJWT, User } from "../../utils/auth.ts";

// Test database setup
export async function setupTestDB() {
  // This would typically set up a test database
  // For now, we'll use mock data
  console.log("Setting up test database...");
}

export async function cleanupTestDB() {
  // Clean up test database
  console.log("Cleaning up test database...");
}

// Mock user data
export const mockUser: User = {
  id: "test-user-123",
  email: "<EMAIL>",
  firstName: "Test",
  lastName: "User",
  companyName: "Test Company",
  role: "admin",
  tenant_id: "test-tenant-456",
  isActive: true,
  emailVerified: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

export const mockDashboardData = {
  totalRevenue: 125000,
  revenueChange: 12.5,
  activeUsers: 8420,
  usersChange: -2.1,
  conversionRate: 3.2,
  conversionChange: 0.8,
  totalLinks: 156,
  linksChange: 5.2,
  recentActivity: [
    {
      id: "1",
      description: "New link created: Product Launch Campaign",
      time: "2 minutes ago",
      type: "link_created" as const
    },
    {
      id: "2",
      description: "Conversion recorded: $125.00",
      time: "5 minutes ago",
      type: "conversion" as const
    }
  ],
  topLinks: [
    {
      id: "1",
      url: "https://example.com/product1",
      title: "Product 1 Campaign",
      clicks: 1250,
      conversions: 45,
      conversionRate: 3.6
    },
    {
      id: "2",
      url: "https://example.com/product2",
      title: "Product 2 Campaign",
      clicks: 980,
      conversions: 28,
      conversionRate: 2.9
    }
  ]
};

export const mockChartData = [
  { timestamp: "2024-01-01T00:00:00Z", value: 100 },
  { timestamp: "2024-01-02T00:00:00Z", value: 150 },
  { timestamp: "2024-01-03T00:00:00Z", value: 120 },
  { timestamp: "2024-01-04T00:00:00Z", value: 180 },
  { timestamp: "2024-01-05T00:00:00Z", value: 200 },
];

// Helper to create authenticated request
export async function createAuthenticatedRequest(
  url: string,
  options: RequestInit = {},
  user: User = mockUser
): Promise<Request> {
  const token = await generateJWT(user);
  
  const headers = new Headers(options.headers);
  headers.set("Authorization", `Bearer ${token}`);
  headers.set("Content-Type", "application/json");
  
  return new Request(url, {
    ...options,
    headers,
  });
}

// Helper to create mock context
export function createMockContext(user?: User) {
  return {
    state: {
      user: user || mockUser,
      isAuthenticated: !!user,
    },
    url: new URL("http://localhost:8000/test"),
    params: {},
  };
}

// Helper to assert response structure
export function assertApiResponse(response: any, expectedKeys: string[]) {
  assertExists(response);
  assertEquals(typeof response, "object");
  
  for (const key of expectedKeys) {
    assertExists(response[key], `Expected key '${key}' to exist in response`);
  }
}

// Helper to assert error response
export function assertErrorResponse(response: any, expectedStatus?: number) {
  assertExists(response);
  assertEquals(typeof response, "object");
  assertExists(response.error);
  assertEquals(response.success, false);
  
  if (expectedStatus) {
    assertExists(response.status);
    assertEquals(response.status, expectedStatus);
  }
}

// Helper to wait for async operations
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Helper to generate test data
export function generateTestData<T>(
  template: T,
  count: number,
  modifier?: (item: T, index: number) => Partial<T>
): T[] {
  return Array.from({ length: count }, (_, index) => ({
    ...template,
    ...(modifier ? modifier(template, index) : {}),
  }));
}

// Mock fetch for testing
export class MockFetch {
  private responses: Map<string, Response> = new Map();
  
  addResponse(url: string, response: Response) {
    this.responses.set(url, response);
  }
  
  addJsonResponse(url: string, data: any, status = 200) {
    const response = new Response(JSON.stringify(data), {
      status,
      headers: { "Content-Type": "application/json" },
    });
    this.responses.set(url, response);
  }
  
  fetch = (url: string | URL): Promise<Response> => {
    const urlString = url.toString();
    const response = this.responses.get(urlString);
    
    if (response) {
      return Promise.resolve(response.clone());
    }
    
    // Default 404 response
    return Promise.resolve(new Response("Not Found", { status: 404 }));
  };
  
  clear() {
    this.responses.clear();
  }
}

// Test environment setup
export function setupTestEnvironment() {
  // Set test environment variables
  Deno.env.set("DENO_ENV", "test");
  Deno.env.set("JWT_SECRET", "test-secret-key");
  Deno.env.set("DATABASE_URL", "postgresql://test:test@localhost:5432/test_db");
  Deno.env.set("REDIS_URL", "redis://localhost:6379/1");
}

// Cleanup test environment
export function cleanupTestEnvironment() {
  // Reset environment variables if needed
  console.log("Test environment cleaned up");
}
