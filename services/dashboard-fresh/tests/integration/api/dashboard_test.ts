import { assertEquals, assertExists } from "$std/testing/asserts.ts";
import { handler as overviewHandler } from "../../../routes/api/dashboard/overview.ts";
import { handler as metricsHandler } from "../../../routes/api/dashboard/metrics.ts";
import { 
  createAuthenticatedRequest, 
  createMockContext, 
  mockUser,
  assertApiResponse,
  assertErrorResponse,
  setupTestEnvironment
} from "../../helpers/test_setup.ts";

// Setup test environment
setupTestEnvironment();

Deno.test("Dashboard API - overview endpoint returns data for authenticated user", async () => {
  const req = await createAuthenticatedRequest("http://localhost:8000/api/dashboard/overview");
  const ctx = createMockContext(mockUser);

  const response = await overviewHandler.GET(req, ctx);
  assertEquals(response.status, 200);

  const data = await response.json();
  assertApiResponse(data, ["success", "data"]);
  assertEquals(data.success, true);
  
  // Check overview data structure
  const overview = data.data;
  assertApiResponse(overview, [
    "totalRevenue",
    "revenueChange", 
    "activeUsers",
    "usersChange",
    "conversionRate",
    "conversionChange",
    "totalLinks",
    "linksChange",
    "recentActivity",
    "topLinks"
  ]);
  
  // Check data types
  assertEquals(typeof overview.totalRevenue, "number");
  assertEquals(typeof overview.revenueChange, "number");
  assertEquals(typeof overview.activeUsers, "number");
  assertEquals(typeof overview.usersChange, "number");
  assertEquals(typeof overview.conversionRate, "number");
  assertEquals(typeof overview.conversionChange, "number");
  assertEquals(typeof overview.totalLinks, "number");
  assertEquals(typeof overview.linksChange, "number");
  assertEquals(Array.isArray(overview.recentActivity), true);
  assertEquals(Array.isArray(overview.topLinks), true);
});

Deno.test("Dashboard API - overview endpoint requires authentication", async () => {
  const req = new Request("http://localhost:8000/api/dashboard/overview");
  const ctx = createMockContext(); // No user
  ctx.state.user = null;
  ctx.state.isAuthenticated = false;

  const response = await overviewHandler.GET(req, ctx);
  assertEquals(response.status, 401);

  const data = await response.json();
  assertErrorResponse(data);
  assertEquals(data.error, "Unauthorized");
});

Deno.test("Dashboard API - overview endpoint accepts query parameters", async () => {
  const req = await createAuthenticatedRequest(
    "http://localhost:8000/api/dashboard/overview?period=7d&date_from=2024-01-01&date_to=2024-01-07"
  );
  const ctx = createMockContext(mockUser);

  const response = await overviewHandler.GET(req, ctx);
  assertEquals(response.status, 200);

  const data = await response.json();
  assertApiResponse(data, ["success", "data"]);
  assertEquals(data.success, true);
});

Deno.test("Dashboard API - metrics endpoint returns time series data", async () => {
  const req = await createAuthenticatedRequest("http://localhost:8000/api/dashboard/metrics");
  const ctx = createMockContext(mockUser);

  const response = await metricsHandler.GET(req, ctx);
  assertEquals(response.status, 200);

  const data = await response.json();
  assertApiResponse(data, ["success", "data"]);
  assertEquals(data.success, true);
  
  // Check metrics data structure
  const metrics = data.data;
  assertApiResponse(metrics, ["revenue", "users", "conversions", "clicks"]);
  
  // Check that each metric is an array
  assertEquals(Array.isArray(metrics.revenue), true);
  assertEquals(Array.isArray(metrics.users), true);
  assertEquals(Array.isArray(metrics.conversions), true);
  assertEquals(Array.isArray(metrics.clicks), true);
  
  // Check time series data structure if data exists
  if (metrics.revenue.length > 0) {
    const dataPoint = metrics.revenue[0];
    assertApiResponse(dataPoint, ["timestamp", "value"]);
    assertEquals(typeof dataPoint.timestamp, "string");
    assertEquals(typeof dataPoint.value, "number");
  }
});

Deno.test("Dashboard API - metrics endpoint requires authentication", async () => {
  const req = new Request("http://localhost:8000/api/dashboard/metrics");
  const ctx = createMockContext();
  ctx.state.user = null;
  ctx.state.isAuthenticated = false;

  const response = await metricsHandler.GET(req, ctx);
  assertEquals(response.status, 401);

  const data = await response.json();
  assertErrorResponse(data);
  assertEquals(data.error, "Unauthorized");
});

Deno.test("Dashboard API - metrics endpoint handles date range parameters", async () => {
  const req = await createAuthenticatedRequest(
    "http://localhost:8000/api/dashboard/metrics?date_from=2024-01-01&date_to=2024-01-31&compare_period=true"
  );
  const ctx = createMockContext(mockUser);

  const response = await metricsHandler.GET(req, ctx);
  assertEquals(response.status, 200);

  const data = await response.json();
  assertApiResponse(data, ["success", "data"]);
  assertEquals(data.success, true);
});

Deno.test("Dashboard API - handles different user roles", async () => {
  // Test with admin user
  const adminUser = { ...mockUser, role: "admin" };
  const adminReq = await createAuthenticatedRequest("http://localhost:8000/api/dashboard/overview");
  const adminCtx = createMockContext(adminUser);

  const adminResponse = await overviewHandler.GET(adminReq, adminCtx);
  assertEquals(adminResponse.status, 200);

  // Test with regular user
  const regularUser = { ...mockUser, role: "user" };
  const userReq = await createAuthenticatedRequest("http://localhost:8000/api/dashboard/overview");
  const userCtx = createMockContext(regularUser);

  const userResponse = await overviewHandler.GET(userReq, userCtx);
  assertEquals(userResponse.status, 200);
});

Deno.test("Dashboard API - handles tenant isolation", async () => {
  // Test with different tenant IDs
  const tenant1User = { ...mockUser, tenant_id: "tenant-1" };
  const tenant2User = { ...mockUser, tenant_id: "tenant-2" };

  const req1 = await createAuthenticatedRequest("http://localhost:8000/api/dashboard/overview");
  const ctx1 = createMockContext(tenant1User);

  const req2 = await createAuthenticatedRequest("http://localhost:8000/api/dashboard/overview");
  const ctx2 = createMockContext(tenant2User);

  const response1 = await overviewHandler.GET(req1, ctx1);
  const response2 = await overviewHandler.GET(req2, ctx2);

  assertEquals(response1.status, 200);
  assertEquals(response2.status, 200);

  // Both should succeed but potentially return different data
  // (In a real implementation, this would be verified by checking the actual data)
});

Deno.test("Dashboard API - handles malformed requests gracefully", async () => {
  // Test with invalid date parameters
  const req = await createAuthenticatedRequest(
    "http://localhost:8000/api/dashboard/overview?date_from=invalid-date&date_to=also-invalid"
  );
  const ctx = createMockContext(mockUser);

  const response = await overviewHandler.GET(req, ctx);
  
  // Should still return 200 with fallback data or handle gracefully
  // The exact behavior depends on implementation
  assertEquals(response.status >= 200 && response.status < 500, true);
});
