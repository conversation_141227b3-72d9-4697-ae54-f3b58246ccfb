// k6 Load Testing Script for Fresh Dashboard
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');

// Test configuration
export let options = {
  stages: [
    { duration: '2m', target: 20 },   // Ramp up to 20 users over 2 minutes
    { duration: '5m', target: 20 },   // Stay at 20 users for 5 minutes
    { duration: '2m', target: 50 },   // Ramp up to 50 users over 2 minutes
    { duration: '5m', target: 50 },   // Stay at 50 users for 5 minutes
    { duration: '2m', target: 100 },  // Ramp up to 100 users over 2 minutes
    { duration: '5m', target: 100 },  // Stay at 100 users for 5 minutes
    { duration: '2m', target: 0 },    // Ramp down to 0 users over 2 minutes
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests should be below 2s
    http_req_failed: ['rate<0.1'],     // Error rate should be below 10%
    errors: ['rate<0.1'],              // Custom error rate should be below 10%
  },
};

// Base URL
const BASE_URL = 'http://localhost:8000';

// Test data
const TEST_USER = {
  email: '<EMAIL>',
  password: 'demo123'
};

// Helper function to login and get auth token
function login() {
  const loginPayload = JSON.stringify(TEST_USER);
  const loginParams = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const loginResponse = http.post(`${BASE_URL}/api/auth/login`, loginPayload, loginParams);
  
  const loginSuccess = check(loginResponse, {
    'login status is 200': (r) => r.status === 200,
    'login response has token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data && body.data.token;
      } catch {
        return false;
      }
    },
  });

  if (!loginSuccess) {
    errorRate.add(1);
    return null;
  }

  try {
    const body = JSON.parse(loginResponse.body);
    return body.data.token;
  } catch {
    errorRate.add(1);
    return null;
  }
}

// Helper function to make authenticated requests
function makeAuthenticatedRequest(url, token, method = 'GET', payload = null) {
  const params = {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };

  let response;
  if (method === 'GET') {
    response = http.get(url, params);
  } else if (method === 'POST') {
    response = http.post(url, payload, params);
  }

  return response;
}

// Main test function
export default function () {
  // Login to get authentication token
  const token = login();
  if (!token) {
    console.error('Failed to login');
    return;
  }

  // Test dashboard overview API
  const overviewResponse = makeAuthenticatedRequest(`${BASE_URL}/api/dashboard/overview`, token);
  const overviewSuccess = check(overviewResponse, {
    'overview status is 200': (r) => r.status === 200,
    'overview response time < 1000ms': (r) => r.timings.duration < 1000,
    'overview has valid data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data && typeof body.data.totalRevenue === 'number';
      } catch {
        return false;
      }
    },
  });

  if (!overviewSuccess) {
    errorRate.add(1);
  }

  sleep(1);

  // Test dashboard metrics API
  const metricsResponse = makeAuthenticatedRequest(`${BASE_URL}/api/dashboard/metrics`, token);
  const metricsSuccess = check(metricsResponse, {
    'metrics status is 200': (r) => r.status === 200,
    'metrics response time < 1500ms': (r) => r.timings.duration < 1500,
    'metrics has valid data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data && Array.isArray(body.data.revenue);
      } catch {
        return false;
      }
    },
  });

  if (!metricsSuccess) {
    errorRate.add(1);
  }

  sleep(1);

  // Test analytics proxy API
  const analyticsResponse = makeAuthenticatedRequest(`${BASE_URL}/api/analytics/overview`, token);
  check(analyticsResponse, {
    'analytics proxy status is 200 or 503': (r) => r.status === 200 || r.status === 503, // 503 if service unavailable
    'analytics response time < 2000ms': (r) => r.timings.duration < 2000,
  });

  sleep(1);

  // Test health check endpoint
  const healthResponse = http.get(`${BASE_URL}/api/health`);
  check(healthResponse, {
    'health check status is 200': (r) => r.status === 200,
    'health check response time < 500ms': (r) => r.timings.duration < 500,
    'health check has status': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.status === 'healthy' || body.status === 'unhealthy';
      } catch {
        return false;
      }
    },
  });

  // Random sleep between 1-3 seconds to simulate user behavior
  sleep(Math.random() * 2 + 1);
}

// Setup function (runs once per VU at the beginning)
export function setup() {
  console.log('Starting load test for Fresh Dashboard');
  
  // Verify the application is running
  const healthResponse = http.get(`${BASE_URL}/api/health`);
  if (healthResponse.status !== 200) {
    throw new Error(`Application not ready. Health check returned: ${healthResponse.status}`);
  }
  
  console.log('Application health check passed');
  return {};
}

// Teardown function (runs once at the end)
export function teardown(data) {
  console.log('Load test completed');
}

// Handle summary (custom summary output)
export function handleSummary(data) {
  return {
    'tests/load/results/summary.json': JSON.stringify(data, null, 2),
    'tests/load/results/summary.html': htmlReport(data),
    stdout: textSummary(data, { indent: ' ', enableColors: true }),
  };
}

// Helper function to generate HTML report
function htmlReport(data) {
  const date = new Date().toISOString();
  const duration = data.state.testRunDurationMs / 1000;
  
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Fresh Dashboard Load Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .metric { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; }
        .pass { border-left-color: #28a745; }
        .fail { border-left-color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Fresh Dashboard Load Test Report</h1>
        <p><strong>Date:</strong> ${date}</p>
        <p><strong>Duration:</strong> ${duration.toFixed(2)} seconds</p>
        <p><strong>Virtual Users:</strong> ${data.metrics.vus_max.values.max}</p>
    </div>
    
    <h2>Key Metrics</h2>
    <div class="metric ${data.metrics.http_req_duration.values.p95 < 2000 ? 'pass' : 'fail'}">
        <strong>95th Percentile Response Time:</strong> ${data.metrics.http_req_duration.values.p95.toFixed(2)}ms
        (Target: < 2000ms)
    </div>
    
    <div class="metric ${data.metrics.http_req_failed.values.rate < 0.1 ? 'pass' : 'fail'}">
        <strong>Error Rate:</strong> ${(data.metrics.http_req_failed.values.rate * 100).toFixed(2)}%
        (Target: < 10%)
    </div>
    
    <div class="metric">
        <strong>Total Requests:</strong> ${data.metrics.http_reqs.values.count}
    </div>
    
    <div class="metric">
        <strong>Requests per Second:</strong> ${data.metrics.http_reqs.values.rate.toFixed(2)}
    </div>
    
    <h2>Detailed Metrics</h2>
    <table>
        <tr>
            <th>Metric</th>
            <th>Average</th>
            <th>Min</th>
            <th>Max</th>
            <th>95th Percentile</th>
        </tr>
        <tr>
            <td>HTTP Request Duration</td>
            <td>${data.metrics.http_req_duration.values.avg.toFixed(2)}ms</td>
            <td>${data.metrics.http_req_duration.values.min.toFixed(2)}ms</td>
            <td>${data.metrics.http_req_duration.values.max.toFixed(2)}ms</td>
            <td>${data.metrics.http_req_duration.values.p95.toFixed(2)}ms</td>
        </tr>
    </table>
</body>
</html>
  `;
}

// Import text summary function (would be imported from k6 in real implementation)
function textSummary(data, options = {}) {
  // Simplified text summary
  return `
Load Test Summary:
==================
Duration: ${(data.state.testRunDurationMs / 1000).toFixed(2)}s
Virtual Users: ${data.metrics.vus_max.values.max}
Total Requests: ${data.metrics.http_reqs.values.count}
Request Rate: ${data.metrics.http_reqs.values.rate.toFixed(2)} req/s
Error Rate: ${(data.metrics.http_req_failed.values.rate * 100).toFixed(2)}%
95th Percentile Response Time: ${data.metrics.http_req_duration.values.p95.toFixed(2)}ms
  `;
}
