import { assertEquals, assertExists } from "$std/testing/asserts.ts";
import { render } from "https://esm.sh/@testing-library/preact@3.2.3";
import { fireEvent } from "https://esm.sh/@testing-library/preact@3.2.3";
import KPIScorecard from "../../../islands/dashboard/KPIScorecard.tsx";

Deno.test("KPIScorecard - renders with correct data", () => {
  const props = {
    title: "Total Revenue",
    value: 125000,
    change: 12.5,
    trend: "up" as const,
    format: "currency" as const,
  };

  const { container, getByText } = render(<KPIScorecard {...props} />);
  
  // Check if title is rendered
  const titleElement = getByText("Total Revenue");
  assertExists(titleElement);
  
  // Check if formatted value is rendered
  const valueElement = getByText("$125,000");
  assertExists(valueElement);
  
  // Check if change percentage is rendered
  const changeElement = getByText("+12.5%");
  assertExists(changeElement);
  
  // Check if component has correct test id
  const cardElement = container.querySelector('[data-testid="kpi-card"]');
  assertExists(cardElement);
});

Deno.test("KPIScorecard - formats different value types correctly", () => {
  // Test currency formatting
  const currencyProps = {
    title: "Revenue",
    value: 125000,
    change: 12.5,
    trend: "up" as const,
    format: "currency" as const,
  };
  
  const { getByText: getCurrencyText } = render(<KPIScorecard {...currencyProps} />);
  const currencyValue = getCurrencyText("$125,000");
  assertExists(currencyValue);
  
  // Test percentage formatting
  const percentageProps = {
    title: "Conversion Rate",
    value: 3.2,
    change: 0.5,
    trend: "up" as const,
    format: "percentage" as const,
  };
  
  const { getByText: getPercentageText } = render(<KPIScorecard {...percentageProps} />);
  const percentageValue = getPercentageText("3.2%");
  assertExists(percentageValue);
  
  // Test number formatting
  const numberProps = {
    title: "Active Users",
    value: 8420,
    change: -2.1,
    trend: "down" as const,
    format: "number" as const,
  };
  
  const { getByText: getNumberText } = render(<KPIScorecard {...numberProps} />);
  const numberValue = getNumberText("8,420");
  assertExists(numberValue);
});

Deno.test("KPIScorecard - handles trend colors correctly", () => {
  // Test up trend (green)
  const upProps = {
    title: "Revenue",
    value: 100,
    change: 10,
    trend: "up" as const,
  };
  
  const { container: upContainer } = render(<KPIScorecard {...upProps} />);
  const upTrendElement = upContainer.querySelector('.text-success-600');
  assertExists(upTrendElement);
  
  // Test down trend (red)
  const downProps = {
    title: "Revenue",
    value: 100,
    change: -10,
    trend: "down" as const,
  };
  
  const { container: downContainer } = render(<KPIScorecard {...downProps} />);
  const downTrendElement = downContainer.querySelector('.text-error-600');
  assertExists(downTrendElement);
  
  // Test neutral trend (gray)
  const neutralProps = {
    title: "Revenue",
    value: 100,
    change: 0,
    trend: "neutral" as const,
  };
  
  const { container: neutralContainer } = render(<KPIScorecard {...neutralProps} />);
  const neutralTrendElement = neutralContainer.querySelector('.text-gray-600');
  assertExists(neutralTrendElement);
});

Deno.test("KPIScorecard - expands and collapses on click", async () => {
  const props = {
    title: "Revenue",
    value: 100,
    change: 10,
    trend: "up" as const,
  };
  
  const { container, queryByTestId } = render(<KPIScorecard {...props} />);
  
  // Initially, details should not be visible
  let detailsElement = queryByTestId("details");
  assertEquals(detailsElement, null);
  
  // Click the card to expand
  const cardElement = container.querySelector('[data-testid="kpi-card"]');
  assertExists(cardElement);
  
  await fireEvent.click(cardElement);
  
  // Details should now be visible
  detailsElement = queryByTestId("details");
  assertExists(detailsElement);
  
  // Click again to collapse
  await fireEvent.click(cardElement);
  
  // Details should be hidden again
  detailsElement = queryByTestId("details");
  assertEquals(detailsElement, null);
});

Deno.test("KPIScorecard - calculates previous period value correctly", () => {
  const props = {
    title: "Revenue",
    value: 110,
    change: 10, // 10% increase
    trend: "up" as const,
  };
  
  const { container } = render(<KPIScorecard {...props} />);
  
  // Click to expand and see details
  const cardElement = container.querySelector('[data-testid="kpi-card"]');
  assertExists(cardElement);
  fireEvent.click(cardElement);
  
  // Previous period should be 100 (110 / 1.1)
  const previousPeriodText = container.textContent;
  assertExists(previousPeriodText);
  // The calculation should show approximately 100
});

Deno.test("KPIScorecard - handles zero and negative values", () => {
  // Test zero value
  const zeroProps = {
    title: "Revenue",
    value: 0,
    change: 0,
    trend: "neutral" as const,
  };
  
  const { getByText: getZeroText } = render(<KPIScorecard {...zeroProps} />);
  const zeroValue = getZeroText("0");
  assertExists(zeroValue);
  
  // Test negative change
  const negativeProps = {
    title: "Revenue",
    value: 90,
    change: -10,
    trend: "down" as const,
  };
  
  const { getByText: getNegativeText } = render(<KPIScorecard {...negativeProps} />);
  const negativeChange = getNegativeText("-10.0%");
  assertExists(negativeChange);
});
