import { assertEquals, assertThrows, assertExists } from "$std/testing/asserts.ts";
import { 
  generateJWT, 
  verifyJWT, 
  hashPassword, 
  verifyPassword,
  getUserTenantId,
  hasRole,
  isAdmin,
  isSuperAdmin
} from "../../../utils/auth.ts";
import { mockUser, setupTestEnvironment } from "../../helpers/test_setup.ts";

// Setup test environment before running tests
setupTestEnvironment();

Deno.test("Auth - JWT generation and verification", async () => {
  const payload = { 
    id: "123", 
    email: "<EMAIL>",
    firstName: "Test",
    lastName: "User",
    role: "admin"
  };
  
  const token = await generateJWT(payload);
  assertExists(token);
  assertEquals(typeof token, "string");
  
  const decoded = await verifyJWT(token);
  assertEquals(decoded.id, payload.id);
  assertEquals(decoded.email, payload.email);
  assertEquals(decoded.firstName, payload.firstName);
  assertEquals(decoded.lastName, payload.lastName);
  assertEquals(decoded.role, payload.role);
});

Deno.test("Auth - JWT verification throws on invalid token", async () => {
  await assertThrows(
    () => verifyJWT("invalid-token"),
    Error,
    "Invalid token"
  );
});

Deno.test("Auth - JWT verification throws on expired token", async () => {
  // Generate a token that expires immediately
  const payload = { 
    id: "123", 
    email: "<EMAIL>",
    firstName: "Test",
    lastName: "User",
    role: "admin"
  };
  
  // This would require mocking the time or using a very short expiry
  // For now, we'll test the error handling structure
  const invalidToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjMiLCJleHAiOjE2MDk0NTkyMDB9.invalid";
  
  await assertThrows(
    () => verifyJWT(invalidToken),
    Error
  );
});

Deno.test("Auth - password hashing and verification", async () => {
  const password = "testPassword123";
  
  const hash = await hashPassword(password);
  assertExists(hash);
  assertEquals(typeof hash, "string");
  
  const isValid = await verifyPassword(password, hash);
  assertEquals(isValid, true);
  
  const isInvalid = await verifyPassword("wrongPassword", hash);
  assertEquals(isInvalid, false);
});

Deno.test("Auth - getUserTenantId returns correct tenant ID", () => {
  // User with explicit tenant_id
  const userWithTenant = { ...mockUser, tenant_id: "tenant-123" };
  assertEquals(getUserTenantId(userWithTenant), "tenant-123");
  
  // User without tenant_id should use user ID
  const userWithoutTenant = { ...mockUser, tenant_id: undefined };
  assertEquals(getUserTenantId(userWithoutTenant), mockUser.id);
});

Deno.test("Auth - hasRole checks user roles correctly", () => {
  const adminUser = { ...mockUser, role: "admin" };
  const userRole = { ...mockUser, role: "user" };
  const superAdminUser = { ...mockUser, role: "super_admin" };
  
  // Admin user should have admin role
  assertEquals(hasRole(adminUser, ["admin"]), true);
  assertEquals(hasRole(adminUser, ["user"]), false);
  assertEquals(hasRole(adminUser, ["admin", "user"]), true);
  
  // User should have user role
  assertEquals(hasRole(userRole, ["user"]), true);
  assertEquals(hasRole(userRole, ["admin"]), false);
  
  // Super admin should have super_admin role
  assertEquals(hasRole(superAdminUser, ["super_admin"]), true);
  assertEquals(hasRole(superAdminUser, ["admin"]), false);
});

Deno.test("Auth - isAdmin checks admin privileges correctly", () => {
  const adminUser = { ...mockUser, role: "admin" };
  const userRole = { ...mockUser, role: "user" };
  const superAdminUser = { ...mockUser, role: "super_admin" };
  
  assertEquals(isAdmin(adminUser), true);
  assertEquals(isAdmin(userRole), false);
  assertEquals(isAdmin(superAdminUser), true); // super_admin should also be admin
});

Deno.test("Auth - isSuperAdmin checks super admin privileges correctly", () => {
  const adminUser = { ...mockUser, role: "admin" };
  const userRole = { ...mockUser, role: "user" };
  const superAdminUser = { ...mockUser, role: "super_admin" };
  
  assertEquals(isSuperAdmin(adminUser), false);
  assertEquals(isSuperAdmin(userRole), false);
  assertEquals(isSuperAdmin(superAdminUser), true);
});

Deno.test("Auth - JWT payload contains all required fields", async () => {
  const user = {
    id: "user-123",
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    companyName: "Test Corp",
    role: "admin",
    tenant_id: "tenant-456"
  };
  
  const token = await generateJWT(user);
  const decoded = await verifyJWT(token);
  
  // Check all fields are present
  assertEquals(decoded.id, user.id);
  assertEquals(decoded.email, user.email);
  assertEquals(decoded.firstName, user.firstName);
  assertEquals(decoded.lastName, user.lastName);
  assertEquals(decoded.companyName, user.companyName);
  assertEquals(decoded.role, user.role);
  assertEquals(decoded.tenant_id, user.tenant_id);
  
  // Check JWT standard fields
  assertExists(decoded.iat); // issued at
  assertExists(decoded.exp); // expires at
});

Deno.test("Auth - handles missing optional fields in JWT", async () => {
  const minimalUser = {
    id: "user-123",
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    role: "user"
    // No companyName or tenant_id
  };
  
  const token = await generateJWT(minimalUser);
  const decoded = await verifyJWT(token);
  
  assertEquals(decoded.id, minimalUser.id);
  assertEquals(decoded.email, minimalUser.email);
  assertEquals(decoded.firstName, minimalUser.firstName);
  assertEquals(decoded.lastName, minimalUser.lastName);
  assertEquals(decoded.role, minimalUser.role);
  assertEquals(decoded.companyName, undefined);
  assertEquals(decoded.tenant_id, undefined);
});
