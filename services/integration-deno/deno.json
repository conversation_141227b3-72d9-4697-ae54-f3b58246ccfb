{"name": "integration-deno", "version": "1.0.0", "description": "Integration service for e-commerce analytics SaaS - Deno 2 implementation", "exports": "./src/main.ts", "tasks": {"dev": "deno run --allow-net --allow-env --allow-read --allow-write --watch src/main.ts", "start": "deno run --allow-net --allow-env --allow-read --allow-write src/main.ts", "test": "deno test --allow-net --allow-env --allow-read --allow-write", "test:watch": "deno test --allow-net --allow-env --allow-read --allow-write --watch", "test:coverage": "deno test --allow-net --allow-env --allow-read --allow-write --coverage=coverage", "lint": "deno lint", "fmt": "deno fmt", "check": "deno check src/main.ts", "cache": "deno cache --import-map=deno.json src/main.ts", "compile": "deno compile --allow-net --allow-env --allow-read --allow-write --output=integration-api src/main.ts"}, "imports": {"@oak/oak": "jsr:@oak/oak@^17.1.0", "@std/assert": "jsr:@std/assert@^1.0.0", "@std/testing": "jsr:@std/testing@^1.0.0", "@std/log": "jsr:@std/log@^0.224.0", "@std/dotenv": "jsr:@std/dotenv@^0.225.0", "@std/crypto": "jsr:@std/crypto@^1.0.0", "@std/encoding": "jsr:@std/encoding@^1.0.0", "@std/http": "jsr:@std/http@^1.0.0", "@std/path": "jsr:@std/path@^1.0.0", "@std/fs": "jsr:@std/fs@^1.0.0", "@std/datetime": "jsr:@std/datetime@^0.225.0", "@std/csv": "jsr:@std/csv@^1.0.0", "@std/uuid": "jsr:@std/uuid@^1.0.0", "postgres": "https://deno.land/x/postgres@v0.19.3/mod.ts", "redis": "https://deno.land/x/redis@v0.32.3/mod.ts", "djwt": "https://deno.land/x/djwt@v3.0.2/mod.ts", "bcrypt": "https://deno.land/x/bcrypt@v0.4.1/mod.ts", "zod": "https://deno.land/x/zod@v3.22.4/mod.ts", "cors": "https://deno.land/x/cors@v1.2.2/mod.ts"}, "compilerOptions": {"lib": ["deno.window"], "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false}, "fmt": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve", "include": ["src/"], "exclude": ["coverage/", "dist/", "node_modules/"]}, "lint": {"include": ["src/"], "exclude": ["coverage/", "dist/", "node_modules/"], "rules": {"tags": ["recommended"], "include": ["ban-untagged-todo"], "exclude": ["no-unused-vars"]}}, "test": {"include": ["src/**/*_test.ts", "tests/**/*_test.ts"], "exclude": ["coverage/", "dist/", "node_modules/"]}, "lock": false, "nodeModulesDir": "none"}