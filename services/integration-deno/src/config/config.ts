import { load } from "@std/dotenv";

// Load environment variables
await load({ export: true });

export interface Config {
  // Server configuration
  port: number;
  host: string;
  nodeEnv: string;

  // Database configuration
  database: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    maxConnections: number;
    connectionTimeoutMs: number;
    idleTimeoutMs: number;
  };

  // Redis configuration
  redis: {
    host: string;
    port: number;
    password?: string;
    database: number;
    maxRetries: number;
    retryDelayMs: number;
  };

  // JWT configuration
  jwt: {
    secret: string;
    expiresIn: string;
    issuer: string;
  };

  // CORS configuration
  cors: {
    origins: string[];
    credentials: boolean;
  };

  // Rate limiting configuration
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };

  // Integration configuration
  integration: {
    syncInterval: number;
    batchSize: number;
    retryAttempts: number;
    webhookTimeout: number;
  };

  // Platform configurations
  platforms: {
    shopify: {
      apiVersion: string;
      rateLimitPerSecond: number;
      burstAllowance: number;
    };
    woocommerce: {
      rateLimitPerSecond: number;
      burstAllowance: number;
    };
    ebay: {
      environment: string;
      rateLimitPerSecond: number;
      burstAllowance: number;
    };
  };

  // Logging configuration
  logging: {
    level: string;
    format: string;
  };

  // External services
  services: {
    analytics: string;
    dashboard: string;
    linkTracking: string;
  };

  // Webhook configuration
  webhooks: {
    maxRetries: number;
    retryDelayMs: number;
    timeoutMs: number;
  };

  // Sync configuration
  sync: {
    enableScheduled: boolean;
    defaultInterval: number;
    maxConcurrentJobs: number;
  };
}

// Default configuration
const defaultConfig: Config = {
  port: parseInt(Deno.env.get("PORT") || "3001"),
  host: Deno.env.get("HOST") || "0.0.0.0",
  nodeEnv: Deno.env.get("NODE_ENV") || "development",

  database: {
    host: Deno.env.get("DB_HOST") || "localhost",
    port: parseInt(Deno.env.get("DB_PORT") || "5432"),
    database: Deno.env.get("DB_NAME") || "ecommerce_analytics",
    username: Deno.env.get("DB_USER") || "postgres",
    password: Deno.env.get("DB_PASSWORD") || "password",
    maxConnections: parseInt(Deno.env.get("DB_MAX_CONNECTIONS") || "20"),
    connectionTimeoutMs: parseInt(Deno.env.get("DB_CONNECTION_TIMEOUT") || "30000"),
    idleTimeoutMs: parseInt(Deno.env.get("DB_IDLE_TIMEOUT") || "30000"),
  },

  redis: {
    host: Deno.env.get("REDIS_HOST") || "localhost",
    port: parseInt(Deno.env.get("REDIS_PORT") || "6379"),
    password: Deno.env.get("REDIS_PASSWORD"),
    database: parseInt(Deno.env.get("REDIS_DB") || "0"),
    maxRetries: parseInt(Deno.env.get("REDIS_MAX_RETRIES") || "3"),
    retryDelayMs: parseInt(Deno.env.get("REDIS_RETRY_DELAY") || "1000"),
  },

  jwt: {
    secret: Deno.env.get("JWT_SECRET") || "your-secret-key",
    expiresIn: Deno.env.get("JWT_EXPIRES_IN") || "24h",
    issuer: Deno.env.get("JWT_ISSUER") || "integration-service",
  },

  cors: {
    origins: (Deno.env.get("CORS_ORIGINS") || "http://localhost:3000,http://localhost:3002").split(","),
    credentials: Deno.env.get("CORS_CREDENTIALS") === "true",
  },

  rateLimit: {
    windowMs: parseInt(Deno.env.get("RATE_LIMIT_WINDOW_MS") || "900000"), // 15 minutes
    maxRequests: parseInt(Deno.env.get("RATE_LIMIT_MAX_REQUESTS") || "1000"),
  },

  integration: {
    syncInterval: parseInt(Deno.env.get("INTEGRATION_SYNC_INTERVAL") || "300000"), // 5 minutes
    batchSize: parseInt(Deno.env.get("INTEGRATION_BATCH_SIZE") || "100"),
    retryAttempts: parseInt(Deno.env.get("INTEGRATION_RETRY_ATTEMPTS") || "3"),
    webhookTimeout: parseInt(Deno.env.get("INTEGRATION_WEBHOOK_TIMEOUT") || "30000"),
  },

  platforms: {
    shopify: {
      apiVersion: Deno.env.get("SHOPIFY_API_VERSION") || "2023-10",
      rateLimitPerSecond: parseInt(Deno.env.get("SHOPIFY_RATE_LIMIT") || "2"),
      burstAllowance: parseInt(Deno.env.get("SHOPIFY_BURST_ALLOWANCE") || "40"),
    },
    woocommerce: {
      rateLimitPerSecond: parseInt(Deno.env.get("WOOCOMMERCE_RATE_LIMIT") || "10"),
      burstAllowance: parseInt(Deno.env.get("WOOCOMMERCE_BURST_ALLOWANCE") || "100"),
    },
    ebay: {
      environment: Deno.env.get("EBAY_ENVIRONMENT") || "sandbox",
      rateLimitPerSecond: parseInt(Deno.env.get("EBAY_RATE_LIMIT") || "1"),
      burstAllowance: parseInt(Deno.env.get("EBAY_BURST_ALLOWANCE") || "10"),
    },
  },

  logging: {
    level: Deno.env.get("LOG_LEVEL") || "INFO",
    format: Deno.env.get("LOG_FORMAT") || "json",
  },

  services: {
    analytics: Deno.env.get("ANALYTICS_SERVICE_URL") || "http://analytics:3002",
    dashboard: Deno.env.get("DASHBOARD_SERVICE_URL") || "http://dashboard:3000",
    linkTracking: Deno.env.get("LINK_TRACKING_SERVICE_URL") || "http://link-tracking:8080",
  },

  webhooks: {
    maxRetries: parseInt(Deno.env.get("WEBHOOK_MAX_RETRIES") || "3"),
    retryDelayMs: parseInt(Deno.env.get("WEBHOOK_RETRY_DELAY") || "1000"),
    timeoutMs: parseInt(Deno.env.get("WEBHOOK_TIMEOUT") || "30000"),
  },

  sync: {
    enableScheduled: Deno.env.get("ENABLE_SCHEDULED_SYNC") !== "false",
    defaultInterval: parseInt(Deno.env.get("SYNC_DEFAULT_INTERVAL") || "3600000"), // 1 hour
    maxConcurrentJobs: parseInt(Deno.env.get("SYNC_MAX_CONCURRENT_JOBS") || "5"),
  },
};

export const config = defaultConfig;
