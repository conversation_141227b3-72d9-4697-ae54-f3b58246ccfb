import { Context, Middleware } from "@oak/oak";
import { logger } from "../utils/logger.ts";

export interface ApiError extends Error {
  status?: number;
  code?: string;
  details?: unknown;
}

/**
 * Global error handling middleware
 */
export const errorHandler: Middleware = async (ctx: Context, next) => {
  try {
    await next();
  } catch (error) {
    const apiError = error as ApiError;
    
    // Default error response
    let status = 500;
    let code = "INTERNAL_SERVER_ERROR";
    let message = "An unexpected error occurred";
    let details: unknown = undefined;

    // Handle different error types
    if (apiError.status) {
      status = apiError.status;
    }

    if (apiError.code) {
      code = apiError.code;
    }

    if (apiError.message) {
      message = apiError.message;
    }

    if (apiError.details) {
      details = apiError.details;
    }

    // Handle specific error types
    const errorName = (error as { name?: string }).name;
    if (errorName === "ValidationError") {
      status = 400;
      code = "VALIDATION_ERROR";
      message = "Request validation failed";
      details = apiError.details || apiError.message;
    } else if (errorName === "UnauthorizedError" || message.includes("Unauthorized")) {
      status = 401;
      code = "UNAUTHORIZED";
      message = "Authentication required";
    } else if (errorName === "ForbiddenError" || message.includes("Forbidden")) {
      status = 403;
      code = "FORBIDDEN";
      message = "Access denied";
    } else if (errorName === "NotFoundError" || message.includes("Not found")) {
      status = 404;
      code = "NOT_FOUND";
      message = "Resource not found";
    } else if (errorName === "ConflictError" || message.includes("Conflict")) {
      status = 409;
      code = "CONFLICT";
      message = "Resource conflict";
    } else if (errorName === "RateLimitError" || message.includes("Rate limit")) {
      status = 429;
      code = "RATE_LIMIT_EXCEEDED";
      message = "Rate limit exceeded";
    }

    // Integration-specific errors
    if (message.includes("webhook") || message.includes("Webhook")) {
      status = 422;
      code = "WEBHOOK_ERROR";
      message = "Webhook processing failed";
    } else if (message.includes("platform") || message.includes("Platform")) {
      status = 502;
      code = "PLATFORM_ERROR";
      message = "External platform error";
    } else if (message.includes("sync") || message.includes("Sync")) {
      status = 503;
      code = "SYNC_ERROR";
      message = "Data synchronization failed";
    }

    // Database-specific errors
    if (message.includes("duplicate key") || message.includes("unique constraint")) {
      status = 409;
      code = "DUPLICATE_RESOURCE";
      message = "Resource already exists";
    } else if (message.includes("foreign key") || message.includes("constraint")) {
      status = 400;
      code = "CONSTRAINT_VIOLATION";
      message = "Data constraint violation";
    } else if (message.includes("connection") || message.includes("timeout")) {
      status = 503;
      code = "SERVICE_UNAVAILABLE";
      message = "Service temporarily unavailable";
    }

    // Log error with appropriate level
    const logContext = {
      method: ctx.request.method,
      url: ctx.request.url.pathname,
      status,
      code,
      userAgent: ctx.request.headers.get("user-agent"),
      ip: ctx.request.headers.get("x-forwarded-for") || 
          ctx.request.headers.get("x-real-ip") || 
          "unknown",
      tenantId: ctx.request.headers.get("x-tenant-id"),
      userId: ctx.state.user?.id,
    };

    if (status >= 500) {
      logger.error("Server error", {
        ...logContext,
        error: apiError.message,
        stack: apiError.stack,
      });
    } else if (status >= 400) {
      logger.warn("Client error", {
        ...logContext,
        error: apiError.message,
      });
    }

    // Set response
    ctx.response.status = status;
    ctx.response.body = {
      success: false,
      error: {
        code,
        message,
        ...(details && typeof details === 'object' && details !== null ? { details } : {}),
        ...(Deno.env.get("NODE_ENV") === "development" && status >= 500 && {
          stack: apiError.stack,
        }),
      },
      timestamp: new Date().toISOString(),
    };

    // Set appropriate headers
    ctx.response.headers.set("Content-Type", "application/json");
    
    // Add retry-after header for rate limiting
    if (status === 429) {
      ctx.response.headers.set("Retry-After", "60");
    }
  }
};

/**
 * Create a custom API error
 */
export function createApiError(
  message: string,
  status: number = 500,
  code?: string,
  details?: unknown,
): ApiError {
  const error = new Error(message) as ApiError;
  error.status = status;
  error.code = code;
  error.details = details;
  return error;
}

/**
 * Common error creators
 */
export const errors = {
  badRequest: (message: string, details?: unknown) =>
    createApiError(message, 400, "BAD_REQUEST", details),

  unauthorized: (message: string = "Authentication required") =>
    createApiError(message, 401, "UNAUTHORIZED"),

  forbidden: (message: string = "Access denied") =>
    createApiError(message, 403, "FORBIDDEN"),

  notFound: (message: string = "Resource not found") =>
    createApiError(message, 404, "NOT_FOUND"),

  conflict: (message: string, details?: unknown) =>
    createApiError(message, 409, "CONFLICT", details),

  unprocessableEntity: (message: string, details?: unknown) =>
    createApiError(message, 422, "UNPROCESSABLE_ENTITY", details),

  tooManyRequests: (message: string = "Rate limit exceeded") =>
    createApiError(message, 429, "RATE_LIMIT_EXCEEDED"),

  internalServerError: (message: string = "Internal server error") =>
    createApiError(message, 500, "INTERNAL_SERVER_ERROR"),

  serviceUnavailable: (message: string = "Service unavailable") =>
    createApiError(message, 503, "SERVICE_UNAVAILABLE"),

  validation: (message: string, details?: unknown) =>
    createApiError(message, 400, "VALIDATION_ERROR", details),

  // Integration-specific errors
  webhookError: (message: string, details?: unknown) =>
    createApiError(message, 422, "WEBHOOK_ERROR", details),

  platformError: (message: string, details?: unknown) =>
    createApiError(message, 502, "PLATFORM_ERROR", details),

  syncError: (message: string, details?: unknown) =>
    createApiError(message, 503, "SYNC_ERROR", details),
};
