import { Application, Middleware } from "@oak/oak";
import { oakCors } from "cors";
import { config } from "../config/config.ts";
import { logger, createRequestLogger } from "../utils/logger.ts";
import { errorHandler } from "./errorHandler.ts";

/**
 * Setup all middleware for the Oak application
 */
export function setupMiddleware(app: Application): void {
  logger.info("Setting up middleware stack");

  // Security headers
  app.use(securityHeaders);

  // CORS configuration
  app.use(oakCors({
    origin: config.cors.origins,
    credentials: config.cors.credentials,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "X-Tenant-ID",
      "X-API-Key",
      "X-Webhook-Signature",
      "X-Platform-Type",
    ],
  }));

  // Request logging
  app.use(createRequestLogger() as Middleware);

  // Basic rate limiting (platform-specific limits will be applied per route)
  app.use(basicRateLimiter);

  // Body parsing is handled automatically by Oak

  // Error handling (last)
  app.use(errorHandler);

  logger.info("Middleware stack setup completed");
}

/**
 * Security headers middleware
 */
const securityHeaders: Middleware = async (ctx, next) => {
  await next();

  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self' https:",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ].join("; ");

  // Set security headers
  ctx.response.headers.set("Content-Security-Policy", csp);
  ctx.response.headers.set("X-Content-Type-Options", "nosniff");
  ctx.response.headers.set("X-Frame-Options", "DENY");
  ctx.response.headers.set("X-XSS-Protection", "1; mode=block");
  ctx.response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  
  // HSTS (only in production with HTTPS)
  if (config.nodeEnv === "production") {
    ctx.response.headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
  }

  // Remove server information
  ctx.response.headers.delete("server");
  ctx.response.headers.delete("x-powered-by");
  
  // Set custom server header
  ctx.response.headers.set("X-Service", "integration-service");
  ctx.response.headers.set("X-Version", "1.0.0");
};

/**
 * Basic rate limiter for general endpoints
 */
const basicRateLimiter: Middleware = async (ctx, next) => {
  // Simple in-memory rate limiting for now
  // In production, this would use Redis-based rate limiting
  await next();
};

// Export individual middleware for selective use
export {
  errorHandler,
  securityHeaders,
};
