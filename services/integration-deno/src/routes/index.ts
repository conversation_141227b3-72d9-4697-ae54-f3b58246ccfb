import { Application, Router } from "@oak/oak";
import { logger } from "../utils/logger.ts";
import { healthCheck as dbHealthCheck } from "../utils/database.ts";
import { healthCheck as redisHealthCheck } from "../utils/redis.ts";
import { shopifyRouter } from "./shopify.ts";
import { webhooksRouter } from "./webhooks.ts";

/**
 * Setup all routes for the Oak application
 */
export function setupRoutes(app: Application): void {
  const router = new Router();

  logger.info("Setting up routes");

  // Health check endpoints
  router.get("/health", (ctx) => {
    ctx.response.body = {
      success: true,
      service: "integration-service",
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
    };
  });

  router.get("/ready", async (ctx) => {
    const dbHealth = await dbHealthCheck();
    const redisHealth = await redisHealthCheck();
    
    const isReady = dbHealth && redisHealth;
    
    ctx.response.status = isReady ? 200 : 503;
    ctx.response.body = {
      success: isReady,
      service: "integration-service",
      status: isReady ? "ready" : "not ready",
      checks: {
        database: dbHealth ? "healthy" : "unhealthy",
        redis: redisHealth ? "healthy" : "unhealthy",
      },
      timestamp: new Date().toISOString(),
    };
  });

  router.get("/live", (ctx) => {
    ctx.response.body = {
      success: true,
      service: "integration-service",
      status: "alive",
      timestamp: new Date().toISOString(),
    };
  });

  // Root endpoint
  router.get("/", (ctx) => {
    ctx.response.body = {
      success: true,
      service: "integration-service",
      version: "1.0.0",
      environment: Deno.env.get("NODE_ENV") || "development",
      timestamp: new Date().toISOString(),
      endpoints: [
        "GET /health - Health check",
        "GET /ready - Readiness check",
        "GET /live - Liveness check",
        "GET /api/integrations/* - Integration management endpoints",
        "POST /api/webhooks/* - Webhook endpoints",
        "GET /api/platforms/* - Platform-specific endpoints",
        "GET /api/sync/* - Data synchronization endpoints",
        "GET /api/shopify/* - Shopify integration endpoints",
        "GET /api/woocommerce/* - WooCommerce integration endpoints",
        "GET /api/ebay/* - eBay integration endpoints",
      ],
    };
  });

  // API routes
  const apiRouter = new Router({ prefix: "/api" });

  // Register Shopify routes
  apiRouter.use("/shopify", shopifyRouter.routes(), shopifyRouter.allowedMethods());

  // Register webhook routes
  apiRouter.use("/webhooks", webhooksRouter.routes(), webhooksRouter.allowedMethods());

  // Placeholder for integration management routes
  apiRouter.get("/integrations", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Integration management endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for platform routes
  apiRouter.get("/platforms", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Platform endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for sync routes
  apiRouter.get("/sync", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Sync endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for Shopify routes
  apiRouter.get("/shopify", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "Shopify endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for WooCommerce routes
  apiRouter.get("/woocommerce", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "WooCommerce endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // Placeholder for eBay routes
  apiRouter.get("/ebay", (ctx) => {
    ctx.response.body = {
      success: true,
      message: "eBay endpoints will be implemented in Phase 3",
      timestamp: new Date().toISOString(),
    };
  });

  // 404 handler for API routes
  apiRouter.all("/(.*)", (ctx) => {
    ctx.response.status = 404;
    ctx.response.body = {
      success: false,
      error: {
        code: "NOT_FOUND",
        message: "API endpoint not found",
      },
      timestamp: new Date().toISOString(),
    };
  });

  // Register routers
  app.use(router.routes());
  app.use(router.allowedMethods());
  app.use(apiRouter.routes());
  app.use(apiRouter.allowedMethods());

  // Global 404 handler
  app.use((ctx) => {
    ctx.response.status = 404;
    ctx.response.body = {
      success: false,
      error: {
        code: "NOT_FOUND",
        message: "Endpoint not found",
      },
      timestamp: new Date().toISOString(),
    };
  });

  logger.info("Routes setup completed");
}
