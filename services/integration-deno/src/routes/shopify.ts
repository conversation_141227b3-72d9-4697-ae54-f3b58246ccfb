import { Router } from "@oak/oak";
import { z } from "zod";
import { logger, logIntegrationEvent } from "../utils/logger.ts";
import { errors } from "../middleware/errorHandler.ts";
import { ShopifyClient } from "../platforms/shopify/shopifyClient.ts";

// Validation schemas using Zod
const shopifyConfigSchema = z.object({
  shopDomain: z.string().min(1),
  accessToken: z.string().min(1),
  apiVersion: z.string().optional(),
});

const syncProductsSchema = z.object({
  limit: z.number().min(1).max(250).default(50),
  sinceId: z.number().optional(),
});

const syncOrdersSchema = z.object({
  limit: z.number().min(1).max(250).default(50),
  sinceId: z.number().optional(),
  status: z.enum(["open", "closed", "cancelled", "any"]).default("any"),
});

const createWebhookSchema = z.object({
  topic: z.string().min(1),
  address: z.string().url(),
});

// Create router
const router = new Router();

// GET /shopify/test-connection - Test Shopify connection
router.post("/test-connection", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    const config = shopifyConfigSchema.parse(body);

    logger.info("Testing Shopify connection", {
      shopDomain: config.shopDomain,
      tenantId: ctx.state.tenantId,
    });

    const client = new ShopifyClient(config);
    const shop = await client.getShop();

    logIntegrationEvent("shopify", "connection_tested", {
      shopDomain: config.shopDomain,
      shopName: shop.name,
      tenantId: ctx.state.tenantId,
    });

    ctx.response.body = {
      success: true,
      data: {
        connected: true,
        shop: {
          id: shop.id,
          name: shop.name,
          domain: shop.domain,
          email: shop.email,
          currency: shop.currency,
          timezone: shop.timezone,
          plan_name: shop.plan_name,
        },
      },
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid Shopify configuration", error.errors);
    }
    
    logger.error("Shopify connection test failed", {
      error: (error as Error).message,
      tenantId: ctx.state.tenantId,
    });
    
    throw errors.platformError("Failed to connect to Shopify", {
      error: (error as Error).message,
    });
  }
});

// POST /shopify/sync/products - Sync products from Shopify
router.post("/sync/products", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    const { shopifyConfig, ...syncOptions } = body;
    
    const config = shopifyConfigSchema.parse(shopifyConfig);
    const options = syncProductsSchema.parse(syncOptions);

    logger.info("Starting Shopify products sync", {
      shopDomain: config.shopDomain,
      limit: options.limit,
      sinceId: options.sinceId,
      tenantId: ctx.state.tenantId,
    });

    const client = new ShopifyClient(config);
    const products = await client.getProducts(options.limit, options.sinceId);

    // TODO(@integration-team): Store products in database
    // const integrationService = getIntegrationService();
    // await integrationService.storeProducts("shopify", products, ctx.state.tenantId);

    logIntegrationEvent("shopify", "products_synced", {
      shopDomain: config.shopDomain,
      count: products.length,
      tenantId: ctx.state.tenantId,
    });

    ctx.response.body = {
      success: true,
      data: {
        synced: products.length,
        products: products.map(p => ({
          id: p.id,
          title: p.title,
          handle: p.handle,
          status: p.status,
          variants_count: p.variants.length,
          created_at: p.created_at,
          updated_at: p.updated_at,
        })),
        next_since_id: products.length > 0 ? Math.max(...products.map(p => p.id)) : null,
      },
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid sync parameters", error.errors);
    }
    
    logger.error("Shopify products sync failed", {
      error: (error as Error).message,
      tenantId: ctx.state.tenantId,
    });
    
    throw errors.syncError("Failed to sync Shopify products", {
      error: (error as Error).message,
    });
  }
});

// POST /shopify/sync/orders - Sync orders from Shopify
router.post("/sync/orders", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    const { shopifyConfig, ...syncOptions } = body;
    
    const config = shopifyConfigSchema.parse(shopifyConfig);
    const options = syncOrdersSchema.parse(syncOptions);

    logger.info("Starting Shopify orders sync", {
      shopDomain: config.shopDomain,
      limit: options.limit,
      sinceId: options.sinceId,
      status: options.status,
      tenantId: ctx.state.tenantId,
    });

    const client = new ShopifyClient(config);
    const orders = await client.getOrders(options.limit, options.sinceId, options.status);

    // TODO(@integration-team): Store orders in database
    // const integrationService = getIntegrationService();
    // await integrationService.storeOrders("shopify", orders, ctx.state.tenantId);

    logIntegrationEvent("shopify", "orders_synced", {
      shopDomain: config.shopDomain,
      count: orders.length,
      status: options.status,
      tenantId: ctx.state.tenantId,
    });

    ctx.response.body = {
      success: true,
      data: {
        synced: orders.length,
        orders: orders.map(o => ({
          id: o.id,
          order_number: o.order_number,
          email: o.email,
          total_price: o.total_price,
          currency: o.currency,
          financial_status: o.financial_status,
          fulfillment_status: o.fulfillment_status,
          created_at: o.created_at,
          updated_at: o.updated_at,
        })),
        next_since_id: orders.length > 0 ? Math.max(...orders.map(o => o.id)) : null,
      },
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid sync parameters", error.errors);
    }
    
    logger.error("Shopify orders sync failed", {
      error: (error as Error).message,
      tenantId: ctx.state.tenantId,
    });
    
    throw errors.syncError("Failed to sync Shopify orders", {
      error: (error as Error).message,
    });
  }
});

// POST /shopify/webhooks - Create webhook
router.post("/webhooks", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    const { shopifyConfig, ...webhookData } = body;
    
    const config = shopifyConfigSchema.parse(shopifyConfig);
    const webhook = createWebhookSchema.parse(webhookData);

    logger.info("Creating Shopify webhook", {
      shopDomain: config.shopDomain,
      topic: webhook.topic,
      address: webhook.address,
      tenantId: ctx.state.tenantId,
    });

    const client = new ShopifyClient(config);
    const createdWebhook = await client.createWebhook(webhook.topic, webhook.address);

    logIntegrationEvent("shopify", "webhook_created", {
      shopDomain: config.shopDomain,
      webhookId: createdWebhook.id,
      topic: webhook.topic,
      tenantId: ctx.state.tenantId,
    });

    ctx.response.body = {
      success: true,
      data: createdWebhook,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid webhook parameters", error.errors);
    }
    
    logger.error("Shopify webhook creation failed", {
      error: (error as Error).message,
      tenantId: ctx.state.tenantId,
    });
    
    throw errors.platformError("Failed to create Shopify webhook", {
      error: (error as Error).message,
    });
  }
});

// GET /shopify/webhooks - Get all webhooks
router.post("/webhooks/list", async (ctx) => {
  try {
    const body = await ctx.request.body.json();
    const config = shopifyConfigSchema.parse(body);

    logger.info("Fetching Shopify webhooks", {
      shopDomain: config.shopDomain,
      tenantId: ctx.state.tenantId,
    });

    const client = new ShopifyClient(config);
    const webhooks = await client.getWebhooks();

    logIntegrationEvent("shopify", "webhooks_fetched", {
      shopDomain: config.shopDomain,
      count: webhooks.length,
      tenantId: ctx.state.tenantId,
    });

    ctx.response.body = {
      success: true,
      data: webhooks,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw errors.validation("Invalid Shopify configuration", error.errors);
    }
    
    logger.error("Shopify webhooks fetch failed", {
      error: (error as Error).message,
      tenantId: ctx.state.tenantId,
    });
    
    throw errors.platformError("Failed to fetch Shopify webhooks", {
      error: (error as Error).message,
    });
  }
});

export { router as shopifyRouter };
