import { Router } from "@oak/oak";
import { logger } from "../utils/logger.ts";
import { WebhookHandler } from "../webhooks/webhookHandler.ts";
import { errors } from "../middleware/errorHandler.ts";

// Create router
const router = new Router();

// POST /webhooks/:platform - Handle incoming webhooks from any platform
router.post("/:platform", async (ctx) => {
  const platform = ctx.params.platform;
  
  if (!platform) {
    throw errors.badRequest("Platform parameter is required");
  }

  // Validate platform
  const supportedPlatforms = ["shopify", "woocommerce", "ebay"];
  if (!supportedPlatforms.includes(platform.toLowerCase())) {
    throw errors.badRequest(`Unsupported platform: ${platform}`);
  }

  logger.info("Webhook received", {
    platform,
    method: ctx.request.method,
    contentType: ctx.request.headers.get("content-type"),
    userAgent: ctx.request.headers.get("user-agent"),
    ip: ctx.request.headers.get("x-forwarded-for") || 
        ctx.request.headers.get("x-real-ip") || 
        "unknown",
  });

  try {
    await WebhookHandler.handleWebhook(ctx);
  } catch (error) {
    logger.error("Webhook handling failed", {
      platform,
      error: (error as Error).message,
    });
    throw error;
  }
});

// GET /webhooks/health - Webhook endpoint health check
router.get("/health", (ctx) => {
  ctx.response.body = {
    success: true,
    service: "webhook-handler",
    status: "healthy",
    supported_platforms: ["shopify", "woocommerce", "ebay"],
    timestamp: new Date().toISOString(),
  };
});

// POST /webhooks/test/:platform - Test webhook endpoint
router.post("/test/:platform", async (ctx) => {
  const platform = ctx.params.platform;
  
  if (!platform) {
    throw errors.badRequest("Platform parameter is required");
  }

  logger.info("Test webhook received", {
    platform,
    timestamp: new Date().toISOString(),
  });

  try {
    const body = await ctx.request.body.json();
    
    ctx.response.body = {
      success: true,
      message: "Test webhook received successfully",
      platform,
      received_data: {
        headers: Object.fromEntries(ctx.request.headers.entries()),
        body,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error("Test webhook failed", {
      platform,
      error: (error as Error).message,
    });
    
    ctx.response.body = {
      success: false,
      message: "Test webhook failed",
      platform,
      error: (error as Error).message,
      timestamp: new Date().toISOString(),
    };
  }
});

export { router as webhooksRouter };
