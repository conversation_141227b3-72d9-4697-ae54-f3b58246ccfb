import { query, queryOne, transaction } from "../utils/database.ts";
import { set, get, getJSON, del } from "../utils/redis.ts";
import { logger, logIntegrationEvent } from "../utils/logger.ts";

export interface Integration {
  id: string;
  tenantId: string;
  platform: string;
  name: string;
  config: Record<string, unknown>;
  status: "active" | "inactive" | "error";
  lastSyncAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SyncJob {
  id: string;
  integrationId: string;
  tenantId: string;
  platform: string;
  type: "products" | "orders" | "customers" | "full";
  status: "pending" | "running" | "completed" | "failed";
  startedAt?: string;
  completedAt?: string;
  error?: string;
  stats: {
    processed: number;
    created: number;
    updated: number;
    errors: number;
  };
}

export class IntegrationService {
  private cacheTTL = {
    short: 300, // 5 minutes
    medium: 1800, // 30 minutes
    long: 3600, // 1 hour
  };

  /**
   * Get all integrations for a tenant
   */
  async getIntegrations(tenantId: string): Promise<Integration[]> {
    try {
      const cacheKey = `integrations:${tenantId}`;
      
      // Try cache first
      const cached = await getJSON<Integration[]>(cacheKey, tenantId);
      if (cached) {
        logger.debug("Integrations cache hit", { tenantId });
        return cached;
      }

      const integrations = await query<Integration>(
        `SELECT id, tenant_id, platform, name, config, status, last_sync_at, created_at, updated_at
         FROM integrations 
         WHERE tenant_id = $1 
         ORDER BY created_at DESC`,
        [tenantId],
        tenantId
      );

      // Cache the result
      await set(cacheKey, integrations, this.cacheTTL.medium, tenantId);

      logger.info("Integrations fetched", {
        tenantId,
        count: integrations.length,
      });

      return integrations;
    } catch (error) {
      logger.error("Failed to get integrations", {
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Get integration by ID
   */
  async getIntegration(id: string, tenantId: string): Promise<Integration | null> {
    try {
      const cacheKey = `integration:${id}`;
      
      // Try cache first
      const cached = await getJSON<Integration>(cacheKey, tenantId);
      if (cached) {
        logger.debug("Integration cache hit", { id, tenantId });
        return cached;
      }

      const integration = await queryOne<Integration>(
        `SELECT id, tenant_id, platform, name, config, status, last_sync_at, created_at, updated_at
         FROM integrations 
         WHERE id = $1 AND tenant_id = $2`,
        [id, tenantId],
        tenantId
      );

      if (integration) {
        // Cache the result
        await set(cacheKey, integration, this.cacheTTL.medium, tenantId);
      }

      return integration;
    } catch (error) {
      logger.error("Failed to get integration", {
        id,
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Create a new integration
   */
  async createIntegration(
    tenantId: string,
    platform: string,
    name: string,
    config: Record<string, unknown>
  ): Promise<Integration> {
    try {
      const integration = await transaction(async (client) => {
        const result = await client.queryObject<Integration>(
          `INSERT INTO integrations (tenant_id, platform, name, config, status, created_at, updated_at)
           VALUES ($1, $2, $3, $4, 'active', NOW(), NOW())
           RETURNING id, tenant_id, platform, name, config, status, last_sync_at, created_at, updated_at`,
          [tenantId, platform, name, JSON.stringify(config)]
        );

        return result.rows[0];
      }, tenantId);

      // Invalidate cache
      await del(`integrations:${tenantId}`, tenantId);

      logIntegrationEvent(platform, "integration_created", {
        integrationId: integration.id,
        tenantId,
        name,
      });

      logger.info("Integration created", {
        id: integration.id,
        platform,
        name,
        tenantId,
      });

      return integration;
    } catch (error) {
      logger.error("Failed to create integration", {
        platform,
        name,
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Update integration
   */
  async updateIntegration(
    id: string,
    tenantId: string,
    updates: Partial<Pick<Integration, "name" | "config" | "status">>
  ): Promise<Integration> {
    try {
      const integration = await transaction(async (client) => {
        const setParts: string[] = [];
        const values: unknown[] = [];
        let paramIndex = 1;

        if (updates.name !== undefined) {
          setParts.push(`name = $${paramIndex}`);
          values.push(updates.name);
          paramIndex++;
        }

        if (updates.config !== undefined) {
          setParts.push(`config = $${paramIndex}`);
          values.push(JSON.stringify(updates.config));
          paramIndex++;
        }

        if (updates.status !== undefined) {
          setParts.push(`status = $${paramIndex}`);
          values.push(updates.status);
          paramIndex++;
        }

        setParts.push(`updated_at = NOW()`);
        values.push(id, tenantId);

        const result = await client.queryObject<Integration>(
          `UPDATE integrations 
           SET ${setParts.join(", ")}
           WHERE id = $${paramIndex} AND tenant_id = $${paramIndex + 1}
           RETURNING id, tenant_id, platform, name, config, status, last_sync_at, created_at, updated_at`,
          values
        );

        if (result.rows.length === 0) {
          throw new Error("Integration not found");
        }

        return result.rows[0];
      }, tenantId);

      // Invalidate cache
      await del(`integration:${id}`, tenantId);
      await del(`integrations:${tenantId}`, tenantId);

      logIntegrationEvent(integration.platform, "integration_updated", {
        integrationId: id,
        tenantId,
        updates: Object.keys(updates),
      });

      logger.info("Integration updated", {
        id,
        tenantId,
        updates: Object.keys(updates),
      });

      return integration;
    } catch (error) {
      logger.error("Failed to update integration", {
        id,
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Delete integration
   */
  async deleteIntegration(id: string, tenantId: string): Promise<void> {
    try {
      await transaction(async (client) => {
        const result = await client.queryObject(
          `DELETE FROM integrations 
           WHERE id = $1 AND tenant_id = $2`,
          [id, tenantId]
        );

        if (result.rowCount === 0) {
          throw new Error("Integration not found");
        }
      }, tenantId);

      // Invalidate cache
      await del(`integration:${id}`, tenantId);
      await del(`integrations:${tenantId}`, tenantId);

      logIntegrationEvent("unknown", "integration_deleted", {
        integrationId: id,
        tenantId,
      });

      logger.info("Integration deleted", {
        id,
        tenantId,
      });
    } catch (error) {
      logger.error("Failed to delete integration", {
        id,
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Create sync job
   */
  async createSyncJob(
    integrationId: string,
    tenantId: string,
    type: SyncJob["type"]
  ): Promise<SyncJob> {
    try {
      const integration = await this.getIntegration(integrationId, tenantId);
      if (!integration) {
        throw new Error("Integration not found");
      }

      const syncJob = await transaction(async (client) => {
        const result = await client.queryObject<SyncJob>(
          `INSERT INTO sync_jobs (integration_id, tenant_id, platform, type, status, stats, created_at, updated_at)
           VALUES ($1, $2, $3, $4, 'pending', '{"processed":0,"created":0,"updated":0,"errors":0}', NOW(), NOW())
           RETURNING id, integration_id, tenant_id, platform, type, status, started_at, completed_at, error, stats, created_at, updated_at`,
          [integrationId, tenantId, integration.platform, type]
        );

        return result.rows[0];
      }, tenantId);

      logIntegrationEvent(integration.platform, "sync_job_created", {
        syncJobId: syncJob.id,
        integrationId,
        tenantId,
        type,
      });

      logger.info("Sync job created", {
        id: syncJob.id,
        integrationId,
        platform: integration.platform,
        type,
        tenantId,
      });

      return syncJob;
    } catch (error) {
      logger.error("Failed to create sync job", {
        integrationId,
        tenantId,
        type,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Get sync jobs for integration
   */
  async getSyncJobs(integrationId: string, tenantId: string, limit = 50): Promise<SyncJob[]> {
    try {
      const syncJobs = await query<SyncJob>(
        `SELECT id, integration_id, tenant_id, platform, type, status, started_at, completed_at, error, stats, created_at, updated_at
         FROM sync_jobs 
         WHERE integration_id = $1 AND tenant_id = $2 
         ORDER BY created_at DESC 
         LIMIT $3`,
        [integrationId, tenantId, limit],
        tenantId
      );

      return syncJobs;
    } catch (error) {
      logger.error("Failed to get sync jobs", {
        integrationId,
        tenantId,
        error: (error as Error).message,
      });
      throw error;
    }
  }
}
