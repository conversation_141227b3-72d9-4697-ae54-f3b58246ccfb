import * as log from "@std/log";
import { config } from "../config/config.ts";

// Log level mapping
const LOG_LEVELS = {
  DEBUG: "DEBUG" as const,
  INFO: "INFO" as const,
  WARN: "WARN" as const,
  ERROR: "ERROR" as const,
  CRITICAL: "CRITICAL" as const,
};

// Initialize logger configuration
export function initializeLogger(): void {
  const logLevel = LOG_LEVELS[config.logging.level as keyof typeof LOG_LEVELS] || "INFO";
  
  log.setup({
    handlers: {
      console: new log.ConsoleHandler(logLevel, {
        formatter: config.logging.format === "json" ? jsonFormatter : textFormatter,
      }),
    },
    loggers: {
      default: {
        level: logLevel,
        handlers: ["console"],
      },
      integration: {
        level: logLevel,
        handlers: ["console"],
      },
    },
  });
}

// JSON formatter for structured logging
function jsonFormatter(logRecord: log.LogRecord): string {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level: logRecord.levelName,
    service: "integration-service",
    message: logRecord.msg,
    ...(logRecord.args[0] && typeof logRecord.args[0] === 'object' ? logRecord.args[0] : {}),
  };

  return JSON.stringify(logEntry);
}

// Text formatter for human-readable logging
function textFormatter(logRecord: log.LogRecord): string {
  const timestamp = new Date().toISOString();
  const level = logRecord.levelName.padEnd(5);
  const message = logRecord.msg;
  const args = logRecord.args.length > 0 ? ` ${JSON.stringify(logRecord.args[0])}` : "";
  
  return `${timestamp} [${level}] integration-service: ${message}${args}`;
}

// Logger interface
export interface Logger {
  debug(message: string, meta?: Record<string, unknown>): void;
  info(message: string, meta?: Record<string, unknown>): void;
  warn(message: string, meta?: Record<string, unknown>): void;
  error(message: string, error?: Error | Record<string, unknown>): void;
  critical(message: string, error?: Error | Record<string, unknown>): void;
}

// Create logger instance
class IntegrationLogger implements Logger {
  private logger = log.getLogger("integration");

  debug(message: string, meta: Record<string, unknown> = {}): void {
    this.logger.debug(message, meta);
  }

  info(message: string, meta: Record<string, unknown> = {}): void {
    this.logger.info(message, meta);
  }

  warn(message: string, meta: Record<string, unknown> = {}): void {
    this.logger.warn(message, meta);
  }

  error(message: string, error: Error | Record<string, unknown> = {}): void {
    if (error instanceof Error) {
      this.logger.error(message, {
        error: error.message,
        stack: error.stack,
        name: error.name,
      });
    } else {
      this.logger.error(message, error);
    }
  }

  critical(message: string, error: Error | Record<string, unknown> = {}): void {
    if (error instanceof Error) {
      this.logger.critical(message, {
        error: error.message,
        stack: error.stack,
        name: error.name,
      });
    } else {
      this.logger.critical(message, error);
    }
  }
}

// Export logger instance
export const logger = new IntegrationLogger();

// Request logging middleware helper
export function createRequestLogger() {
  return async (ctx: { request: { method: string; url: { pathname: string }; headers: { get: (key: string) => string | null } }; response: { status: number } }, next: () => Promise<void>) => {
    const start = Date.now();
    const method = ctx.request.method;
    const url = ctx.request.url.pathname;
    const userAgent = ctx.request.headers.get("user-agent") || "unknown";
    const ip = ctx.request.headers.get("x-forwarded-for") || 
               ctx.request.headers.get("x-real-ip") || 
               "unknown";

    logger.info("Incoming request", {
      method,
      url,
      ip,
      userAgent,
    });

    return await next().then(() => {
      const duration = Date.now() - start;
      const status = ctx.response.status;

      logger.info("Request completed", {
        method,
        url,
        status,
        duration,
        ip,
      });
    }).catch((error) => {
      const duration = Date.now() - start;
      const status = ctx.response.status || 500;

      logger.error("Request failed", {
        method,
        url,
        status,
        duration,
        ip,
        error: error.message,
      });

      throw error;
    });
  };
}

// Performance logging helper
export function logPerformance(operation: string, startTime: number, metadata: Record<string, unknown> = {}): void {
  const duration = Date.now() - startTime;
  
  if (duration > 1000) {
    logger.warn("Slow operation detected", {
      operation,
      duration,
      ...metadata,
    });
  } else {
    logger.debug("Operation completed", {
      operation,
      duration,
      ...metadata,
    });
  }
}

// Integration logging helper
export function logIntegrationEvent(
  platform: string,
  event: string,
  metadata: Record<string, unknown> = {}
): void {
  logger.info("Integration event", {
    platform,
    event,
    ...metadata,
  });
}

// Webhook logging helper
export function logWebhookEvent(
  platform: string,
  event: string,
  metadata: Record<string, unknown> = {}
): void {
  logger.info("Webhook event", {
    platform,
    event,
    ...metadata,
  });
}

// Sync logging helper
export function logSyncEvent(
  platform: string,
  operation: string,
  metadata: Record<string, unknown> = {}
): void {
  logger.info("Sync event", {
    platform,
    operation,
    ...metadata,
  });
}

// Error logging with context
export function logErrorWithContext(
  message: string,
  error: Error,
  context: Record<string, unknown> = {},
): void {
  logger.error(message, {
    error: error.message,
    stack: error.stack,
    name: error.name,
    ...context,
  });
}
