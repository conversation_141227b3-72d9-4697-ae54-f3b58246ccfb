import { assertEquals, assertExists } from "@std/assert";
import { ShopifyClient } from "../src/platforms/shopify/shopifyClient.ts";

Deno.test("Integration Service - Basic Tests", async (t) => {
  await t.step("ShopifyClient - should create instance", () => {
    const client = new ShopifyClient({
      shopDomain: "test-shop",
      accessToken: "test-token",
    });
    
    assertExists(client);
  });

  await t.step("ShopifyClient - should verify webhook signature", async () => {
    const body = '{"test": "data"}';
    const secret = "test-secret";
    
    // Create a valid signature
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey(
      "raw",
      encoder.encode(secret),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );

    const signatureBuffer = await crypto.subtle.sign("HMAC", key, encoder.encode(body));
    const signature = btoa(String.fromCharCode(...new Uint8Array(signatureBuffer)));

    const isValid = await ShopifyClient.verifyWebhook(body, signature, secret);
    assertEquals(isValid, true);
  });

  await t.step("ShopifyClient - should reject invalid webhook signature", async () => {
    const body = '{"test": "data"}';
    const secret = "test-secret";
    const invalidSignature = "invalid-signature";

    const isValid = await ShopifyClient.verifyWebhook(body, invalidSignature, secret);
    assertEquals(isValid, false);
  });
});

Deno.test("Integration Service - Configuration Tests", async (t) => {
  await t.step("Config - should load environment variables", async () => {
    const { config } = await import("../src/config/config.ts");
    
    assertExists(config);
    assertExists(config.port);
    assertExists(config.database);
    assertExists(config.redis);
    assertExists(config.platforms);
  });

  await t.step("Config - should have platform configurations", async () => {
    const { config } = await import("../src/config/config.ts");
    
    assertExists(config.platforms.shopify);
    assertExists(config.platforms.woocommerce);
    assertExists(config.platforms.ebay);
    
    assertEquals(typeof config.platforms.shopify.rateLimitPerSecond, "number");
    assertEquals(typeof config.platforms.woocommerce.rateLimitPerSecond, "number");
    assertEquals(typeof config.platforms.ebay.rateLimitPerSecond, "number");
  });
});

Deno.test("Integration Service - Logger Tests", async (t) => {
  await t.step("Logger - should initialize without errors", async () => {
    const { initializeLogger, logger } = await import("../src/utils/logger.ts");
    
    // This should not throw
    initializeLogger();
    
    assertExists(logger);
    assertExists(logger.info);
    assertExists(logger.error);
    assertExists(logger.debug);
    assertExists(logger.warn);
  });

  await t.step("Logger - should log messages without errors", async () => {
    const { logger } = await import("../src/utils/logger.ts");
    
    // These should not throw
    logger.info("Test info message");
    logger.debug("Test debug message", { test: "data" });
    logger.warn("Test warning message");
  });
});

Deno.test("Integration Service - HTTP Client Tests", async (t) => {
  await t.step("HttpClient - should create instance", async () => {
    const { HttpClient } = await import("../src/utils/httpClient.ts");
    
    const client = new HttpClient({
      baseURL: "https://api.example.com",
      timeout: 5000,
    });
    
    assertExists(client);
  });

  await t.step("HttpClient - should have correct methods", async () => {
    const { HttpClient } = await import("../src/utils/httpClient.ts");
    
    const client = new HttpClient();
    
    assertExists(client.get);
    assertExists(client.post);
    assertExists(client.put);
    assertExists(client.patch);
    assertExists(client.delete);
  });
});

Deno.test("Integration Service - Webhook Handler Tests", async (t) => {
  await t.step("WebhookHandler - should exist", async () => {
    const { WebhookHandler } = await import("../src/webhooks/webhookHandler.ts");
    
    assertExists(WebhookHandler);
    assertExists(WebhookHandler.handleWebhook);
  });
});

Deno.test("Integration Service - Integration Service Tests", async (t) => {
  await t.step("IntegrationService - should create instance", async () => {
    const { IntegrationService } = await import("../src/services/integrationService.ts");
    
    const service = new IntegrationService();
    assertExists(service);
  });

  await t.step("IntegrationService - should have required methods", async () => {
    const { IntegrationService } = await import("../src/services/integrationService.ts");
    
    const service = new IntegrationService();
    
    assertExists(service.getIntegrations);
    assertExists(service.getIntegration);
    assertExists(service.createIntegration);
    assertExists(service.updateIntegration);
    assertExists(service.deleteIntegration);
    assertExists(service.createSyncJob);
    assertExists(service.getSyncJobs);
  });
});
