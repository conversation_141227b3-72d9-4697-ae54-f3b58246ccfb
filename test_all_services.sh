#!/bin/bash

echo "🔍 Testing All Services Health Status"
echo "======================================"

# Test Analytics Service (Port 3002)
echo "📊 Analytics Service (Port 3002):"
curl -s http://localhost:3002/health | jq '.service, .status' || echo "❌ Failed"
echo ""

# Test Dashboard Service (Port 3000)
echo "📈 Dashboard Service (Port 3000):"
curl -s http://localhost:3000/health | jq '.status' || echo "❌ Failed"
echo ""

# Test Integration Service (Port 3001)
echo "🔗 Integration Service (Port 3001):"
curl -s http://localhost:3001/health | jq '.service, .status' || echo "❌ Failed"
echo ""

# Test Admin Service (Port 3004)
echo "⚙️  Admin Service (Port 3004):"
curl -s http://localhost:3004/api/health | jq '.service, .status' || echo "❌ Failed"
echo ""

# Test Billing Service (Port 3003)
echo "💰 Billing Service (Port 3003):"
curl -s http://localhost:3003/health | jq '.service, .status' || echo "❌ Failed"
echo ""

echo "✅ Service Health Check Complete"
