#!/bin/bash

echo "🌐 Testing Cross-Service Communication"
echo "====================================="

TENANT_ID="00000000-0000-0000-0000-000000000001"

echo "1. Analytics → Billing Integration Test:"
echo "---------------------------------------"

# Get current usage from analytics
ANALYTICS_EVENTS=$(curl -s "http://localhost:3002/api/enhanced-analytics/dashboard?tenantId=$TENANT_ID" | jq '.data.realtime.total_events_today')
echo "📊 Analytics Service reports: $ANALYTICS_EVENTS events today"

# Get usage from billing service
BILLING_EVENTS=$(curl -s "http://localhost:3003/api/billing/usage?tenantId=$TENANT_ID" | jq '.data.currentMonth.total_events')
echo "💰 Billing Service reports: $BILLING_EVENTS total events"

# Verify data consistency
if [ "$ANALYTICS_EVENTS" -gt 0 ] && [ "$BILLING_EVENTS" -gt 0 ]; then
    echo "✅ Both services have consistent event data"
else
    echo "⚠️  Data inconsistency detected"
fi
echo ""

echo "2. Service Readiness Check:"
echo "--------------------------"

# Check all services are ready
echo "📊 Analytics Service Ready:"
curl -s "http://localhost:3002/ready" | jq '.success, .checks'
echo ""

echo "⚙️  Admin Service Ready:"
curl -s "http://localhost:3004/api/health/ready" | jq '.status'
echo ""

echo "💰 Billing Service Ready:"
curl -s "http://localhost:3003/ready" | jq '.success, .checks'
echo ""

echo "3. Multi-Service Workflow Test:"
echo "------------------------------"

# Simulate a complete workflow
echo "🔄 Simulating customer journey workflow..."

# Step 1: Get subscription info
SUBSCRIPTION=$(curl -s "http://localhost:3003/api/billing/subscription?tenantId=$TENANT_ID")
PLAN_ID=$(echo $SUBSCRIPTION | jq -r '.data.planId')
echo "📋 Customer has plan: $PLAN_ID"

# Step 2: Check current usage
CURRENT_USAGE=$(curl -s "http://localhost:3003/api/billing/usage?tenantId=$TENANT_ID")
EVENTS_USED=$(echo $CURRENT_USAGE | jq '.data.currentMonth.total_events')
EVENTS_LIMIT=$(echo $CURRENT_USAGE | jq '.data.limits.events')
echo "📈 Usage: $EVENTS_USED / $EVENTS_LIMIT events"

# Step 3: Get analytics insights
ANALYTICS_DATA=$(curl -s "http://localhost:3002/api/enhanced-analytics/dashboard?tenantId=$TENANT_ID")
REVENUE_TODAY=$(echo $ANALYTICS_DATA | jq '.data.realtime.revenue_today')
PURCHASES_TODAY=$(echo $ANALYTICS_DATA | jq '.data.realtime.purchases_today')
echo "💰 Revenue today: \$$REVENUE_TODAY from $PURCHASES_TODAY purchases"

# Calculate usage percentage
USAGE_PERCENT=$(echo "scale=2; $EVENTS_USED * 100 / $EVENTS_LIMIT" | bc)
echo "📊 Usage percentage: $USAGE_PERCENT%"

if (( $(echo "$USAGE_PERCENT > 80" | bc -l) )); then
    echo "⚠️  High usage detected - billing alert recommended"
else
    echo "✅ Usage within normal limits"
fi

echo ""
echo "✅ Cross-Service Communication Test Complete"
