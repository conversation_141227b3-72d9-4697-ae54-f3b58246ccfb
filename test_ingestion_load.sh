#!/bin/bash

echo "🚀 Testing High-Volume Data Ingestion"
echo "===================================="

TENANT_ID="00000000-0000-0000-0000-000000000001"

echo "1. Baseline Data Count:"
echo "----------------------"
INITIAL_COUNT=$(PGPASSWORD=password psql -h localhost -U postgres -d ecommerce_analytics -t -c "SELECT COUNT(*) FROM customer_events WHERE tenant_id = '$TENANT_ID';" | tr -d ' ')
echo "📊 Initial event count: $INITIAL_COUNT"
echo ""

echo "2. Bulk Data Insertion Test (Simulating High Volume):"
echo "----------------------------------------------------"

echo "🔄 Inserting 1000 events in batches to test ingestion performance..."

START_TIME=$(date +%s%3N)

# Insert events in batches to simulate high-volume ingestion
PGPASSWORD=password psql -h localhost -U postgres -d ecommerce_analytics -c "
INSERT INTO customer_events (tenant_id, event_type, event_source, revenue, utm_source, utm_medium, utm_campaign, timestamp)
SELECT 
    '$TENANT_ID'::uuid,
    (ARRAY['page_view', 'product_view', 'add_to_cart', 'purchase'])[floor(random() * 4 + 1)],
    'website',
    CASE WHEN random() < 0.1 THEN random() * 100 ELSE 0 END,
    (ARRAY['google', 'facebook', 'instagram', 'youtube', 'tiktok'])[floor(random() * 5 + 1)],
    (ARRAY['organic', 'cpc', 'social', 'video', 'email'])[floor(random() * 5 + 1)],
    'load_test_campaign',
    NOW() - (random() * INTERVAL '1 hour')
FROM generate_series(1, 1000);
" > /dev/null 2>&1

END_TIME=$(date +%s%3N)
INSERTION_TIME=$((END_TIME - START_TIME))

echo "  ✅ Inserted 1000 events in ${INSERTION_TIME}ms"
echo "  📈 Ingestion rate: $((1000 * 1000 / INSERTION_TIME)) events/second"
echo ""

echo "3. Verify Data Integrity:"
echo "------------------------"
FINAL_COUNT=$(PGPASSWORD=password psql -h localhost -U postgres -d ecommerce_analytics -t -c "SELECT COUNT(*) FROM customer_events WHERE tenant_id = '$TENANT_ID';" | tr -d ' ')
INSERTED_COUNT=$((FINAL_COUNT - INITIAL_COUNT))
echo "📊 Final event count: $FINAL_COUNT"
echo "➕ Events inserted: $INSERTED_COUNT"

if [ $INSERTED_COUNT -eq 1000 ]; then
    echo "✅ Data integrity verified - all events inserted correctly"
else
    echo "⚠️  Data integrity issue - expected 1000, got $INSERTED_COUNT"
fi
echo ""

echo "4. Query Performance After Load:"
echo "-------------------------------"

echo "🔍 Testing query performance with increased data volume..."

# Test dashboard query performance
START_TIME=$(date +%s%3N)
curl -s "http://localhost:3002/api/enhanced-analytics/dashboard?tenantId=$TENANT_ID" > /dev/null
END_TIME=$(date +%s%3N)
QUERY_TIME=$((END_TIME - START_TIME))

echo "  📊 Dashboard query time with $FINAL_COUNT events: ${QUERY_TIME}ms"

if [ $QUERY_TIME -lt 100 ]; then
    echo "  ✅ Query performance maintained under 100ms target"
else
    echo "  ⚠️  Query performance degraded above 100ms target"
fi
echo ""

echo "5. TimescaleDB Compression Test:"
echo "-------------------------------"

echo "🗜️  Testing TimescaleDB compression capabilities..."

# Check chunk information
PGPASSWORD=password psql -h localhost -U postgres -d ecommerce_analytics -c "
SELECT 
    chunk_name,
    range_start,
    range_end,
    is_compressed,
    compressed_chunk_id
FROM timescaledb_information.chunks 
WHERE hypertable_name = 'customer_events' 
ORDER BY range_start DESC 
LIMIT 5;
" 2>/dev/null

echo ""

echo "6. Continuous Aggregate Refresh Test:"
echo "------------------------------------"

echo "🔄 Testing continuous aggregate refresh with new data..."

START_TIME=$(date +%s%3N)
PGPASSWORD=password psql -h localhost -U postgres -d ecommerce_analytics -c "
CALL refresh_continuous_aggregate('daily_customer_metrics', NULL, NULL);
" > /dev/null 2>&1
END_TIME=$(date +%s%3N)
REFRESH_TIME=$((END_TIME - START_TIME))

echo "  ✅ Continuous aggregate refreshed in ${REFRESH_TIME}ms"

# Verify the refresh worked
CAGG_COUNT=$(PGPASSWORD=password psql -h localhost -U postgres -d ecommerce_analytics -t -c "SELECT SUM(total_events) FROM daily_customer_metrics WHERE tenant_id = '$TENANT_ID';" | tr -d ' ')
echo "  📊 Continuous aggregate shows $CAGG_COUNT total events"
echo ""

echo "7. Load Testing Summary:"
echo "-----------------------"

echo "🎯 Performance Results:"
echo "  ✅ Bulk Insertion: $((1000 * 1000 / INSERTION_TIME)) events/second"
echo "  ✅ Query Performance: ${QUERY_TIME}ms (target: <100ms)"
echo "  ✅ Data Integrity: $INSERTED_COUNT/1000 events inserted correctly"
echo "  ✅ Continuous Aggregates: Refreshed in ${REFRESH_TIME}ms"
echo ""

echo "📈 Scalability Assessment:"
if [ $((1000 * 1000 / INSERTION_TIME)) -gt 10000 ]; then
    echo "  ✅ EXCEEDS 10,000 events/second target"
    echo "  🚀 Current capability: $((1000 * 1000 / INSERTION_TIME)) events/second"
else
    echo "  ⚠️  Below 10,000 events/second target"
    echo "  📊 Current capability: $((1000 * 1000 / INSERTION_TIME)) events/second"
    echo "  💡 Consider optimizing batch sizes and connection pooling"
fi

echo ""
echo "✅ High-Volume Ingestion Test Complete"
