#!/bin/bash

echo "🔗 Testing Service Integration"
echo "=============================="

TENANT_ID="00000000-0000-0000-0000-000000000001"

echo "1. Testing Analytics Service Enhanced Endpoints:"
echo "------------------------------------------------"

# Test dashboard metrics
echo "📊 Dashboard Metrics:"
curl -s "http://localhost:3002/api/enhanced-analytics/dashboard?tenantId=$TENANT_ID" | jq '.success, .data.realtime.total_events_today'
echo ""

# Test events endpoint
echo "📋 Recent Events (limit 3):"
curl -s "http://localhost:3002/api/enhanced-analytics/events?tenantId=$TENANT_ID&limit=3" | jq '.success, .count'
echo ""

echo "2. Testing Billing Service Integration:"
echo "--------------------------------------"

# Test billing plans
echo "💰 Available Plans:"
curl -s "http://localhost:3003/api/billing/plans" | jq '.success, (.data | length)'
echo ""

# Test subscription status
echo "📋 Subscription Status:"
curl -s "http://localhost:3003/api/billing/subscription?tenantId=$TENANT_ID" | jq '.success, .data.status'
echo ""

# Test usage tracking
echo "📈 Usage Tracking:"
curl -s "http://localhost:3003/api/billing/usage?tenantId=$TENANT_ID" | jq '.success, .data.currentMonth.total_events'
echo ""

echo "3. Testing Error Handling:"
echo "-------------------------"

# Test missing tenant ID
echo "❌ Missing Tenant ID (should fail):"
curl -s "http://localhost:3002/api/enhanced-analytics/dashboard" | jq '.success, .error'
echo ""

# Test invalid endpoint
echo "❌ Invalid Endpoint (should return 404):"
curl -s "http://localhost:3002/api/nonexistent" | jq '.success, .error'
echo ""

echo "4. Testing Multi-tenant Data Isolation:"
echo "---------------------------------------"

# Test with different tenant ID
TENANT_ID_2="00000000-0000-0000-0000-000000000002"
echo "🏢 Different Tenant Data:"
curl -s "http://localhost:3002/api/enhanced-analytics/dashboard?tenantId=$TENANT_ID_2" | jq '.success, .data.realtime.total_events_today'
echo ""

echo "✅ Integration Testing Complete"
