const request = require('supertest');
const nock = require('nock');
const crypto = require('crypto');
const { pool } = require('../../services/integration/src/database');
const app = require('../../services/integration/src/app');
const { SyncManager } = require('../../services/integration/src/services/syncManager');

describe('Data Synchronization and Webhook Reliability', () => {
  let testTenant;
  let shopifyIntegration;
  let wooIntegration;
  let syncManager;

  beforeAll(async () => {
    // Setup test tenant
    const tenantResult = await pool.query(
      'INSERT INTO tenants (id, name, created_at) VALUES ($1, $2, NOW()) RETURNING *',
      ['sync-test-tenant', 'Sync Test Tenant']
    );
    testTenant = tenantResult.rows[0];

    // Setup Shopify integration
    const shopifyResult = await pool.query(
      `INSERT INTO integrations (id, tenant_id, platform, store_url, api_credentials, webhook_secret, is_active) 
       VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [
        'sync-shopify-integration',
        testTenant.id,
        'shopify',
        'sync-test.myshopify.com',
        JSON.stringify({ access_token: 'sync-test-token' }),
        'sync-webhook-secret',
        true
      ]
    );
    shopifyIntegration = shopifyResult.rows[0];

    // Setup WooCommerce integration
    const wooResult = await pool.query(
      `INSERT INTO integrations (id, tenant_id, platform, store_url, api_credentials, webhook_secret, is_active) 
       VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [
        'sync-woo-integration',
        testTenant.id,
        'woocommerce',
        'https://sync-test.com',
        JSON.stringify({ consumer_key: 'sync_ck', consumer_secret: 'sync_cs' }),
        'sync-woo-secret',
        true
      ]
    );
    wooIntegration = wooResult.rows[0];

    syncManager = new SyncManager();
  });

  afterAll(async () => {
    // Cleanup test data
    await pool.query('DELETE FROM webhook_logs WHERE integration_id IN ($1, $2)', 
      [shopifyIntegration.id, wooIntegration.id]);
    await pool.query('DELETE FROM orders WHERE integration_id IN ($1, $2)', 
      [shopifyIntegration.id, wooIntegration.id]);
    await pool.query('DELETE FROM integrations WHERE tenant_id = $1', [testTenant.id]);
    await pool.query('DELETE FROM tenants WHERE id = $1', [testTenant.id]);
    await pool.end();
  });

  beforeEach(() => {
    nock.cleanAll();
  });

  describe('Webhook Delivery and Processing', () => {
    it('should handle webhook delivery failures with retry logic', async () => {
      const orderData = {
        id: 'retry-test-order',
        order_number: 1001,
        email: '<EMAIL>',
        total_price: '99.99',
        created_at: '2024-01-15T10:00:00Z'
      };

      const hmac = crypto
        .createHmac('sha256', 'sync-webhook-secret')
        .update(JSON.stringify(orderData))
        .digest('base64');

      // First attempt - simulate processing failure
      let attemptCount = 0;
      const originalProcess = require('../../services/integration/src/processors/shopifyOrderProcessor');
      const mockProcessor = {
        processOrder: jest.fn().mockImplementation(() => {
          attemptCount++;
          if (attemptCount === 1) {
            throw new Error('Database connection failed');
          }
          return { success: true, trackingId: 'track-123' };
        })
      };

      // Mock the processor
      jest.doMock('../../services/integration/src/processors/shopifyOrderProcessor', () => mockProcessor);

      const response1 = await request(app)
        .post('/api/webhooks/shopify/orders/create')
        .set('X-Shopify-Hmac-Sha256', hmac)
        .set('X-Shopify-Shop-Domain', 'sync-test.myshopify.com')
        .send(orderData);

      expect(response1.status).toBe(200);
      expect(response1.body.success).toBe(true); // Always return 200 to Shopify

      // Verify webhook failure was logged
      const webhookLog = await pool.query(
        'SELECT * FROM webhook_logs WHERE integration_id = $1 AND event_type = $2 ORDER BY created_at DESC LIMIT 1',
        [shopifyIntegration.id, 'order_create']
      );

      expect(webhookLog.rows).toHaveLength(1);
      expect(webhookLog.rows[0].status).toBe('failed');
      expect(webhookLog.rows[0].error_message).toContain('Database connection failed');

      jest.clearAllMocks();
    });

    it('should detect and handle duplicate webhooks', async () => {
      const orderData = {
        id: 'duplicate-test-order',
        order_number: 2001,
        email: '<EMAIL>',
        total_price: '149.99',
        created_at: '2024-01-15T10:00:00Z'
      };

      const hmac = crypto
        .createHmac('sha256', 'sync-webhook-secret')
        .update(JSON.stringify(orderData))
        .digest('base64');

      // Send same webhook twice
      const response1 = await request(app)
        .post('/api/webhooks/shopify/orders/create')
        .set('X-Shopify-Hmac-Sha256', hmac)
        .set('X-Shopify-Shop-Domain', 'sync-test.myshopify.com')
        .set('X-Shopify-Webhook-Id', 'webhook-123')
        .send(orderData);

      const response2 = await request(app)
        .post('/api/webhooks/shopify/orders/create')
        .set('X-Shopify-Hmac-Sha256', hmac)
        .set('X-Shopify-Shop-Domain', 'sync-test.myshopify.com')
        .set('X-Shopify-Webhook-Id', 'webhook-123') // Same webhook ID
        .send(orderData);

      expect(response1.status).toBe(200);
      expect(response2.status).toBe(200);

      // Verify only one order was created
      const orders = await pool.query(
        'SELECT * FROM orders WHERE platform_order_id = $1',
        ['duplicate-test-order']
      );

      expect(orders.rows).toHaveLength(1);

      // Verify duplicate was logged
      const webhookLogs = await pool.query(
        'SELECT * FROM webhook_logs WHERE integration_id = $1 AND webhook_id = $2',
        [shopifyIntegration.id, 'webhook-123']
      );

      expect(webhookLogs.rows).toHaveLength(2);
      expect(webhookLogs.rows.some(log => log.status === 'duplicate')).toBe(true);
    });

    it('should handle out-of-order webhook delivery', async () => {
      const orderCreateData = {
        id: 'order-sequence-test',
        order_number: 3001,
        email: '<EMAIL>',
        total_price: '199.99',
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z'
      };

      const orderUpdateData = {
        id: 'order-sequence-test',
        financial_status: 'paid',
        fulfillment_status: 'fulfilled',
        updated_at: '2024-01-15T12:00:00Z' // Later timestamp
      };

      // Send update webhook first (out of order)
      const updateHmac = crypto
        .createHmac('sha256', 'sync-webhook-secret')
        .update(JSON.stringify(orderUpdateData))
        .digest('base64');

      await request(app)
        .post('/api/webhooks/shopify/orders/update')
        .set('X-Shopify-Hmac-Sha256', updateHmac)
        .set('X-Shopify-Shop-Domain', 'sync-test.myshopify.com')
        .send(orderUpdateData);

      // Then send create webhook
      const createHmac = crypto
        .createHmac('sha256', 'sync-webhook-secret')
        .update(JSON.stringify(orderCreateData))
        .digest('base64');

      await request(app)
        .post('/api/webhooks/shopify/orders/create')
        .set('X-Shopify-Hmac-Sha256', createHmac)
        .set('X-Shopify-Shop-Domain', 'sync-test.myshopify.com')
        .send(orderCreateData);

      // Verify final order state reflects the latest update
      const order = await pool.query(
        'SELECT * FROM orders WHERE platform_order_id = $1',
        ['order-sequence-test']
      );

      expect(order.rows).toHaveLength(1);
      expect(order.rows[0].status).toBe('fulfilled');
      expect(order.rows[0].customer_email).toBe('<EMAIL>');
    });
  });

  describe('Data Consistency and Integrity', () => {
    it('should maintain data consistency during concurrent webhook processing', async () => {
      const baseOrderData = {
        id: 'concurrent-test-order',
        order_number: 4001,
        email: '<EMAIL>',
        total_price: '299.99',
        created_at: '2024-01-15T10:00:00Z'
      };

      // Create multiple concurrent webhooks with slight variations
      const webhookPromises = Array.from({ length: 5 }, (_, i) => {
        const orderData = {
          ...baseOrderData,
          updated_at: new Date(Date.now() + i * 1000).toISOString()
        };

        const hmac = crypto
          .createHmac('sha256', 'sync-webhook-secret')
          .update(JSON.stringify(orderData))
          .digest('base64');

        return request(app)
          .post('/api/webhooks/shopify/orders/create')
          .set('X-Shopify-Hmac-Sha256', hmac)
          .set('X-Shopify-Shop-Domain', 'sync-test.myshopify.com')
          .set('X-Shopify-Webhook-Id', `concurrent-webhook-${i}`)
          .send(orderData);
      });

      const responses = await Promise.all(webhookPromises);

      // All webhooks should return success
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // Verify only one order was created
      const orders = await pool.query(
        'SELECT * FROM orders WHERE platform_order_id = $1',
        ['concurrent-test-order']
      );

      expect(orders.rows).toHaveLength(1);

      // Verify all webhook attempts were logged
      const webhookLogs = await pool.query(
        'SELECT * FROM webhook_logs WHERE integration_id = $1 AND platform_order_id = $2',
        [shopifyIntegration.id, 'concurrent-test-order']
      );

      expect(webhookLogs.rows).toHaveLength(5);
    });

    it('should handle partial data updates correctly', async () => {
      // Create initial order
      await pool.query(
        `INSERT INTO orders (id, integration_id, platform_order_id, customer_email, total_amount, status, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
        ['partial-update-order', shopifyIntegration.id, 'partial-test-order', '<EMAIL>', 99.99, 'pending']
      );

      // Send partial update webhook
      const partialUpdateData = {
        id: 'partial-test-order',
        financial_status: 'paid',
        // Note: missing other fields that should be preserved
        updated_at: '2024-01-15T12:00:00Z'
      };

      const hmac = crypto
        .createHmac('sha256', 'sync-webhook-secret')
        .update(JSON.stringify(partialUpdateData))
        .digest('base64');

      await request(app)
        .post('/api/webhooks/shopify/orders/update')
        .set('X-Shopify-Hmac-Sha256', hmac)
        .set('X-Shopify-Shop-Domain', 'sync-test.myshopify.com')
        .send(partialUpdateData);

      // Verify original data was preserved
      const order = await pool.query(
        'SELECT * FROM orders WHERE platform_order_id = $1',
        ['partial-test-order']
      );

      expect(order.rows).toHaveLength(1);
      expect(order.rows[0].customer_email).toBe('<EMAIL>'); // Preserved
      expect(parseFloat(order.rows[0].total_amount)).toBe(99.99); // Preserved
      expect(order.rows[0].status).toBe('paid'); // Updated
    });
  });

  describe('Scheduled Synchronization', () => {
    it('should perform incremental sync correctly', async () => {
      // Mock Shopify API responses for incremental sync
      nock('https://sync-test.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .query(query => {
          return query.updated_at_min && query.limit;
        })
        .reply(200, {
          orders: [
            {
              id: 'sync-order-1',
              order_number: 5001,
              email: '<EMAIL>',
              total_price: '149.99',
              created_at: '2024-01-15T10:00:00Z',
              updated_at: '2024-01-15T10:00:00Z'
            },
            {
              id: 'sync-order-2',
              order_number: 5002,
              email: '<EMAIL>',
              total_price: '249.99',
              created_at: '2024-01-15T11:00:00Z',
              updated_at: '2024-01-15T11:00:00Z'
            }
          ]
        });

      const result = await syncManager.performIncrementalSync(shopifyIntegration);

      expect(result.success).toBe(true);
      expect(result.ordersProcessed).toBe(2);

      // Verify orders were stored
      const orders = await pool.query(
        'SELECT * FROM orders WHERE integration_id = $1 AND platform_order_id IN ($2, $3)',
        [shopifyIntegration.id, 'sync-order-1', 'sync-order-2']
      );

      expect(orders.rows).toHaveLength(2);

      // Verify last sync timestamp was updated
      const integration = await pool.query(
        'SELECT last_sync_at FROM integrations WHERE id = $1',
        [shopifyIntegration.id]
      );

      expect(integration.rows[0].last_sync_at).toBeTruthy();
    });

    it('should handle sync failures and implement retry logic', async () => {
      // Mock API failure
      nock('https://sync-test.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .query(true)
        .reply(500, 'Internal Server Error');

      // Mock successful retry
      nock('https://sync-test.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .query(true)
        .reply(200, { orders: [] });

      const result = await syncManager.performIncrementalSync(shopifyIntegration);

      expect(result.success).toBe(true);
      expect(result.retryCount).toBeGreaterThan(0);
    });

    it('should detect and resolve data conflicts during sync', async () => {
      // Create order with webhook data
      await pool.query(
        `INSERT INTO orders (id, integration_id, platform_order_id, customer_email, total_amount, status, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW(), $7)`,
        ['conflict-order', shopifyIntegration.id, 'conflict-test-order', '<EMAIL>', 99.99, 'pending', '2024-01-15T10:00:00Z']
      );

      // Mock API returning different data for same order
      nock('https://sync-test.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .query(true)
        .reply(200, {
          orders: [
            {
              id: 'conflict-test-order',
              order_number: 6001,
              email: '<EMAIL>', // Different email
              total_price: '199.99', // Different amount
              financial_status: 'paid', // Different status
              created_at: '2024-01-15T10:00:00Z',
              updated_at: '2024-01-15T12:00:00Z' // Later timestamp
            }
          ]
        });

      const result = await syncManager.performIncrementalSync(shopifyIntegration);

      expect(result.success).toBe(true);
      expect(result.conflictsResolved).toBe(1);

      // Verify API data took precedence (newer timestamp)
      const order = await pool.query(
        'SELECT * FROM orders WHERE platform_order_id = $1',
        ['conflict-test-order']
      );

      expect(order.rows[0].customer_email).toBe('<EMAIL>');
      expect(parseFloat(order.rows[0].total_amount)).toBe(199.99);
      expect(order.rows[0].status).toBe('paid');
    });
  });

  describe('Cross-Platform Data Synchronization', () => {
    it('should maintain data consistency across multiple platforms', async () => {
      // Create orders from different platforms for same customer
      const customerEmail = '<EMAIL>';

      // Shopify order
      const shopifyOrderData = {
        id: 'shopify-multi-order',
        order_number: 7001,
        email: customerEmail,
        total_price: '99.99',
        created_at: '2024-01-15T10:00:00Z'
      };

      const shopifyHmac = crypto
        .createHmac('sha256', 'sync-webhook-secret')
        .update(JSON.stringify(shopifyOrderData))
        .digest('base64');

      await request(app)
        .post('/api/webhooks/shopify/orders/create')
        .set('X-Shopify-Hmac-Sha256', shopifyHmac)
        .set('X-Shopify-Shop-Domain', 'sync-test.myshopify.com')
        .send(shopifyOrderData);

      // WooCommerce order
      const wooOrderData = {
        id: 'woo-multi-order',
        status: 'processing',
        total: '149.99',
        currency: 'USD',
        billing: {
          email: customerEmail,
          first_name: 'Multi',
          last_name: 'Platform'
        },
        date_created: '2024-01-15T11:00:00'
      };

      const wooSignature = crypto
        .createHmac('sha256', 'sync-woo-secret')
        .update(JSON.stringify(wooOrderData))
        .digest('base64');

      await request(app)
        .post('/api/webhooks/woocommerce/orders/create')
        .set('X-WC-Webhook-Signature', wooSignature)
        .set('X-WC-Webhook-Source', 'https://sync-test.com')
        .send(wooOrderData);

      // Verify both orders exist for same customer
      const orders = await pool.query(
        'SELECT * FROM orders WHERE customer_email = $1 ORDER BY created_at',
        [customerEmail]
      );

      expect(orders.rows).toHaveLength(2);
      expect(orders.rows[0].platform_order_id).toBe('shopify-multi-order');
      expect(orders.rows[1].platform_order_id).toBe('woo-multi-order');

      // Verify customer journey tracking
      const customerJourney = await pool.query(
        `SELECT o.platform_order_id, i.platform, o.total_amount, o.created_at
         FROM orders o
         JOIN integrations i ON o.integration_id = i.id
         WHERE o.customer_email = $1
         ORDER BY o.created_at`,
        [customerEmail]
      );

      expect(customerJourney.rows).toHaveLength(2);
      expect(customerJourney.rows[0].platform).toBe('shopify');
      expect(customerJourney.rows[1].platform).toBe('woocommerce');
    });
  });
});
