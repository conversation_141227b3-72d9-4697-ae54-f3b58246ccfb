const request = require('supertest');
const nock = require('nock');
const crypto = require('crypto');
const app = require('../../services/integration/src/app');
const { pool } = require('../../services/integration/src/database');

describe('E-commerce Platform Integration Tests', () => {
  let testTenant;
  let testIntegration;

  beforeAll(async () => {
    // Setup test tenant and integration
    const tenantResult = await pool.query(
      'INSERT INTO tenants (id, name, created_at) VALUES ($1, $2, NOW()) RETURNING *',
      ['test-tenant-1', 'Test Tenant']
    );
    testTenant = tenantResult.rows[0];

    const integrationResult = await pool.query(
      `INSERT INTO integrations (id, tenant_id, platform, store_url, api_credentials, webhook_secret, is_active) 
       VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [
        'test-integration-1',
        testTenant.id,
        'shopify',
        'test-store.myshopify.com',
        JSON.stringify({ access_token: 'test-token' }),
        'test-webhook-secret',
        true
      ]
    );
    testIntegration = integrationResult.rows[0];
  });

  afterAll(async () => {
    // Cleanup test data
    await pool.query('DELETE FROM integrations WHERE tenant_id = $1', [testTenant.id]);
    await pool.query('DELETE FROM tenants WHERE id = $1', [testTenant.id]);
    await pool.end();
  });

  beforeEach(() => {
    // Clear all nock interceptors before each test
    nock.cleanAll();
  });

  describe('Shopify Integration', () => {
    describe('Webhook Handling', () => {
      it('should process Shopify order creation webhook', async () => {
        const orderData = {
          id: 12345,
          order_number: 1001,
          email: '<EMAIL>',
          total_price: '99.99',
          currency: 'USD',
          line_items: [
            {
              id: 1,
              product_id: 123,
              variant_id: 456,
              title: 'Test Product',
              quantity: 1,
              price: '99.99'
            }
          ],
          customer: {
            id: 789,
            email: '<EMAIL>',
            first_name: 'John',
            last_name: 'Doe'
          },
          created_at: '2024-01-15T10:00:00Z'
        };

        const hmac = crypto
          .createHmac('sha256', 'test-webhook-secret')
          .update(JSON.stringify(orderData))
          .digest('base64');

        const response = await request(app)
          .post('/api/webhooks/shopify/orders/create')
          .set('X-Shopify-Hmac-Sha256', hmac)
          .set('X-Shopify-Shop-Domain', 'test-store.myshopify.com')
          .send(orderData);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);

        // Verify order was stored in database
        const orderResult = await pool.query(
          'SELECT * FROM orders WHERE platform_order_id = $1 AND integration_id = $2',
          [orderData.id.toString(), testIntegration.id]
        );
        
        expect(orderResult.rows).toHaveLength(1);
        expect(orderResult.rows[0].customer_email).toBe(orderData.email);
        expect(parseFloat(orderResult.rows[0].total_amount)).toBe(99.99);
      });

      it('should reject webhook with invalid signature', async () => {
        const orderData = { id: 12345, total_price: '99.99' };

        const response = await request(app)
          .post('/api/webhooks/shopify/orders/create')
          .set('X-Shopify-Hmac-Sha256', 'invalid-signature')
          .set('X-Shopify-Shop-Domain', 'test-store.myshopify.com')
          .send(orderData);

        expect(response.status).toBe(401);
        expect(response.body.error).toContain('signature verification failed');
      });

      it('should handle Shopify order update webhook', async () => {
        // First create an order
        await pool.query(
          `INSERT INTO orders (id, integration_id, platform_order_id, customer_email, total_amount, status, created_at)
           VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
          ['order-1', testIntegration.id, '12345', '<EMAIL>', 99.99, 'pending']
        );

        const updateData = {
          id: 12345,
          financial_status: 'paid',
          fulfillment_status: 'fulfilled',
          updated_at: '2024-01-15T12:00:00Z'
        };

        const hmac = crypto
          .createHmac('sha256', 'test-webhook-secret')
          .update(JSON.stringify(updateData))
          .digest('base64');

        const response = await request(app)
          .post('/api/webhooks/shopify/orders/update')
          .set('X-Shopify-Hmac-Sha256', hmac)
          .set('X-Shopify-Shop-Domain', 'test-store.myshopify.com')
          .send(updateData);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);

        // Verify order was updated
        const orderResult = await pool.query(
          'SELECT * FROM orders WHERE platform_order_id = $1',
          ['12345']
        );
        
        expect(orderResult.rows[0].status).toBe('fulfilled');
      });
    });

    describe('API Client', () => {
      it('should handle rate limiting correctly', async () => {
        const ShopifyClient = require('../../services/integration/src/platforms/shopify/client');
        
        // Mock rate limited response
        nock('https://test-store.myshopify.com')
          .get('/admin/api/2023-10/orders.json')
          .reply(429, 'Too Many Requests', {
            'Retry-After': '2',
            'X-Shopify-Shop-Api-Call-Limit': '40/40'
          });

        // Mock successful retry
        nock('https://test-store.myshopify.com')
          .get('/admin/api/2023-10/orders.json')
          .reply(200, { orders: [] }, {
            'X-Shopify-Shop-Api-Call-Limit': '1/40'
          });

        const client = new ShopifyClient('test-store', 'test-token');
        
        const startTime = Date.now();
        const result = await client.getOrders();
        const endTime = Date.now();

        expect(result.orders).toEqual([]);
        expect(endTime - startTime).toBeGreaterThan(2000); // Should wait for retry
      });

      it('should authenticate requests correctly', async () => {
        const ShopifyClient = require('../../services/integration/src/platforms/shopify/client');
        
        nock('https://test-store.myshopify.com')
          .get('/admin/api/2023-10/orders.json')
          .matchHeader('X-Shopify-Access-Token', 'test-token')
          .reply(200, { orders: [] });

        const client = new ShopifyClient('test-store', 'test-token');
        const result = await client.getOrders();

        expect(result.orders).toEqual([]);
      });
    });
  });

  describe('WooCommerce Integration', () => {
    beforeEach(async () => {
      // Create WooCommerce integration
      await pool.query(
        `INSERT INTO integrations (id, tenant_id, platform, store_url, api_credentials, webhook_secret, is_active) 
         VALUES ($1, $2, $3, $4, $5, $6, $7) ON CONFLICT (id) DO UPDATE SET
         platform = EXCLUDED.platform, store_url = EXCLUDED.store_url`,
        [
          'test-woo-integration',
          testTenant.id,
          'woocommerce',
          'https://test-store.com',
          JSON.stringify({ consumer_key: 'ck_test', consumer_secret: 'cs_test' }),
          'woo-webhook-secret',
          true
        ]
      );
    });

    describe('Webhook Handling', () => {
      it('should process WooCommerce order creation webhook', async () => {
        const orderData = {
          id: 789,
          status: 'processing',
          total: '149.99',
          currency: 'USD',
          billing: {
            email: '<EMAIL>',
            first_name: 'Jane',
            last_name: 'Smith'
          },
          line_items: [
            {
              id: 1,
              product_id: 456,
              name: 'WooCommerce Product',
              quantity: 2,
              total: '149.99'
            }
          ],
          date_created: '2024-01-15T10:00:00'
        };

        const signature = crypto
          .createHmac('sha256', 'woo-webhook-secret')
          .update(JSON.stringify(orderData))
          .digest('base64');

        const response = await request(app)
          .post('/api/webhooks/woocommerce/orders/create')
          .set('X-WC-Webhook-Signature', signature)
          .set('X-WC-Webhook-Source', 'https://test-store.com')
          .send(orderData);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);

        // Verify order was stored
        const orderResult = await pool.query(
          'SELECT * FROM orders WHERE platform_order_id = $1',
          [orderData.id.toString()]
        );
        
        expect(orderResult.rows).toHaveLength(1);
        expect(orderResult.rows[0].customer_email).toBe(orderData.billing.email);
      });

      it('should handle WooCommerce authentication correctly', async () => {
        const WooCommerceClient = require('../../services/integration/src/platforms/woocommerce/client');
        
        nock('https://test-store.com')
          .get('/wp-json/wc/v3/orders')
          .query(true) // Match any query parameters
          .basicAuth({ user: 'ck_test', pass: 'cs_test' })
          .reply(200, []);

        const client = new WooCommerceClient('https://test-store.com', 'ck_test', 'cs_test');
        const result = await client.getOrders();

        expect(result.orders).toEqual([]);
      });
    });
  });

  describe('eBay Integration', () => {
    beforeEach(async () => {
      // Create eBay integration
      await pool.query(
        `INSERT INTO integrations (id, tenant_id, platform, store_url, api_credentials, is_active) 
         VALUES ($1, $2, $3, $4, $5, $6) ON CONFLICT (id) DO UPDATE SET
         platform = EXCLUDED.platform`,
        [
          'test-ebay-integration',
          testTenant.id,
          'ebay',
          'ebay.com',
          JSON.stringify({ 
            client_id: 'ebay_client_id',
            client_secret: 'ebay_client_secret',
            refresh_token: 'ebay_refresh_token'
          }),
          true
        ]
      );
    });

    describe('Authentication', () => {
      it('should refresh access token when expired', async () => {
        const EbayClient = require('../../services/integration/src/platforms/ebay/client');
        
        // Mock token refresh
        nock('https://api.sandbox.ebay.com')
          .post('/identity/v1/oauth2/token')
          .reply(200, {
            access_token: 'new_access_token',
            expires_in: 7200
          });

        // Mock API call with new token
        nock('https://api.sandbox.ebay.com')
          .get('/sell/analytics/v1/seller_standards_profile')
          .matchHeader('Authorization', 'Bearer new_access_token')
          .reply(200, { profile: 'test' });

        const client = new EbayClient({
          clientId: 'ebay_client_id',
          clientSecret: 'ebay_client_secret',
          refreshToken: 'ebay_refresh_token',
          sandbox: true
        });

        const result = await client.testConnection();
        expect(result.success).toBe(true);
      });

      it('should handle authentication errors gracefully', async () => {
        const EbayClient = require('../../services/integration/src/platforms/ebay/client');
        
        // Mock failed token refresh
        nock('https://api.sandbox.ebay.com')
          .post('/identity/v1/oauth2/token')
          .reply(401, { error: 'invalid_grant' });

        const client = new EbayClient({
          clientId: 'invalid_client',
          clientSecret: 'invalid_secret',
          refreshToken: 'invalid_token',
          sandbox: true
        });

        await expect(client.refreshAccessToken()).rejects.toThrow('eBay authentication failed');
      });
    });

    describe('Data Synchronization', () => {
      it('should sync orders from eBay API', async () => {
        const EbayClient = require('../../services/integration/src/platforms/ebay/client');
        
        // Mock token refresh
        nock('https://api.sandbox.ebay.com')
          .post('/identity/v1/oauth2/token')
          .reply(200, {
            access_token: 'test_token',
            expires_in: 7200
          });

        // Mock orders API
        nock('https://api.sandbox.ebay.com')
          .get('/sell/fulfillment/v1/order')
          .query(true)
          .reply(200, {
            orders: [
              {
                orderId: 'ebay-order-123',
                orderFulfillmentStatus: 'FULFILLED',
                totalCostSummary: {
                  value: '75.50',
                  currency: 'USD'
                },
                buyer: {
                  username: 'ebay_buyer'
                },
                creationDate: '2024-01-15T10:00:00.000Z'
              }
            ]
          });

        const client = new EbayClient({
          clientId: 'ebay_client_id',
          clientSecret: 'ebay_client_secret',
          refreshToken: 'ebay_refresh_token',
          sandbox: true
        });

        const orders = await client.getOrders();
        expect(orders).toHaveLength(1);
        expect(orders[0].orderId).toBe('ebay-order-123');
      });
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle network timeouts gracefully', async () => {
      const ShopifyClient = require('../../services/integration/src/platforms/shopify/client');
      
      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .delay(35000) // Longer than timeout
        .reply(200, { orders: [] });

      const client = new ShopifyClient('test-store', 'test-token');
      
      await expect(client.getOrders()).rejects.toThrow('timeout');
    });

    it('should retry failed requests with exponential backoff', async () => {
      const WooCommerceClient = require('../../services/integration/src/platforms/woocommerce/client');
      
      // Mock failed requests followed by success
      nock('https://test-store.com')
        .get('/wp-json/wc/v3/orders')
        .query(true)
        .reply(500, 'Internal Server Error');
      
      nock('https://test-store.com')
        .get('/wp-json/wc/v3/orders')
        .query(true)
        .reply(500, 'Internal Server Error');
      
      nock('https://test-store.com')
        .get('/wp-json/wc/v3/orders')
        .query(true)
        .reply(200, []);

      const client = new WooCommerceClient('https://test-store.com', 'ck_test', 'cs_test');
      
      const startTime = Date.now();
      const result = await client.getOrders();
      const endTime = Date.now();

      expect(result.orders).toEqual([]);
      expect(endTime - startTime).toBeGreaterThan(1000); // Should have retry delays
    });

    it('should handle malformed webhook data', async () => {
      const malformedData = {
        // Missing required fields
        incomplete: 'data'
      };

      const hmac = crypto
        .createHmac('sha256', 'test-webhook-secret')
        .update(JSON.stringify(malformedData))
        .digest('base64');

      const response = await request(app)
        .post('/api/webhooks/shopify/orders/create')
        .set('X-Shopify-Hmac-Sha256', hmac)
        .set('X-Shopify-Shop-Domain', 'test-store.myshopify.com')
        .send(malformedData);

      // Should still return 200 to prevent webhook retries
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe('Multi-tenant Isolation', () => {
    it('should ensure webhook data is isolated by tenant', async () => {
      // Create second tenant and integration
      const tenant2Result = await pool.query(
        'INSERT INTO tenants (id, name, created_at) VALUES ($1, $2, NOW()) RETURNING *',
        ['test-tenant-2', 'Test Tenant 2']
      );
      const tenant2 = tenant2Result.rows[0];

      await pool.query(
        `INSERT INTO integrations (id, tenant_id, platform, store_url, api_credentials, webhook_secret, is_active) 
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          'test-integration-2',
          tenant2.id,
          'shopify',
          'tenant2-store.myshopify.com',
          JSON.stringify({ access_token: 'tenant2-token' }),
          'tenant2-webhook-secret',
          true
        ]
      );

      const orderData = {
        id: 99999,
        order_number: 2001,
        email: '<EMAIL>',
        total_price: '199.99'
      };

      const hmac = crypto
        .createHmac('sha256', 'tenant2-webhook-secret')
        .update(JSON.stringify(orderData))
        .digest('base64');

      await request(app)
        .post('/api/webhooks/shopify/orders/create')
        .set('X-Shopify-Hmac-Sha256', hmac)
        .set('X-Shopify-Shop-Domain', 'tenant2-store.myshopify.com')
        .send(orderData);

      // Verify order is associated with correct tenant
      const orderResult = await pool.query(
        `SELECT o.*, i.tenant_id FROM orders o 
         JOIN integrations i ON o.integration_id = i.id 
         WHERE o.platform_order_id = $1`,
        ['99999']
      );
      
      expect(orderResult.rows).toHaveLength(1);
      expect(orderResult.rows[0].tenant_id).toBe(tenant2.id);

      // Cleanup
      await pool.query('DELETE FROM integrations WHERE tenant_id = $1', [tenant2.id]);
      await pool.query('DELETE FROM tenants WHERE id = $1', [tenant2.id]);
    });
  });
});
