const nock = require('nock');
const { RateLimiter } = require('../../services/integration/src/utils/rateLimiter');
const ShopifyClient = require('../../services/integration/src/platforms/shopify/client');
const WooCommerceClient = require('../../services/integration/src/platforms/woocommerce/client');
const EbayClient = require('../../services/integration/src/platforms/ebay/client');

describe('Rate Limiting and API Quota Management', () => {
  beforeEach(() => {
    nock.cleanAll();
  });

  afterEach(() => {
    nock.cleanAll();
  });

  describe('RateLimiter Utility', () => {
    it('should enforce rate limits correctly', async () => {
      const limiter = new RateLimiter({
        requestsPerSecond: 2,
        maxConcurrent: 1
      });

      const startTime = Date.now();
      
      // Make 3 requests - should be rate limited
      const promises = [
        limiter.execute(() => Promise.resolve('request1')),
        limiter.execute(() => Promise.resolve('request2')),
        limiter.execute(() => Promise.resolve('request3'))
      ];

      const results = await Promise.all(promises);
      const endTime = Date.now();

      expect(results).toEqual(['request1', 'request2', 'request3']);
      expect(endTime - startTime).toBeGreaterThan(1000); // Should take at least 1 second due to rate limiting
    });

    it('should handle concurrent request limits', async () => {
      const limiter = new RateLimiter({
        requestsPerSecond: 10,
        maxConcurrent: 2
      });

      let concurrentCount = 0;
      let maxConcurrent = 0;

      const slowRequest = () => {
        return new Promise((resolve) => {
          concurrentCount++;
          maxConcurrent = Math.max(maxConcurrent, concurrentCount);
          
          setTimeout(() => {
            concurrentCount--;
            resolve('done');
          }, 100);
        });
      };

      const promises = Array.from({ length: 5 }, () => limiter.execute(slowRequest));
      await Promise.all(promises);

      expect(maxConcurrent).toBeLessThanOrEqual(2);
    });

    it('should handle burst allowances', async () => {
      const limiter = new RateLimiter({
        requestsPerSecond: 1,
        maxConcurrent: 5,
        burstAllowance: 3
      });

      const startTime = Date.now();
      
      // Make 3 burst requests - should execute immediately
      const burstPromises = [
        limiter.execute(() => Promise.resolve('burst1')),
        limiter.execute(() => Promise.resolve('burst2')),
        limiter.execute(() => Promise.resolve('burst3'))
      ];

      const burstResults = await Promise.all(burstPromises);
      const burstTime = Date.now() - startTime;

      expect(burstResults).toEqual(['burst1', 'burst2', 'burst3']);
      expect(burstTime).toBeLessThan(500); // Burst should be fast

      // Next request should be rate limited
      const startRateLimited = Date.now();
      await limiter.execute(() => Promise.resolve('rate-limited'));
      const rateLimitedTime = Date.now() - startRateLimited;

      expect(rateLimitedTime).toBeGreaterThan(900); // Should wait ~1 second
    });
  });

  describe('Shopify Rate Limiting', () => {
    it('should respect Shopify API call limits', async () => {
      const client = new ShopifyClient('test-store', 'test-token');

      // Mock responses with rate limit headers
      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(200, { orders: [] }, {
          'X-Shopify-Shop-Api-Call-Limit': '38/40'
        });

      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/products.json')
        .reply(200, { products: [] }, {
          'X-Shopify-Shop-Api-Call-Limit': '39/40'
        });

      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/customers.json')
        .reply(200, { customers: [] }, {
          'X-Shopify-Shop-Api-Call-Limit': '40/40'
        });

      // This should trigger rate limiting
      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .query({ page: 2 })
        .delay(2000) // Simulate waiting for rate limit reset
        .reply(200, { orders: [] }, {
          'X-Shopify-Shop-Api-Call-Limit': '1/40'
        });

      await client.getOrders();
      await client.getProducts();
      await client.getCustomers();

      const startTime = Date.now();
      await client.getOrders({ page: 2 });
      const endTime = Date.now();

      expect(endTime - startTime).toBeGreaterThan(1500); // Should have waited
    });

    it('should handle 429 rate limit responses', async () => {
      const client = new ShopifyClient('test-store', 'test-token');

      // Mock 429 response
      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(429, 'Too Many Requests', {
          'Retry-After': '2',
          'X-Shopify-Shop-Api-Call-Limit': '40/40'
        });

      // Mock successful retry
      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(200, { orders: [] }, {
          'X-Shopify-Shop-Api-Call-Limit': '1/40'
        });

      const startTime = Date.now();
      const result = await client.getOrders();
      const endTime = Date.now();

      expect(result.orders).toEqual([]);
      expect(endTime - startTime).toBeGreaterThan(2000); // Should have waited 2 seconds
    });

    it('should implement exponential backoff for repeated rate limits', async () => {
      const client = new ShopifyClient('test-store', 'test-token');

      // Mock multiple 429 responses
      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(429, 'Too Many Requests', { 'Retry-After': '1' });

      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(429, 'Too Many Requests', { 'Retry-After': '2' });

      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(200, { orders: [] });

      const startTime = Date.now();
      const result = await client.getOrders();
      const endTime = Date.now();

      expect(result.orders).toEqual([]);
      expect(endTime - startTime).toBeGreaterThan(3000); // Should have waited 1s + 2s
    });
  });

  describe('WooCommerce Rate Limiting', () => {
    it('should handle WooCommerce rate limits gracefully', async () => {
      const client = new WooCommerceClient('https://test-store.com', 'ck_test', 'cs_test');

      // WooCommerce typically returns 429 without specific headers
      nock('https://test-store.com')
        .get('/wp-json/wc/v3/orders')
        .query(true)
        .reply(429, 'Too Many Requests');

      nock('https://test-store.com')
        .get('/wp-json/wc/v3/orders')
        .query(true)
        .reply(200, []);

      const startTime = Date.now();
      const result = await client.getOrders();
      const endTime = Date.now();

      expect(result.orders).toEqual([]);
      expect(endTime - startTime).toBeGreaterThan(1000); // Should have default retry delay
    });

    it('should respect WooCommerce concurrent request limits', async () => {
      const client = new WooCommerceClient('https://test-store.com', 'ck_test', 'cs_test');

      // Mock multiple concurrent requests
      const mockRequests = Array.from({ length: 10 }, (_, i) => {
        return nock('https://test-store.com')
          .get('/wp-json/wc/v3/orders')
          .query({ page: i + 1 })
          .reply(200, []);
      });

      const promises = Array.from({ length: 10 }, (_, i) => 
        client.getOrders({ page: i + 1 })
      );

      const startTime = Date.now();
      const results = await Promise.all(promises);
      const endTime = Date.now();

      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.orders).toEqual([]);
      });

      // Should have some delay due to concurrent limits
      expect(endTime - startTime).toBeGreaterThan(500);
    });
  });

  describe('eBay Rate Limiting', () => {
    it('should handle eBay daily quota limits', async () => {
      const client = new EbayClient({
        clientId: 'test_client',
        clientSecret: 'test_secret',
        refreshToken: 'test_refresh',
        sandbox: true
      });

      // Mock token refresh
      nock('https://api.sandbox.ebay.com')
        .post('/identity/v1/oauth2/token')
        .reply(200, {
          access_token: 'test_token',
          expires_in: 7200
        });

      // Mock quota exceeded response
      nock('https://api.sandbox.ebay.com')
        .get('/sell/fulfillment/v1/order')
        .query(true)
        .reply(429, {
          errors: [{
            errorId: 1001,
            domain: 'API_ANALYTICS',
            category: 'REQUEST',
            message: 'The call limit for this resource has been reached for today.'
          }]
        });

      await expect(client.getOrders()).rejects.toThrow('call limit');
    });

    it('should handle eBay requests per second limits', async () => {
      const client = new EbayClient({
        clientId: 'test_client',
        clientSecret: 'test_secret',
        refreshToken: 'test_refresh',
        sandbox: true
      });

      // Mock token refresh
      nock('https://api.sandbox.ebay.com')
        .post('/identity/v1/oauth2/token')
        .reply(200, {
          access_token: 'test_token',
          expires_in: 7200
        });

      // Mock multiple requests with rate limiting
      const mockRequests = Array.from({ length: 6 }, () => {
        return nock('https://api.sandbox.ebay.com')
          .get('/sell/fulfillment/v1/order')
          .query(true)
          .reply(200, { orders: [] });
      });

      const promises = Array.from({ length: 6 }, () => client.getOrders());

      const startTime = Date.now();
      const results = await Promise.all(promises);
      const endTime = Date.now();

      expect(results).toHaveLength(6);
      // Should take at least 1 second due to 5 requests/second limit
      expect(endTime - startTime).toBeGreaterThan(1000);
    });
  });

  describe('Cross-Platform Rate Limiting', () => {
    it('should handle rate limits across multiple platforms simultaneously', async () => {
      const shopifyClient = new ShopifyClient('test-store', 'test-token');
      const wooClient = new WooCommerceClient('https://test-store.com', 'ck_test', 'cs_test');

      // Mock Shopify rate limit
      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(429, 'Too Many Requests', { 'Retry-After': '2' });

      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(200, { orders: [] });

      // Mock WooCommerce success
      nock('https://test-store.com')
        .get('/wp-json/wc/v3/orders')
        .query(true)
        .reply(200, []);

      const startTime = Date.now();
      const [shopifyResult, wooResult] = await Promise.all([
        shopifyClient.getOrders(),
        wooClient.getOrders()
      ]);
      const endTime = Date.now();

      expect(shopifyResult.orders).toEqual([]);
      expect(wooResult.orders).toEqual([]);
      
      // Shopify should have been delayed, but WooCommerce should be fast
      expect(endTime - startTime).toBeGreaterThan(2000);
      expect(endTime - startTime).toBeLessThan(3000);
    });

    it('should track API usage across platforms', async () => {
      const { ApiUsageTracker } = require('../../services/integration/src/utils/apiUsageTracker');
      const tracker = new ApiUsageTracker();

      // Simulate API calls
      tracker.recordCall('shopify', 'orders', 'success', 150);
      tracker.recordCall('shopify', 'products', 'success', 200);
      tracker.recordCall('woocommerce', 'orders', 'success', 300);
      tracker.recordCall('ebay', 'orders', 'error', 500);

      const usage = tracker.getUsageStats();

      expect(usage.shopify.totalCalls).toBe(2);
      expect(usage.shopify.successRate).toBe(100);
      expect(usage.shopify.avgResponseTime).toBe(175);

      expect(usage.woocommerce.totalCalls).toBe(1);
      expect(usage.woocommerce.successRate).toBe(100);

      expect(usage.ebay.totalCalls).toBe(1);
      expect(usage.ebay.successRate).toBe(0);
    });
  });

  describe('Rate Limit Recovery', () => {
    it('should implement circuit breaker pattern for repeated failures', async () => {
      const { CircuitBreaker } = require('../../services/integration/src/utils/circuitBreaker');
      
      const breaker = new CircuitBreaker({
        failureThreshold: 3,
        resetTimeout: 5000
      });

      let callCount = 0;
      const failingFunction = () => {
        callCount++;
        return Promise.reject(new Error('API Error'));
      };

      // First 3 calls should fail and open the circuit
      await expect(breaker.execute(failingFunction)).rejects.toThrow('API Error');
      await expect(breaker.execute(failingFunction)).rejects.toThrow('API Error');
      await expect(breaker.execute(failingFunction)).rejects.toThrow('API Error');

      expect(breaker.state).toBe('OPEN');

      // Next call should fail fast without calling the function
      await expect(breaker.execute(failingFunction)).rejects.toThrow('Circuit breaker is OPEN');
      expect(callCount).toBe(3); // Function should not have been called again
    });

    it('should recover from rate limit errors automatically', async () => {
      const client = new ShopifyClient('test-store', 'test-token');

      // Mock initial rate limit error
      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(429, 'Too Many Requests', { 'Retry-After': '1' });

      // Mock successful recovery
      nock('https://test-store.myshopify.com')
        .get('/admin/api/2023-10/orders.json')
        .reply(200, { orders: [{ id: 1 }] });

      const result = await client.getOrders();

      expect(result.orders).toHaveLength(1);
      expect(result.orders[0].id).toBe(1);
    });
  });
});
