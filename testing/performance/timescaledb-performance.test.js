const { Pool } = require('pg');
const { performance } = require('perf_hooks');

describe('TimescaleDB Performance Tests', () => {
  let pool;
  let testTenant;

  beforeAll(async () => {
    // Setup database connection
    pool = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'ecommerce_analytics_test',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    // Create test tenant
    const tenantResult = await pool.query(
      'INSERT INTO tenants (id, name, created_at) VALUES ($1, $2, NOW()) RETURNING *',
      ['perf-test-tenant', 'Performance Test Tenant']
    );
    testTenant = tenantResult.rows[0];

    // Setup TimescaleDB hypertables if not exists
    await setupTimescaleDB();
    
    // Generate test data
    await generateTestData();
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    await pool.end();
  });

  async function setupTimescaleDB() {
    const client = await pool.connect();
    try {
      // Enable TimescaleDB extension
      await client.query('CREATE EXTENSION IF NOT EXISTS timescaledb');
      
      // Create hypertables for performance testing
      await client.query(`
        SELECT create_hypertable(
          'clicks', 
          'clicked_at',
          chunk_time_interval => INTERVAL '1 day',
          if_not_exists => TRUE
        )
      `);
      
      await client.query(`
        SELECT create_hypertable(
          'orders', 
          'created_at',
          chunk_time_interval => INTERVAL '1 day',
          if_not_exists => TRUE
        )
      `);

      await client.query(`
        SELECT create_hypertable(
          'attributions', 
          'created_at',
          chunk_time_interval => INTERVAL '1 day',
          if_not_exists => TRUE
        )
      `);

    } finally {
      client.release();
    }
  }

  async function generateTestData() {
    const client = await pool.connect();
    try {
      console.log('Generating test data for performance tests...');
      
      // Create test links
      await client.query(`
        INSERT INTO links (id, tenant_id, original_url, short_code, created_at)
        SELECT 
          'perf-link-' || generate_series,
          $1,
          'https://example.com/product/' || generate_series,
          'perf' || generate_series,
          NOW() - (generate_series || ' days')::INTERVAL
        FROM generate_series(1, 100)
      `, [testTenant.id]);

      // Generate large dataset of clicks (1 million records over 90 days)
      await client.query(`
        INSERT INTO clicks (id, tenant_id, link_id, ip_address, user_agent, country, device_type, clicked_at)
        SELECT 
          'perf-click-' || row_number() OVER (),
          $1,
          'perf-link-' || (1 + (random() * 99)::int),
          '192.168.' || (1 + (random() * 254)::int) || '.' || (1 + (random() * 254)::int),
          CASE (random() * 3)::int
            WHEN 0 THEN 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            WHEN 1 THEN 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            ELSE 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
          END,
          CASE (random() * 5)::int
            WHEN 0 THEN 'US'
            WHEN 1 THEN 'CA'
            WHEN 2 THEN 'GB'
            WHEN 3 THEN 'DE'
            ELSE 'FR'
          END,
          CASE (random() * 3)::int
            WHEN 0 THEN 'desktop'
            WHEN 1 THEN 'mobile'
            ELSE 'tablet'
          END,
          NOW() - (random() * 90 || ' days')::INTERVAL - (random() * 24 || ' hours')::INTERVAL
        FROM generate_series(1, 1000000)
      `, [testTenant.id]);

      // Generate orders (100k records)
      await client.query(`
        INSERT INTO orders (id, integration_id, platform_order_id, customer_email, total_amount, status, created_at)
        SELECT 
          'perf-order-' || generate_series,
          'perf-integration-1',
          'platform-order-' || generate_series,
          'customer' || generate_series || '@example.com',
          (random() * 500 + 10)::numeric(10,2),
          CASE (random() * 3)::int
            WHEN 0 THEN 'pending'
            WHEN 1 THEN 'completed'
            ELSE 'cancelled'
          END,
          NOW() - (random() * 90 || ' days')::INTERVAL
        FROM generate_series(1, 100000)
      `);

      // Generate attributions (50k records)
      await client.query(`
        INSERT INTO attributions (id, click_id, order_id, attribution_model, attribution_weight, created_at)
        SELECT 
          'perf-attr-' || generate_series,
          'perf-click-' || (1 + (random() * 999999)::int),
          'perf-order-' || (1 + (random() * 99999)::int),
          CASE (random() * 3)::int
            WHEN 0 THEN 'first_click'
            WHEN 1 THEN 'last_click'
            ELSE 'linear'
          END,
          random()::numeric(5,4),
          NOW() - (random() * 90 || ' days')::INTERVAL
        FROM generate_series(1, 50000)
      `);

      console.log('Test data generation completed');
      
    } finally {
      client.release();
    }
  }

  async function cleanupTestData() {
    const client = await pool.connect();
    try {
      await client.query('DELETE FROM attributions WHERE id LIKE $1', ['perf-attr-%']);
      await client.query('DELETE FROM orders WHERE id LIKE $1', ['perf-order-%']);
      await client.query('DELETE FROM clicks WHERE id LIKE $1', ['perf-click-%']);
      await client.query('DELETE FROM links WHERE id LIKE $1', ['perf-link-%']);
      await client.query('DELETE FROM tenants WHERE id = $1', [testTenant.id]);
    } finally {
      client.release();
    }
  }

  describe('Time-Series Query Performance', () => {
    it('should perform daily aggregation queries efficiently', async () => {
      const client = await pool.connect();
      try {
        const startTime = performance.now();
        
        const result = await client.query(`
          SELECT 
            DATE(clicked_at) as date,
            COUNT(*) as total_clicks,
            COUNT(DISTINCT ip_address) as unique_visitors,
            COUNT(DISTINCT country) as countries
          FROM clicks 
          WHERE tenant_id = $1 
            AND clicked_at >= NOW() - INTERVAL '30 days'
          GROUP BY DATE(clicked_at)
          ORDER BY date
        `, [testTenant.id]);
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        expect(result.rows.length).toBeGreaterThan(0);
        expect(executionTime).toBeLessThan(1000); // Should complete within 1 second
        
        console.log(`Daily aggregation query: ${executionTime.toFixed(2)}ms`);
        
      } finally {
        client.release();
      }
    });

    it('should perform hourly aggregation queries efficiently', async () => {
      const client = await pool.connect();
      try {
        const startTime = performance.now();
        
        const result = await client.query(`
          SELECT 
            DATE_TRUNC('hour', clicked_at) as hour,
            COUNT(*) as total_clicks,
            COUNT(DISTINCT ip_address) as unique_visitors,
            AVG(CASE WHEN device_type = 'mobile' THEN 1.0 ELSE 0.0 END) * 100 as mobile_percentage
          FROM clicks 
          WHERE tenant_id = $1 
            AND clicked_at >= NOW() - INTERVAL '7 days'
          GROUP BY DATE_TRUNC('hour', clicked_at)
          ORDER BY hour
        `, [testTenant.id]);
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        expect(result.rows.length).toBeGreaterThan(0);
        expect(executionTime).toBeLessThan(2000); // Should complete within 2 seconds
        
        console.log(`Hourly aggregation query: ${executionTime.toFixed(2)}ms`);
        
      } finally {
        client.release();
      }
    });

    it('should perform complex cohort analysis efficiently', async () => {
      const client = await pool.connect();
      try {
        const startTime = performance.now();
        
        const result = await client.query(`
          WITH customer_cohorts AS (
            SELECT 
              o.customer_email,
              DATE_TRUNC('month', MIN(o.created_at)) as cohort_month
            FROM orders o
            WHERE o.customer_email LIKE '<EMAIL>'
              AND o.created_at >= NOW() - INTERVAL '90 days'
            GROUP BY o.customer_email
          ),
          monthly_activity AS (
            SELECT 
              o.customer_email,
              DATE_TRUNC('month', o.created_at) as activity_month
            FROM orders o
            WHERE o.customer_email LIKE '<EMAIL>'
              AND o.created_at >= NOW() - INTERVAL '90 days'
            GROUP BY o.customer_email, DATE_TRUNC('month', o.created_at)
          )
          SELECT 
            cc.cohort_month,
            ma.activity_month,
            EXTRACT(month FROM AGE(ma.activity_month, cc.cohort_month)) as period_number,
            COUNT(DISTINCT cc.customer_email) as cohort_size,
            COUNT(DISTINCT ma.customer_email) as retained_customers,
            ROUND(
              (COUNT(DISTINCT ma.customer_email)::decimal / COUNT(DISTINCT cc.customer_email)::decimal) * 100, 
              2
            ) as retention_rate
          FROM customer_cohorts cc
          LEFT JOIN monthly_activity ma ON cc.customer_email = ma.customer_email
            AND ma.activity_month >= cc.cohort_month
          GROUP BY cc.cohort_month, ma.activity_month
          ORDER BY cc.cohort_month, ma.activity_month
        `);
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        expect(result.rows.length).toBeGreaterThan(0);
        expect(executionTime).toBeLessThan(3000); // Should complete within 3 seconds
        
        console.log(`Cohort analysis query: ${executionTime.toFixed(2)}ms`);
        
      } finally {
        client.release();
      }
    });
  });

  describe('Partitioning Strategy Performance', () => {
    it('should demonstrate partition pruning effectiveness', async () => {
      const client = await pool.connect();
      try {
        // Query with time range that should use partition pruning
        const startTime = performance.now();
        
        const result = await client.query(`
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT COUNT(*) 
          FROM clicks 
          WHERE tenant_id = $1 
            AND clicked_at >= '2024-01-01' 
            AND clicked_at < '2024-01-02'
        `, [testTenant.id]);
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        const plan = result.rows[0]['QUERY PLAN'][0];
        
        // Verify partition pruning occurred
        expect(plan['Execution Time']).toBeLessThan(100); // Should be very fast with pruning
        expect(executionTime).toBeLessThan(500);
        
        console.log(`Partition pruning query: ${executionTime.toFixed(2)}ms`);
        console.log(`Execution time from plan: ${plan['Execution Time']}ms`);
        
      } finally {
        client.release();
      }
    });

    it('should test chunk exclusion for old data queries', async () => {
      const client = await pool.connect();
      try {
        // Query very recent data (should exclude most chunks)
        const startTime = performance.now();
        
        const result = await client.query(`
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT 
            COUNT(*) as total_clicks,
            COUNT(DISTINCT ip_address) as unique_visitors
          FROM clicks 
          WHERE tenant_id = $1 
            AND clicked_at >= NOW() - INTERVAL '1 hour'
        `, [testTenant.id]);
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        const plan = result.rows[0]['QUERY PLAN'][0];
        
        expect(plan['Execution Time']).toBeLessThan(50); // Should be very fast
        expect(executionTime).toBeLessThan(200);
        
        console.log(`Recent data query: ${executionTime.toFixed(2)}ms`);
        
      } finally {
        client.release();
      }
    });
  });

  describe('Index Performance', () => {
    it('should test composite index effectiveness', async () => {
      const client = await pool.connect();
      try {
        const startTime = performance.now();
        
        const result = await client.query(`
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT 
            device_type,
            COUNT(*) as clicks,
            COUNT(DISTINCT ip_address) as unique_visitors
          FROM clicks 
          WHERE tenant_id = $1 
            AND clicked_at >= NOW() - INTERVAL '7 days'
            AND country = 'US'
          GROUP BY device_type
        `, [testTenant.id]);
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        const plan = result.rows[0]['QUERY PLAN'][0];
        
        // Should use index scan, not sequential scan
        const planText = JSON.stringify(plan);
        expect(planText).toContain('Index');
        expect(plan['Execution Time']).toBeLessThan(200);
        
        console.log(`Composite index query: ${executionTime.toFixed(2)}ms`);
        
      } finally {
        client.release();
      }
    });

    it('should test partial index performance', async () => {
      const client = await pool.connect();
      try {
        // Create partial index for recent data
        await client.query(`
          CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clicks_recent_tenant_device 
          ON clicks (tenant_id, device_type, clicked_at) 
          WHERE clicked_at >= CURRENT_DATE - INTERVAL '30 days'
        `);
        
        const startTime = performance.now();
        
        const result = await client.query(`
          SELECT device_type, COUNT(*) as count
          FROM clicks 
          WHERE tenant_id = $1 
            AND clicked_at >= CURRENT_DATE - INTERVAL '7 days'
          GROUP BY device_type
        `, [testTenant.id]);
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        expect(result.rows.length).toBeGreaterThan(0);
        expect(executionTime).toBeLessThan(100); // Should be very fast with partial index
        
        console.log(`Partial index query: ${executionTime.toFixed(2)}ms`);
        
      } finally {
        client.release();
      }
    });
  });

  describe('Concurrent Access Performance', () => {
    it('should handle concurrent read queries efficiently', async () => {
      const concurrentQueries = 10;
      const queryPromises = [];
      
      for (let i = 0; i < concurrentQueries; i++) {
        queryPromises.push(
          pool.query(`
            SELECT 
              DATE(clicked_at) as date,
              COUNT(*) as clicks
            FROM clicks 
            WHERE tenant_id = $1 
              AND clicked_at >= NOW() - INTERVAL '30 days'
            GROUP BY DATE(clicked_at)
            ORDER BY date
          `, [testTenant.id])
        );
      }
      
      const startTime = performance.now();
      const results = await Promise.all(queryPromises);
      const endTime = performance.now();
      
      const totalExecutionTime = endTime - startTime;
      const avgExecutionTime = totalExecutionTime / concurrentQueries;
      
      expect(results).toHaveLength(concurrentQueries);
      expect(totalExecutionTime).toBeLessThan(5000); // All queries within 5 seconds
      expect(avgExecutionTime).toBeLessThan(1000); // Average under 1 second
      
      console.log(`Concurrent queries (${concurrentQueries}): ${totalExecutionTime.toFixed(2)}ms total, ${avgExecutionTime.toFixed(2)}ms average`);
    });

    it('should handle mixed read/write workload', async () => {
      const readQueries = 5;
      const writeQueries = 2;
      const allPromises = [];
      
      // Add read queries
      for (let i = 0; i < readQueries; i++) {
        allPromises.push(
          pool.query(`
            SELECT COUNT(*) FROM clicks 
            WHERE tenant_id = $1 
              AND clicked_at >= NOW() - INTERVAL '1 day'
          `, [testTenant.id])
        );
      }
      
      // Add write queries (inserts)
      for (let i = 0; i < writeQueries; i++) {
        allPromises.push(
          pool.query(`
            INSERT INTO clicks (id, tenant_id, link_id, ip_address, clicked_at)
            VALUES ($1, $2, $3, $4, NOW())
          `, [`concurrent-click-${i}`, testTenant.id, 'perf-link-1', '*************'])
        );
      }
      
      const startTime = performance.now();
      const results = await Promise.all(allPromises);
      const endTime = performance.now();
      
      const totalExecutionTime = endTime - startTime;
      
      expect(results).toHaveLength(readQueries + writeQueries);
      expect(totalExecutionTime).toBeLessThan(3000); // Should complete within 3 seconds
      
      console.log(`Mixed workload: ${totalExecutionTime.toFixed(2)}ms`);
      
      // Cleanup
      await pool.query('DELETE FROM clicks WHERE id LIKE $1', ['concurrent-click-%']);
    });
  });

  describe('Materialized View Performance', () => {
    it('should test materialized view refresh performance', async () => {
      const client = await pool.connect();
      try {
        // Create test materialized view
        await client.query(`
          CREATE MATERIALIZED VIEW IF NOT EXISTS test_daily_summary AS
          SELECT 
            tenant_id,
            DATE(clicked_at) as date,
            COUNT(*) as total_clicks,
            COUNT(DISTINCT ip_address) as unique_visitors
          FROM clicks 
          WHERE clicked_at >= CURRENT_DATE - INTERVAL '30 days'
          GROUP BY tenant_id, DATE(clicked_at)
        `);
        
        const startTime = performance.now();
        await client.query('REFRESH MATERIALIZED VIEW test_daily_summary');
        const endTime = performance.now();
        
        const refreshTime = endTime - startTime;
        
        expect(refreshTime).toBeLessThan(5000); // Should refresh within 5 seconds
        
        console.log(`Materialized view refresh: ${refreshTime.toFixed(2)}ms`);
        
        // Test query performance against materialized view
        const queryStartTime = performance.now();
        const result = await client.query(`
          SELECT * FROM test_daily_summary 
          WHERE tenant_id = $1 
          ORDER BY date
        `, [testTenant.id]);
        const queryEndTime = performance.now();
        
        const queryTime = queryEndTime - queryStartTime;
        
        expect(result.rows.length).toBeGreaterThan(0);
        expect(queryTime).toBeLessThan(50); // Should be very fast
        
        console.log(`Materialized view query: ${queryTime.toFixed(2)}ms`);
        
        // Cleanup
        await client.query('DROP MATERIALIZED VIEW test_daily_summary');
        
      } finally {
        client.release();
      }
    });
  });

  describe('Query Optimization Validation', () => {
    it('should validate query plans for optimal performance', async () => {
      const client = await pool.connect();
      try {
        const result = await client.query(`
          EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
          SELECT
            l.short_code,
            COUNT(c.id) as clicks,
            COUNT(DISTINCT c.ip_address) as unique_visitors,
            COUNT(a.id) as conversions
          FROM links l
          LEFT JOIN clicks c ON l.id = c.link_id
            AND c.clicked_at >= NOW() - INTERVAL '30 days'
          LEFT JOIN attributions a ON c.id = a.click_id
          WHERE l.tenant_id = $1
          GROUP BY l.id, l.short_code
          ORDER BY clicks DESC
          LIMIT 10
        `, [testTenant.id]);

        const plan = result.rows[0]['QUERY PLAN'][0];

        // Validate performance characteristics
        expect(plan['Execution Time']).toBeLessThan(1000); // Under 1 second
        expect(plan['Planning Time']).toBeLessThan(50); // Planning should be fast

        // Check for efficient join strategies
        const planText = JSON.stringify(plan);
        expect(planText).not.toContain('Seq Scan'); // Should avoid sequential scans

        console.log(`Complex join query execution time: ${plan['Execution Time']}ms`);
        console.log(`Planning time: ${plan['Planning Time']}ms`);

      } finally {
        client.release();
      }
    });
  });

  describe('Compression and Storage Performance', () => {
    it('should test compression policy effectiveness', async () => {
      const client = await pool.connect();
      try {
        // Check current compression status
        const compressionStatus = await client.query(`
          SELECT
            chunk_schema,
            chunk_name,
            compression_status,
            before_compression_total_bytes,
            after_compression_total_bytes,
            ROUND(
              (1 - after_compression_total_bytes::decimal / before_compression_total_bytes::decimal) * 100,
              2
            ) as compression_ratio
          FROM chunk_compression_stats('clicks')
          WHERE compression_status = 'Compressed'
          LIMIT 5
        `);

        if (compressionStatus.rows.length > 0) {
          compressionStatus.rows.forEach(row => {
            expect(row.compression_ratio).toBeGreaterThan(50); // At least 50% compression
            console.log(`Chunk ${row.chunk_name}: ${row.compression_ratio}% compression`);
          });
        }

        // Test query performance on compressed chunks
        const startTime = performance.now();
        const result = await client.query(`
          SELECT COUNT(*)
          FROM clicks
          WHERE tenant_id = $1
            AND clicked_at >= NOW() - INTERVAL '30 days'
            AND clicked_at < NOW() - INTERVAL '7 days'
        `, [testTenant.id]);
        const endTime = performance.now();

        const queryTime = endTime - startTime;
        expect(queryTime).toBeLessThan(2000); // Should still be reasonably fast on compressed data

        console.log(`Query on compressed data: ${queryTime.toFixed(2)}ms`);

      } finally {
        client.release();
      }
    });

    it('should test data retention policy performance', async () => {
      const client = await pool.connect();
      try {
        // Check retention policy status
        const retentionInfo = await client.query(`
          SELECT
            hypertable_name,
            older_than,
            cascade_to_materializations
          FROM timescaledb_information.drop_chunks_policies
          WHERE hypertable_name IN ('clicks', 'orders', 'attributions')
        `);

        expect(retentionInfo.rows.length).toBeGreaterThan(0);

        // Test chunk information
        const chunkInfo = await client.query(`
          SELECT
            hypertable_name,
            chunk_name,
            range_start,
            range_end,
            is_compressed
          FROM timescaledb_information.chunks
          WHERE hypertable_name = 'clicks'
          ORDER BY range_start DESC
          LIMIT 10
        `);

        expect(chunkInfo.rows.length).toBeGreaterThan(0);

        console.log(`Found ${chunkInfo.rows.length} chunks for clicks table`);

      } finally {
        client.release();
      }
    });
  });

  describe('Memory and Resource Usage', () => {
    it('should monitor memory usage during large aggregations', async () => {
      const client = await pool.connect();
      try {
        // Get initial memory stats
        const initialStats = await client.query(`
          SELECT
            pg_size_pretty(pg_database_size(current_database())) as db_size,
            pg_size_pretty(pg_total_relation_size('clicks')) as clicks_table_size
        `);

        console.log(`Database size: ${initialStats.rows[0].db_size}`);
        console.log(`Clicks table size: ${initialStats.rows[0].clicks_table_size}`);

        // Perform memory-intensive aggregation
        const startTime = performance.now();
        const result = await client.query(`
          SELECT
            tenant_id,
            DATE_TRUNC('hour', clicked_at) as hour,
            device_type,
            country,
            COUNT(*) as clicks,
            COUNT(DISTINCT ip_address) as unique_visitors,
            PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM clicked_at)) as median_timestamp
          FROM clicks
          WHERE clicked_at >= NOW() - INTERVAL '7 days'
          GROUP BY tenant_id, DATE_TRUNC('hour', clicked_at), device_type, country
          ORDER BY hour, clicks DESC
        `);
        const endTime = performance.now();

        const executionTime = endTime - startTime;

        expect(result.rows.length).toBeGreaterThan(0);
        expect(executionTime).toBeLessThan(10000); // Should complete within 10 seconds

        console.log(`Memory-intensive aggregation: ${executionTime.toFixed(2)}ms`);
        console.log(`Result rows: ${result.rows.length}`);

      } finally {
        client.release();
      }
    });

    it('should test connection pool efficiency under load', async () => {
      const connectionCount = 15; // Test with more connections than pool size
      const queryPromises = [];

      for (let i = 0; i < connectionCount; i++) {
        queryPromises.push(
          (async () => {
            const startTime = performance.now();
            const result = await pool.query(`
              SELECT
                COUNT(*) as total_clicks,
                AVG(CASE WHEN device_type = 'mobile' THEN 1.0 ELSE 0.0 END) * 100 as mobile_percentage
              FROM clicks
              WHERE tenant_id = $1
                AND clicked_at >= NOW() - INTERVAL '1 day'
            `, [testTenant.id]);
            const endTime = performance.now();

            return {
              executionTime: endTime - startTime,
              result: result.rows[0]
            };
          })()
        );
      }

      const startTime = performance.now();
      const results = await Promise.all(queryPromises);
      const totalTime = performance.now() - startTime;

      const avgExecutionTime = results.reduce((sum, r) => sum + r.executionTime, 0) / results.length;
      const maxExecutionTime = Math.max(...results.map(r => r.executionTime));

      expect(results).toHaveLength(connectionCount);
      expect(totalTime).toBeLessThan(15000); // All queries within 15 seconds
      expect(avgExecutionTime).toBeLessThan(2000); // Average under 2 seconds
      expect(maxExecutionTime).toBeLessThan(5000); // No single query over 5 seconds

      console.log(`Connection pool test - Total: ${totalTime.toFixed(2)}ms, Avg: ${avgExecutionTime.toFixed(2)}ms, Max: ${maxExecutionTime.toFixed(2)}ms`);
    });
  });
});
