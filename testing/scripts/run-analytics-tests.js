#!/usr/bin/env node

/**
 * Comprehensive Analytics Test Runner
 * 
 * This script runs all analytics-related tests including:
 * - Unit tests for analytics calculations
 * - Performance benchmarks
 * - Edge case validation
 * - Multi-tenant isolation tests
 */

const { execSync, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class AnalyticsTestRunner {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      skipped: 0,
      total: 0,
      coverage: {},
      performance: {},
      errors: []
    };
    
    this.testSuites = [
      {
        name: 'Advanced Analytics Service',
        path: 'services/analytics/src/services/__tests__/advancedAnalyticsService.test.js',
        timeout: 30000,
        critical: true
      },
      {
        name: 'Advanced Analytics Processor',
        path: 'services/analytics/src/services/__tests__/advancedAnalyticsProcessor.test.js',
        timeout: 30000,
        critical: true
      },
      {
        name: 'Data Exploration Service',
        path: 'services/analytics/src/services/__tests__/dataExplorationService.test.js',
        timeout: 30000,
        critical: true
      },
      {
        name: 'Cohort Service',
        path: 'services/analytics/src/services/__tests__/cohortService.test.js',
        timeout: 30000,
        critical: true
      },
      {
        name: 'Analytics Service',
        path: 'services/analytics/src/services/__tests__/analyticsService.test.js',
        timeout: 30000,
        critical: false
      },
      {
        name: 'Attribution Service',
        path: 'services/analytics/src/services/__tests__/attributionService.test.js',
        timeout: 30000,
        critical: false
      }
    ];
  }

  async runAllTests() {
    console.log('🧪 Starting Comprehensive Analytics Test Suite\n');
    console.log('=' * 60);
    
    const startTime = Date.now();
    
    try {
      // Setup test environment
      await this.setupTestEnvironment();
      
      // Run unit tests
      await this.runUnitTests();
      
      // Run performance tests
      await this.runPerformanceTests();
      
      // Run edge case tests
      await this.runEdgeCaseTests();
      
      // Run multi-tenant isolation tests
      await this.runTenantIsolationTests();
      
      // Generate coverage report
      await this.generateCoverageReport();
      
      // Generate final report
      this.generateFinalReport(Date.now() - startTime);
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async setupTestEnvironment() {
    console.log('🔧 Setting up test environment...');
    
    try {
      // Check if test database is available
      execSync('npm run test:db:check', { stdio: 'pipe' });
      
      // Install test dependencies
      execSync('npm ci', { 
        cwd: path.join(__dirname, '../../services/analytics'),
        stdio: 'pipe' 
      });
      
      // Setup test data
      execSync('npm run test:setup', { 
        cwd: path.join(__dirname, '../../services/analytics'),
        stdio: 'pipe' 
      });
      
      console.log('✅ Test environment ready\n');
      
    } catch (error) {
      throw new Error(`Failed to setup test environment: ${error.message}`);
    }
  }

  async runUnitTests() {
    console.log('🧪 Running Unit Tests...\n');
    
    for (const suite of this.testSuites) {
      await this.runTestSuite(suite);
    }
  }

  async runTestSuite(suite) {
    console.log(`  📋 ${suite.name}...`);
    
    try {
      const result = execSync(
        `npx jest ${suite.path} --coverage --json --outputFile=test-results.json`,
        { 
          cwd: path.join(__dirname, '../../services/analytics'),
          timeout: suite.timeout,
          stdio: 'pipe'
        }
      );
      
      const testResult = JSON.parse(
        fs.readFileSync(
          path.join(__dirname, '../../services/analytics/test-results.json'),
          'utf8'
        )
      );
      
      this.processTestResults(suite, testResult);
      
      if (testResult.success) {
        console.log(`    ✅ ${suite.name} - ${testResult.numPassedTests}/${testResult.numTotalTests} tests passed`);
      } else {
        console.log(`    ❌ ${suite.name} - ${testResult.numFailedTests} tests failed`);
        if (suite.critical) {
          throw new Error(`Critical test suite failed: ${suite.name}`);
        }
      }
      
    } catch (error) {
      console.log(`    ❌ ${suite.name} - Error: ${error.message}`);
      this.testResults.errors.push({
        suite: suite.name,
        error: error.message
      });
      
      if (suite.critical) {
        throw error;
      }
    }
  }

  async runPerformanceTests() {
    console.log('\n⚡ Running Performance Tests...\n');
    
    const performanceTests = [
      {
        name: 'CLV Calculation Performance',
        test: 'calculateCustomerLifetimeValue',
        expectedTime: 1000, // ms
        dataSize: 'large'
      },
      {
        name: 'Cohort Analysis Performance',
        test: 'generateCohortAnalysis',
        expectedTime: 2000, // ms
        dataSize: 'large'
      },
      {
        name: 'Funnel Analysis Performance',
        test: 'generateFunnelAnalysis',
        expectedTime: 1500, // ms
        dataSize: 'large'
      },
      {
        name: 'RFM Segmentation Performance',
        test: 'generateRFMSegments',
        expectedTime: 3000, // ms
        dataSize: 'large'
      }
    ];
    
    for (const perfTest of performanceTests) {
      await this.runPerformanceTest(perfTest);
    }
  }

  async runPerformanceTest(perfTest) {
    console.log(`  ⏱️  ${perfTest.name}...`);
    
    try {
      const result = execSync(
        `npx jest --testNamePattern="${perfTest.test}.*performance" --json`,
        { 
          cwd: path.join(__dirname, '../../services/analytics'),
          timeout: perfTest.expectedTime * 2,
          stdio: 'pipe'
        }
      );
      
      const testResult = JSON.parse(result.toString());
      const executionTime = testResult.testResults[0]?.perfStats?.runtime || 0;
      
      this.testResults.performance[perfTest.name] = {
        executionTime,
        expectedTime: perfTest.expectedTime,
        passed: executionTime <= perfTest.expectedTime
      };
      
      if (executionTime <= perfTest.expectedTime) {
        console.log(`    ✅ ${perfTest.name} - ${executionTime}ms (expected: <${perfTest.expectedTime}ms)`);
      } else {
        console.log(`    ⚠️  ${perfTest.name} - ${executionTime}ms (slower than expected: ${perfTest.expectedTime}ms)`);
      }
      
    } catch (error) {
      console.log(`    ❌ ${perfTest.name} - Error: ${error.message}`);
      this.testResults.performance[perfTest.name] = {
        executionTime: null,
        expectedTime: perfTest.expectedTime,
        passed: false,
        error: error.message
      };
    }
  }

  async runEdgeCaseTests() {
    console.log('\n🔍 Running Edge Case Tests...\n');
    
    const edgeCases = [
      'empty datasets',
      'malformed data',
      'extreme values',
      'boundary conditions',
      'null/undefined inputs',
      'concurrent access'
    ];
    
    try {
      const result = execSync(
        'npx jest --testNamePattern="edge case|boundary|malformed|empty|null|concurrent" --json',
        { 
          cwd: path.join(__dirname, '../../services/analytics'),
          timeout: 60000,
          stdio: 'pipe'
        }
      );
      
      const testResult = JSON.parse(result.toString());
      
      console.log(`  ✅ Edge case tests - ${testResult.numPassedTests}/${testResult.numTotalTests} passed`);
      
    } catch (error) {
      console.log(`  ❌ Edge case tests failed: ${error.message}`);
    }
  }

  async runTenantIsolationTests() {
    console.log('\n🔒 Running Multi-tenant Isolation Tests...\n');
    
    try {
      const result = execSync(
        'npx jest --testNamePattern="tenant.*isolation|isolation.*tenant" --json',
        { 
          cwd: path.join(__dirname, '../../services/analytics'),
          timeout: 30000,
          stdio: 'pipe'
        }
      );
      
      const testResult = JSON.parse(result.toString());
      
      console.log(`  ✅ Tenant isolation tests - ${testResult.numPassedTests}/${testResult.numTotalTests} passed`);
      
      if (testResult.numFailedTests > 0) {
        throw new Error('Tenant isolation tests failed - this is a critical security issue');
      }
      
    } catch (error) {
      console.log(`  ❌ Tenant isolation tests failed: ${error.message}`);
      throw error;
    }
  }

  async generateCoverageReport() {
    console.log('\n📊 Generating Coverage Report...\n');
    
    try {
      execSync(
        'npx jest --coverage --coverageReporters=text-summary --coverageReporters=lcov',
        { 
          cwd: path.join(__dirname, '../../services/analytics'),
          stdio: 'inherit'
        }
      );
      
    } catch (error) {
      console.log(`  ⚠️  Coverage report generation failed: ${error.message}`);
    }
  }

  processTestResults(suite, testResult) {
    this.testResults.passed += testResult.numPassedTests;
    this.testResults.failed += testResult.numFailedTests;
    this.testResults.total += testResult.numTotalTests;
    
    if (testResult.coverageMap) {
      this.testResults.coverage[suite.name] = testResult.coverageMap;
    }
  }

  generateFinalReport(executionTime) {
    console.log('\n' + '=' * 60);
    console.log('📋 FINAL TEST REPORT');
    console.log('=' * 60);
    
    console.log(`\n📊 Test Results:`);
    console.log(`  ✅ Passed: ${this.testResults.passed}`);
    console.log(`  ❌ Failed: ${this.testResults.failed}`);
    console.log(`  📊 Total: ${this.testResults.total}`);
    console.log(`  ⏱️  Execution Time: ${(executionTime / 1000).toFixed(2)}s`);
    
    const successRate = (this.testResults.passed / this.testResults.total * 100).toFixed(2);
    console.log(`  📈 Success Rate: ${successRate}%`);
    
    console.log(`\n⚡ Performance Results:`);
    Object.entries(this.testResults.performance).forEach(([name, result]) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`  ${status} ${name}: ${result.executionTime}ms`);
    });
    
    if (this.testResults.errors.length > 0) {
      console.log(`\n❌ Errors:`);
      this.testResults.errors.forEach(error => {
        console.log(`  - ${error.suite}: ${error.error}`);
      });
    }
    
    console.log('\n' + '=' * 60);
    
    if (this.testResults.failed === 0) {
      console.log('🎉 All tests passed! Analytics calculations are ready for production.');
      process.exit(0);
    } else {
      console.log('❌ Some tests failed. Please review and fix before deploying.');
      process.exit(1);
    }
  }
}

// Run the test suite if this script is executed directly
if (require.main === module) {
  const runner = new AnalyticsTestRunner();
  runner.runAllTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = AnalyticsTestRunner;
