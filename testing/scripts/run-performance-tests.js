#!/usr/bin/env node

/**
 * Comprehensive Performance Test Runner
 * 
 * This script runs performance tests for:
 * - TimescaleDB time-series queries
 * - Database partitioning strategies
 * - Query optimization validation
 * - Concurrent access patterns
 * - Memory and resource usage
 */

const { execSync, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { Pool } = require('pg');

class PerformanceTestRunner {
  constructor() {
    this.results = {
      timescaledb: {},
      analytics: {},
      integration: {},
      overall: {
        startTime: Date.now(),
        endTime: null,
        totalDuration: null,
        testsRun: 0,
        testsPassed: 0,
        testsFailed: 0
      }
    };
    
    this.benchmarks = {
      timeSeriesQuery: { target: 1000, unit: 'ms' }, // 1 second
      aggregationQuery: { target: 2000, unit: 'ms' }, // 2 seconds
      cohortAnalysis: { target: 3000, unit: 'ms' }, // 3 seconds
      concurrentQueries: { target: 5000, unit: 'ms' }, // 5 seconds total
      memoryUsage: { target: 512, unit: 'MB' }, // 512 MB max
      connectionPool: { target: 2000, unit: 'ms' } // 2 seconds average
    };
    
    this.pool = null;
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Performance Test Suite\n');
    console.log('=' * 60);
    
    try {
      await this.setupTestEnvironment();
      await this.runTimescaleDBTests();
      await this.runAnalyticsPerformanceTests();
      await this.runIntegrationPerformanceTests();
      await this.generatePerformanceReport();
      
    } catch (error) {
      console.error('❌ Performance test suite failed:', error.message);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }

  async setupTestEnvironment() {
    console.log('🔧 Setting up performance test environment...');
    
    try {
      // Setup database connection
      this.pool = new Pool({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 5432,
        database: process.env.DB_NAME || 'ecommerce_analytics_test',
        user: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || 'password',
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      });
      
      // Test database connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      
      // Check TimescaleDB extension
      const tsdbCheck = await this.pool.query(
        "SELECT * FROM pg_extension WHERE extname = 'timescaledb'"
      );
      
      if (tsdbCheck.rows.length === 0) {
        throw new Error('TimescaleDB extension not found');
      }
      
      console.log('✅ Performance test environment ready\n');
      
    } catch (error) {
      throw new Error(`Failed to setup test environment: ${error.message}`);
    }
  }

  async runTimescaleDBTests() {
    console.log('📊 Running TimescaleDB Performance Tests...\n');
    
    const testSuites = [
      {
        name: 'Time-Series Query Performance',
        command: 'npx jest testing/performance/timescaledb-performance.test.js --testNamePattern="Time-Series Query Performance"',
        benchmark: 'timeSeriesQuery'
      },
      {
        name: 'Partitioning Strategy Performance',
        command: 'npx jest testing/performance/timescaledb-performance.test.js --testNamePattern="Partitioning Strategy Performance"',
        benchmark: 'aggregationQuery'
      },
      {
        name: 'Concurrent Access Performance',
        command: 'npx jest testing/performance/timescaledb-performance.test.js --testNamePattern="Concurrent Access Performance"',
        benchmark: 'concurrentQueries'
      },
      {
        name: 'Memory and Resource Usage',
        command: 'npx jest testing/performance/timescaledb-performance.test.js --testNamePattern="Memory and Resource Usage"',
        benchmark: 'memoryUsage'
      }
    ];
    
    for (const suite of testSuites) {
      await this.runTestSuite(suite, 'timescaledb');
    }
  }

  async runAnalyticsPerformanceTests() {
    console.log('\n📈 Running Analytics Performance Tests...\n');
    
    const testSuites = [
      {
        name: 'CLV Calculation Performance',
        command: 'npx jest services/analytics/src/services/__tests__/advancedAnalyticsService.test.js --testNamePattern="performance"',
        benchmark: 'cohortAnalysis'
      },
      {
        name: 'Cohort Analysis Performance',
        command: 'npx jest services/analytics/src/services/__tests__/cohortService.test.js --testNamePattern="performance"',
        benchmark: 'cohortAnalysis'
      },
      {
        name: 'Funnel Analysis Performance',
        command: 'npx jest services/analytics/src/services/__tests__/dataExplorationService.test.js --testNamePattern="performance"',
        benchmark: 'aggregationQuery'
      }
    ];
    
    for (const suite of testSuites) {
      await this.runTestSuite(suite, 'analytics');
    }
  }

  async runIntegrationPerformanceTests() {
    console.log('\n🔗 Running Integration Performance Tests...\n');
    
    const testSuites = [
      {
        name: 'Rate Limiting Performance',
        command: 'npx jest testing/integration/rate-limiting.test.js --testNamePattern="performance"',
        benchmark: 'connectionPool'
      },
      {
        name: 'Data Synchronization Performance',
        command: 'npx jest testing/integration/data-synchronization.test.js --testNamePattern="performance"',
        benchmark: 'aggregationQuery'
      }
    ];
    
    for (const suite of testSuites) {
      await this.runTestSuite(suite, 'integration');
    }
  }

  async runTestSuite(suite, category) {
    console.log(`  📋 ${suite.name}...`);
    
    try {
      const startTime = Date.now();
      
      const result = execSync(suite.command + ' --json', {
        cwd: path.join(__dirname, '../..'),
        timeout: 300000, // 5 minutes timeout
        stdio: 'pipe'
      });
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      
      const testResult = JSON.parse(result.toString());
      
      this.results[category][suite.name] = {
        passed: testResult.success,
        executionTime,
        numTests: testResult.numTotalTests,
        numPassed: testResult.numPassedTests,
        numFailed: testResult.numFailedTests,
        benchmark: this.benchmarks[suite.benchmark],
        meetsTarget: executionTime <= this.benchmarks[suite.benchmark].target
      };
      
      this.results.overall.testsRun += testResult.numTotalTests;
      this.results.overall.testsPassed += testResult.numPassedTests;
      this.results.overall.testsFailed += testResult.numFailedTests;
      
      const status = testResult.success ? '✅' : '❌';
      const targetStatus = this.results[category][suite.name].meetsTarget ? '🎯' : '⚠️';
      
      console.log(`    ${status} ${targetStatus} ${suite.name} - ${executionTime}ms (target: ${this.benchmarks[suite.benchmark].target}ms)`);
      
    } catch (error) {
      console.log(`    ❌ ${suite.name} - Error: ${error.message}`);
      
      this.results[category][suite.name] = {
        passed: false,
        executionTime: null,
        error: error.message,
        benchmark: this.benchmarks[suite.benchmark],
        meetsTarget: false
      };
      
      this.results.overall.testsFailed++;
    }
  }

  async generatePerformanceReport() {
    this.results.overall.endTime = Date.now();
    this.results.overall.totalDuration = this.results.overall.endTime - this.results.overall.startTime;
    
    console.log('\n' + '=' * 60);
    console.log('📊 PERFORMANCE TEST REPORT');
    console.log('=' * 60);
    
    // Overall summary
    console.log(`\n📈 Overall Results:`);
    console.log(`  ✅ Tests Passed: ${this.results.overall.testsPassed}`);
    console.log(`  ❌ Tests Failed: ${this.results.overall.testsFailed}`);
    console.log(`  📊 Total Tests: ${this.results.overall.testsRun}`);
    console.log(`  ⏱️  Total Duration: ${(this.results.overall.totalDuration / 1000).toFixed(2)}s`);
    
    const successRate = (this.results.overall.testsPassed / this.results.overall.testsRun * 100).toFixed(2);
    console.log(`  📈 Success Rate: ${successRate}%`);
    
    // Performance benchmarks
    console.log(`\n🎯 Performance Benchmarks:`);
    
    Object.entries(this.results).forEach(([category, tests]) => {
      if (category === 'overall') return;
      
      console.log(`\n  ${category.toUpperCase()}:`);
      Object.entries(tests).forEach(([testName, result]) => {
        const status = result.meetsTarget ? '✅' : '❌';
        const time = result.executionTime ? `${result.executionTime}ms` : 'FAILED';
        const target = `${result.benchmark.target}${result.benchmark.unit}`;
        
        console.log(`    ${status} ${testName}: ${time} (target: ${target})`);
      });
    });
    
    // Database performance metrics
    await this.generateDatabaseMetrics();
    
    // Save detailed report
    await this.saveDetailedReport();
    
    console.log('\n' + '=' * 60);
    
    // Determine overall success
    const allBenchmarksMet = this.checkAllBenchmarksMet();
    const allTestsPassed = this.results.overall.testsFailed === 0;
    
    if (allTestsPassed && allBenchmarksMet) {
      console.log('🎉 All performance tests passed and benchmarks met!');
      process.exit(0);
    } else {
      console.log('❌ Some performance tests failed or benchmarks not met.');
      process.exit(1);
    }
  }

  async generateDatabaseMetrics() {
    if (!this.pool) return;
    
    try {
      console.log(`\n💾 Database Performance Metrics:`);
      
      // Database size
      const sizeResult = await this.pool.query(`
        SELECT pg_size_pretty(pg_database_size(current_database())) as db_size
      `);
      console.log(`  📦 Database Size: ${sizeResult.rows[0].db_size}`);
      
      // Query statistics
      const queryStats = await this.pool.query(`
        SELECT 
          calls,
          total_time,
          mean_time,
          query
        FROM pg_stat_statements 
        WHERE query LIKE '%clicks%' OR query LIKE '%orders%'
        ORDER BY total_time DESC 
        LIMIT 5
      `);
      
      if (queryStats.rows.length > 0) {
        console.log(`  🔍 Top Queries by Total Time:`);
        queryStats.rows.forEach((row, i) => {
          console.log(`    ${i + 1}. ${row.mean_time.toFixed(2)}ms avg (${row.calls} calls)`);
        });
      }
      
      // Connection stats
      const connStats = await this.pool.query(`
        SELECT 
          state,
          COUNT(*) as count
        FROM pg_stat_activity 
        WHERE datname = current_database()
        GROUP BY state
      `);
      
      console.log(`  🔗 Connection States:`);
      connStats.rows.forEach(row => {
        console.log(`    ${row.state}: ${row.count}`);
      });
      
    } catch (error) {
      console.log(`  ⚠️  Could not retrieve database metrics: ${error.message}`);
    }
  }

  checkAllBenchmarksMet() {
    for (const [category, tests] of Object.entries(this.results)) {
      if (category === 'overall') continue;
      
      for (const [testName, result] of Object.entries(tests)) {
        if (!result.meetsTarget) {
          return false;
        }
      }
    }
    return true;
  }

  async saveDetailedReport() {
    const reportPath = path.join(__dirname, '../reports/performance-report.json');
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Add timestamp and environment info
    const detailedReport = {
      ...this.results,
      timestamp: new Date().toISOString(),
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        dbHost: process.env.DB_HOST || 'localhost',
        dbName: process.env.DB_NAME || 'ecommerce_analytics_test'
      },
      benchmarks: this.benchmarks
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(detailedReport, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  }

  async cleanup() {
    if (this.pool) {
      await this.pool.end();
    }
  }
}

// Run the performance test suite if this script is executed directly
if (require.main === module) {
  const runner = new PerformanceTestRunner();
  runner.runAllTests().catch(error => {
    console.error('Performance test runner failed:', error);
    process.exit(1);
  });
}

module.exports = PerformanceTestRunner;
