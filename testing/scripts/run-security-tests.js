#!/usr/bin/env node

/**
 * Comprehensive Security Test Runner
 * 
 * This script runs security tests for:
 * - Multi-tenant isolation
 * - Access control and RBAC
 * - Authentication and authorization
 * - Data leakage prevention
 * - Input validation and sanitization
 * - API security
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const { Pool } = require('pg');

class SecurityTestRunner {
  constructor() {
    this.results = {
      multiTenantIsolation: {},
      accessControl: {},
      authentication: {},
      dataProtection: {},
      overall: {
        startTime: Date.now(),
        endTime: null,
        totalDuration: null,
        testsRun: 0,
        testsPassed: 0,
        testsFailed: 0,
        criticalIssues: 0,
        highIssues: 0,
        mediumIssues: 0,
        lowIssues: 0
      }
    };
    
    this.securityChecks = {
      multiTenantIsolation: {
        name: 'Multi-tenant Data Isolation',
        severity: 'CRITICAL',
        tests: [
          'should prevent cross-tenant data access',
          'should enforce RLS policies',
          'should validate tenant boundaries'
        ]
      },
      accessControl: {
        name: 'Access Control and RBAC',
        severity: 'HIGH',
        tests: [
          'should enforce role-based permissions',
          'should prevent privilege escalation',
          'should validate API key permissions'
        ]
      },
      authentication: {
        name: 'Authentication Security',
        severity: 'HIGH',
        tests: [
          'should validate JWT integrity',
          'should prevent token tampering',
          'should handle session security'
        ]
      },
      inputValidation: {
        name: 'Input Validation and Sanitization',
        severity: 'MEDIUM',
        tests: [
          'should prevent SQL injection',
          'should sanitize user input',
          'should validate data types'
        ]
      }
    };
    
    this.pool = null;
  }

  async runAllTests() {
    console.log('🔒 Starting Comprehensive Security Test Suite\n');
    console.log('=' * 60);
    
    try {
      await this.setupSecurityTestEnvironment();
      await this.runMultiTenantIsolationTests();
      await this.runAccessControlTests();
      await this.runAuthenticationTests();
      await this.runDataProtectionTests();
      await this.runVulnerabilityScans();
      await this.generateSecurityReport();
      
    } catch (error) {
      console.error('❌ Security test suite failed:', error.message);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }

  async setupSecurityTestEnvironment() {
    console.log('🔧 Setting up security test environment...');
    
    try {
      // Setup database connection for security checks
      this.pool = new Pool({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 5432,
        database: process.env.DB_NAME || 'ecommerce_analytics_test',
        user: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || 'password',
        max: 5,
      });
      
      // Test database connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      
      // Verify security extensions
      await this.verifySecurityExtensions();
      
      console.log('✅ Security test environment ready\n');
      
    } catch (error) {
      throw new Error(`Failed to setup security test environment: ${error.message}`);
    }
  }

  async verifySecurityExtensions() {
    const extensions = await this.pool.query(`
      SELECT extname FROM pg_extension 
      WHERE extname IN ('uuid-ossp', 'pgcrypto', 'timescaledb')
    `);
    
    const requiredExtensions = ['uuid-ossp', 'pgcrypto'];
    const installedExtensions = extensions.rows.map(row => row.extname);
    
    for (const ext of requiredExtensions) {
      if (!installedExtensions.includes(ext)) {
        console.warn(`⚠️  Security extension '${ext}' not found`);
      }
    }
  }

  async runMultiTenantIsolationTests() {
    console.log('🏢 Running Multi-tenant Isolation Tests...\n');
    
    const testSuite = {
      name: 'Multi-tenant Isolation',
      command: 'npx jest testing/security/multi-tenant-isolation.test.js --verbose --json',
      severity: 'CRITICAL'
    };
    
    await this.runSecurityTestSuite(testSuite, 'multiTenantIsolation');
  }

  async runAccessControlTests() {
    console.log('\n🔐 Running Access Control Tests...\n');
    
    const testSuite = {
      name: 'Access Control and RBAC',
      command: 'npx jest testing/security/access-control.test.js --verbose --json',
      severity: 'HIGH'
    };
    
    await this.runSecurityTestSuite(testSuite, 'accessControl');
  }

  async runAuthenticationTests() {
    console.log('\n🔑 Running Authentication Security Tests...\n');
    
    // Run authentication-specific tests
    const testSuites = [
      {
        name: 'JWT Security',
        command: 'npx jest testing/security/access-control.test.js --testNamePattern="Authentication Security" --json',
        severity: 'HIGH'
      },
      {
        name: 'Session Security',
        command: 'npx jest testing/security/access-control.test.js --testNamePattern="Session Security" --json',
        severity: 'MEDIUM'
      }
    ];
    
    for (const suite of testSuites) {
      await this.runSecurityTestSuite(suite, 'authentication');
    }
  }

  async runDataProtectionTests() {
    console.log('\n🛡️  Running Data Protection Tests...\n');
    
    const testSuites = [
      {
        name: 'Input Validation',
        command: 'npx jest testing/security/access-control.test.js --testNamePattern="Input Validation" --json',
        severity: 'MEDIUM'
      },
      {
        name: 'Data Leakage Prevention',
        command: 'npx jest testing/security/multi-tenant-isolation.test.js --testNamePattern="Data Leakage" --json',
        severity: 'HIGH'
      }
    ];
    
    for (const suite of testSuites) {
      await this.runSecurityTestSuite(suite, 'dataProtection');
    }
  }

  async runSecurityTestSuite(suite, category) {
    console.log(`  🔍 ${suite.name}...`);
    
    try {
      const startTime = Date.now();
      
      const result = execSync(suite.command, {
        cwd: path.join(__dirname, '../..'),
        timeout: 120000, // 2 minutes timeout
        stdio: 'pipe'
      });
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      
      const testResult = JSON.parse(result.toString());
      
      this.results[category][suite.name] = {
        passed: testResult.success,
        executionTime,
        numTests: testResult.numTotalTests,
        numPassed: testResult.numPassedTests,
        numFailed: testResult.numFailedTests,
        severity: suite.severity,
        issues: this.categorizeSecurityIssues(testResult, suite.severity)
      };
      
      this.results.overall.testsRun += testResult.numTotalTests;
      this.results.overall.testsPassed += testResult.numPassedTests;
      this.results.overall.testsFailed += testResult.numFailedTests;
      
      // Count security issues by severity
      if (testResult.numFailedTests > 0) {
        switch (suite.severity) {
          case 'CRITICAL':
            this.results.overall.criticalIssues += testResult.numFailedTests;
            break;
          case 'HIGH':
            this.results.overall.highIssues += testResult.numFailedTests;
            break;
          case 'MEDIUM':
            this.results.overall.mediumIssues += testResult.numFailedTests;
            break;
          default:
            this.results.overall.lowIssues += testResult.numFailedTests;
        }
      }
      
      const status = testResult.success ? '✅' : '❌';
      const severityIcon = this.getSeverityIcon(suite.severity);
      
      console.log(`    ${status} ${severityIcon} ${suite.name} - ${testResult.numPassed}/${testResult.numTotalTests} passed (${executionTime}ms)`);
      
      if (!testResult.success) {
        console.log(`      ⚠️  ${testResult.numFailedTests} ${suite.severity} security issues found`);
      }
      
    } catch (error) {
      console.log(`    ❌ ${suite.name} - Error: ${error.message}`);
      
      this.results[category][suite.name] = {
        passed: false,
        executionTime: null,
        error: error.message,
        severity: suite.severity,
        issues: []
      };
      
      this.results.overall.testsFailed++;
      
      if (suite.severity === 'CRITICAL') {
        this.results.overall.criticalIssues++;
      }
    }
  }

  categorizeSecurityIssues(testResult, severity) {
    const issues = [];
    
    if (testResult.testResults && testResult.testResults.length > 0) {
      testResult.testResults.forEach(result => {
        if (result.status === 'failed') {
          result.assertionResults.forEach(assertion => {
            if (assertion.status === 'failed') {
              issues.push({
                test: assertion.title,
                severity: severity,
                message: assertion.failureMessages ? assertion.failureMessages[0] : 'Test failed',
                location: result.name
              });
            }
          });
        }
      });
    }
    
    return issues;
  }

  getSeverityIcon(severity) {
    switch (severity) {
      case 'CRITICAL': return '🚨';
      case 'HIGH': return '🔴';
      case 'MEDIUM': return '🟡';
      case 'LOW': return '🟢';
      default: return '⚪';
    }
  }

  async runVulnerabilityScans() {
    console.log('\n🔎 Running Vulnerability Scans...\n');
    
    await this.scanDatabaseSecurity();
    await this.scanPasswordPolicies();
    await this.scanEncryptionStatus();
  }

  async scanDatabaseSecurity() {
    console.log('  🗄️  Database Security Scan...');
    
    try {
      // Check for RLS enabled tables
      const rlsCheck = await this.pool.query(`
        SELECT schemaname, tablename, rowsecurity
        FROM pg_tables 
        WHERE schemaname = 'public' AND rowsecurity = true
      `);
      
      console.log(`    ✅ ${rlsCheck.rows.length} tables have Row Level Security enabled`);
      
      // Check for unencrypted sensitive columns
      const sensitiveColumns = await this.pool.query(`
        SELECT table_name, column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'public' 
          AND (column_name LIKE '%password%' OR column_name LIKE '%secret%' OR column_name LIKE '%token%')
          AND data_type = 'text'
      `);
      
      if (sensitiveColumns.rows.length > 0) {
        console.log(`    ⚠️  ${sensitiveColumns.rows.length} potentially unencrypted sensitive columns found`);
        this.results.overall.mediumIssues += sensitiveColumns.rows.length;
      } else {
        console.log('    ✅ No unencrypted sensitive columns detected');
      }
      
    } catch (error) {
      console.log(`    ❌ Database security scan failed: ${error.message}`);
    }
  }

  async scanPasswordPolicies() {
    console.log('  🔒 Password Policy Scan...');
    
    try {
      // Check for weak password hashes (this is a simplified check)
      const weakPasswords = await this.pool.query(`
        SELECT COUNT(*) as count
        FROM users 
        WHERE password_hash IS NOT NULL 
          AND (LENGTH(password_hash) < 50 OR password_hash NOT LIKE '$2b$%')
      `);
      
      if (parseInt(weakPasswords.rows[0].count) > 0) {
        console.log(`    ⚠️  ${weakPasswords.rows[0].count} users with potentially weak password hashes`);
        this.results.overall.mediumIssues += parseInt(weakPasswords.rows[0].count);
      } else {
        console.log('    ✅ All password hashes appear to use strong algorithms');
      }
      
    } catch (error) {
      console.log(`    ❌ Password policy scan failed: ${error.message}`);
    }
  }

  async scanEncryptionStatus() {
    console.log('  🔐 Encryption Status Scan...');
    
    try {
      // Check SSL connection status
      const sslStatus = await this.pool.query('SHOW ssl');
      console.log(`    ℹ️  Database SSL status: ${sslStatus.rows[0].ssl}`);
      
      // Check for encrypted columns (simplified check)
      const encryptedColumns = await this.pool.query(`
        SELECT table_name, column_name
        FROM information_schema.columns
        WHERE table_schema = 'public' 
          AND (column_name LIKE '%encrypted%' OR column_name LIKE '%cipher%')
      `);
      
      console.log(`    ℹ️  ${encryptedColumns.rows.length} encrypted columns detected`);
      
    } catch (error) {
      console.log(`    ❌ Encryption status scan failed: ${error.message}`);
    }
  }

  async generateSecurityReport() {
    this.results.overall.endTime = Date.now();
    this.results.overall.totalDuration = this.results.overall.endTime - this.results.overall.startTime;
    
    console.log('\n' + '=' * 60);
    console.log('🔒 SECURITY TEST REPORT');
    console.log('=' * 60);
    
    // Overall security status
    console.log(`\n🛡️  Overall Security Status:`);
    console.log(`  ✅ Tests Passed: ${this.results.overall.testsPassed}`);
    console.log(`  ❌ Tests Failed: ${this.results.overall.testsFailed}`);
    console.log(`  📊 Total Tests: ${this.results.overall.testsRun}`);
    console.log(`  ⏱️  Total Duration: ${(this.results.overall.totalDuration / 1000).toFixed(2)}s`);
    
    // Security issues by severity
    console.log(`\n🚨 Security Issues by Severity:`);
    console.log(`  🚨 Critical: ${this.results.overall.criticalIssues}`);
    console.log(`  🔴 High: ${this.results.overall.highIssues}`);
    console.log(`  🟡 Medium: ${this.results.overall.mediumIssues}`);
    console.log(`  🟢 Low: ${this.results.overall.lowIssues}`);
    
    // Detailed results by category
    console.log(`\n📋 Detailed Results:`);
    Object.entries(this.results).forEach(([category, tests]) => {
      if (category === 'overall') return;
      
      console.log(`\n  ${category.toUpperCase()}:`);
      Object.entries(tests).forEach(([testName, result]) => {
        const status = result.passed ? '✅' : '❌';
        const severityIcon = this.getSeverityIcon(result.severity);
        
        console.log(`    ${status} ${severityIcon} ${testName}: ${result.numPassed || 0}/${result.numTests || 0} passed`);
        
        if (result.issues && result.issues.length > 0) {
          result.issues.forEach(issue => {
            console.log(`      - ${issue.test}: ${issue.message}`);
          });
        }
      });
    });
    
    // Security recommendations
    this.generateSecurityRecommendations();
    
    // Save detailed report
    await this.saveSecurityReport();
    
    console.log('\n' + '=' * 60);
    
    // Determine overall security status
    const hasSecurityIssues = this.results.overall.criticalIssues > 0 || this.results.overall.highIssues > 0;
    
    if (!hasSecurityIssues && this.results.overall.testsFailed === 0) {
      console.log('🎉 All security tests passed! System is secure for production.');
      process.exit(0);
    } else {
      console.log('❌ Security issues found. Please address before deploying to production.');
      process.exit(1);
    }
  }

  generateSecurityRecommendations() {
    console.log(`\n💡 Security Recommendations:`);
    
    if (this.results.overall.criticalIssues > 0) {
      console.log('  🚨 CRITICAL: Address multi-tenant isolation issues immediately');
      console.log('  🚨 CRITICAL: Review and fix access control vulnerabilities');
    }
    
    if (this.results.overall.highIssues > 0) {
      console.log('  🔴 HIGH: Strengthen authentication mechanisms');
      console.log('  🔴 HIGH: Review role-based access control implementation');
    }
    
    if (this.results.overall.mediumIssues > 0) {
      console.log('  🟡 MEDIUM: Improve input validation and sanitization');
      console.log('  🟡 MEDIUM: Consider implementing additional encryption');
    }
    
    console.log('  📚 GENERAL: Regular security audits and penetration testing recommended');
    console.log('  📚 GENERAL: Keep all dependencies updated and monitor for vulnerabilities');
  }

  async saveSecurityReport() {
    const reportPath = path.join(__dirname, '../reports/security-report.json');
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const detailedReport = {
      ...this.results,
      timestamp: new Date().toISOString(),
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        dbHost: process.env.DB_HOST || 'localhost',
        dbName: process.env.DB_NAME || 'ecommerce_analytics_test'
      },
      securityChecks: this.securityChecks
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(detailedReport, null, 2));
    console.log(`\n📄 Detailed security report saved to: ${reportPath}`);
  }

  async cleanup() {
    if (this.pool) {
      await this.pool.end();
    }
  }
}

// Run the security test suite if this script is executed directly
if (require.main === module) {
  const runner = new SecurityTestRunner();
  runner.runAllTests().catch(error => {
    console.error('Security test runner failed:', error);
    process.exit(1);
  });
}

module.exports = SecurityTestRunner;
