const { Pool } = require('pg');
const request = require('supertest');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

describe('Multi-tenant Isolation Security Tests', () => {
  let pool;
  let tenant1, tenant2, tenant3;
  let user1, user2, user3, adminUser;
  let tenant1Token, tenant2Token, tenant3Token, adminToken;

  beforeAll(async () => {
    // Setup database connection
    pool = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'ecommerce_analytics_test',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
      max: 10,
    });

    await setupTestTenants();
    await setupTestUsers();
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await pool.end();
  });

  async function setupTestTenants() {
    // Create test tenants
    const tenantResults = await pool.query(`
      INSERT INTO tenants (id, name, slug, plan, is_active, created_at)
      VALUES 
        ('tenant-1', 'Tenant One', 'tenant-one', 'enterprise', true, NOW()),
        ('tenant-2', 'Tenant Two', 'tenant-two', 'professional', true, NOW()),
        ('tenant-3', 'Tenant Three', 'tenant-three', 'basic', true, NOW())
      RETURNING *
    `);

    [tenant1, tenant2, tenant3] = tenantResults.rows;
  }

  async function setupTestUsers() {
    // Create test users
    const userResults = await pool.query(`
      INSERT INTO users (id, email, password_hash, first_name, last_name, role, is_active, created_at)
      VALUES 
        ('user-1', '<EMAIL>', '$2b$10$hash1', 'User', 'One', 'user', true, NOW()),
        ('user-2', '<EMAIL>', '$2b$10$hash2', 'User', 'Two', 'user', true, NOW()),
        ('user-3', '<EMAIL>', '$2b$10$hash3', 'User', 'Three', 'user', true, NOW()),
        ('admin-user', '<EMAIL>', '$2b$10$hashadmin', 'Admin', 'User', 'super_admin', true, NOW())
      RETURNING *
    `);

    [user1, user2, user3, adminUser] = userResults.rows;

    // Create user-tenant relationships
    await pool.query(`
      INSERT INTO user_tenants (user_id, tenant_id, role)
      VALUES 
        ('user-1', 'tenant-1', 'admin'),
        ('user-2', 'tenant-2', 'admin'),
        ('user-3', 'tenant-3', 'member')
    `);

    // Generate JWT tokens for testing
    const secret = process.env.JWT_SECRET || 'test-secret';
    tenant1Token = jwt.sign({ userId: user1.id, tenantId: tenant1.id, role: 'admin' }, secret);
    tenant2Token = jwt.sign({ userId: user2.id, tenantId: tenant2.id, role: 'admin' }, secret);
    tenant3Token = jwt.sign({ userId: user3.id, tenantId: tenant3.id, role: 'member' }, secret);
    adminToken = jwt.sign({ userId: adminUser.id, role: 'super_admin' }, secret);
  }

  async function setupTestData() {
    // Create integrations for each tenant
    await pool.query(`
      INSERT INTO integrations (id, tenant_id, platform, store_url, api_credentials, is_active)
      VALUES 
        ('integration-1', 'tenant-1', 'shopify', 'tenant1.myshopify.com', '{"token": "token1"}', true),
        ('integration-2', 'tenant-2', 'woocommerce', 'https://tenant2.com', '{"key": "key2"}', true),
        ('integration-3', 'tenant-3', 'ebay', 'ebay.com', '{"client_id": "client3"}', true)
    `);

    // Create links for each tenant
    await pool.query(`
      INSERT INTO links (id, tenant_id, original_url, short_code, created_at)
      VALUES 
        ('link-1', 'tenant-1', 'https://tenant1.com/product1', 'T1P1', NOW()),
        ('link-2', 'tenant-2', 'https://tenant2.com/product2', 'T2P2', NOW()),
        ('link-3', 'tenant-3', 'https://tenant3.com/product3', 'T3P3', NOW())
    `);

    // Create clicks for each tenant
    await pool.query(`
      INSERT INTO clicks (id, tenant_id, link_id, ip_address, user_agent, clicked_at)
      VALUES 
        ('click-1', 'tenant-1', 'link-1', '***********', 'Browser1', NOW()),
        ('click-2', 'tenant-2', 'link-2', '***********', 'Browser2', NOW()),
        ('click-3', 'tenant-3', 'link-3', '***********', 'Browser3', NOW())
    `);

    // Create orders for each tenant
    await pool.query(`
      INSERT INTO orders (id, integration_id, platform_order_id, customer_email, total_amount, status, created_at)
      VALUES 
        ('order-1', 'integration-1', 'shopify-order-1', '<EMAIL>', 100.00, 'completed', NOW()),
        ('order-2', 'integration-2', 'woo-order-2', '<EMAIL>', 200.00, 'completed', NOW()),
        ('order-3', 'integration-3', 'ebay-order-3', '<EMAIL>', 300.00, 'completed', NOW())
    `);
  }

  async function cleanupTestData() {
    const tables = ['orders', 'clicks', 'links', 'integrations', 'user_tenants', 'users', 'tenants'];
    
    for (const table of tables) {
      await pool.query(`DELETE FROM ${table} WHERE id LIKE 'tenant-%' OR id LIKE 'user-%' OR id LIKE 'admin-%' OR id LIKE 'integration-%' OR id LIKE 'link-%' OR id LIKE 'click-%' OR id LIKE 'order-%'`);
    }
  }

  describe('Data Isolation Tests', () => {
    it('should prevent cross-tenant data access in links', async () => {
      // Tenant 1 should only see their own links
      const tenant1Links = await pool.query(
        'SELECT * FROM links WHERE tenant_id = $1',
        [tenant1.id]
      );
      expect(tenant1Links.rows).toHaveLength(1);
      expect(tenant1Links.rows[0].id).toBe('link-1');

      // Tenant 2 should only see their own links
      const tenant2Links = await pool.query(
        'SELECT * FROM links WHERE tenant_id = $1',
        [tenant2.id]
      );
      expect(tenant2Links.rows).toHaveLength(1);
      expect(tenant2Links.rows[0].id).toBe('link-2');

      // Cross-tenant query should return empty
      const crossTenantQuery = await pool.query(
        'SELECT * FROM links WHERE tenant_id = $1 AND id = $2',
        [tenant1.id, 'link-2'] // Tenant 1 trying to access Tenant 2's link
      );
      expect(crossTenantQuery.rows).toHaveLength(0);
    });

    it('should prevent cross-tenant data access in clicks', async () => {
      // Each tenant should only see their own clicks
      const tenant1Clicks = await pool.query(
        'SELECT * FROM clicks WHERE tenant_id = $1',
        [tenant1.id]
      );
      expect(tenant1Clicks.rows).toHaveLength(1);
      expect(tenant1Clicks.rows[0].id).toBe('click-1');

      // Verify clicks are properly isolated by link_id as well
      const crossTenantClickQuery = await pool.query(`
        SELECT c.* FROM clicks c
        JOIN links l ON c.link_id = l.id
        WHERE c.tenant_id = $1 AND l.tenant_id != $1
      `, [tenant1.id]);
      expect(crossTenantClickQuery.rows).toHaveLength(0);
    });

    it('should prevent cross-tenant data access in orders', async () => {
      // Each tenant should only see orders from their integrations
      const tenant1Orders = await pool.query(`
        SELECT o.* FROM orders o
        JOIN integrations i ON o.integration_id = i.id
        WHERE i.tenant_id = $1
      `, [tenant1.id]);
      expect(tenant1Orders.rows).toHaveLength(1);
      expect(tenant1Orders.rows[0].id).toBe('order-1');

      // Cross-tenant order access should be prevented
      const crossTenantOrderQuery = await pool.query(`
        SELECT o.* FROM orders o
        JOIN integrations i ON o.integration_id = i.id
        WHERE i.tenant_id = $1 AND o.id = $2
      `, [tenant1.id, 'order-2']); // Tenant 1 trying to access Tenant 2's order
      expect(crossTenantOrderQuery.rows).toHaveLength(0);
    });

    it('should prevent cross-tenant data access in integrations', async () => {
      // Each tenant should only see their own integrations
      const tenant1Integrations = await pool.query(
        'SELECT * FROM integrations WHERE tenant_id = $1',
        [tenant1.id]
      );
      expect(tenant1Integrations.rows).toHaveLength(1);
      expect(tenant1Integrations.rows[0].platform).toBe('shopify');

      // Cross-tenant integration access should be prevented
      const crossTenantIntegrationQuery = await pool.query(
        'SELECT * FROM integrations WHERE tenant_id = $1 AND id = $2',
        [tenant1.id, 'integration-2']
      );
      expect(crossTenantIntegrationQuery.rows).toHaveLength(0);
    });
  });

  describe('Row-Level Security (RLS) Tests', () => {
    it('should enforce RLS policies on sensitive tables', async () => {
      // Test that RLS is enabled on critical tables
      const rlsStatus = await pool.query(`
        SELECT schemaname, tablename, rowsecurity
        FROM pg_tables 
        WHERE tablename IN ('users', 'tenants', 'integrations', 'orders', 'clicks', 'links')
          AND schemaname = 'public'
      `);

      // Verify RLS is enabled where expected
      const tablesWithRLS = rlsStatus.rows.filter(row => row.rowsecurity);
      expect(tablesWithRLS.length).toBeGreaterThan(0);
    });

    it('should test RLS policy effectiveness with different user contexts', async () => {
      // Create a function to set user context
      await pool.query(`
        CREATE OR REPLACE FUNCTION set_current_user_id(user_id UUID)
        RETURNS void AS $$
        BEGIN
          PERFORM set_config('app.current_user_id', user_id::text, true);
        END;
        $$ LANGUAGE plpgsql;
      `);

      // Test with user 1 context
      await pool.query('SELECT set_current_user_id($1)', [user1.id]);
      
      const user1Context = await pool.query(`
        SELECT * FROM user_tenants WHERE user_id = current_setting('app.current_user_id')::uuid
      `);
      expect(user1Context.rows).toHaveLength(1);
      expect(user1Context.rows[0].tenant_id).toBe(tenant1.id);

      // Test with user 2 context
      await pool.query('SELECT set_current_user_id($1)', [user2.id]);
      
      const user2Context = await pool.query(`
        SELECT * FROM user_tenants WHERE user_id = current_setting('app.current_user_id')::uuid
      `);
      expect(user2Context.rows).toHaveLength(1);
      expect(user2Context.rows[0].tenant_id).toBe(tenant2.id);
    });
  });

  describe('Authentication and Authorization Tests', () => {
    it('should validate JWT token tenant isolation', () => {
      const secret = process.env.JWT_SECRET || 'test-secret';
      
      // Decode and verify tenant 1 token
      const decoded1 = jwt.verify(tenant1Token, secret);
      expect(decoded1.tenantId).toBe(tenant1.id);
      expect(decoded1.userId).toBe(user1.id);

      // Decode and verify tenant 2 token
      const decoded2 = jwt.verify(tenant2Token, secret);
      expect(decoded2.tenantId).toBe(tenant2.id);
      expect(decoded2.userId).toBe(user2.id);

      // Verify tokens are different
      expect(decoded1.tenantId).not.toBe(decoded2.tenantId);
      expect(decoded1.userId).not.toBe(decoded2.userId);
    });

    it('should prevent unauthorized tenant access', async () => {
      // Test that user 1 cannot access tenant 2 data
      const unauthorizedAccess = await pool.query(`
        SELECT ut.* FROM user_tenants ut
        WHERE ut.user_id = $1 AND ut.tenant_id = $2
      `, [user1.id, tenant2.id]);
      
      expect(unauthorizedAccess.rows).toHaveLength(0);

      // Test that user 2 cannot access tenant 1 data
      const unauthorizedAccess2 = await pool.query(`
        SELECT ut.* FROM user_tenants ut
        WHERE ut.user_id = $1 AND ut.tenant_id = $2
      `, [user2.id, tenant1.id]);
      
      expect(unauthorizedAccess2.rows).toHaveLength(0);
    });

    it('should validate role-based access within tenants', async () => {
      // User 1 is admin of tenant 1
      const user1Role = await pool.query(`
        SELECT role FROM user_tenants WHERE user_id = $1 AND tenant_id = $2
      `, [user1.id, tenant1.id]);
      expect(user1Role.rows[0].role).toBe('admin');

      // User 3 is member of tenant 3
      const user3Role = await pool.query(`
        SELECT role FROM user_tenants WHERE user_id = $1 AND tenant_id = $2
      `, [user3.id, tenant3.id]);
      expect(user3Role.rows[0].role).toBe('member');
    });
  });

  describe('API Endpoint Isolation Tests', () => {
    // Note: These tests would require the actual API server to be running
    // For now, we'll test the middleware logic directly

    it('should validate tenant middleware logic', () => {
      const mockReq = {
        user: { id: user1.id, tenantId: tenant1.id, role: 'admin' },
        params: { tenantId: tenant1.id }
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const mockNext = jest.fn();

      // Simulate the requireTenant middleware
      const requireTenant = (req, res, next) => {
        const tenantId = req.params.tenantId;
        
        if (!tenantId) {
          return res.status(400).json({ error: 'Tenant ID required' });
        }

        if (!req.user) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        if (req.user.tenantId !== tenantId && req.user.role !== 'super_admin') {
          return res.status(403).json({ error: 'Access denied for this tenant' });
        }

        next();
      };

      requireTenant(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should reject cross-tenant access in middleware', () => {
      const mockReq = {
        user: { id: user1.id, tenantId: tenant1.id, role: 'admin' },
        params: { tenantId: tenant2.id } // Trying to access different tenant
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const mockNext = jest.fn();

      const requireTenant = (req, res, next) => {
        const tenantId = req.params.tenantId;
        
        if (req.user.tenantId !== tenantId && req.user.role !== 'super_admin') {
          return res.status(403).json({ error: 'Access denied for this tenant' });
        }

        next();
      };

      requireTenant(mockReq, mockRes, mockNext);
      expect(mockNext).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Access denied for this tenant' });
    });
  });

  describe('Data Leakage Prevention Tests', () => {
    it('should prevent data leakage through aggregation queries', async () => {
      // Test that aggregation queries respect tenant boundaries
      const aggregationQuery = await pool.query(`
        SELECT 
          COUNT(*) as total_clicks,
          COUNT(DISTINCT ip_address) as unique_visitors
        FROM clicks 
        WHERE tenant_id = $1
      `, [tenant1.id]);

      expect(aggregationQuery.rows[0].total_clicks).toBe('1');
      expect(aggregationQuery.rows[0].unique_visitors).toBe('1');

      // Verify that global aggregation without tenant filter is not accessible
      // (This would be prevented by application logic, not database constraints)
      const globalAggregation = await pool.query(`
        SELECT tenant_id, COUNT(*) as clicks
        FROM clicks 
        GROUP BY tenant_id
      `);

      // Each tenant should only see their own data
      expect(globalAggregation.rows).toHaveLength(3);
      globalAggregation.rows.forEach(row => {
        expect(row.clicks).toBe('1');
      });
    });

    it('should prevent data leakage through JOIN operations', async () => {
      // Test that JOINs respect tenant boundaries
      const joinQuery = await pool.query(`
        SELECT 
          l.short_code,
          COUNT(c.id) as click_count
        FROM links l
        LEFT JOIN clicks c ON l.id = c.link_id
        WHERE l.tenant_id = $1
        GROUP BY l.id, l.short_code
      `, [tenant1.id]);

      expect(joinQuery.rows).toHaveLength(1);
      expect(joinQuery.rows[0].short_code).toBe('T1P1');
      expect(joinQuery.rows[0].click_count).toBe('1');

      // Verify that cross-tenant JOINs don't leak data
      const crossTenantJoin = await pool.query(`
        SELECT 
          l.short_code,
          c.ip_address
        FROM links l
        JOIN clicks c ON l.id = c.link_id
        WHERE l.tenant_id = $1 AND c.tenant_id = $2
      `, [tenant1.id, tenant2.id]);

      expect(crossTenantJoin.rows).toHaveLength(0);
    });
  });

  describe('Tenant Configuration Isolation Tests', () => {
    it('should isolate tenant-specific configurations', async () => {
      // Create tenant-specific configurations
      await pool.query(`
        INSERT INTO tenant_configs (tenant_id, config_key, config_value)
        VALUES 
          ('tenant-1', 'analytics_retention_days', '365'),
          ('tenant-2', 'analytics_retention_days', '180'),
          ('tenant-3', 'analytics_retention_days', '90')
        ON CONFLICT (tenant_id, config_key) DO UPDATE SET config_value = EXCLUDED.config_value
      `);

      // Each tenant should only see their own configuration
      const tenant1Config = await pool.query(`
        SELECT config_value FROM tenant_configs 
        WHERE tenant_id = $1 AND config_key = 'analytics_retention_days'
      `, [tenant1.id]);
      expect(tenant1Config.rows[0].config_value).toBe('365');

      const tenant2Config = await pool.query(`
        SELECT config_value FROM tenant_configs 
        WHERE tenant_id = $1 AND config_key = 'analytics_retention_days'
      `, [tenant2.id]);
      expect(tenant2Config.rows[0].config_value).toBe('180');

      // Cross-tenant config access should be prevented
      const crossTenantConfig = await pool.query(`
        SELECT config_value FROM tenant_configs 
        WHERE tenant_id = $1 AND config_key = 'analytics_retention_days'
      `, [tenant1.id]);
      expect(crossTenantConfig.rows[0].config_value).not.toBe('180');
    });
  });
});
